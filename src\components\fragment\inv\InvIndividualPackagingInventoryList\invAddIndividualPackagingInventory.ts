import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// 個装在庫追加ダイアログのアイテム定義
export const getIndividualInventoryAddFormItems: () => CustomFormType['formItems'] =
  () => ({
    unitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    matNo: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtItemCode') },
      formModelValue: '',
      rules: [rules.required('textBox')],
      formRole: 'textBox',
      props: { disabled: true },
    },
    matNm: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtItemName') },
      formModelValue: '',
      rules: [rules.required('textBox')],
      formRole: 'textBox',
      props: { disabled: true },
    },
    lotNoSel: {
      formModelValue: '',
      formRole: 'button',
      props: { text: t('Inv.Chr.txtManageNoSelect') },
      onClickHandler() {},
    },
    lotNo: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtManageNo') },
      formModelValue: '',
      rules: [rules.required('textBox')],
      formRole: 'textBox',
      props: { disabled: true },
    },
    totalInvQty: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtTotalInvQty') },
      formModelValue: '',
      suffix: { formModelProp: 'unitNm' },
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.positiveRealNumericOnly(),
      ],
      formRole: 'textBox',
    },
    invQty: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtInvQty') },
      formModelValue: '',
      suffix: { formModelProp: 'unitNm' },
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.positiveRealNumericOnly(),
      ],
      formRole: 'textBox',
    },
    invPackageQty: {
      label: { text: t('Inv.Chr.txtInvPackageQty') },
      formModelValue: '',
      props: { disabled: true },
      formRole: 'textBox',
    },
    zoneNo: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtZone') },
      formModelValue: '',
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: 'zoneNo',
    },
    locNo: {
      label: { text: t('Inv.Chr.txtLocation') },
      formModelValue: '',
      rules: [],
      formRole: 'selectComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: 'locNo',
    },
    accrualDts: {
      label: { text: t('Inv.Chr.txtAccrualDate') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    afmRefNo: {
      label: { text: t('Inv.Chr.txtAfmRefNo') },
      formModelValue: '',
      formRole: 'textBox',
      rules: [
        rules.length(16, t('Cm.Chr.txtLength', [16])),
        rules.upperCaseSingleByteAlphanumeric(),
      ],
    },
    icRsnCd: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtReasonCode') },
      formModelValue: '',
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: 'rsnCd',
    },
    cmtCatInvMod: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtComment') },
      formModelValue: '',
      rules: [
        rules.required('textComboBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
      ],
      formRole: 'textComboBox',
      props: { clearable: true },
      selectOptions: [],
      cmbId: 'cmtInvAdd',
    },
  });

// 個装在庫追加ダイアログのモデル定義
export const individualInventoryAddFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getIndividualInventoryAddFormItems());
