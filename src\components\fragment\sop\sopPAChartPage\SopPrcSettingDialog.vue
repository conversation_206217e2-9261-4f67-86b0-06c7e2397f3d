<template>
  <!-- SOP全体設定ダイアログ -->
  <DialogWindow
    :title="$t('SOP.Chr.txtSopSettingTitle')"
    :dialogVisible="props.dialogVisible"
    :onReject="() => handleMessageBoxClose()"
    :onResolve="async () => resolveClickHandler()"
    :closeOnClickModal="false"
  >
    <CustomForm
      :formModel="sopSettingDialogFormRef.formModel"
      :formItems="sopSettingDialogFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          sopSettingDialogFormRef.customForm = v;
        }
      "
    />
    <MessageBox
      v-show="dialogVisibleRef.singleButtonRef"
      :dialog-props="messageBoxSingleButtonRef"
      :cancelCallback="() => handleMessageBoxClose()"
      :submitCallback="() => handleMessageBoxClose()"
    />
  </DialogWindow>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { useUpdatePrcSopFlow } from '@/hooks/useApi';
import { UpdatePrcSopFlow } from '@/types/HookUseApi/SopTypes';
import CreateMessageBox from '@/components/parts/MessageBox/CreateMessageBox';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { PrcSopSettingOption } from '@/types/SopDialogInterface';
import CONST from '@/constants/utils';
import { toNumberOrNull } from '@/utils';
import { RuleOption } from '@/types/ValidatorTypes';
import {
  sopPrcSettingDialogFormItems,
  sopPrcSettingDialogFormModel,
  fromToDate,
} from './sopPrcSettingDialog';

type Props = {
  dialogVisible: boolean;
  commonRequest: CommonRequestType;
  sopSetting: PrcSopSettingOption;
};
type DialogRefKey = 'singleButtonRef';

/**
 * 多言語
 */
const props = defineProps<Props>();
const { t } = useI18n();

const sopSettingDialogFormRef = ref<CustomFormType>({
  formItems: sopPrcSettingDialogFormItems,
  formModel: sopPrcSettingDialogFormModel,
});
const initialState: InitialDialogState<DialogRefKey> = {
  singleButtonRef: false,
};
const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
const emit = defineEmits(['update:dialogVisible', 'closeDialog']);
const handleMessageBoxClose = () => {
  closeDialog('singleButtonRef');
  emit('update:dialogVisible', false);
  emit('closeDialog');
};
const checkValidate = () => {
  const settingItem = [
    'stYmd',
    'sopFlowNmJp',
    'recApprovFlg',
    'forcePrivGrpCd',
    'edYmd',
  ];
  // @ts-expect-error keyを利用して、valueを取得します
  const validate = settingItem.every((item) => props.sopSetting[item] !== '');
  return validate;
};
/**
 * 終了日のルールを設定
 * @param {RuleOption[]} rules - ルール
 * @param {string} dataId - データID
 */
const getEndDateRules = (rules: RuleOption[], dataId: string) =>
  rules.map((item: RuleOption) => {
    let rectItem = { ...item };
    if ('type' in item) {
      if (item.type === 'fromToDate') {
        rectItem = {
          ...item,
          validator: fromToDate,
          dataId,
        };
      }
    }
    return rectItem;
  });
/**
 * 実行ボタンクリックイベント
 */
const resolveClickHandler = async () => {
  const validate =
    sopSettingDialogFormRef.value.customForm !== undefined &&
    (await sopSettingDialogFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (!validate) {
    return false;
  }
  const pdfExtensions: string[] = ['pdf'];
  if (sopSettingDialogFormRef.value.formModel.helpBinPath !== '') {
    const helpPathFlg = pdfExtensions.some((extension: string) => {
      if (
        sopSettingDialogFormRef.value.formModel.helpBinPath
          .toString()
          .endsWith(`.${extension}`)
      ) {
        return true;
      }
      return false;
    });
    if (!helpPathFlg) {
      CreateMessageBox({
        dialogProps: {
          title: t('Cm.Chr.txtValidationError'),
          content: t('SOP.Msg.filePathSOPSettingValidationError'),
          type: 'error',
          isSingleBtn: true,
        },
      });
      return false;
    }
  }
  // 工程任意SOPフローマスタ情報を更新する
  const apiRequestData: UpdatePrcSopFlow = {
    sopFlowNo: <string>sopSettingDialogFormRef.value.formModel.sopFlowNo,
    sopFlowNmJp: <string>sopSettingDialogFormRef.value.formModel.sopFlowNmJp,
    stYmd: <string>sopSettingDialogFormRef.value.formModel.stYmd,
    edYmd: <string>sopSettingDialogFormRef.value.formModel.edYmd,
    recApprovFlg: <string>sopSettingDialogFormRef.value.formModel.recApprovFlg,
    helpBinPath: <string>sopSettingDialogFormRef.value.formModel.helpBinPath,
    forcePrivGrpCd: <string>(
      sopSettingDialogFormRef.value.formModel.forcePrivGrpCd
    ),
    skipPrivGrpCd: <string>(
      sopSettingDialogFormRef.value.formModel.skipPrivGrpCd
    ),
    dspSeq: toNumberOrNull(sopSettingDialogFormRef.value.formModel.dspSeq),
    updDts: props.sopSetting.updDts,
  };
  const { responseRef, errorRef } = await useUpdatePrcSopFlow({
    ...props.commonRequest,
    btnId: 'btnDecision',
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    openDialog('singleButtonRef');
    messageBoxSingleButtonRef.value.type = 'error';
    return false;
  }
  if (responseRef.value?.data.rCode === CONST.API_STATUS_CODE.SUCCESS) {
    messageBoxSingleButtonRef.value.title = responseRef.value.data.rTitle;
    messageBoxSingleButtonRef.value.content = responseRef.value.data.rMsg;
    messageBoxSingleButtonRef.value.type = 'info';
    openDialog('singleButtonRef');
    return false;
  }
  return true;
};

onMounted(() => {
  // 終了日のルールを設定
  sopSettingDialogFormRef.value.formItems.edYmd.rules = getEndDateRules(
    sopSettingDialogFormRef.value.formItems.edYmd.rules!,
    'stYmd',
  ) as RuleOption[];
});

defineExpose({ checkValidate });
</script>
<style lang="scss" scoped>
$namespace: 'order-addition-dialog';

.#{$namespace} {
  background-color: $white750;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
</style>
