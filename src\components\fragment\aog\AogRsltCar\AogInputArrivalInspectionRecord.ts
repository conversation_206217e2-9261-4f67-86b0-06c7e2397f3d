import CONST from '@/constants/utils';
import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

export const getAogInputArrivalInspectionRecord: () => CustomFormType['formItems'] =
  () => ({
    aogInstNo: {
      label: { text: t('Aog.Chr.txtAogInstNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
      span: 12,
    },
    aogInstGrpNo: {
      label: { text: t('Aog.Chr.txtAogInstGrpNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
      span: 12,
    },
    matNo: {
      label: { text: t('Aog.Chr.txtItemCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
      span: 12,
    },
    lotNo: {
      label: { text: t('Aog.Chr.txtLotNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
      span: 12,
    },
    matNm: {
      label: { text: t('Aog.Chr.txtItemName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'large' },
    },
    mBpId: {
      label: { text: t('Aog.Chr.txtBusinessPartnerCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    bpNm: {
      label: { text: t('Aog.Chr.txtBusinessPartnerName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'large' },
    },
    edNo: {
      label: { text: t('Aog.Chr.txtEditionNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    pickPltCnt: {
      label: { text: t('Aog.Chr.txtPalletCnt') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    makerLotNo: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Aog.Chr.txtBusinessPartnerLotNumber') },
      formModelValue: '',
      rules: [
        rules.required('textBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      ],
      formRole: 'textBox',
      props: { clearable: true, size: 'small' },
    },
    shelfLifeYmd: {
      label: { text: t('Aog.Chr.txtShelfDts') },
      formModelValue: '',
      rules: [rules.required('textBox')],
      formRole: 'textBox',
      props: { disabled: false },
    },
    expiryYmd: {
      label: { text: t('Aog.Chr.txtExpiryDts') },
      formModelValue: '',
      rules: [rules.required('textBox')],
      formRole: 'textBox',
      props: { disabled: false },
    },
  });

export const AogRsltCarFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getAogInputArrivalInspectionRecord());
