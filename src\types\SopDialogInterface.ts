import { Node, Edge } from '@antv/x6';
import type { ComboBoxDataOptionData } from '@/types/HookUseApi/CommonTypes';

export interface SopChartResponse {
  code: number;
  nodeList: string;
  blockInfoList: string;
  blockInfoDetailList: string;
}

export interface SelectOptionResponse extends SelectOption {
  code: number;
  optionValues: string;
}

export interface PartItemOption {
  itemId: string;
  itemChilds: string[];
}

export interface PartItemChildOption {
  [key: string]: string[];
}

export interface SelectOption {
  label: string;
  value: string;
}
export interface SelectOptionNumber {
  label: string;
  value: number;
}
export interface SelectBranchOption extends SelectOption {
  type?: string;
  outgoingType?: string;
  outgoingId?: string;
  // 「課題No.292」 ADD ST branchNameを通じて、ブランチが新しいブランチであるかどうかを判断します。
  branchName?: string;
  // 「課題No.292」 ADD ED
}

export interface ImageOption {
  fileName: string;
  path: string;
}
export interface OperatorsOption {
  type: string;
  icon: string;
  name: string;
}

export interface PositionOption {
  x: number;
  y: number;
}

export interface InBranchNode {
  index: number;
  childIds: string[];
}

export interface SizeOption {
  width: number;
  height: number;
}
// 「課題No.292」 ADD ST branchNameを通じて、ブランチが新しいブランチであるかどうかを判断できるために、branchMenuを追加します。
export type PartMenuBranchingProps = {
  branchingMethod: string;
  branchesNumSet: number;
  conditional1: string;
  branchMenu1: string;
  branchNodeId1: string;
  conditional2: string;
  branchMenu2: string;
  branchNodeId2: string;
  conditional3: string;
  branchMenu3: string;
  branchNodeId3: string;
  conditional4: string;
  branchMenu4: string;
  branchNodeId4: string;
  conditional5: string;
  branchMenu5: string;
  branchNodeId5: string;
  conditional6: string;
  branchMenu6: string;
  branchNodeId6: string;
  conditional7: string;
  branchMenu7: string;
  branchNodeId7: string;
  conditional8: string;
  branchMenu8: string;
  branchNodeId8: string;
};
// 「課題No.292」 ADD ED

export interface ControlPartCondBranchProps {
  branchNumber: number;
  judgmentValueX: string;
  conditional1: string;
  conditionalNode1: string;
  conditional2: string;
  conditionalNode2: string;
  conditional3: string;
  conditionalNode3: string;
  conditional4: string;
  conditionalNode4: string;
  conditional5: string;
  conditionalNode5: string;
  conditional6: string;
  conditionalNode6: string;
  conditional7: string;
  conditionalNode7: string;
  conditional8: string;
  conditionalNode8: string;
}

export interface ControlPartNumCalcProps {
  expressionInput: string;
  outputSetting: string;
}

export interface ControlPartProgressCheckProps {
  inputValue: string;
  inputSourceSet: string;
}

export interface PartInstructionConfirmProps {
  confirmRecordOptions: string;
  conditionBranch: string;
  deviationBranchNodeId: string;
  deviationMessageText: string;
}
export interface PartSopTimerProps {
  instructionTime: string;
  instructionTimeFixedValue: string;
  instructionTimeNodeId: string;
  startMethod: string;
  judgeValueShowFlg: string;
  conditionBranch: string;
  deviationBranchNodeId: string;
  deviationMessageText: string;
}
export interface PartNumericTextInputProps {
  inputSelection: string;
  instructionValueSetting: string;
  instructionFixedValue: string;
  instructionNodeId: string;
  instructionGCode: string;
  inputMethod: string;
  inputMethodDecimalPlaces: number;
  inputMethodFixedValue: string;
  inputMethodNodeId: string;
  inputMethodGCode: string;
  inputMethodFormula: string;
  inputMethodMatNo: string;
  inputMethodOrderItem: string;
  outputSetting: string;
  outputVolumeContainerSetting: string;
  outputVolumePaletteSetting: string;
  volumePaletteNodeId: string;
  volumeContainerNodeId: string;
  volumeZoneCd: string;
  outputSettingGCode: string;
  outputSettingWorkItem: string;
}
export interface PartDateRecordProps {
  recordMethod: string;
}
export interface PartReceiveConsumptionProps {
  consumptionMatNo: string;
  consumptionSequence: string;
  instructionValueSetting: string;
  instructionNodeId: string;
  instructionGCode: string;
  destinationZone: string;
  destinationZoneCd: string;
  destinationNodeId: string;
  inventoryConsumption: string;
  consumptionReceiveCheckSetting: string;
  fifoZoneGroupCd: string;
}
export interface PartResultConfirmProps {
  preReceive: string;
  preReceiveNodeId: string;
  nonPreReceiveType: string;
  nonPreReceiveMatNo: string;
  nonPreReceiveNodeId: string;
  nonPreReceiveConsumptionSeq: string;
  fifoZoneGroupCd: string;
  instructionValueSetting: string;
  instructionNodeId: string;
  instructionGCode: string;
  destinationZone: string;
  destinationZoneCd: string;
  destinationNodeId: string;
  consumptionReceiveCheck: string;
  inventoryDrawdown: string;
  returnInventory: string;
  returnInventoryZoneCd: string;
  returnInventoryLabel: string;
  disposalAmount: string;
  disposalAmountSelect: string;
  disposalAmount1: string;
  disposalAmountReasonCode1: string;
  disposalAmount2: string;
  disposalAmountReasonCode2: string;
  disposalAmount3: string;
  disposalAmountReasonCode3: string;
  inputMethodDecimalPlaces: number;
}
export interface PartElectronicFileProps {
  processingOriginalData: string;
  attachment: string;
  dataFileDefine: string;
  dataFileFolder: string;
  dataFileNodeId: string;
  dataFileGCode: string;
}
export interface PartButtonBranchProps {
  branchNumSetting: number;
  branchMenu1: string;
  branchNodeId1: string;
  branchMenu2: string;
  branchNodeId2: string;
  branchMenu3: string;
  branchNodeId3: string;
  branchMenu4: string;
  branchNodeId4: string;
  branchMenu5: string;
  branchNodeId5: string;
  branchMenu6: string;
  branchNodeId6: string;
  branchMenu7: string;
  branchNodeId7: string;
  branchMenu8: string;
  branchNodeId8: string;
}
export interface PartSystemBranchProps {
  branchNumSetting: number;
  branchNodeIdDefault: string;
  branchMessageDefault: string;
  branchNodeId1: string;
  branchMessage1: string;
  branchNodeId2: string;
  branchMessage2: string;
  branchNodeId3: string;
  branchMessage3: string;
  branchNodeId4: string;
  branchMessage4: string;
  branchNodeId5: string;
  branchMessage5: string;
  branchNodeId6: string;
  branchMessage6: string;
}
export interface PartExternalDeviceProps {
  connectedDeviceSetting: string;
  connectedDeviceReadValue: string;
  connectedDeviceWriteValue: string;
  connectedDeviceSettingDataNum: number;
  connectedDeviceTimeOut: string;
  upperLowerLimitCheck?: string;
  conditionBranch: string;
  deviationBranchNodeId: string;
  deviationMessageText: string;
  numericTextInput?: NumericTextInputProps[];
}
export interface NumericTextInputProps {
  itemText: string;
  instructionValueSetting: string;
  instructionValueType: string;
  instructionValue: string;
  sourceItemNode: string;
  sourceItem: string;
  sourceItemTag: string;
  outputSetting: string;
  outputValue: string;
  judgeType: string;
  deviationLowerLimit: string;
  deviationUpperLimit: string;
  sourceItemId: string;
}
export interface PartElectronicShelfLabelProps {
  shelfGateWayIP: string;
  shelfIdSetting: string;
  shelfIdFixedValue: string;
  shelfIdNodeId: string;
  shelfIdGCode: string;
  shelfJP: string;
  sendTextSetting1: string;
  sendTextFixedValue1: string;
  sendTextNodeId1: string;
  sendTextSetting2: string;
  sendTextFixedValue2: string;
  sendTextNodeId2: string;
  sendTextSetting3: string;
  sendTextFixedValue3: string;
  sendTextNodeId3: string;
  sendTextSetting4: string;
  sendTextFixedValue4: string;
  sendTextNodeId4: string;
  sendTextSetting5: string;
  sendTextFixedValue5: string;
  sendTextNodeId5: string;
  sendTextSetting6: string;
  sendTextFixedValue6: string;
  sendTextNodeId6: string;
  sendTextSetting7: string;
  sendTextFixedValue7: string;
  sendTextNodeId7: string;
  sendTextSetting8: string;
  sendTextFixedValue8: string;
  sendTextNodeId8: string;
  sendTextSetting9: string;
  sendTextFixedValue9: string;
  sendTextNodeId9: string;
  sendTextSetting10: string;
  sendTextFixedValue10: string;
  sendTextNodeId10: string;
}
export interface PartInventoryConsumptionProps {
  consumptionType: string;
  consumptionTypeMatNo: string;
  consumptionTypeNodeId: string;
  consumptionTypeItemClass: string;
  zoneType: string;
  zoneTypeZoneCd: string;
  inputMethodDecimalPlaces: number;
  reasonCode: string;
  instructionValueSetting: string;
  instructionFixedValue: string;
  instructionNodeId: string;
  instructionGCode: string;
  fifoCheckSetting: string;
}
export interface PartEquipmentContainerProps {
  deviceContainerType: string;
  deviceAndVesselSetting: string;
  deviceContainerNo: string;
  deviceContainerNodeId: string;
  instructionSelection: string;
}
export interface PartLabelOutputProps {
  labelType: string;
  labelContainerSetting: string;
  labelContainerNodeId: string;
  labelPaletteSetting: string;
  labelPaletteNodeId: string;
  outputMethod: string;
  isOutputPending: string;
  timeoutSeconds: string;
  numberOfCopies: string;
  labelPrinterIP: string;
  labelPrinterMasterSelect: string;
  labelPrinterIPNodeId: string;
  labelItemSetting1: string;
  labelItemFixedValue1: string;
  labelItemNodeId1: string;
  labelItemSetting2: string;
  labelItemFixedValue2: string;
  labelItemNodeId2: string;
  labelItemSetting3: string;
  labelItemFixedValue3: string;
  labelItemNodeId3: string;
  labelItemSetting4: string;
  labelItemFixedValue4: string;
  labelItemNodeId4: string;
  labelItemSetting5: string;
  labelItemFixedValue5: string;
  labelItemNodeId5: string;
  labelItemSetting6: string;
  labelItemFixedValue6: string;
  labelItemNodeId6: string;
  labelItemSetting7: string;
  labelItemFixedValue7: string;
  labelItemNodeId7: string;
  labelItemSetting8: string;
  labelItemFixedValue8: string;
  labelItemNodeId8: string;
  labelItemSetting9: string;
  labelItemFixedValue9: string;
  labelItemNodeId9: string;
  labelItemSetting10: string;
  labelItemFixedValue10: string;
  labelItemNodeId10: string;
}
export interface PartWeightCalibrationProps {
  deviceSetting: string;
  calibSetting: string;
  deviationMessageText: string;
  wgtLogDIFlg: string;
  conditionBranch: string;
  deviationBranchNodeId: string;
}
export interface PartPalletCargoProps {
  palletMode: string;
  palletIdSetting: string;
  palletIdFixedValue: string;
  palletIdNodeId: string;
  palletIdGCode: string;
  mixedCargo: string;
}
export interface PartCommonSetting {
  sopNodeNmJp: string;
  cmtMain1: string;
  cmtMain2: string;
  cmtMain3: string;
  cmtEm: string;
  cmtSub1: string;
  cmtSub2: string;
  cmtSub3: string;
  dcheckFlg: string;
  dcheckPrivGrpCd: string;
  deviationInputFlg: string;
  deviationPrivGrpCd: string;
  devCorrLv: string;
  scnShowFlg: string;
  runLoopFlg: string;
  workBreakFlg: string;
  skippableFlg: string;
  helpShowFlg: string;
  confShowFlg: string;
  recFillFlg: string;
  recReType: string;
  recConfFlg: string;
  helpFileType: string;
  helpBinPath1: string;
  helpBinPath2: string;
  helpBinPath3: string;
  helpBinPath4: string;
  helpBinPath5: string;
  helpTxt1: string;
  helpTxt2: string;
  helpTxt3: string;
}

export interface PartHelpSetting {
  helpFileType: string;
  helpBinPath1: string;
  helpBinPath2: string;
  helpBinPath3: string;
  helpBinPath4: string;
  helpBinPath5: string;
  helpTxt1: string;
  helpTxt2: string;
  helpTxt3: string;
}

export interface PartUpperLowerSetting {
  thJudgeFlg: string;
  thRangeType: string;
  thValType: string;
  thValLlmt: string;
  thValUlmt: string;
}

export interface PartIndividualPara {
  PartInstructionConfirm: PartInstructionConfirmProps;
  PartSopTimer: PartSopTimerProps;
  PartNumericTextInput: PartNumericTextInputProps;
  PartDateRecord: PartDateRecordProps;
  PartReceiveConsumption: PartReceiveConsumptionProps;
  PartResultConfirm: PartResultConfirmProps;
  PartElectronicFile: PartElectronicFileProps;
  PartButtonBranch: PartButtonBranchProps;
  PartSystemBranch: PartSystemBranchProps;
  PartExternalDevice: PartExternalDeviceProps;
  PartElectronicShelfLabel: PartElectronicShelfLabelProps;
  PartInventoryConsumption: PartInventoryConsumptionProps;
  PartEquipmentContainer: PartEquipmentContainerProps;
  PartLabelOutput: PartLabelOutputProps;
  PartWeightCalibration: PartWeightCalibrationProps;
  PartPalletCargo: PartPalletCargoProps;
}
export interface IndividualPara
  extends ControlPartCondBranchProps,
    ControlPartNumCalcProps,
    ControlPartProgressCheckProps,
    PartInstructionConfirmProps,
    PartSopTimerProps,
    PartNumericTextInputProps,
    PartDateRecordProps,
    PartReceiveConsumptionProps,
    PartResultConfirmProps,
    PartElectronicFileProps,
    PartButtonBranchProps,
    PartSystemBranchProps,
    PartExternalDeviceProps,
    PartElectronicShelfLabelProps,
    PartInventoryConsumptionProps,
    PartEquipmentContainerProps,
    PartLabelOutputProps,
    PartWeightCalibrationProps,
    PartPalletCargoProps {}

export interface SopConditionProps {
  judgeValueShowFlg: string;
  deviationMessageText: string;
  conditionBranch: string;
  deviationBranchNodeId: string;
}

export interface SopPartDataSource {
  id: string;
  settingNodeCd: string;
  incomingList: SelectOption[];
  outgoingList: SelectOption[];
  destinationNodeNames: SelectOption[];
  receiveConsumptionNodes: SelectOption[];
  commonSetting: PartCommonSetting;
  individualPara: IndividualPara | null;
  // systemBuranch情報まとめた型;
  sopCondition: SopConditions;
  // ADD ED
  nodeClassName: string;
  dispNodeId: string;
  sopNodeNo: string;
  dispFlg: boolean;
  conditionProps: SopConditionProps;
  privGroupList: ComboBoxDataOptionData[];
  settingDecisionFlg?: boolean;
  upperLowerSetting: PartUpperLowerSetting;
  instUnitTxt: string;
  errorDestinationNodeList: SelectOption[];
  confluenceNodeId?: string;
}

export interface SopPartDataOption {
  sopNodeNo: string;
  sopPartsCd: string;
  nodeId: string;
  position: PositionOption;
  commonSetting: PartCommonSetting;
  helpSetting: PartHelpSetting;
  individualPara: object;
  sopCondition: SopConditions;
  label: string;
  dspSeq: number;
  upperLowerSetting: PartUpperLowerSetting;
  instUnitTxt: string;
}

export interface SopBlockOption {
  sopFlowNo: string;
  sopFlowNmJp: string;
  sopCatTxtJp: string;
  untSopCat: string;
  updDts: string;
}

export interface SopPartConstOption {
  partWidth: number;
  partHeight: number;
  blockWidth: number;
  blockHeight: number;
  addPartWidth: number;
  addPartHeight: number;
  startPartWidth: number;
  startPartHeight: number;
  marginLeft: number;
  marginTop: number;
  defaultWidth: number;
  blockSpace: number;
  tempConfluenceLinkYDistance: number;
}

export interface SopSelectBlock extends SopBlockOption {
  // eslint-disable-next-line
  helpSettings: any;
  nodes: Node[];
  edges: Edge[];
  commonSettings: PartCommonSetting[];
  individualParas: object[];
  upperLowerSetting: PartUpperLowerSetting[];
}
export interface SopBlockDataOption extends SopBlockOption {
  flowList: SopFlowDataOption[];
}

export interface ChartOptions {
  blockData: SopBlockDataOption;
  cellData: object;
  blockNo: string;
}

export interface SopGroupsOption {
  name: string;
  title: string;
  graphHeight: number;
  graphWidth: number;
  layoutOptions?: {
    rowHeight: number;
  };
}

export interface SopRectPartsOption {
  shape: string;
  sopPartsCD: string;
  imageName: string[];
  x?: number;
  y?: number;
  commonSetting?: PartCommonSetting;
  individualPara?: object;
  conditionProps?: SopConditionProps;
  sopCondition?: SopConditions;
  upperLowerSetting?: PartUpperLowerSetting;
  zIndex?: number;
  cloneFlag?: boolean;
  settingConfirmFlg?: boolean;
  attrs: {
    body: {
      rx: number;
      ry: number;
      stroke: string;
      'stroke-width': number;
      fill: string;
    };
    image?: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
    text: {
      text: string;
    };
    title: {
      text: string;
    };
    nodeId: {
      text: string;
    };
    line: {
      width: number;
      height: number;
      refX: number;
      refY: number;
      rx: number;
      ry: number;
      fill: string;
      stroke: string;
      strokeWidth: number;
    };
    edit: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
    trash: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
    copy: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
    wCheck: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
    abnormalityLevel1: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
    abnormalityLevel2: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
    abnormalityLevel3: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
    abnormalityLevel4: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
    abnormalityLevel5: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
    write: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
    formula: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
    gvalNval: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
  };
  instUnitTxt?: string;
}

export interface SopBlockPartOption {
  shape: string;
  sopPartsCD: string;
  imageName: string;
  id?: string;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  zIndex?: number;
  cloneFlag?: boolean;
  commonSetting?: PartCommonSetting;
  individualPara?: object;
  sopCondition?: SopConditions;
  upperLowerSetting?: PartUpperLowerSetting;
  blkFlowNo?: string;
  attrs: {
    body: {
      rx: number;
      ry: number;
      stroke: string;
      'stroke-width': number;
      fill: string;
    };
    image?: {
      'xlink:href'?: string;
      width: number;
      height: number;
      x: number;
      y: number;
    };
    title: {
      text: string;
    };
    line: {
      width: number;
      height: number;
      refX: number;
      refY: number;
      rx: number;
      ry: number;
      fill: string;
      stroke: string;
      strokeWidth: number;
    };
    line2: {
      width: number;
      height: number;
      refX: number;
      refY: number;
      rx: number;
      ry: number;
      fill: string;
      stroke: string;
      strokeWidth: number;
    };
  };
  label?: string;
}

export interface SopControlPartOption {
  shape: string;
  sopPartsCD: string;
  id?: string;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  zIndex?: number;
  cloneFlag?: boolean;
  imageName?: string;
  commonSetting?: PartCommonSetting;
  sopCondition?: SopConditions;
  upperLowerSetting?: PartUpperLowerSetting;
  individualPara?: object;
  attrs: {
    body: {
      rx?: number;
      ry?: number;
      refPoints?: string;
      stroke: string;
      strokeWidth: number;
      fill: string;
    };
    text?: {
      text?: string;
    };
    image?: {
      'xlink:href'?: string;
    };
    button: {
      width: number;
      height: number;
      refX: number;
      refY: number;
      rx: number;
      ry: number;
      fill: string;
      stroke: string;
      strokeWidth: number;
      cursor: string;
      event: string;
    };
  };
  label?: string;
  ports?: SopPorts;
  branchOption?: SelectBranchOption;
}

export interface SopMarkupChildrenOption {
  tagName: string;
  selector?: string;
  attrs: {
    fill?: string;
    'pointer-events': string;
  };
}

export interface SopMarkupOption {
  tagName: string;
  selector?: string;
  children?: SopMarkupChildrenOption[];
}

export interface SopCustomNodeOption {
  name: string;
  data: {
    inherit: string;
    width: number;
    height: number;
    markup?: SopMarkupOption[];
    attrs: {
      body: {
        strokeWidth: number;
        stroke: string;
        fill: string;
      };
      text?: {
        fontSize: number;
        fill: string;
      };
      nodeId?: {
        text: string;
      };
      image?: {
        width: number;
        height: number;
        x?: number;
        y?: number;
      };
      label?: {
        refX: number;
        refY: number;
        textAnchor: string;
        textVerticalAnchor: string;
        fontSize: number;
        fill: string;
      };
    };
    ports: object;
  };
}

export interface SopPortGroupOption {
  position: string;
  attrs: {
    circle: {
      r: number;
      magnet: boolean;
      stroke: string;
      strokeWidth: number;
      fill: string;
      style: {
        visibility: string;
      };
    };
  };
}

export interface SopPortItem {
  group: string;
  args?: {
    x: number;
    y: number;
  };
}

export interface SopPorts {
  groups: {
    top: SopPortGroupOption;
    bottom: SopPortGroupOption;
  };
  items: SopPortItem[];
}

export interface SopNodeProperty {
  groups: SopGroupsOption[];
  rectParts: SopRectPartsOption[];
  controlParts: SopControlPartOption[];
  blockParts: SopBlockPartOption[];
  customNode: SopCustomNodeOption[];
  ports: SopPorts;
  addPartPorts?: SopPorts;
  partCds: string[];
  controlPartCds: string[];
  blockPartCds: string[];
  conditionPartCds: string[];
  sopPartConst?: SopPartConstOption;
  addPartCds: string[];
  blockTypes: SelectOption[];
  operators: OperatorsOption[];
  commonSetting: PartCommonSetting;
  individualPara: PartIndividualPara;
  conditionProps: SopConditionProps;
  sopCondition: SopConditions;
  upperLowerSetting: PartUpperLowerSetting;
}

export interface NodeProperty extends SopNodeProperty {
  startParts: SopControlPartOption[];
}

export interface NeighborsOption {
  incoming: SelectOption[];
  outgoing: SelectOption[];
  id: string;
  sopPartsCD?: string;
}

export interface SopSelectVisableOption {
  // [課題399] ADD ST 新しいListを作ります。ブロックのノードを保存します。
  selectList: SopFlowGetDataOption[];
  // [課題399] ADD ED
  type: string;
  block: string;
  data: SopSelectBlock;
  name: string;
  blockId: string;
}

export interface SopCellLocationOption {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface SelectCellOption {
  templateNodeFlag: boolean;
  noNeighborsFlag: boolean;
  startNodeCount: number;
  allNeighbors: object[];
}

export interface SopFlowEdgeOption {
  fromId: string;
  toId: string;
}

export interface SopFlowGetDataOption {
  // [課題399] ADD ST
  childNodeFlg: string;
  nodeId: string;
  commonSetting: PartCommonSetting;
  helpSetting: PartHelpSetting;
  individualPara: string;
  sopJoin: {
    nextCondSeq: number;
    nextCondLeft: string;
    nextCondOpe: string;
    nextCondRight: string;
    nextNodeNo: string;
  };
  sopCondition: SopConditions;
  parentSopNodeNo: string;
  // [課題399] ADD ED
  sopCieX: number;
  sopCieY: number;
  sopFlowNo: string;
  sopNodeNo: string;
  sopPartsCd: string;
  blkFlowNo: string;
  // [課題369] DEL ST ブロック/テンプレート登録のリファクタリング
  // blkSopFlowVer: number;
  // [課題369] DEL ST
  blkSopSeqNo: number;
  dspSeq: number;
  upperLowerSetting: {
    thJudgeFlg: string;
    thRangeType: string;
    thValType: string;
    thValLlmt: number | null;
    thValUlmt: number | null;
  };
  instUnitTxt: string;
  confluenceNodeId?: string;
}
export interface SopFlowDataOption {
  parentSopNodeNo: string;
  // [課題外] ADD ST ブロックとテンプレートをグラフに追加する時に、一部のパラメータをアサインします。
  helpSetting: PartHelpSetting;
  // [課題外] ADD ED
  nodeId: string;
  commonSetting: PartCommonSetting;
  individualPara: string;
  sopJoin: {
    nextCondSeq: number;
    nextCondLeft: string;
    nextCondOpe: string;
    nextCondRight: string;
    nextNodeNo: string;
  };
  sopCondition: SopConditions;
  sopCieX: number;
  sopCieY: number;
  sopFlowNo: string;
  sopNodeNo: string;
  sopPartsCd: string;
  blkFlowNo: string;
  // [課題369] DEL ST ブロック/テンプレート登録のリファクタリング
  // blkSopFlowVer: number;
  // [課題369] DEL ED
  blkSopSeqNo: number;
  dspSeq: number;
  upperLowerSetting: {
    thJudgeFlg: string;
    thRangeType: string;
    thValType: string;
    thValLlmt: number | null;
    thValUlmt: number | null;
  };
  instUnitTxt: string;
}
export interface SopBranchOption extends Node<Node.Properties> {
  individualPara?: string;
  sopPartsNm?: string;
  sopPartsCd?: string;
}

export interface NodeIdOption {
  itemId: string;
  itemV4Id: string;
}

export interface SopJoinDstOption {
  condBrDst1: string;
  condBrDst2: string;
  condBrDst3: string;
  condBrDst4: string;
  condBrDst5: string;
  condBrDst6: string;
  condBrDst7: string;
  condBrDst8: string;
}
export interface SopJoinCndOption {
  condBrCond1: string;
  condBrCond2: string;
  condBrCond3: string;
  condBrCond4: string;
  condBrCond5: string;
  condBrCond6: string;
  condBrCond7: string;
  condBrCond8: string;
}

export type CommDevice = {
  itemsName: string;
  itemsCode: string;
};

export type ExternalDeviceTableRow = {
  item_no: number;
  item_text: string;
  item_indicative_type: string;
  item_indicative_value: string;
  item_instruction_value_type: string;
  item_input_name: string;
  item_output_type: string;
  item_output_value: string;
  item_range_value_type: string;
  item_management_lower_limit: string;
  item_warning_lower_limit: string;
  item_warning_upper_limit: string;
  item_management_upper_limit: string;
  item_source_item_id: string;
};

export type RxSopSettingOption = {
  sopFlowNo: string;
  sopFlowNmJp: string;
  matNo: string;
  matNm: string;
  rxNo: string;
  rxNm: string;
  prcSeq: number;
  prcNm: string;
  sopBatchType: string;
  helpBinPath: string;
  forcePrivGrpCd: string;
  skipPrivGrpCd: string;
  mstRelPermFlg: string;
  dspSeq: number | null;
  crtDts: string;
  updDts: string;
};

export type WgtSopSettingOption = {
  sopFlowNo: string;
  sopFlowNmJp: string;
  wgtRoomNo: string;
  wgtRoomNm: string;
  wgtSopCat: string;
  helpBinPath: string;
  forcePrivGrpCd: string;
  skipPrivGrpCd: string;
  mstRelPermFlg: string;
  dspSeq: number | null;
  crtDts: string;
  updDts: string;
};

export type PrcSopSettingOption = {
  sopFlowNo: string;
  sopFlowNmJp: string;
  prcNo: string;
  prcNm: string;
  stYmd: string;
  edYmd: string;
  recApprovFlg: string;
  helpBinPath: string;
  forcePrivGrpCd: string;
  skipPrivGrpCd: string;
  mstRelPermFlg: string;
  dspSeq: number | null;
  crtDts: string;
  updDts: string;
};

export type PartsNameOption = {
  partsCD: string;
  text: string;
};

// 投入順取得(一時対応のため、標準コンボボックス置き換え後に削除予定)
export interface InputSeqOption {
  inputSeq: string;
}
export interface GCodeMemo {
  gCodeMemo: string;
  gCodeMemoComment: string;
}

export interface NumericTextInputChildNodeProps {
  itemText: string;
  instructionValueSetting: string;
  instructionValue: string;
  sourceItemNode: string;
  sourceItemTag: string;
  sourceItem: string;
  outputSetting: string;
  outputValue: string;
  instructionValueType: string;
}

export interface ConditionInfo {
  nextCondLeft: string;
  nextCondOpe: string;
  nextCondRight: string;
}

export interface SopConditions {
  condition1: ConditionInfo;
  condition2: ConditionInfo;
  condition3: ConditionInfo;
  condition4: ConditionInfo;
  condition5: ConditionInfo;
  condition6: ConditionInfo;
}

export interface RxBomMatOption {
  bomMatNo: string;
  bomMatNm: string;
}

export interface DeviceCmdOption {
  cmdId: string;
  cmdNm: string;
}

export interface CheckResult {
  checkStatus: number;
  formulaResult: string;
}

export type SysExSopInfo = {
  sopFlowNo: string;
  editDts: string;
  editUsrId: string;
  editUsrNm: string;
};
