import CONST from '@/constants/utils';
import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// 計画変更ダイアログのアイテム定義
export const getOdrEditScheduleFormItems: () => CustomFormType['formItems'] =
  // NOTE: GetScheduleModifyInitResDataの命名と合わせている
  () => ({
    unitNmJp: {
      formModelValue: '',
      formRole: 'suffix',
    },
    planPrcNo: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPlanProcedureNo') }, // 計画番号
      formRole: 'textBox',
      props: { disabled: true },
    },
    matNo: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtMaterialCode') }, // 品目コード
      formRole: 'textBox',
      props: { disabled: true },
    },
    dspNmJp: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtMaterialName') }, // 品名
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 処方選択ダイアログを開くためのボタン
    buttonOdrSelectRx: {
      formModelValue: '',
      formRole: 'button',
      onClickHandler() {}, // vueで上書きします
      props: {
        text: '処方選択',
      },
    },
    rxNo: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPrescriptionCode') }, // 処方コード
      formRole: 'textBox',
      props: { disabled: true },
    },
    rxNmJp: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPrescriptionName') }, // 処方名
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('textBox')],
      formRole: 'textBox',
      props: { disabled: true },
    },
    mbrNo: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtMasterBatchRecordNo') }, // MBR番号
      formRole: 'textBox',
      props: { disabled: true },
    },
    validYmd: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPrescriptionDeadlineDate') }, // 処方有効期限
      formRole: 'textBox',
      props: { disabled: true },
    },
    stdPrdQty: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtStandardProductionQuantity') }, // 標準生産量
      suffix: { formModelProp: 'unitNmJp' },
      formRole: 'textBox',
      props: { disabled: true },
    },
    planQty: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtOrderQuantity') }, // 計画生産量
      suffix: { formModelProp: 'unitNmJp' },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.positiveRealNumericOnly(),
      ],
      formRole: 'textBox',
      props: { disabled: true },
    },
    odrDts: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtOrderDate') }, // 製造開始予定日
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('date')],
      formRole: 'date',
      props: { modelValue: '', type: 'date' },
    },
    skdAddExpl: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPlanComment') }, // 計画コメント
      rules: [
        rules.length(64),
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      ],
      formRole: 'textBox',
    },
    rxModExpl: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPrescriptionChangeComment') }, // 処方変更コメント
      // NOTE:この項目は処方選択を実行後に書き変わる。この記載は初期状態。rules追加時は注意。
      rules: [
        rules.length(64),
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      ],
      formRole: 'textBox',
      // NOTE:この項目は処方選択を実行後に活性状態になる
      props: { disabled: true },
    },
  });

// 計画変更ダイアログのモデル定義
export const odrEditScheduleFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getOdrEditScheduleFormItems());
