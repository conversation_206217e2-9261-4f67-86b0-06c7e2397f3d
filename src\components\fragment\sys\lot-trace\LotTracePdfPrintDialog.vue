<template>
  <!-- PDF出力ダイアログ -->
  <DialogWindow
    :title="$t('Sys.Chr.btnPdfPrint')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    :buttons="[
      {
        text: $t('Cm.Chr.btnCancel'),
        type: 'secondary',
        size: 'normal',
        clickHandler: () => {
          closeDialog('fragmentDialogVisible');
        },
      },
    ]"
  >
    <CustomForm
      :formModel="LotTracePdfPrintFormRef.formModel"
      :formItems="LotTracePdfPrintFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          LotTracePdfPrintFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';

import {
  GetLotTraceListReq,
  GetLotTraceReq,
  LotTraceData,
  LotTraceListData,
} from '@/types/HookUseApi/LotTraceType';
import { useGetLotTrace } from '@/hooks/useApi';
import {
  getLotTracePdfPrintFormItems,
  LotTracePdfPrintFormModel,
} from './LotTracePdfPrintDialog';

const LotTracePdfPrintFormRef = ref<CustomFormType>({
  formItems: getLotTracePdfPrintFormItems(),
  formModel: LotTracePdfPrintFormModel,
});

type Props = {
  isClicked: boolean;
  searchData: GetLotTraceListReq;
  selectedRowData: LotTraceListData | null;
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

const lotTraceReqDataRef = ref<GetLotTraceReq>();

let lotTraceModData: LotTraceData = {
  prcNm: '',
  odrNo: '',
  bomWgtRsltDate: '',
  bomMakerLotNo: '',
  bomMakerNm: '',
  bomLotNo: '',
  bomItemCls: '',
  bomItemCode: '',
  bomItemNm: '',
  bomRsltDate: '',
  bomInvedQualitySts: '',
  bomInvedTestCmtExt: '',
  bomInvedTestCmt: '',
  bomInvedEfectiveDate: '',
  bomInvedExpiryDate: '',
  bomCrtQualitySts: '',
  bomCrtTestCmtExt: '',
  bomCrtTestCmt: '',
  bomCrtEfectiveDate: '',
  bomCrtExpiryDate: '',
  vlmLotNo: '',
  vlmItemCode: '',
  vlmItemNm: '',
  vlmRsltDate: '',
  vlmCrtQualitySts: '',
  vlmCrtTestCmtExt: '',
  vlmCrtTestCmt: '',
  lotTraceBinList: [],
};

type DialogRefKey =
  | 'singleButton'
  | 'editAogArrivalPlanConfirm'
  | 'editAogArrivalPlan'
  | 'fragmentDialogVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  editAogArrivalPlanConfirm: false,
  editAogArrivalPlan: false,
  fragmentDialogVisible: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const FILE_LOT_TRACE_BIN = {
  MODEL_KEY: 'lotTraceBinList',
  NAME_KEY: 'lotTraceBinFileNm',
  UNIQUE_KEY: 'lotTraceBinNo',
} as const;

// 初期表示データ設定
const lotTracePdfPrintDialogInit = async () => {
  if (!props.selectedRowData) return;
  showLoading();
  updateDialogChangeFlagRef(false);

  // 連携テストダイアログの初期化必要なら書く
  LotTracePdfPrintFormRef.value.formItems = getLotTracePdfPrintFormItems();

  lotTraceReqDataRef.value = {
    lotSid: props.selectedRowData?.lotSid || '',
    matNo: props.selectedRowData?.bomItemCode || '',
    lotNo: props.selectedRowData?.bomLotNo || '',
    odrNo: props.selectedRowData?.odrNo || '',
    prcSeq: props.selectedRowData?.vlmPrcSeq || 0,
    batchNo: props.selectedRowData?.vlmBatchNo || 0,
    prdMatNo: props.selectedRowData?.vlmItemCode || '',
    rsltLnum: props.selectedRowData?.vlmRsltLnum || 0,
  };

  // 選択されたロットトレース情報取得
  const { responseRef, errorRef } = await useGetLotTrace({
    ...props.privilegesBtnRequestData,
    ...lotTraceReqDataRef.value,
  });

  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }

  if (responseRef.value) {
    lotTraceModData = responseRef.value.data.rData;

    setFormModelValueFromApiResponse(LotTracePdfPrintFormRef, lotTraceModData, {
      fileKeys: [
        {
          formModelKey: FILE_LOT_TRACE_BIN.MODEL_KEY,
          fileNameKey: FILE_LOT_TRACE_BIN.NAME_KEY,
          fileKeyPropName: FILE_LOT_TRACE_BIN.UNIQUE_KEY,
        },
      ],
      commonRequestData: props.privilegesBtnRequestData,
    });

    openDialog('fragmentDialogVisible');
  }

  closeLoading();
};

watch(
  () => props.isClicked,
  async () => {
    await lotTracePdfPrintDialogInit();
  },
);
</script>
