import { setup, type Preview } from "@storybook/vue3";
import '@/assets/scss/storybook/index.scss';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css'
import 'tabulator-tables/dist/css/tabulator.min.css'
import i18n from "../src/constants/lang/index";
import 'virtual:svg-icons-register'
import router from '../src/router/routers';
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import setStores from '../src/stories/setStores.ts';

setup((app) => {
  // app が Vue インスタンスにあたるので Vue I18n インスタンスを注入する
  // 同一の Vue インスタンスに対して setup 関数は複数回実行されるため、既に注入済みかを確認する
  // if (!app.__VUE_I18N__) {
  const pinia = createPinia();
  pinia.use(piniaPluginPersistedstate);
  app.use(ElementPlus);
  app.use(i18n);
  app.use(router);
  app.use(pinia);
  setStores();
  // }
})

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
};

export default preview;
