<template>
  <!-- 個装在庫生成ダイアログ -->
  <DialogWindow
    :title="$t('Inv.Chr.txtCreateIndividualInventory')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkCreateForm()"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="individualInventoryCreateFormRef.formModel"
      :formItems="individualInventoryCreateFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          individualInventoryCreateFormRef.customForm = v;
        }
      "
      @selectedItem="updateFormItems"
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <!-- 個装在庫生成の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.createIndividualInventoryConfirm"
    :dialogProps="messageBoxCreateIndividualInventoryConfirmProps"
    :cancelCallback="() => closeDialog('createIndividualInventoryConfirm')"
    :submitCallback="aogVarFlgCheck"
  />
  <!-- 入り数割れ数量チェック -->
  <MessageBox
    v-if="dialogVisibleRef.aogVarFlgCheckConfirm"
    :dialogProps="messageBoxAogVarFlgCheckProps"
    :cancelCallback="() => closeDialog('aogVarFlgCheckConfirm')"
    :submitCallback="closeCheckDialog"
  />
  <!-- 個装在庫生成のチェックメッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.createIndividualInventory"
    :dialogProps="messageBoxCreateIndividualInventoryPropsRef"
    :submitCallback="closeAllDialog"
  />
  <!-- 個装在庫生成成功メッセージ（警告有） -->
  <MessageBox
    v-if="dialogVisibleRef.createIndivisualInvenroryWarning"
    :dialogProps="messageBoxCreateIndivisualInvenroryWarningPropsRef"
    :submitCallback="closeAllDialog"
  />
  <!-- 個装在庫生成のAPIを再度呼び出す -->
  <MessageBox
    v-if="dialogVisibleRef.addInventoryLotWarning"
    :dialogProps="messageBoxAddInventoryLotPropsRef"
    :cancelCallback="() => closeDialog('addInventoryLotWarning')"
    :submitCallback="closeWarningDialog"
  />
</template>
<script setup lang="ts">
import { nextTick, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import CONST from '@/constants/utils';
import { rules, check } from '@/utils/validator';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType, FormItem } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  ComboBoxDataOptionData,
  ComboBoxDataStandardReturnData,
  CommonRequestType,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  setCustomFormComboBoxOptionList,
  setCustomFormFilterComboBoxOptionList,
  getComboBoxOptionList,
} from '@/utils/comboBoxOptionList';
import {
  getDateByType,
  toBigOrNull,
  toFixedDecimalString,
} from '@/utils/index';
import { useGetComboBoxDataStandard, useAddInventoryLot } from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import createMessageBoxForm from '@/utils/commentMessageBox';
import { AddInventoryLotReq } from '@/types/HookUseApi/InvTypes';
import Big from 'big.js';
import {
  getIndividualInventoryCreateFormItems,
  individualInventoryCreateFormModel,
} from './invCreateIndividualPackagingInventory';

const individualInventoryCreateFormRef = ref<CustomFormType>({
  formItems: getIndividualInventoryCreateFormItems(),
  formModel: individualInventoryCreateFormModel,
});

type Props = {
  isClicked: boolean;
  dspNarrowType: string;
  privilegesBtnRequestData: CommonRequestType;
};

const { t } = useI18n();
const customFormRenderingTriggerRef = ref(false);
let comboBoxOptionList: ComboBoxDataOptionData[] = [];
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

/**
 * 警告要否フラグ
 * - UNNECESSARY: 警告が不要であることを表します（値: 0）。
 * - NECESSITY: 警告が必要であることを表します（値: 1）：デフォルト値。
 */
const WARNING_NECESSITY_FLAG = {
  UNNECESSARY: 0,
  NECESSITY: 1,
} as const;
const constantData = {
  matNo: 'matNo',
  bpId: 'bpId',
  totalInvQty: 'totalInvQty',
  invQty: 'invQty',
  zoneNo: 'zoneNo',
  locNo: 'locNo',
};
let isShelfLifeStCd40 = false;
let isExpiryStCd40 = false;

type DialogRefKey =
  | 'singleButton'
  | 'createIndividualInventoryConfirm'
  | 'createIndividualInventory'
  | 'fragmentDialogVisible'
  | 'aogVarFlgCheckConfirm'
  | 'addInventoryLotWarning'
  | 'createIndivisualInvenroryWarning';

const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  createIndividualInventoryConfirm: false,
  createIndividualInventory: false,
  fragmentDialogVisible: false,
  aogVarFlgCheckConfirm: false,
  addInventoryLotWarning: false,
  createIndivisualInvenroryWarning: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxCreateIndividualInventoryConfirmProps: DialogProps = {
  title: t('Inv.Chr.txtConfirm'),
  content: t('Inv.Msg.createIndividualInventoryCorrectionConfirm'),
  type: 'question',
};

const messageBoxCreateIndividualInventoryPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxForm = createMessageBoxForm('message', 'cmtWarningZoneEnt');
let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;
const messageBoxAddInventoryLotPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

const messageBoxAogVarFlgCheckProps: DialogProps = {
  title: t('Inv.Chr.txtAogVarFlgNumberConfirm'),
  content: t('Inv.Msg.excessiveQuantity'),
  type: 'warning',
};

const messageBoxCreateIndivisualInvenroryWarningPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'warning',
});

/**
 * 個装在庫生成（実行）
 */
const checkCreateForm = async () => {
  const validate =
    individualInventoryCreateFormRef.value.customForm !== undefined &&
    (await individualInventoryCreateFormRef.value.customForm.validate(
      (isValid) => {
        onValidateHandler(isValid);
      },
    ));

  if (validate) {
    if (
      !(isShelfLifeStCd40 || isExpiryStCd40) &&
      check.fromToDate(
        individualInventoryCreateFormRef.value.formModel.calcShelfDts.toString(),
        individualInventoryCreateFormRef.value.formModel.calcStDts.toString(),
      )
    ) {
      messageBoxSingleButtonRef.value.title = t('Tst.Msg.txtChecksumExpiry');
      messageBoxSingleButtonRef.value.content = t(
        'Tst.Msg.txtExpiryMustBeInTheFuture',
      );
      messageBoxSingleButtonRef.value.type = 'error';
      closeLoading();
      openDialog('singleButton');
      return false;
    }
    // 個装在庫生成の確認メッセージ
    openDialog('createIndividualInventoryConfirm');
  }
  return false;
};

/**
 * 個装在庫生成前チェック
 */
const apiHandler = async (
  messageBoxProps: DialogProps,
  warningNecessityFlg: number = WARNING_NECESSITY_FLAG.NECESSITY,
) => {
  showLoading();
  let createIndividualInventoryFormModel: ExtendCommonRequestType<AddInventoryLotReq> =
    {
      ...props.privilegesBtnRequestData,
      matNo: individualInventoryCreateFormRef.value.formModel.matNo.toString(),
      bpId: individualInventoryCreateFormRef.value.formModel.bpId.toString(),
      lotNo: individualInventoryCreateFormRef.value.formModel.lotNo.toString(),
      totalInvQty:
        individualInventoryCreateFormRef.value.formModel.totalInvQty.toString(),
      invQty:
        individualInventoryCreateFormRef.value.formModel.invQty.toString(),
      zoneNo:
        individualInventoryCreateFormRef.value.formModel.zoneNo.toString(),
      locNo: individualInventoryCreateFormRef.value.formModel.locNo.toString(),
      makerLotNo:
        individualInventoryCreateFormRef.value.formModel.makerLotNo.toString(),
      calcStDts:
        individualInventoryCreateFormRef.value.formModel.calcStDts.toString(),
      calcShelfDts:
        individualInventoryCreateFormRef.value.formModel.calcShelfDts.toString(),
      edNo: individualInventoryCreateFormRef.value.formModel.edNo.toString(),
      afmRefNo:
        individualInventoryCreateFormRef.value.formModel.afmRefNo.toString(),
      icRsnCd:
        individualInventoryCreateFormRef.value.formModel.icRsnCd.toString(),
      afmExpl:
        individualInventoryCreateFormRef.value.formModel.afmExpl.toString(),
      warnAfmExpl: '',
      msgboxTitleTxt: messageBoxProps.title,
      msgboxMsgTxt: messageBoxProps.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      warningNecessityFlg,
    };

  if (
    'isPrompt' in messageBoxProps &&
    warningNecessityFlg === WARNING_NECESSITY_FLAG.UNNECESSARY &&
    comboBoxDataStandardReturnData
  ) {
    createIndividualInventoryFormModel = {
      ...createIndividualInventoryFormModel,
      msgboxInputCmt: messageBoxProps.formModel.message.toString(),
      warnAfmExpl: messageBoxProps.formModel.message.toString(),
    };
  }

  // ３．個装在庫を生成する
  const { responseRef, errorRef } = await useAddInventoryLot(
    createIndividualInventoryFormModel,
  );

  if (errorRef.value) {
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      messageBoxAddInventoryLotPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxAddInventoryLotPropsRef.value.content =
        errorRef.value.response.rMsg;
      const resetData = createMessageBoxForm('message', 'cmtWarningZoneEnt');
      if (
        'isPrompt' in messageBoxAddInventoryLotPropsRef.value &&
        comboBoxDataStandardReturnData
      ) {
        messageBoxAddInventoryLotPropsRef.value.formItems = resetData.formItems;
        setCustomFormComboBoxOptionList(
          messageBoxAddInventoryLotPropsRef.value.formItems,
          comboBoxDataStandardReturnData.rData.rList,
        );
      }
      openDialog('addInventoryLotWarning');
    } else if (
      errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1013
    ) {
      messageBoxCreateIndivisualInvenroryWarningPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxCreateIndivisualInvenroryWarningPropsRef.value.content =
        errorRef.value.response.rMsg;
      openDialog('createIndivisualInvenroryWarning');
    } else {
      // 警告メッセージ表示前は1、表示後は0を設定する
      messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
      messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
      messageBoxSingleButtonRef.value.type = 'error';
      openDialog('singleButton');
    }
    closeLoading();
    return;
  }
  if (responseRef.value) {
    messageBoxCreateIndividualInventoryPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxCreateIndividualInventoryPropsRef.value.content =
      responseRef.value.data.rMsg;
    openDialog('createIndividualInventory');
  }
  closeLoading();
};

const closeCheckDialog = () => {
  closeDialog('aogVarFlgCheckConfirm');
  apiHandler(messageBoxAogVarFlgCheckProps);
};

const closeWarningDialog = () => {
  closeDialog('addInventoryLotWarning');
  apiHandler(
    messageBoxAddInventoryLotPropsRef.value,
    WARNING_NECESSITY_FLAG.UNNECESSARY,
  );
};

const aogVarFlgCheck = () => {
  closeDialog('createIndividualInventoryConfirm');
  let aogVarFlgType = false;
  // ２ 入り数割れ数量チェック
  if (
    individualInventoryCreateFormRef.value.formItems.matNo.formRole ===
    'selectComboBox'
  ) {
    if (
      individualInventoryCreateFormRef.value.formItems.matNo.selectOptions
        .length > 0 &&
      individualInventoryCreateFormRef.value.formItems.matNo.formModelValue !==
        ''
    ) {
      const selectOptionData =
        individualInventoryCreateFormRef.value.formItems.matNo.selectOptions;
      const matNo =
        individualInventoryCreateFormRef.value.formItems.matNo.formModelValue.toString();
      const aogBaleUnit = getComboBoxOptionList(
        selectOptionData,
        matNo,
        'aog_bale_unit',
      )!.optionValList[0];
      const aogVarFlg = getComboBoxOptionList(
        selectOptionData,
        matNo,
        'aog_var_flg',
      )!.optionValList[0].toString();
      if (aogVarFlg === '1') {
        const bigInvQty = toBigOrNull(
          individualInventoryCreateFormRef.value.formModel.invQty,
        );
        const bigAogBaleUnit = toBigOrNull(aogBaleUnit);
        if (bigInvQty === null || bigAogBaleUnit === null) return;
        if (bigInvQty.gt(bigAogBaleUnit)) {
          aogVarFlgType = true;
        }
      }
    }
  }
  if (aogVarFlgType) {
    openDialog('aogVarFlgCheckConfirm');
  } else {
    apiHandler(messageBoxCreateIndividualInventoryConfirmProps);
  }
};

/**
 * ダイアログウィンドウを閉じる
 */
const closeAllDialog = async () => {
  closeDialog('createIndividualInventory');
  closeDialog('fragmentDialogVisible');
  closeDialog('createIndivisualInvenroryWarning');
  emit('submit', props.privilegesBtnRequestData);
};

const initializeOverrides = () => ({
  tags: [] as FormItem['tags'],
  rules: [] as FormItem['rules'],
  propsDisabled: true,
});

/**
 * 指定アイテムの関連更新を行う
 */
const updateFormItems = async (fieldId: string) => {
  // 品目コード/品名の絞り込み
  if (fieldId === constantData.matNo) {
    if (
      individualInventoryCreateFormRef.value.formItems.matNo.formRole ===
      'selectComboBox'
    ) {
      if (
        individualInventoryCreateFormRef.value.formItems.matNo.selectOptions
          .length > 0 &&
        individualInventoryCreateFormRef.value.formItems.matNo
          .formModelValue !== ''
      ) {
        const selectOptionData =
          individualInventoryCreateFormRef.value.formItems.matNo.selectOptions;
        const matNo =
          individualInventoryCreateFormRef.value.formItems.matNo.formModelValue.toString();

        // メーカー名
        // NOTE:品目コード/品名のフォーカスアウト時：matNoのoptionListから、bp_nm_jpのラベルとm_bp_idの値を取得し表示する。
        // 品目コード/品名の選択項目が変更されたとき、メーカーコンボボックスの項目を切り替える
        if (
          individualInventoryCreateFormRef.value.formItems.bpId.formRole ===
          'selectComboBox'
        ) {
          const mBpIdValue =
            getComboBoxOptionList(
              selectOptionData,
              matNo,
              'm_bp_id',
            )?.optionValList[0].toString() ?? '';
          individualInventoryCreateFormRef.value.formItems.bpId.selectOptions =
            [
              {
                cmbId: '',
                condKey: '',
                optionList: [],
                value: mBpIdValue,
                label:
                  getComboBoxOptionList(
                    selectOptionData,
                    matNo,
                    'bp_nm_jp',
                  )?.optionValList[0].toString() ?? '',
              },
            ];
          individualInventoryCreateFormRef.value.formItems.bpId.formModelValue =
            mBpIdValue;
        }

        // 全体量単位、１梱の数量単位
        // NOTE:品目コード/品名のフォーカスアウト時：matNoのoptionListから、unit_nmの値を取得し表示する。
        individualInventoryCreateFormRef.value.formItems.unitNm.formModelValue =
          getComboBoxOptionList(
            selectOptionData,
            matNo,
            'mes_unit_nm',
          )!.optionValList[0].toString();
        // １梱の数量
        // 品目コード/品名選択時に、マスタ設定された標準入れ目の値を入力する。
        // NOTE:品目コード/品名のフォーカスアウト時：matNoのoptionListから、aog_bale_unitの値を取得し表示する。
        individualInventoryCreateFormRef.value.formItems.invQty.formModelValue =
          getComboBoxOptionList(
            selectOptionData,
            matNo,
            'aog_bale_unit',
          )!.optionValList[0].toString();
        // 版番号
        // NOTE:品目コード/品名のフォーカスアウト時：matNoのoptionListから、ed_mgt_typeの値を取得し必須/不可を変更する。
        if (
          individualInventoryCreateFormRef.value.formItems.edNo.formRole ===
          'textBox'
        ) {
          let {
            tags: overrideTags,
            rules: overrideRules,
            propsDisabled: overridePropsDisabled,
          } = initializeOverrides();
          const edMgtType = getComboBoxOptionList(
            selectOptionData,
            matNo,
            'ed_mgt_type',
          )!.optionValList[0].toString();
          if (edMgtType === '1') {
            // 版管理する品目の場合
            overrideTags = [
              { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
            ];
            overrideRules = [
              rules.required('textBox'),
              rules.upperCaseSingleByteAlphanumeric(),
              rules.length(10, t('Cm.Chr.txtLength', [10])),
            ];
            overridePropsDisabled = false;
          } else {
            // 値入力ありの場合
            overrideTags = [];
            overrideRules = [
              rules.upperCaseSingleByteAlphanumeric(),
              rules.length(10, t('Cm.Chr.txtLength', [10])),
              rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
            ];
            individualInventoryCreateFormRef.value.formItems.edNo.formModelValue =
              ''; // 版番号の値をクリア
            overridePropsDisabled = true;
          }
          individualInventoryCreateFormRef.value.formItems.edNo.tags =
            overrideTags;
          individualInventoryCreateFormRef.value.formItems.edNo.rules =
            overrideRules;
          individualInventoryCreateFormRef.value.formItems.edNo.props!.disabled =
            overridePropsDisabled;
          individualInventoryCreateFormRef.value.customForm?.clearValidate(
            'edNo',
          );
          await nextTick();
          customFormRenderingTriggerRef.value =
            !customFormRenderingTriggerRef.value;
        }

        const qltReqFlg = getComboBoxOptionList(
          selectOptionData,
          matNo,
          'qlt_req_flg',
        )!.optionValList[0].toString();
        // 使用期限起算日
        // NOTE:品目コード/品名のフォーカスアウト時：matNoのoptionListの、expiry_st_cdから使用期限のマスタ設定を取得する
        // 同様に、qlt_req_flgから試験依頼のマスタ設定を取得する。
        if (
          individualInventoryCreateFormRef.value.formItems.calcStDts
            .formRole === 'date'
        ) {
          let {
            tags: overrideTags,
            rules: overrideRules,
            propsDisabled: overridePropsDisabled,
          } = initializeOverrides();
          let overrideLabel = t('Inv.Chr.txtExpirationDate');
          overrideLabel = getComboBoxOptionList(
            selectOptionData,
            matNo,
            'expiry_st_ttl_dsp',
          )!.optionValList[0].toString();
          const expiryStCd = getComboBoxOptionList(
            selectOptionData,
            matNo,
            'expiry_st_cd',
          )!.optionValList[0].toString();
          // 試験依頼をLIMSに送信する場合
          if (
            qltReqFlg === '1' &&
            expiryStCd.length > 0 &&
            expiryStCd !== 'N'
          ) {
            overrideTags = [
              { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
            ];
            overrideRules = [rules.required('date'), rules.futureDate()];
            overridePropsDisabled = false;

            isExpiryStCd40 = false;
            // 起算日の場合は未来日チェックをしない
            if (expiryStCd === '40') {
              isExpiryStCd40 = true;
              overrideRules = [rules.required('date')];
            }
          }

          individualInventoryCreateFormRef.value.formItems.calcStDts.label!.text =
            overrideLabel;
          individualInventoryCreateFormRef.value.formItems.calcStDts.formModelValue =
            '';
          individualInventoryCreateFormRef.value.formItems.calcStDts.props!.disabled =
            overridePropsDisabled;
          individualInventoryCreateFormRef.value.formItems.calcStDts.tags =
            overrideTags;
          individualInventoryCreateFormRef.value.formItems.calcStDts.rules =
            overrideRules;
          individualInventoryCreateFormRef.value.customForm?.clearValidate(
            'calcStDts',
          );
          await nextTick();
          customFormRenderingTriggerRef.value =
            !customFormRenderingTriggerRef.value;
        }

        // 有効期限起算日
        // NOTE:品目コード/品名のフォーカスアウト時：matNoのoptionListの、shelf_life_st_cdから有効期限のマスタ設定を取得する
        // 同様に、qlt_req_flgから試験依頼のマスタ設定を取得する。
        if (
          individualInventoryCreateFormRef.value.formItems.calcShelfDts
            .formRole === 'date'
        ) {
          let {
            tags: overrideTags,
            rules: overrideRules,
            propsDisabled: overridePropsDisabled,
          } = initializeOverrides();
          let overrideLabel = t('Inv.Chr.txtEffectiveDate');
          overrideLabel = getComboBoxOptionList(
            selectOptionData,
            matNo,
            'shelf_life_st_ttl_dsp',
          )!.optionValList[0].toString();
          const shelfLifeStCd = getComboBoxOptionList(
            selectOptionData,
            matNo,
            'shelf_life_st_cd',
          )!.optionValList[0].toString();
          // 試験依頼をLIMSに送信する場合
          if (
            qltReqFlg === '1' &&
            shelfLifeStCd.length > 0 &&
            shelfLifeStCd !== 'N'
          ) {
            overrideTags = [
              { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
            ];
            overrideRules = [rules.required('date'), rules.futureDate()];
            overridePropsDisabled = false;

            isShelfLifeStCd40 = false;
            // 起算日の場合は未来日チェックをしない
            if (shelfLifeStCd === '40') {
              isShelfLifeStCd40 = true;
              overrideRules = [rules.required('date')];
            }
          }
          individualInventoryCreateFormRef.value.formItems.calcShelfDts.label!.text =
            overrideLabel;
          individualInventoryCreateFormRef.value.formItems.calcShelfDts.formModelValue =
            '';
          individualInventoryCreateFormRef.value.formItems.calcShelfDts.props!.disabled =
            overridePropsDisabled;
          individualInventoryCreateFormRef.value.formItems.calcShelfDts.tags =
            overrideTags;
          individualInventoryCreateFormRef.value.formItems.calcShelfDts.rules =
            overrideRules;
          individualInventoryCreateFormRef.value.customForm?.clearValidate(
            'calcShelfDts',
          );
          await nextTick();
          customFormRenderingTriggerRef.value =
            !customFormRenderingTriggerRef.value;
        }
      }
    }
  }
  // 全体量/１梱の数量の絞り込み
  if (fieldId === constantData.totalInvQty || fieldId === constantData.invQty) {
    // 梱数 = 「全体量 ÷ １梱の数量」
    const { totalInvQty, invQty, invPackageQty } =
      individualInventoryCreateFormRef.value.formItems;
    const bigTotalInvQty = toBigOrNull(totalInvQty.formModelValue);
    const bigInvQty = toBigOrNull(invQty.formModelValue);
    invPackageQty.formModelValue =
      bigTotalInvQty === null || bigInvQty === null
        ? ''
        : toFixedDecimalString(bigTotalInvQty.div(bigInvQty), {
            decimalDigits: 0,
            roundMode: Big.roundUp,
          });
  }
  // ゾーン
  if (fieldId === constantData.zoneNo) {
    // イベント：フォーカスアウト時に選択されているゾーンに属するロケーションをロケーションコンボボックスに反映。
    setCustomFormFilterComboBoxOptionList(
      individualInventoryCreateFormRef.value.formItems,
      comboBoxOptionList,
      constantData.locNo,
      constantData.locNo,
      [fieldId],
    );
    let { propsDisabled: overridePropsDisabled } = initializeOverrides();
    // ゾーンが選択済みかつ、選択したゾーンに紐づくロケーションが存在する場合：
    // 選択したゾーンに紐づくロケーションを表示。
    // ロケーションの選択を必須とする。
    if (
      individualInventoryCreateFormRef.value.formItems.locNo.formRole ===
      'selectComboBox'
    ) {
      if (
        individualInventoryCreateFormRef.value.formItems.locNo.selectOptions
          .length > 0 &&
        individualInventoryCreateFormRef.value.formItems.zoneNo
          .formModelValue !== ''
      ) {
        overridePropsDisabled = false;
      }
      individualInventoryCreateFormRef.value.formItems.locNo.props!.disabled =
        overridePropsDisabled;
      individualInventoryCreateFormRef.value.customForm?.clearValidate('locNo');
      customFormRenderingTriggerRef.value =
        !customFormRenderingTriggerRef.value;
    }
  }
};

// 初期表示データ設定
const invCreateIndividualPackagingInventoryInit = async () => {
  showLoading();
  updateDialogChangeFlagRef(false);
  // 連携テストダイアログの初期化必要なら書く
  individualInventoryCreateFormRef.value.formItems =
    getIndividualInventoryCreateFormItems();

  individualInventoryCreateFormRef.value.formItems.accrualDts.formModelValue =
    getDateByType(new Date().toString(), 'YYYY/MM/DD');
  if (
    individualInventoryCreateFormRef.value.formItems.locNo.formRole ===
    'selectComboBox'
  ) {
    individualInventoryCreateFormRef.value.formItems.locNo.props!.disabled =
      false;
  }
  // 標準コンボボックスデータ取得
  let dspNarrowTypeVal = '';
  if (props.dspNarrowType === 'M') {
    dspNarrowTypeVal = 'M,N';
  } else if (props.dspNarrowType === 'P') {
    dspNarrowTypeVal = 'P,N';
  }
  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'matNo',
        condKey: 'm_mat_and_maker',
        where:
          props.dspNarrowType === 'N'
            ? {}
            : { dsp_narrow_type: dspNarrowTypeVal },
        optionCol: {
          bp_nm_jp: 'bpNmJp', // メーカ名：画面に表示する値
          m_bp_id: 'mBpId', // メーカ名：APIに送信する値
          mes_unit_nm: 'unitNmJp',
          expiry_st_cd: 'expiryStCd',
          shelf_life_st_cd: 'shelfLifeStCd',
          expiry_st_ttl_dsp: 'expiryStTtlDsp',
          shelf_life_st_ttl_dsp: 'shelfLifeStTtlDsp',
          ed_mgt_type: 'edMgtType',
          aog_bale_unit: 'aogBaleUnit',
          aog_var_flg: 'aogVarFlg',
          qlt_req_flg: 'qltReqFlg',
        },
      },
      {
        cmbId: 'zoneNo',
        condKey: 'm_ic_zone',
        where:
          props.dspNarrowType === 'N'
            ? { inv_gen_flg: '1', inv_hidden_flg: '0' }
            : {
                inv_gen_flg: '1',
                inv_hidden_flg: '0',
                dsp_narrow_type: dspNarrowTypeVal,
              },
      },
      {
        cmbId: 'locNo',
        condKey: 'm_ic_loc',
        optionCol: { zone_no: 'zoneNo' },
      },
      {
        cmbId: 'rsnCd',
        condKey: 'm_ic_rsn',
        where: { inv_gen_flg: '1' },
      },
      {
        cmbId: 'cmtInvGen',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'INV_LOT_GEN' },
      },
      {
        cmbId: 'cmtWarningZoneEnt',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'INV_WARNING' },
      },
    ],
  });
  if (
    comboBoxDataStandardReturnData &&
    comboBoxDataStandardReturnData.rData.rList.length > 0
  ) {
    comboBoxOptionList = comboBoxDataStandardReturnData.rData.rList;
    setCustomFormComboBoxOptionList(
      individualInventoryCreateFormRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );

    if ('formItems' in messageBoxAddInventoryLotPropsRef.value) {
      // コメントメッセージボックス選択肢
      setCustomFormComboBoxOptionList(
        messageBoxAddInventoryLotPropsRef.value.formItems,
        comboBoxDataStandardReturnData.rData.rList,
      );
    }
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, invCreateIndividualPackagingInventoryInit);
</script>
