import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

const { t } = i18n.global;

// ロット移動ダイアログのアイテム定義
export const getInventoryLotMoveFormItems: () => CustomFormType['formItems'] =
  () => ({
    matNo: {
      label: { text: t('Inv.Chr.txtItemCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    matNm: {
      label: { text: t('Inv.Chr.txtItemName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    lotNo: {
      label: { text: t('Inv.Chr.txtManageNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    lotStsNm: {
      label: { text: t('Inv.Chr.txtNoqualityStatus') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    lotLockStsNm: {
      label: { text: t('Inv.Chr.txtLotLocked') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    zoneNo: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtTranferZoneNo') },
      formModelValue: '',
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: 'zoneNo',
    },
    locNo: {
      label: { text: t('Inv.Chr.txtTranferLotNo') },
      formModelValue: '',
      rules: [],
      formRole: 'selectComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: 'locNo',
    },
    afmRefNo: {
      formModelValue: '',
      label: { text: t('Inv.Chr.txtAfmRefNo') },
      formRole: 'textBox',
      rules: [
        rules.length(16, t('Cm.Chr.txtLength', [16])),
        rules.upperCaseSingleByteAlphanumeric(),
      ],
    },
    cmtCatInvMov: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtComment') },
      formModelValue: '',
      rules: [
        rules.required('textComboBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
      ],
      formRole: 'textComboBox',
      props: {
        clearable: true,
      },
      selectOptions: [],
      cmbId: 'cmtInvMov',
    },
  });

// ロット移動ダイアログのモデル定義
export const inventoryLotMoveFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getInventoryLotMoveFormItems());

export const tablePropsData: TabulatorTableIF = {
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'lblNo',
  column: [
    { title: 'Inv.Chr.txtZone', field: 'zoneNm', width: COLUMN_WIDTHS.ZONE_NM },
    {
      title: 'Inv.Chr.txtLocation',
      field: 'locNm',
      width: COLUMN_WIDTHS.LOC_NM,
    },
    {
      title: 'Inv.Chr.txtAmount',
      field: 'invQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.INV_QTY,
    },
    {
      title: 'Inv.Chr.txtMesUnit',
      field: 'unitNm',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    {
      title: 'Inv.Chr.txtMoveIndividualInventory',
      field: 'lblLockStsNm',
      width: COLUMN_WIDTHS.INV.LOCK_STS_NM,
    },
    {
      title: 'Inv.Chr.txtPaletteNo',
      field: 'pltNo',
      width: COLUMN_WIDTHS.PLT_NO,
    },
    {
      title: 'Inv.Chr.txtIndividualNo',
      field: 'lblNo',
      width: COLUMN_WIDTHS.LBL_NO,
    },
  ],
  tableData: [],
  noUseConditionSearch: true, // 絞り込み条件無し
  showConditionSearch: false, // ConditionSearch不要
};
