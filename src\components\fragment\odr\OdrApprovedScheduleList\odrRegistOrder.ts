import CONST from '@/constants/utils';
import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';
import { GetOrderAddInitResData } from '@/types/HookUseApi/OdrTypes';

const { t } = i18n.global;

// NOTE:複数個所、特にvueで利用する場合に定義。tsで名称のみ記載の場合は直書きして定義を省いている。
// フォーム名の定義群
export const FORM_NAME = {
  // 特別作業指示
  ODR_ATT_BIN_LIST: 'odrAttBinList',
  // NOTE:「小日程計画.製造開始予定日」はodrDts。「製造指図.製造開始予定日」はodrStDts。
  // 製造開始予定日
  ODR_DTS: 'odrDts',
  // 使用期限日
  EXPIRY_DTS: 'expiryDts',
  // 使用期限起算日
  EXPIRY_CALC_DTS: 'expiryCalcDts',
  // 有効期限日
  SHELF_LIFE_DTS: 'shelfLifeDts',
  // 有効期限起算日
  SHELF_LIFE_CALC_DTS: 'shelfLifeCalcDts',
  // 製造番号発番
  BUTTON_SCHEDULED_DATE: 'buttonScheduledDate',
  // 製造番号
  DSP_LOT_NO: 'dspLotNo',
  // 指図コメント
  HAND_OVER_TXT: 'handOverTxt',
  // 単位名
  UNIT_NM_JP: 'unitNmJp',
} as const;

// fileUploadタイプ用 ファイル情報キー
export const FILE_ODR_ATT_BIN = {
  // フォームモデル名
  MODEL_KEY: FORM_NAME.ODR_ATT_BIN_LIST,
  NAME_KEY: 'binFileNm', // ファイル名キー。レスポンス名と一致する必要がある
  FILE_KEY: 'addAttBinFile', // ファイルキー。名称自由だが、リクエスト名に合わせておく。
  UNIQUE_KEY: 'appBinNo', // ユニークキー。レスポンス名と一致する必要がある
} as const;

// ファイルアップロード用定義
type OdrAttBinListType = {
  binFileNm: string; // ファイル名
  appBinNo: string; // 特別作業指示管理No
};

// fileUploadを絡めた、CustomFormの初期値設定を動作させる為の中間保持データ
export type OrderAppendixDataType = {
  [FORM_NAME.ODR_ATT_BIN_LIST]: OdrAttBinListType[]; // fileUpload用データ
} & GetOrderAddInitResData;

// 追加ファイルデータ定義
export type AddAttBinData = {
  [FILE_ODR_ATT_BIN.FILE_KEY]: string;
  [FILE_ODR_ATT_BIN.NAME_KEY]: string;
};

// 指図登録ダイアログのアイテム定義
export const getDialogFormItems: () => CustomFormType['formItems'] = () => ({
  // 単位名
  [FORM_NAME.UNIT_NM_JP]: {
    formModelValue: '',
    formRole: 'suffix',
  },
  // 計画番号
  planPrcNo: {
    formModelValue: '',
    label: { text: t('Odr.Chr.txtPlanProcedureNo') },
    formRole: 'textBox',
    props: { disabled: true },
  },
  // 品目コード
  matNo: {
    formModelValue: '',
    label: { text: t('Odr.Chr.txtMaterialCode') },
    formRole: 'textBox',
    props: { disabled: true },
  },
  // 品名
  dspNmJp: {
    formModelValue: '',
    label: { text: t('Odr.Chr.txtMaterialName') },
    formRole: 'textBox',
    props: { disabled: true },
  },
  // 処方名
  rxNmJp: {
    formModelValue: '',
    label: { text: t('Odr.Chr.txtPrescriptionName') },
    formRole: 'textBox',
    props: { disabled: true },
  },
  // MBR番号
  mbrNo: {
    formModelValue: '',
    label: { text: t('Odr.Chr.txtMasterBatchRecordNo') },
    formRole: 'textBox',
    props: { disabled: true },
  },
  // 処方有効期限
  validYmd: {
    formModelValue: '',
    label: { text: t('Odr.Chr.txtPrescriptionDeadlineDate') },
    formRole: 'textBox',
    props: { disabled: true },
  },
  // 標準生産量
  stdPrdQty: {
    formModelValue: '',
    label: { text: t('Odr.Chr.txtStandardProductionQuantity') },
    suffix: { formModelProp: FORM_NAME.UNIT_NM_JP },
    formRole: 'textBox',
    props: { disabled: true },
  },
  // 計画生産量
  planQty: {
    formModelValue: '',
    label: { text: t('Odr.Chr.txtOrderQuantity') },
    suffix: { formModelProp: FORM_NAME.UNIT_NM_JP },
    formRole: 'textBox',
    props: { disabled: true },
  },
  // 製造指図量
  odrQty: {
    formModelValue: '',
    label: { text: t('Odr.Chr.txtOdrQty') },
    suffix: { formModelProp: FORM_NAME.UNIT_NM_JP },
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    rules: [
      rules.required('textBox'),
      rules.placesOfNumeric({ int: 11, decimal: 12 }),
      rules.positiveRealNumericOnly(),
    ],
    formRole: 'textBox',
  },
  // 製造開始予定日
  [FORM_NAME.ODR_DTS]: {
    formModelValue: '',
    label: { text: t('Odr.Chr.txtOrderDate') },
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    rules: [rules.required('date'), rules.futureDate()],
    formRole: 'date',
    props: { modelValue: '', type: 'date' },
  },
  // 指図コメント
  [FORM_NAME.HAND_OVER_TXT]: {
    formModelValue: '',
    label: { text: t('Odr.Chr.txtHandOverComment') },
    rules: [
      rules.length(64),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ],
    formRole: 'textBox',
  },
  // 特別作業指示
  [FORM_NAME.ODR_ATT_BIN_LIST]: {
    formModelValue: [],
    label: { text: t('Odr.Chr.txtAppendix') },
    formRole: 'fileUpload',
    props: { fileList: [], fileType: 'application/pdf', maxLength: 1 },
  },
  // 使用期限日
  [FORM_NAME.EXPIRY_DTS]: {
    formModelValue: '',
    // NOTE:label,tags,rulesは実行時に生成します
    formRole: 'date',
    props: { modelValue: '', type: 'date' },
  },
  // 使用期限起算日
  [FORM_NAME.EXPIRY_CALC_DTS]: {
    formModelValue: '',
    // NOTE:label,tags,rulesは実行時に生成します
    formRole: 'date',
    props: { modelValue: '', type: 'date' },
  },
  // 有効期限日
  [FORM_NAME.SHELF_LIFE_DTS]: {
    formModelValue: '',
    // NOTE:label,tags,rulesは実行時に生成します
    formRole: 'date',
    props: { modelValue: '', type: 'date' },
  },
  // 有効期限起算日
  [FORM_NAME.SHELF_LIFE_CALC_DTS]: {
    formModelValue: '',
    // NOTE:label,tags,rulesは実行時に生成します
    formRole: 'date',
    props: { modelValue: '', type: 'date' },
  },
  // 製造番号発番
  [FORM_NAME.BUTTON_SCHEDULED_DATE]: {
    formModelValue: '',
    formRole: 'button',
    onClickHandler() {}, // NOTE:vueで上書きします
    props: {
      text: t('Odr.Chr.btnScheduledDate'),
    },
  },
  // 製造番号
  [FORM_NAME.DSP_LOT_NO]: {
    formModelValue: '',
    // NOTE:label,tags,rulesは実行時に生成します
    formRole: 'textBox',
    props: { disabled: true },
  },
});
// 指図登録ダイアログのモデル定義
export const dialogFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getDialogFormItems());
