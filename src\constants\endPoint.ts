const END_POINT = {
  AUTH_LOGIN: '/mpc/auth/login',
  AUTH_LOGOUT: '/mpc/auth/logout',
  AUTH_CHANGE_PASSWORD: '/mpc/auth/change-password',
  AUTH_CHECK_PASSWORD: '/mpc/auth/check-password',

  // mpc/odr/odr-skd-list
  // 小日程計画一覧取得
  GET_SKD_LIST: '/mpc/odr/odr-skd-list/get-skd-list',
  // 小日程計画確定前チェック
  CHECK_BEFORE_SKD_APPROVAL: '/mpc/odr/odr-skd-list/check-before-skd-approval',
  // 小日程計画確定
  MOD_SKD_APPROVAL: '/mpc/odr/odr-skd-list/mod-skd-approval',
  // 小日程計画確定取消
  MOD_SKD_PLAN: '/mpc/odr/odr-skd-list/mod-skd-plan',
  // 小日程計画追加
  ADD_SKD: '/mpc/odr/odr-skd-list/add-skd',
  // 処方一覧取得
  GET_RX_LIST: '/mpc/odr/odr-skd-list/get-rx-list',
  // 小日程計画変更初期表示
  GET_SKD_MOD_INIT: '/mpc/odr/odr-skd-list/get-skd-mod-init',
  // 小日程計画変更
  MOD_SKD: '/mpc/odr/odr-skd-list/mod-skd',
  // 小日程計画削除初期表示
  GET_SKD_DEL_INIT: '/mpc/odr/odr-skd-list/get-skd-del-init',
  // 小日程計画削除
  DEL_SKD: '/mpc/odr/odr-skd-list/del-skd',
  // 投入品一覧取得
  GET_BOM_LIST: '/mpc/odr/odr-skd-list/get-bom-list',
  // 原材料ロット一覧取得
  GET_RAW_BOM_LOT_LIST: '/mpc/odr/odr-skd-list/get-raw-bom-lot-list',
  // 原材料ロット予約前チェック
  CHECK_BEFORE_ADD_SKD_BOM_LOT:
    '/mpc/odr/odr-skd-list/check-before-add-skd-bom-lot',
  // 原材料ロット予約
  ADD_MATERIAL_SKD_BOM_LOT: '/mpc/odr/odr-skd-list/add-material-skd-bom-lot',
  // 中間製品ロット一覧取得
  GET_BULK_BOM_LOT_LIST: '/mpc/odr/odr-skd-list/get-bulk-bom-lot-list',
  // 中間製品ロット予約
  ADD_BULK_SKD_BOM_LOT: '/mpc/odr/odr-skd-list/add-bulk-skd-bom-lot',
  // ロット予約解除
  DEL_SKD_BOM_LOT: '/mpc/odr/odr-skd-list/del-skd-bom-lot',
  // 計画取込
  ADD_SKD_IMPORT: '/mpc/odr/odr-skd-list/add-skd-import',

  // mpc/odr/odr-approval
  // 製造指図承認一覧取得
  GET_ODR_APPROVAL_LIST: '/mpc/odr/odr-approval/get-odr-approval-list',
  // 製造指図承認
  MOD_ODR_APPROVAL: '/mpc/odr/odr-approval/mod-odr-approval',
  // 製造指図否認初期表示
  GET_ODR_DENIAL_INIT: '/mpc/odr/odr-approval/get-odr-denial-init',
  // 製造指図否認
  MOD_ODR_DENIAL: '/mpc/odr/odr-approval/mod-odr-denial',

  // mpc/odr/odr-list
  // 製造指図一覧取得
  GET_APPROVED_ODR_LIST: '/mpc/odr/odr-list/get-approved-odr-list',
  // 製造工程一覧取得
  GET_ODR_PRC_LIST_IN_REBATCH_INST:
    '/mpc/odr/odr-list/get-odr-prc-list-in-rebatch-inst',
  // 処方SOPフロー一覧取得
  GET_ODR_SOP_FLOW_LIST: '/mpc/odr/odr-list/get-odr-sop-flow-list',
  // 再バッチ指示登録
  ADD_ODR_REBATCH_INST: '/mpc/odr/odr-list/add-odr-rebatch-inst',
  // 製造指図中止初期表示
  GET_ODR_DISCONTINUE_INIT: '/mpc/odr/odr-list/get-odr-discontinue-init',
  // 製造指図中止
  MOD_ODR_DISCONTINUE: '/mpc/odr/odr-list/mod-odr-discontinue',
  // 製造指図取消初期表示
  GET_ODR_ERASE_INIT: '/mpc/odr/odr-list/get-odr-erase-init',
  // 製造指図取消
  MOD_ODR_ERASE: '/mpc/odr/odr-list/mod-odr-erase',

  // mpc/odr/odr-registration
  // 確定小日程計画一覧取得
  GET_APPROVAL_SKD_LIST: '/mpc/odr/odr-registration/get-approval-skd-list',
  // 指図登録初期表示
  GET_ODR_ADD_INIT: '/mpc/odr/odr-registration/get-odr-add-init',
  // 製造番号情報取得前チェック
  CHECK_BEFORE_GET_LOT_NO_INFO:
    '/mpc/odr/odr-registration/check-before-get-lot-no-info',
  // 製造番号情報取得
  GET_LOT_NO_INFO: '/mpc/odr/odr-registration/get-lot-no-info',
  // 指図登録前チェック
  CHECK_BEFORE_ADD_ODR: '/mpc/odr/odr-registration/check-before-add-odr',
  // 指図登録
  ADD_ODR: '/mpc/odr/odr-registration/add-odr',
  // 処方変更初期表示
  GET_RX_MOD: '/mpc/odr/odr-registration/get-rx-mod',
  // 小日程計画処方変更
  MOD_SKD_RX: '/mpc/odr/odr-registration/mod-skd-rx',
  // 指図予定詳細取得
  GET_M_RX_PRC_LIST: '/mpc/odr/odr-registration/get-m-rx-prc-list',
  // 構成品予定取得
  GET_M_RX_BOM_LIST: '/mpc/odr/odr-registration/get-m-rx-bom-list',
  // 版変更削除
  DEL_SKD_BOM: '/mpc/odr/odr-registration/del-skd-bom',
  // 版変更初期表示
  GET_SKD_BOM_INIT: '/mpc/odr/odr-registration/get-skd-bom-init',
  // 版変更
  ADD_SKD_BOM: '/mpc/odr/odr-registration/add-skd-bom',
  // SOPフロー予定取得
  GET_M_RX_SOP_FLOW: '/mpc/odr/odr-registration/get-m-rx-sop-flow',

  // mpc/odr/odr-reference
  // 製造指図登録後一覧取得
  GET_REGISTERED_ODR_LIST: '/mpc/odr/odr-reference/get-registered-odr-list',
  // 指図詳細取得
  GET_ODR_PRC_LIST: '/mpc/odr/odr-reference/get-odr-prc-list',
  // 構成品一覧取得
  GET_ODR_BOM_LIST: '/mpc/odr/odr-reference/get-odr-bom-list',
  // SOPフロー一覧取得
  GET_ODR_SOP_FLOW: '/mpc/odr/odr-reference/get-odr-sop-flow',
  // 指図コメント入力初期表示
  GET_ODR_HAND_OVER_TXT: '/mpc/odr/odr-reference/get-odr-hand-over-txt',
  // 指図コメント更新
  MOD_ODR_HAND_OVER_TXT: '/mpc/odr/odr-reference/mod-odr-hand-over-txt',
  // 秤量指示明細一覧取得
  GET_WGT_INST_LIST_FROM_ODR:
    '/mpc/odr/odr-reference/get-wgt-inst-list-from-odr',
  // 特別作業指示初期表示
  GET_ODR_APPENDIX: '/mpc/odr/odr-reference/get-odr-appendix',
  // 特別作業指示更新
  MOD_ODR_APPENDIX: '/mpc/odr/odr-reference/mod-odr-appendix',

  SEARCH_INVENTORY_RETURN_INSTRUCTION_ITEM:
    '/mpc/inventoryReturnInstructionItem',

  // 返納指示書チェック
  CHK_INV_RTN_INST_GRP: '/mpc/inv/inv-trf-rtn-recv/chk-inv-rtn-inst-grp',
  // 返納物チェック
  CHK_INV_RTN_INST: '/mpc/inv/inv-trf-rtn-recv/chk-inv-rtn-inst',
  // 返納明細一覧取得
  GET_INV_RTN_INST_LIST_RECV: '/mpc/inv/inv-trf-rtn-recv/get-inv-rtn-inst-list',
  // 返納指示強制完了
  MOD_INV_RTN_INST: '/mpc/inv/inv-trf-rtn-recv/mod-inv-rtn-inst',

  // mpc/common
  // 標準コンボボックスデータ取得
  COMMON_GET_COMBO_DATA_STD: '/mpc/common/get-combo-data-std',
  // 署名：パスワード認証
  CHK_SIGN_PASSWORD: '/mpc/common/chk-sign-password',
  // 署名：顔認証
  CHK_SIGN_FACE: '/mpc/common/chk-sign-face',
  // ファイルダウンロード
  GET_SYSBIN: '/mpc/common/get-sysbin',
  // ファイルダウンロード
  GET_REPOR_FILE_BY_FILENAME: '/mpc/common/get-repor-file-by-filename',

  // mpc/usr-grid
  // グリッド列表示情報取得
  GET_USR_GRID: '/mpc/usr-grid/get-usr-grid',
  // グリッド列並び順更新
  UPDATE_USR_GRID: '/mpc/usr-grid/update-usr-grid',

  // mpc/inv/inv-edit
  // 在庫一覧.一覧
  GET_INV_LIST: '/mpc/inv/inv-edit/get-inv-list',
  // 個装在庫情報取得
  GET_INV_LBL: '/mpc/inv/inv-edit/get-inv-lbl',
  // 個装在庫情報取得(移動用)
  GET_INV_LBL_MOV: '/mpc/inv/inv-edit/get-inv-lbl-mov',
  // 個装在庫情報取得(修正用)
  GET_INV_LBL_MOD: '/mpc/inv/inv-edit/get-inv-lbl-mod',
  // 個装在庫情報取得(ロック用)
  GET_INV_LBL_LOCK: '/mpc/inv/inv-edit/get-inv-lbl-lock',
  // 個装在庫情報取得(ロック解除用)
  GET_INV_LBL_UNLOCK: '/mpc/inv/inv-edit/get-inv-lbl-unlock',
  // ロット在庫情報取得(ロック用)
  GET_INV_LOT_LOCK: '/mpc/inv/inv-edit/get-inv-lot-lock',
  // ロット在庫情報取得(ロック解除用)
  GET_INV_LOT_UNLOCK: '/mpc/inv/inv-edit/get-inv-lot-unlock',
  // ロット在庫情報取得(移動用)
  GET_INV_LOT_MOV: '/mpc/inv/inv-edit/get-inv-lot-mov',
  // 個装在庫修正
  MOD_INV_LBL: '/mpc/inv/inv-edit/mod-inv-lbl',
  // 個装在庫移動前チェック
  CHK_BEFORE_LBL_MOVE: '/mpc/inv/inv-edit/chk-before-lbl-move',
  // 個装在庫移動
  MOD_INV_LBL_MOVE: '/mpc/inv/inv-edit/mod-inv-lbl-move',
  // 個装ロック
  MOD_INV_LBL_LOCK: '/mpc/inv/inv-edit/mod-inv-lbl-lock',
  // 個装ロック解除
  MOD_INV_LBL_UNLOCK: '/mpc/inv/inv-edit/mod-inv-lbl-unlock',
  // ロットロック
  MOD_INV_LOT_LOCK: '/mpc/inv/inv-edit/mod-inv-lot-lock',
  // ロットロック解除
  MOD_INV_LOT_UNLOCK: '/mpc/inv/inv-edit/mod-inv-lot-unlock',
  // ロット移動前チェック
  CHK_INV_BEFORE_LOT_MOVE: '/mpc/inv/inv-edit/chk-inv-before-lot-move',
  // ロット移動
  MOD_INV_LOT_MOVE: '/mpc/inv/inv-edit/mod-inv-lot-move',
  // 個装在庫追加
  ADD_INV_MULTI_LBL: '/mpc/inv/inv-edit/add-inv-multi-lbl',
  // 個装在庫生成
  ADD_INV_LOT: '/mpc/inv/inv-edit/add-inv-lot',
  // 管理番号/製造番号検索初期表示データ取得
  GET_LOT_SEARCH_INIT: '/mpc/inv/inv-edit/get-lot-search-init',
  // ロット情報取得(管理番号/製造番号検索用)
  GET_INV_LOT: '/mpc/inv/inv-edit/get-inv-lot',
  // 保管出納検索
  GET_AFM_LIST: '/mpc/inv/inv-afm/get-afm-list',
  // 在庫アンマッチリスト検索
  GET_UNMATCH_LIST: '/mpc/inv/inv-unmatch/get-unmatch-list',
  // 在庫スナップショット初期表示データ取得
  GET_INV_SNAPSHOT_INIT: '/mpc/inv/inv-snapshot/get-inv-snapshot-init',
  // 在庫スナップショット実績検索
  GET_SNAPSHOT_ACHIEVEMENT_LIST:
    '/mpc/inv/inv-snapshot/get-snapshot-achievement-list',
  // 返品在庫検索
  GET_RTN_INV_LIST: '/mpc/inv/inv-rtn/get-rtn-inv-list',
  // 返品登録
  DEL_INV_BY_RTN: '/mpc/inv/inv-rtn/del-inv-by-rtn',
  // 返納指示作成前チェック
  CHK_INV_BEFORE_RTN_INST: '/mpc/inv/inv-trf-rtn-inst/chk-inv-before-rtn-inst',
  // 返納指示作成
  ADD_INV_RTN_INST: '/mpc/inv/inv-trf-rtn-inst/add-inv-rtn-inst',
  // 返納指示書一覧取得
  GET_INV_RTN_INST_GRP_LIST:
    '/mpc/inv/inv-trf-rtn-inst/get-inv-rtn-inst-grp-list',
  // 返納指示（明細）情報取得
  GET_INV_RTN_INST_LIST: '/mpc/inv/inv-trf-rtn-inst/get-inv-rtn-inst-list',

  // mpc/aog/aog-plan
  // 入荷予定一覧一覧取得
  GET_AOG_PLAN_LIST: '/mpc/aog/aog-plan/get-aog-plan-list',
  // 入荷予定削除
  DEL_AOG_PLAN: '/mpc/aog/aog-plan/del-aog-plan',
  // 入荷指示一覧一覧取得
  GET_AOG_INST_LIST: '/mpc/aog/aog-Inst/get-aog-inst-list',
  // 入荷指示明細データ取得
  GET_AOG_INST: '/mpc/aog/aog-Inst/get-aog-inst',
  // 入荷指示印刷
  GET_AOG_INST_GRP_PRINT: '/mpc/aog/aog-Inst/get-aog-inst-grp-print',
  // 入荷指示取消
  DEL_AOG_INST_GRP: '/mpc/aog/aog-Inst/del-aog-inst-grp',
  // 入荷検品継続解除
  MOD_AOG_IOA_FREE: '/mpc/aog/aog-Inst/mod-aog-ioa-free',
  // 入荷検品強制完了データ取得
  GET_AOG_IOA_FORCE: '/mpc/aog/aog-Inst/get-aog-ioa-force',
  // 入荷検品強制完了
  MOD_AOG_IOA_FORCE: '/mpc/aog/aog-Inst/mod-aog-ioa-force',
  // 入荷検品完了取消データ取得
  GET_AOG_IOA_CANCEL: '/mpc/aog/aog-Inst/get-aog-ioa-cancel',
  // 入荷検品完了取消
  MOD_AOG_IOA_CANCEL: '/mpc/aog/aog-Inst/mod-aog-ioa-cancel',
  // 入荷指示作成一覧取得
  GET_AOG_INST_CR_LIST: '/mpc/aog/aog-inst-cr/get-aog-inst-cr-list',
  // 入荷指示作成初期表示データ取得
  GET_AOG_INST_CR_LIST_INIT: '/mpc/aog/aog-inst-cr/get-aog-inst-cr-list-init',
  // 入荷指示作成
  ADD_AOG_INST: '/mpc/aog/aog-inst-cr/add-aog-inst',
  // 入荷予定詳細データ取得
  GET_AOG_PLAN: '/mpc/aog/aog-inst-cr/get-aog-plan',
  // 入荷予定コピー追加データ取得
  GET_AOG_PLAN_COPY_ADD: '/mpc/aog/aog-plan/get-aog-plan-copy-add',
  // 入荷予定コピー追加
  ADD_AOG_PLAN_COPY: '/mpc/aog/aog-plan/add-aog-plan-copy',
  // 入荷予定修正データ取得
  GET_AOG_PLAN_MOD: '/mpc/aog/aog-plan/get-aog-plan-mod',
  // 入荷予定修正
  MOD_AOG_PLAN: '/mpc/aog/aog-plan/mod-aog-plan',
  // 受入実績確定一覧取得
  GET_AOG_RSLT_FIX_LIST: '/mpc/aog/aog-rslt-fix/get-aog-rslt-fix-list',
  // 受入実績確定
  ADD_AOG_RSLT_FIX: '/mpc/aog/aog-rslt-fix/add-aog-rslt-fix',
  // 受入実績修正データ取得
  GET_AOG_RSLT_FIX: '/mpc/aog/aog-rslt-fix/get-aog-rslt-fix',
  // 受入実績確定修正
  MOD_AOG_RSLT_FIX: '/mpc/aog/aog-rslt-fix/mod-aog-rslt-fix',
  // 受入実績確定初期表示データ取得
  GET_AOG_RSLT_FIX_INIT: '/mpc/aog/aog-rslt-fix/get-aog-rslt-fix-init',

  // mpc/aog/aog-print-lbl
  // 一覧取得
  SEARCH_AOG_LABEL_ISSUANCE_PLAN_LIST:
    '/mpc/aog/aog-print-lbl/get-aog-print-lbl-list',
  // パレット積載情報取得
  SEARCH_AOG_EDIT_PALETTE_LOADING: '/mpc/aog/aog-print-lbl/get-aog-print-lbl',
  // パレット積載情報修正
  MOD_AOG_EDIT_PALETTE_LOADING_LIST: '/mpc/aog/aog-print-lbl/mod-aog-print-lbl',
  // ラベル発行
  MOD_AOG_LBL_LIST_PRINT: '/mpc/aog/aog-print-lbl/mod-aog-lbl-list-print',
  // mpc/aog/aog-rslt-car
  // 入荷時検品記録画面一覧取得
  GET_AOG_RSLT_CAR_LIST: '/mpc/aog/aog-rslt-car/get-aog-rslt-car-list',
  // 入荷時検品記録初期表示データ取得
  GET_AOG_RSLT_CAR_INIT: '/mpc/aog/aog-rslt-car/get-aog-rslt-car-init',
  // 入荷時検品記録データ取得
  GET_AOG_RSLT_CAR: '/mpc/aog/aog-rslt-car/get-aog-rslt-car',
  // 入荷時検品記録データ入力
  MOD_AOG_RSLT_CAR: '/mpc/aog/aog-rslt-car/mod-aog-rslt-car',
  // 入荷時検品記録確認画面一覧取得
  GET_AOG_RSLT_CAR_CHECK_LIST:
    '/mpc/aog/aog-rslt-car/get-aog-rslt-car-check-list',
  // 入荷時検品記録確認
  MOD_AOG_RSLT_CAR_CHECK: '/mpc/aog/aog-rslt-car/mod-aog-rslt-car-check',
  // 入荷時検品記録確認取消
  MOD_AOG_RSLT_CAR_CHECK_CANCEL:
    '/mpc/aog/aog-rslt-car/mod-aog-rslt-car-check-cancel',

  // 秤量指示書作成検索
  GET_WGT_INST: '/mpc/wgt/wgt-inst-cr/get-wgt-inst',
  // 秤量指示書登録初期表示
  GET_WGT_INST_ENTRY_INIT: '/mpc/wgt/wgt-inst-cr/get-wgt-inst-entry-init',
  // 秤量指示書登録
  ADD_WGT_INST_ENTRY: '/mpc/wgt/wgt-inst-cr/add-wgt-inst-entry',
  // 秤量指示書一覧検索
  GET_WGT_INST_LIST: '/mpc/wgt/wgt-inst/get-wgt-inst-list',
  // 秤量指示書詳細(確定)初期表示
  GET_WGT_INST_CONFIRM_INIT: '/mpc/wgt/wgt-inst/get-wgt-inst-confirm-init',
  // 秤量指示書確定
  MOD_WGT_INST_CONFIRM: '/mpc/wgt/wgt-inst/mod-wgt-inst-confirm',
  // 秤量指示書詳細(取消)初期表示
  GET_WGT_INST_ERASE_INIT: '/mpc/wgt/wgt-inst/get-wgt-inst-erase-init',
  // 秤量指示書取消
  DEL_WGT_INST_ERASE: '/mpc/wgt/wgt-inst/del-wgt-inst-erase',
  // 秤量指示書詳細(印刷)初期表示
  GET_WGT_INST_PRINT_INIT: '/mpc/wgt/wgt-inst/get-wgt-inst-print-init',
  // 秤量指示書印刷
  MOD_WGT_INST_PRINT: '/mpc/wgt/wgt-inst/mod-wgt-inst-print',
  // 秤量室変更検索
  GET_WGT_ROOM_CHG: '/mpc/wgt/wgt-room-chg/get-wgt-room-chg',
  // 秤量室変更詳細(依頼)初期表示
  GET_WGT_ROOM_CHG_REQUEST_INIT:
    '/mpc/wgt/wgt-room-chg/get-wgt-room-chg-request-init',
  // 秤量室変更依頼前チェック
  CHK_WGT_ROOM_CHG_REQUEST: '/mpc/wgt/wgt-room-chg/chk-wgt-room-chg-request',
  // 秤量室変更依頼完了
  MOD_WGT_ROOM_CHG_REQUEST: '/mpc/wgt/wgt-room-chg/mod-wgt-room-chg-request',
  // 秤量室変更依頼取消
  MOD_WGT_ROOM_CHG_REQUEST_ERASE:
    '/mpc/wgt/wgt-room-chg/mod-wgt-room-chg-request-erase',
  // 秤量室変更詳細(承認)初期表示
  GET_WGT_ROOM_CHG_APPROVAL_INIT:
    '/mpc/wgt/wgt-room-chg/get-wgt-room-chg-approval-init',
  // 秤量室変更承認完了
  MOD_WGT_ROOM_CHG_APPROVAL: '/mpc/wgt/wgt-room-chg/mod-wgt-room-chg-approval',
  // 再秤量指示書検索
  GET_RE_WGT_INST: '/mpc/wgt/wgt-re-wgt-inst-cr/get-re-wgt-inst',
  // 秤量記録確認検索
  GET_WGT_RECORD_CONFIRM: '/mpc/wgt/wgt-rec-conf/get-wgt-rec-conf',
  // 秤量記録確認
  MOD_WGT_RECORD_CONFIRM: '/mpc/wgt/wgt-rec-conf/mod-wgt-rec-conf',
  // 秤量記録詳細初期表示
  GET_WGT_RECORD_DETAIL_INIT: '/mpc/wgt/wgt-rec-conf/get-wgt-rec-detail-init',
  // 秤量前後SOP記録確認
  GET_WGT_SOP_CONF_INIT: '/mpc/wgt/wgt-rec-conf/get-wgt-sop-conf-init',
  // 再秤量指示登録(小分け)初期表示
  GET_RE_WGT_INST_WRAPPING_INIT:
    '/mpc/wgt/wgt-re-wgt-inst-cr/get-re-wgt-inst-wrapping-init',
  // 再秤量指示登録(小分け)実行(再秤量指示作成)
  MOD_RE_WGT_INST_WRAPPING:
    '/mpc/wgt/wgt-re-wgt-inst-cr/mod-re-wgt-inst-wrapping',
  // 再秤量指示登録(明細)初期表示
  GET_RE_WGT_INST_DETAIL_INIT:
    '/mpc/wgt/wgt-re-wgt-inst-cr/get-re-wgt-inst-detail-init',
  // 再秤量指示登録(明細)実行(再秤量指示作成)
  MOD_RE_WGT_INST_DETAIL: '/mpc/wgt/wgt-re-wgt-inst-cr/mod-re-wgt-inst-detail',
  // 秤量前後SOP作業詳細記録初期表示
  GET_WGT_SOP_DETAIL_INIT: '/mpc/wgt/wgt-rec-conf/get-wgt-sop-detail-init',
  // 秤量前後SOP作業詳細記録検印実行
  MOD_WGT_SOP_DETAIL_RECORD_STAMP:
    '/mpc/wgt/wgt-rec-conf/mod-wgt-sop-detail-record-stamp',
  // 秤量記録修正初期表示
  GET_WGT_RECORD_EDIT_INIT: '/mpc/wgt/wgt-rec-conf/get-wgt-rec-edit-init',
  // 秤量記録修正修正確定
  MOD_WGT_RECORD_EDIT_CONFIRM: '/mpc/wgt/wgt-rec-conf/mod-wgt-rec-edit-confirm',
  // 秤量前後SOP修正履歴初期表示
  GET_WGT_SOP_HISTORY_MODIFY_INIT:
    '/mpc/wgt/wgt-rec-conf/get-wgt-sop-history-modify-init',
  // 秤量前後SOP異状履歴 初期表示
  GET_WGT_SOP_HISTORY_DEVIANT_INIT:
    '/mpc/wgt/wgt-rec-conf/get-wgt-sop-history-deviant-init',
  // 秤量前後SOP記録修正初期表示
  GET_WGT_SOP_REC_EDIT_INIT: '/mpc/wgt/wgt-rec-conf/get-wgt-sop-rec-edit-init',
  // 秤量前後SOP記録修正実行
  MOD_WGT_SOP_REC_EDIT: '/mpc/wgt/wgt-rec-conf/mod-wgt-sop-rec-edit',
  // 秤量前後SOP記録修正記録値判定
  CHK_WGT_SOP_REC_EDIT: '/mpc/wgt/wgt-rec-conf/chk-wgt-sop-rec-edit',
  // 秤量前後SOP一覧検索
  GET_WGT_SOP_LIST: '/mpc/wgt/wgt-sop-list/get-wgt-sop-list',

  // 製造記録確認_指図一覧検索
  GET_CONF_ODR_INFO_LIST: '/mpc/prd/prd-rec-conf/get-conf-odr-info-list',
  // 製造記録確認_SOP記録初期表示
  GET_CONF_SOP_FLOW_LIST_INIT:
    '/mpc/prd/prd-rec-conf/get-conf-sop-flow-list-init',
  // 製造記録確認_記録詳細初期表示
  GET_CONF_REC_INFO_INIT: '/mpc/prd/prd-rec-conf/get-conf-rec-info-init',
  // 製造記録確認_記録詳細指図記録確認
  MOD_CONF_REC_INFO: '/mpc/prd/prd-rec-conf/mod-conf-rec-info',
  // 製造記録確認_投入実績初期表示
  GET_CONF_BOM_LIST_INIT: '/mpc/prd/prd-rec-conf/get-conf-bom-list-init',
  // 製造記録確認_投入記録詳細初期表示
  GET_CONF_BOM_INFO_LIST_INIT:
    '/mpc/prd/prd-rec-conf/get-conf-bom-info-list-init',
  // 投入記録修正初期表示
  GET_CONF_BOM_REC_INIT: '/mpc/prd/prd-rec-conf/get-conf-bom-rec-init',
  // 投入記録修正確定
  MOD_CONF_BOM_REC: '/mpc/prd/prd-rec-conf/mod-conf-bom-rec',
  // 製造記録確認_秤量記録
  GET_CONF_WGT_INIT: '/mpc/prd/prd-rec-conf/get-conf-wgt-init',
  // 製造記録確認_出来高記録初期表示
  GET_CONF_PRD_LIST_INIT: '/mpc/prd/prd-rec-conf/get-conf-prd-list-init',
  // 製造記録確認_出来高記録詳細初期表示
  GET_CONF_PRD_INFO_LIST_INIT:
    '/mpc/prd/prd-rec-conf/get-conf-prd-info-list-init',
  // 出来高修正初期表示
  GET_CONF_PRD_MOD_INIT: '/mpc/prd/prd-rec-conf/get-conf-prd-mod-init',
  // 出来高修正実行
  MOD_CONF_PRD_MOD: '/mpc/prd/prd-rec-conf/mod-conf-prd-mod',
  // 収率修正初期表示
  GET_CONF_YIELD: '/mpc/prd/prd-rec-conf/get-conf-yield',
  // 収率修正実行
  MOD_CONF_YIELD: '/mpc/prd/prd-rec-conf/mod-conf-yield',
  // 作業工数確認初期表示
  GET_CONF_WORK: '/mpc/prd/prd-rec-conf/get-conf-work',
  // 作業工数確認実行
  MOD_CONF_WORK: '/mpc/prd/prd-rec-conf/mod-conf-work',
  // 製造記録確認_コメント確認
  GET_CONF_CMT_INFO: '/mpc/prd/prd-rec-conf/get-conf-cmt-info',
  // 製造記録確認_異状履歴
  GET_CONF_DEV_INFO: '/mpc/prd/prd-rec-conf/get-conf-dev-info',
  // 製造記録確認_SOP作業詳細初期表示
  GET_CONF_SOP_FLOW_INFO_INIT:
    '/mpc/prd/prd-rec-conf/get-conf-sop-flow-info-init',
  // 製造記録確認_SOP作業詳細記録確認
  MOD_CONF_SOP_FLOW_INFO: '/mpc/prd/prd-rec-conf/mod-conf-sop-flow-info',
  // 製造記録修正_SOP実施記録初期表示
  GET_CONF_SOP_REC_INIT: '/mpc/prd/prd-rec-conf/get-conf-sop-rec-init',
  // 製造記録修正_SOP実施記録修正値判定
  CHK_CONF_SOP_REC: '/mpc/prd/prd-rec-conf/chk-conf-sop-rec',
  // 製造記録修正_SOP実施記録修正確定
  MOD_CONF_SOP_REC: '/mpc/prd/prd-rec-conf/mod-conf-sop-rec',
  // 製造記録確認_SOP修正履歴一覧
  GET_CONF_MOD_INFO: '/mpc/prd/prd-rec-conf/get-conf-mod-info',
  // 製造記録確認_投入修正履歴一覧
  GET_CONF_BOM_MOD_INFO: '/mpc/prd/prd-rec-conf/get-conf-bom-mod-info',
  // 製造記録確認_出来高修正履歴一覧
  GET_CONF_PRD_MOD_INFO: '/mpc/prd/prd-rec-conf/get-conf-prd-mod-info',
  // 製造記録確認_秤量記録修正履歴一覧
  GET_CONF_WGT_REC_MOD_INFO: '/mpc/prd/prd-rec-conf/get-conf-wgt-rec-mod-info',
  // 製造記録承認_指図一覧検索
  GET_APP_ODR_INFO_LIST: '/mpc/prd/prd-rec-app/get-app-odr-info-list',
  // 製造記録承認_記録詳細初期表示
  GET_APP_REC_INFO_INIT: '/mpc/prd/prd-rec-app/get-app-rec-info-init',
  // 製造記録承認_記録詳細確認取消
  MOD_APP_INFO: '/mpc/prd/prd-rec-app/mod-app-info',
  // 製造記録承認_記録詳細記録承認
  MOD_APP_REC_APP: '/mpc/prd/prd-rec-app/mod-app-rec-app',
  // 製造記録承認_SOP記録
  GET_APP_SOP_FLOW_LIST: '/mpc/prd/prd-rec-app/get-app-sop-flow-list',
  // 製造記録承認_投入実績
  GET_APP_BOM: '/mpc/prd/prd-rec-app/get-app-bom',
  // 製造記録承認_投入記録詳細
  GET_APP_BOM_INFO: '/mpc/prd/prd-rec-app/get-app-bom-info',
  // 製造記録承認_秤量記録
  GET_APP_WGT: '/mpc/prd/prd-rec-app/get-app-wgt',
  // 製造記録承認_出来高記録
  GET_APP_PRD: '/mpc/prd/prd-rec-app/get-app-prd',
  // 製造記録承認_出来高記録詳細
  GET_APP_PRD_INFO: '/mpc/prd/prd-rec-app/get-app-prd-info',
  // 製造記録承認_SOP修正履歴一覧
  GET_APP_MOD_INFO_INIT: '/mpc/prd/prd-rec-app/get-app-mod-info-init',
  // 製造記録承認_投入修正履歴一覧
  GET_APP_BOM_MOD_INFO_INIT: '/mpc/prd/prd-rec-app/get-app-bom-mod-info-init',
  // 製造記録承認_出来高修正履歴一覧
  GET_APP_PRD_MOD_INFO_INIT: '/mpc/prd/prd-rec-app/get-app-prd-mod-info-init',
  // 製造記録承認_作業工数確認初期表示
  GET_APP_WORK: '/mpc/prd/prd-rec-app/get-app-work',
  // 製造記録承認_異状履歴一覧
  GET_APP_DEV_LIST_INFO_INIT: '/mpc/prd/prd-rec-app/get-app-dev-list-info-init',
  // 製造記録承認_SOP作業詳細
  GET_APP_SOP_FLOW_INFO: '/mpc/prd/prd-rec-app/get-app-sop-flow-info',
  // 製造記録承認_コメント確認
  GET_APP_CMT_INFO: '/mpc/prd/prd-rec-app/get-app-cmt-info',
  // 製造記録承認_異状確認履歴初期表示
  GET_APP_DEV_INFO_INIT: '/mpc/prd/prd-rec-app/get-app-dev-info-init',
  // 製造記録承認_異状確認履歴確認
  MOD_APP_DEV_INFO: '/mpc/prd/prd-rec-app/mod-app-dev-info',
  // 製造記録参照検索
  GET_REF_INFO: '/mpc/prd/prd-rec-ref/get-ref-info',
  // 製造記録参照_記録詳細初期表示
  GET_REF_INFO_INIT: '/mpc/prd/prd-rec-ref/get-ref-info-init',
  // 製造記録参照承認取消
  MOD_REF_APP: '/mpc/prd/prd-rec-ref/mod-ref-app',
  // 印刷設定_製造記録参照帳票出力
  GET_REF_PRINT: '/mpc/prd/prd-rec-ref/get-ref-print',
  // 工程作業記録検索
  GET_PRC_FLOW_LIST: '/mpc/prd/prd-work-rec/get-prc-flow-list',
  // 工程作業記録_記録承認取消
  MOD_PRC_APP: '/mpc/prd/prd-work-rec/mod-prc-app',
  // 印刷設定_製造記録参照帳票出力
  GET_PRC_PRINT: '/mpc/prd/prd-work-rec/get-prc-print',
  // 工程作業記録_SOP作業詳細初期表示
  GET_SOP_FLOW_INIT: '/mpc/prd/prd-work-rec/get-sop-flow-init',
  // 工程作業記録_SOP作業詳細記録承認
  MOD_SOP_FLOW_REC: '/mpc/prd/prd-work-rec/mod-sop-flow-rec',
  // 工程作業記録_製造記録修正初期表示
  GET_REC_INIT: '/mpc/prd/prd-work-rec/get-rec-init',
  // 工程作業記録_製造記録修正値判定
  CHK_REC_INFO: '/mpc/prd/prd-work-rec/chk-rec-info',
  // 工程作業記録_製造記録修正確定
  MOD_REC_INFO: '/mpc/prd/prd-work-rec/mod-rec-info',
  // 工程作業記録_修正履歴一覧
  GET_MOD_LOG_LIST: '/mpc/prd/prd-work-rec/get-mod-log-list',
  // 工程作業記録_コメント確認
  GET_CMT_LOG_LIST: '/mpc/prd/prd-work-rec/get-cmt-log-list',

  // mpc/sog
  // 製品出庫指示作成一覧取得
  GET_SOG_TRF_INST_CR_LIST: '/mpc/sog/sog-trf-inst-cr/get-sog-trf-inst-cr-list',
  // 出庫指示作成
  ADD_SOG_TRF_INST_CR: '/mpc/sog/sog-trf-inst-cr/add-sog-trf-inst-cr',
  // 出荷実績確定初期表示データ取得
  GET_SOG_RSLT_FIX_INIT: '/mpc/sog/sog-rslt-fix/get-sog-slip-fix-init',
  // 出荷実績一覧取得
  GET_SOG_RSLT_LIST: '/mpc/sog/sog-rslt-fix/get-sog-slip-fix-list',
  // 出荷実績修正データ取得
  GET_SOG_RSLT_FIX_MOD: '/mpc/sog/sog-rslt-fix/get-sog-slip-fix-mod',
  // 出荷実績修正
  MOD_SOG_RSLT_FIX: '/mpc/sog/sog-rslt-fix/mod-sog-slip-fix',
  // 出荷実績確定
  ADD_SOG_RSLT_FIX: '/mpc/sog/sog-rslt-fix/add-sog-slip-fix',
  // 強制出荷データ取得
  GET_SOG_FORCE: '/mpc/sog/sog-rslt-fix/get-sog-ship-force',
  // 強制出荷
  MOD_SOG_FORCE: '/mpc/sog/sog-rslt-fix/mod-sog-ship-force',
  // 製品出庫指示一覧一覧取得
  GET_SOG_TRF_INST_LIST: '/mpc/sog/sog-trf-inst/get-sog-trf-inst-list',
  // 納品予定PDF取得
  GET_SOG_SLIP_PRINT: '/mpc/sog/sog-trf-inst/get-sog-slip-print',
  // 出庫指示PDF取得
  GET_SOG_INST_PRINT: '/mpc/sog/sog-trf-inst/get-sog-inst-print',
  // 出庫指示書取消
  DEL_SOG_TRF_INST_CR: '/mpc/sog/sog-trf-inst/del-sog-trf-inst-cr',
  // ピッキング中解除
  MOD_SOG_TRF_INST_PICKING: '/mpc/sog/sog-trf-inst/mod-sog-trf-inst-picking',

  // mpc/sog/sog-inst-cr
  // 製品出庫指示一覧データ取得
  GET_SOG_RSLT_FIX_LIST: '/mpc/sog/sog-inst-cr/get-sog-inst-rslt-fix-list',
  // 出荷情報変更データ取得
  GET_SOG_INST_SHIP_MOD: '/mpc/sog/sog-inst-cr/get-sog-slip',
  // 出荷指示中止
  MOD_SOG_INST_SHIP_DISCON: '/mpc/sog/sog-inst-cr/mod-sog-slip-discon',
  // 出荷情報変更
  MOD_SOG_INST_SHIP_MOD: '/mpc/sog/sog-inst-cr/mod-sog-slip',

  // mpc/sog/sog-rslt-fix
  // 出荷実績確定一覧一覧取得
  GET_SOG_SLIP_FIX_LIST: '/mpc/sog/sog-rslt-fix/get-sog-slip-fix-list',

  // mpc/trf
  // 出庫可能指図一覧取得
  GET_TRF_ODR_LIST: '/mpc/trf/trf-req/get-trf-odr-list',
  // 出庫可能指図状態チェック
  CHK_TRF_ODR_STS: '/mpc/trf/trf-req/chk-trf-odr-sts',
  // 出庫指図参照構成品一覧取得
  GET_TRF_ODR_REF_BOM_LIST: '/mpc/trf/trf-req-cr/get-trf-odr-ref-bom-list',
  // 出庫ゾーン毎在庫量一覧取得
  GET_TRF_ZONE_INV_LIST: '/mpc/trf/trf-req-cr/get-trf-zone-inv-list',
  // 出庫依頼作成前チェック
  CHK_TRF_PLAN_ADD: '/mpc/trf/trf-req-cr/chk-trf-plan-add',
  // 出庫依頼作成
  ADD_TRF_PLAN: '/mpc/trf/trf-req-cr/add-trf-plan',
  // 出庫依頼修正
  MOD_TRF_PLAN: '/mpc/trf/trf-req-cr/mod-trf-plan',
  // 出庫依頼明細作成可否チェック
  CHK_TRF_PLAN_BEFORE_ADD: '/mpc/trf/trf-pkg-req/chk-trf-plan-before-add',
  // 現場在庫量取得
  GET_TRF_PLAN_INV_QTY: '/mpc/trf/trf-pkg-req/get-trf-plan-inv-qty',
  // 出庫依頼一覧検索
  GET_TRF_PLAN_GRP_LIST: '/mpc/trf/trf-req-edit/get-trf-plan-grp-list',
  // 出庫指示作成前出庫依頼詳細取得
  GET_TRF_PLAN_DETAIL_BEFORE_INST_ADD:
    '/mpc/trf/trf-req-edit/get-trf-plan-detail-before-inst-add',
  // 出庫依頼詳細取得
  GET_TRF_PLAN_DETAIL: '/mpc/trf/trf-req-edit/get-trf-plan-detail',
  // 出庫依頼取消
  DEL_TRF_PLAN_GRP: '/mpc/trf/trf-req-edit/del-trf-plan-grp',
  // 出庫指示作成
  ADD_TRF_INST_GRP: '/mpc/trf/trf-inst/add-trf-inst-grp',
  // 出庫指示一覧取得
  GET_TRF_INST_LIST: '/mpc/trf/trf-inst/get-trf-inst-list',
  // 出庫実績確定
  ADD_TRF_INST_RSLT: '/mpc/trf/trf-inst/add-trf-inst-rslt',
  // 出庫指示明細情報取得
  GET_TRF_INST_DETAIL: '/mpc/trf/trf-inst/get-trf-inst-detail',
  // 出庫指示明細情報取得_確定用
  GET_TRF_INST_DETAIL_RESULT: '/mpc/trf/trf-inst/get-trf-inst-detail-result',
  // 出庫指示取消
  DEL_TRF_INST: '/mpc/trf/trf-inst/del-trf-inst',
  // 出庫指示書ダウンロード
  GET_TRF_INST_GRP_FILE: '/mpc/trf/trf-inst/get-trf-inst-grp-file',
  // 出庫報告書ダウンロード
  GET_TRF_RSLT_REP_FILE: '/mpc/trf/trf-inst/get-trf-rslt-rep-file',
  // ピッキング中解除
  MOD_TRF_PICKING: '/mpc/trf/trf-inst/mod-trf-picking',
  // 出庫報告書状態取得
  GET_TRF_RSLT_REP_STS: '/mpc/trf/trf-recv/get-trf-rslt-rep-sts',
  // 荷受確認ラベル発行
  ADD_TRF_RECV_PCK_LBL: '/mpc/trf/trf-recv/add-trf-recv-pck-lbl',
  // 出庫実績確定一覧取得
  GET_TRF_RSLT_FIX_LIST: '/mpc/trf/trf-recv/get-trf-rslt-fix-list',
  // 荷受強制完了
  MOD_TRF_RSLT_REP_FORCE_END: '/mpc/trf/trf-recv/mod-trf-rslt-rep-force-end',

  // mpc/tst
  // 試験候補一覧取得
  GET_TST_REQ_CR_LIST: '/mpc/tst/tst-req-cr/get-tst-req-cr-list',
  // 試験依頼作成データ取得
  GET_TST_REQ_CR: '/mpc/tst/tst-req-cr/get-tst-req-cr',
  // 試験依頼作成
  ADD_TST_REQ: '/mpc/tst/tst-req-cr/add-tst-req',
  // 試験依頼一覧取得
  GET_TST_REQ_LIST: '/mpc/tst/tst-req/get-tst-req-list',
  // 試験依頼詳細データ取得
  GET_TST_REQ_DETAIL: '/mpc/tst/tst-req/get-tst-req-detail',
  // 試験中止データ取得
  GET_TST_REQ_DISCON: '/mpc/tst/tst-req/get-tst-req-discon',
  // 試験中止データ取得
  MOD_TST_REQ_DISCON: '/mpc/tst/tst-req/mod-tst-req-discon',

  // tst/tst-qlt-edit
  // 品質状態・期限変更初期表示データ取得
  GET_TST_QLT_EDIT_INIT: '/mpc/tst/tst-qlt-edit/get-tst-qlt-edit-init',
  // 品質管理対象在庫一覧検索
  GET_INV_LIST_QLT_REQ: '/mpc/tst/tst-qlt-edit/get-inv-list-qlt-req',
  // ロット情報取得（品質状態・期限変更用）
  GET_INV_LOT_QLT_REQ: '/mpc/tst/tst-qlt-edit/get-inv-lot-qlt-req',
  // 品質状態・期限変更
  MOD_LOT_STS: '/mpc/tst/tst-qlt-edit/mod-lot-sts',

  // mpc/tst/tst-inv-edit
  // サンプリング実績一覧検索
  GET_SAMPLING_ACHIEVEMENT_LIST:
    '/mpc/tst/tst-inv-edit/get-sampling-achievement-list',
  // サンプリング実績情報取得
  GET_SAMPLING_ACHIEVEMENT: '/mpc/tst/tst-inv-edit/get-sampling-achievement',
  // サンプリング在庫修正
  MOD_INV_SAMPLING: '/mpc/tst/tst-inv-edit/mod-inv-sampling',

  // mpc/sjg
  // 出荷判定結果一覧情報取得
  GET_SJG_RSLT_LIST: '/mpc/sjg/sjg-rel-rslt/get-sjg-rslt-list',
  // 出荷可否決定書のダウンロードを行う
  GET_RELEASE_RSLT_FILE: '/mpc/sjg/sjg-rel-rslt/get-release-rslt-file',
  // 出荷判定候補一覧取得
  GET_SJG_LIST: '/mpc/sjg/sjg-rel/get-sjg-list',
  // 出荷判定可否チェック
  CHK_BEFORE_RELEASE: '/mpc/sjg/sjg-rel/chk-before-release',
  // 出荷判定用チェック
  CHK_VERIFY_RSLT_RELEASE: '/mpc/sjg/sjg-rel/chk-verify-rslt-release',
  // 出荷判定完了
  MOD_RELEASE_FIN: '/mpc/sjg/sjg-rel/mod-release-fin',
  // 照査結果詳細取得
  GET_VERIFY_RESULT: '/mpc/sjg/sjg-verify-mgmt/get-verify-result',
  // 構成品情報一覧取得
  SJG_GET_BOM_LIST: '/mpc/sjg/sjg-verify-odr/get-bom-list',
  // 構成品情報一覧(ラベル単位)取得
  GET_BOM_LBL_LIST: '/mpc/sjg/sjg-verify-odr/get-bom-lbl-list',
  // 構成品情報確認完了
  MOD_BOM_VERIFY_FIN: '/mpc/sjg/sjg-verify-odr/mod-bom-verify-fin',
  // 照査情報有効チェック
  CHK_VALID_VERIFY: '/mpc/sjg/sjg-verify/chk-valid-verify',

  // mpc/sog
  // 出荷予定一覧一覧取得
  GET_SOG_PLAN_LIST: '/mpc/sog/sog-plan/get-sog-plan-list',
  // 出荷予定出荷予定CSV取込
  ADD_SOG_PLAN_CSV_LIST: '/mpc/sog/sog-plan/add-sog-plan-csv-list',
  // 出荷予定削除
  DEL_SOG_PLAN: '/mpc/sog/sog-plan/del-sog-plan',
  // 出荷予定修正データ取得
  GET_SOG_PLAN: '/mpc/sog/sog-plan/get-sog-plan',
  // 出荷予定修正
  MOD_SOG_PLAN: '/mpc/sog/sog-plan/mod-sog-plan',
  // 出荷予定確定取消
  MOD_SOG_PLAN_FIX_CANCEL: '/mpc/sog/sog-plan/mod-sog-plan-fix-cancel',
  // 出荷予定確定
  MOD_SOG_PLAN_FIX: '/mpc/sog/sog-plan/mod-sog-plan-fix',
  // 出荷予定追加
  ADD_SOG_PLAN: '/mpc/sog/sog-plan/add-sog-plan',
  // 製品出庫実績確定
  GET_SOG_TRF_RSLT_FIX_LIST:
    '/mpc/sog/sog-trf-rslt-fix/get-sog-trf-rslt-fix-list',
  // 出庫実績確定
  MOD_SOG_TRF_RSLT_FIX: '/mpc/sog/sog-trf-rslt-fix/mod-sog-trf-rslt-fix',
  // 出荷指示一覧一覧取得
  GET_SOG_INST_LIST: '/mpc/sog/sog-inst/get-sog-slip-list',
  // 出荷指示取消
  DEL_SOG_INST_CR: '/mpc/sog/sog-inst/del-sog-slip',
  // 受領書PDF取得
  GET_SOG_INST_RECEIPT_PRINT: '/mpc/sog/sog-inst/get-sog-receipt-print',
  // 納品書PDF取得
  GET_SOG_INST_SLIP_PRINT: '/mpc/sog/sog-inst/get-sog-delivery-print',
  // トラック明細報告書PDF取得
  GET_SOG_INST_TRUCK_PRINT: '/mpc/sog/sog-inst/get-sog-truck-print',

  // mpc/sog/sog-report
  // 出荷実績表作成一覧取得
  GET_SOG_RLST_FIX_PRINT_LIST:
    '/mpc/sog/sog-report/get-sog-slip-fix-print-list',
  // 出荷報告書ダウンロード
  GET_SOG_RLST_FIX_PRINT: '/mpc/sog/sog-report/get-sog-slip-fix-print',

  // mpc/sog
  // 出荷報告書作成一覧取得
  GET_SOG_RLST_REPORT_PRINT_LIST:
    '/mpc/sog/sog-report/get-sog-slip-fix-report-print-list',
  // 出荷報告書ダウンロード
  GET_SOG_RLST_REPORT_PRINT:
    '/mpc/sog/sog-report/get-sog-slip-fix-report-print',

  // mpc/mst/mbr-create
  // MBR作成.品目・処方一覧
  GET_MST_MBR_MAT_RX_LIST: '/mpc/mst/mbr-create/get-mst-mbr-mat-rx-list',
  // MBR作成.作成
  ADD_MST_MBR: '/mpc/mst/mbr-create/add-mst-mbr',
  // MBR比較マスタ選択.MBRマスタ一覧(MBR作成)
  GET_MST_MBR_MASTER_LIST_CREATE:
    '/mpc/mst/mbr-create/get-mst-mbr-master-list-create',
  // MBRマスタ比較確認.MBRマスタ確認結果一覧(MBR作成)
  GET_CONFIRM_MBR_MASTER_LIST_CREATE:
    '/mpc/mst/mbr-create/get-confirm-mbr-master-list-create',
  // MBR申請.MBR一覧
  GET_MST_MBR_LIST: '/mpc/mst/mbr-application/get-mst-mbr-list',
  // MBR比較マスタ選択.MBRマスタ一覧(MBR申請)
  GET_MST_MBR_MASTER_LIST_APPLICATION:
    '/mpc/mst/mbr-application/get-mst-mbr-master-list-application',
  // MBRマスタ比較確認.MBRマスタ確認結果一覧(MBR申請)
  GET_CONFIRM_MBR_MASTER_LIST_APPLICATION:
    '/mpc/mst/mbr-application/get-confirm-mbr-master-list-application',
  // MBR申請.削除
  DEL_MST_MBR: '/mpc/mst/mbr-application/del-mst-mbr',
  // 整合性チェック結果.整合性チェック結果一覧
  GET_MST_CONSISTENCY_CHK_RESULTS_LIST:
    '/mpc/mst/mbr-application/get-mst-consistency-chk-results-list',
  // 申請コメント入力.作成情報
  GET_MST_CREATION_INFO: '/mpc/mst/mbr-application/get-mst-creation-info',
  // 申請コメント入力.申請
  MOD_MST_MBR_APPLY: '/mpc/mst/mbr-application/mod-mst-mbr-apply',
  // MBR承認.承認待ちMBR一覧
  GET_MST_AWAITING_APPROVAL_MBR_LIST:
    '/mpc/mst/mbr-approval/get-mst-awaiting-approval-mbr-list',
  // MBR比較マスタ選択.MBRマスタ一覧(MBR承認)
  GET_MST_MBR_MASTER_LIST_APPROVE:
    '/mpc/mst/mbr-approval/get-mst-mbr-master-list-approve',
  // MBRマスタ比較確認.MBRマスタ確認結果一覧(MBR承認)
  GET_CONFIRM_MBR_MASTER_LIST_APPROVE:
    '/mpc/mst/mbr-approval/get-confirm-mbr-master-list-approve',
  // MBR申請確認.申請情報
  GET_MST_APPLICATION_INFO: '/mpc/mst/mbr-approval/get-mst-application-info',
  // MBR申請確認.承認
  MOD_MST_MBR_APPROVE: '/mpc/mst/mbr-approval/mod-mst-mbr-approve',
  // MBR申請確認.否認
  MOD_MST_MBR_DENY: '/mpc/mst/mbr-approval/mod-mst-mbr-deny',
  // マスタ一覧.マスタ一覧
  GET_MST_MASTER_LIST: '/mpc/mst/master-approval/get-mst-master-list',
  // マスタ承認.マスタ確認結果一覧
  GET_MST_CONFIRM_MASTER_LIST:
    '/mpc/mst/master-approval/get-mst-confirm-master-list',
  // マスタ承認.承認
  MOD_MST_MASTER_APPROVE: '/mpc/mst/master-approval/mod-mst-master-approve',

  // SOPフロー登録
  ADD_SOP_CHART: '/mpc/sop/sop-chart/add-sop-chart',
  // SOPフロー更新
  MOD_SOP_CHART: '/mpc/sop/sop-chart/mod-sop-chart',
  // SOPフロー取得
  GET_SOP_CHART: '/mpc/sop/sop-chart/get-sop-chart',
  // SOPブロック一覧取得
  GET_ALL_BLOCK: '/mpc/sop/sop-chart/get-all-block',
  // SOPブロック登録
  ADD_SOP_BLOCK: '/mpc/sop/sop-chart/add-sop-block',
  // SOPブロック削除
  DEL_SOP_BLOCK: '/mpc/sop/sop-chart/del-sop-block',
  // SOP判定値情報(手入力)取得
  GET_SOP_BATCH_WGT_VALUE: '/mpc/sop/sop-chart/get-sop-batch-wgt-value',
  // SOP品目単位取得
  GET_SOP_ITEM_UNIT_VALUE: '/mpc/sop/sop-chart/get-sop-item-unit-value',
  // SOP投入順取得
  GET_SOP_INPUT_SEQ_VALUE: '/mpc/sop/sop-chart/get-sop-input-seq-value',
  // SOP処方構成品品目取得
  GET_SOP_RX_BOM_MAT_VALUE: '/mpc/sop/sop-chart/get-sop-rx-bom-mat-value',
  // SOP通信コマンド取得
  GET_SOP_DEVICE_CMD_VALUE: '/mpc/sop/sop-chart/get-sop-device-cmd-value',
  // SOPG-コードメモ一覧取得
  GET_SOP_GCODE_MEMO: '/mpc/sop/sop-chart/get-sop-gcode-memo',
  // SOPG-コードメモ登録
  ADD_SOP_GCODE_MEMO: '/mpc/sop/sop-chart/add-sop-gcode-memo',
  // SOPG-コードメモ編集
  MOD_SOP_GCODE_MEMO: '/mpc/sop/sop-chart/mod-sop-gcode-memo',
  // SOPG-コードメモ削除
  DEL_SOP_GCODE_MEMO: '/mpc/sop/sop-chart/del-sop-gcode-memo',
  // 指図SOP一覧取得
  GET_RX_SOP_LIST: '/mpc/sop/sop-list/get-rx-sop-flow-list',
  // 指図SOP取得
  GET_RX_SOP_LIST_BY_NO: '/mpc/sop/sop-list/get-rx-sop-flow-by-no',
  // 指図SOP登録
  ADD_SOP_RX_FLOW: '/mpc/sop/sop-list/add-rx-sop-flow',
  // 指図SOP更新
  MOD_SOP_RX_FLOW: '/mpc/sop/sop-list/mod-rx-sop-flow',
  // 指図SOP登録(コピー追加)
  ADD_COPY_SOP_RX_FLOW: '/mpc/sop/sop-list/add-copy-rx-sop-flow',
  // 指図SOP削除
  DEL_SOP_RX_FLOW: '/mpc/sop/sop-list/del-rx-sop-flow',
  // 工程任意SOP一覧取得
  GET_PRC_SOP_LIST: '/mpc/sop/sop-prc-list/get-prc-sop-flow-list',
  // 工程任意SOP取得
  GET_PRC_SOP_LIST_BY_NO: '/mpc/sop/sop-prc-list/get-prc-sop-flow-by-no',
  // 工程任意SOP登録
  ADD_PRC_SOP_FLOW: '/mpc/sop/sop-prc-list/add-prc-sop-flow',
  // 工程任意SOP更新
  MOD_PRC_SOP_FLOW: '/mpc/sop/sop-prc-list/mod-prc-sop-flow',
  // 工程任意SOP登録(コピー追加)
  ADD_COPY_PRC_SOP_FLOW: '/mpc/sop/sop-prc-list/add-copy-prc-sop-flow',
  // 工程任意SOP削除
  DEL_PRC_SOP_FLOW: '/mpc/sop/sop-prc-list/del-prc-sop-flow',
  // 秤量前後SOP一覧取得
  GET_SOP_WGT_LIST: '/mpc/sop/sop-wgt-list/get-wgt-sop-flow-list',
  // 秤量前後SOP登録
  ADD_SOP_WGT_FLOW: '/mpc/sop/sop-wgt-list/add-wgt-sop-flow',
  // 工程任意SOP更新
  MOD_SOP_WGT_FLOW: '/mpc/sop/sop-wgt-list/mod-wgt-sop-flow',
  // 秤量前後SOP登録(コピー追加)
  ADD_COPY_SOP_WGT_FLOW: '/mpc/sop/sop-wgt-list/add-copy-wgt-sop-flow',
  // 秤量前後SOP削除
  DEL_SOP_WGT_FLOW: '/mpc/sop/sop-wgt-list/del-wgt-sop-flow',
  // 秤量前後SOP取得
  GET_SOP_WGT_LIST_BY_NO: '/mpc/sop/sop-wgt-list/get-wgt-sop-flow-by-no',
  // 式入力チェック
  CHK_SOP_FORMULA: '/mpc/sop/sop-chart/chk-sop-formula',
  // SOPマスタ排他制御管理テーブル取得
  GET_SYS_EX_SOP: '/mpc/sop/sop-chart/get-sys-ex-sop',
  // SOPマスタ排他制御管理テーブル登録
  ADD_SYS_EX_SOP: '/mpc/sop/sop-chart/add-sys-ex-sop',
  // SOPマスタ排他制御管理テーブル削除
  DEL_SYS_EX_SOP: '/mpc/sop/sop-chart/del-sys-ex-sop',

  // mpc/sog
  // 製品出庫指示書データ取得
  GET_SOG_RSLT_FIX: '/mpc/sog/sog-inst-cr/get-sog-inst-rslt-fix',
  // 出荷指示確定データ取得(予定出荷指示登録)
  GET_SOG_SLIP: '/mpc/sog/sog-inst-cr/get-sog-rslt-fix-slip',
  // 出荷指示確定データ取得(組替出荷指示登録)
  GET_SOG_SLIP_LIST: '/mpc/sog/sog-inst-cr/get-sog-rslt-fix-slip-list',
  // 出荷指示確定
  ADD_SOG_SLIP: '/mpc/sog/sog-inst-cr/add-sog-slip',

  // mpc/sjg
  // 照査一覧取得
  GET_VERIFY_LIST: '/mpc/sjg/sjg-verify/get-verify-list',
  // ロット照査情報作成
  ADD_LOT_VERIFY: '/mpc/sjg/sjg-verify/add-lot-verify',
  // 照査項目情報取得
  GET_VERIFY_ITEM: '/mpc/sjg/sjg-verify-mgmt/get-verify-item',
  // 照査開始(製造記録照査開始)
  MOD_MANUFACTURING_REC_VERIFY_START:
    '/mpc/sjg/sjg-verify-mgmt/mod-manufacturing-rec-verify-start',
  // 製造記録照査取消
  DEL_ODR_VERIFY: '/mpc/sjg/sjg-verify-mgmt/del-odr-verify',
  // 品質記録照査確認開始
  MOD_QUALITY_REC_VERIFY_START:
    '/mpc/sjg/sjg-verify-mgmt/mod-quality-rec-verify-start',
  // GMP確認開始
  MOD_GMP_VERIFY_START: '/mpc/sjg/sjg-verify-mgmt/mod-gmp-verify-start',
  // 照査完了全チェック
  CHK_FIN_ALL: '/mpc/sjg/sjg-verify-mgmt/chk-fin-all',
  // 照査取消可否チェック
  CHK_VERIFY_CANCEL: '/mpc/sjg/sjg-verify-mgmt/chk-verify-cancel',
  // 照査取消
  DEL_ALL_VERIFY: '/mpc/sjg/sjg-verify-mgmt/del-all-verify',
  // GMP確認情報取得
  GET_GMP: '/mpc/sjg/sjg-verify-gmp/get-gmp',
  // GMP確認完了
  MOD_GMP_FIN: '/mpc/sjg/sjg-verify-gmp/mod-gmp-fin',
  // GMP確認中断
  MOD_GMP_STOP: '/mpc/sjg/sjg-verify-gmp/mod-gmp-stop',

  // mpc/sys/sys-label-trace
  // ラベルトレース.一覧取得
  GET_LBL_TRC_LIST: '/mpc/sys/sys-label-trace/get-lbl-trace-list',

  // mpc/sys/sys-reprint-label
  // ラベル再発行.一覧取得
  GET_RPT_LBL_LIST: '/mpc/sys/sys-reprint-label/get-lbl-info-list',
  // ラベル再発行.ラベル印刷
  GET_RPT_LBL_PRT: '/mpc/sys/sys-reprint-label/get-lbl-info-print',
  // 照査未実施チェック
  CHK_INITIAL: '/mpc/sjg/sjg-verify-fix/chk-initial',
  // 照査完了確認の完了
  MOD_VERIFY_FIN: '/mpc/sjg/sjg-verify-fix/mod-verify-fin',
  // 品質記録照査情報取得
  GET_QUALITY_REC: '/mpc/sjg/sjg-verify-qlt/get-quality-rec',
  // 照査可否チェック
  CHK_VERIFY_STS: '/mpc/sjg/sjg-verify-odr/chk-verify-sts',
  // 品質記録照査確認完了
  MOD_QUALITY_REC_VERIFY_FIN:
    '/mpc/sjg/sjg-verify-qlt/mod-quality-rec-verify-fin',
  // 製造記録照査完了
  MOD_MANUFACTURING_REC_VERIFY_FIN:
    '/mpc/sjg/sjg-verify-odr/mod-manufacturing-rec-verify-fin',
  // 製造指図記録一覧取得
  GET_ODR_REC_LIST: '/mpc/sjg/sjg-verify-odr/get-odr-rec-list',
  // 製造指図記録確認完了
  MOD_ODR_REC_VERIFY_FIN: '/mpc/sjg/sjg-verify-odr/mod-odr-rec-verify-fin',
  // 製造記録照査項目情報取得
  GET_MANUFACTURING_REC_ITEM:
    '/mpc/sjg/sjg-verify-odr/get-manufacturing-rec-item',
  // ロットトレース情報一覧取得
  GET_LOT_TRACE_LIST: '/mpc/sys/sys-lot-trace/get-lot-trace-list',
  // ロットトレース情報取得
  GET_LOT_TRACE: '/mpc/sys/sys-lot-trace/get-lot-trace',
  // SOP記録一覧取得
  GET_SOP_REC_LIST: '/mpc/sjg/sjg-verify-odr/get-sop-rec-list',
  // SOP記録確認完了
  MOD_SOP_REC_VERIFY_FIN: '/mpc/sjg/sjg-verify-odr/mod-sop-rec-verify-fin',
} as const;

export default END_POINT;
