import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { GmpDocNeedTypeNmListData } from '@/types/HookUseApi/SjgTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;
export const getSjgGMPConfirmationAddAndEditFormItems: (
  gmpDocNeedTypeNmList?: GmpDocNeedTypeNmListData[],
) => CustomFormType['formItems'] = (
  gmpDocNeedTypeNmList: GmpDocNeedTypeNmListData[] = [],
) => {
  const optionsData = {
    label: gmpDocNeedTypeNmList.map((item) => item.cdNm),
    value: gmpDocNeedTypeNmList.map((item) => item.cdVal),
  };
  return {
    gmpMngNo: {
      label: { text: t('Sjg.Chr.txtGMPMngNo') },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      formModelValue: '',
      rules: [rules.required('textBox'), rules.length(20)],
      formRole: 'textBox',
    },
    gmpTitle: {
      label: { text: t('Sjg.Chr.txtGMPTitle') },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      formModelValue: '',
      rules: [rules.required('textBox'), rules.length(50)],
      formRole: 'textBox',
    },
    gmpDocNeedTypeVerify: {
      label: { text: t('Sjg.Chr.txtVerifyGmpDocNeedType') },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      formModelValue: '',
      rules: [rules.required('radio')],
      props: {
        isHorizontal: true,
        size: 'small',
        modelValue: '',
        optionsData,
      },
      formRole: 'radio',
    },
    gmpDes: {
      label: { text: t('Sjg.Chr.txtGMPDes') },
      formModelValue: '',
      formRole: 'textBox',
      rules: [rules.length(200)],
    },
  };
};

export const sjgGMPConfirmationAddAndEditFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getSjgGMPConfirmationAddAndEditFormItems());
