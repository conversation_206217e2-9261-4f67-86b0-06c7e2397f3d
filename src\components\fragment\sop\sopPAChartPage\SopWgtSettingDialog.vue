<template>
  <!-- SOP全体設定ダイアログ -->
  <DialogWindow
    :title="$t('SOP.Chr.txtSopSettingTitle')"
    :dialogVisible="props.dialogVisible"
    :onReject="() => handleMessageBoxClose()"
    :onResolve="async () => resolveClickHandler()"
    :closeOnClickModal="false"
  >
    <CustomForm
      :formModel="sopSettingDialogFormRef.formModel"
      :formItems="sopSettingDialogFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          sopSettingDialogFormRef.customForm = v;
        }
      "
    />
    <MessageBox
      v-show="dialogVisibleRef.singleButtonRef"
      :dialog-props="messageBoxSingleButtonRef"
      :cancelCallback="() => handleMessageBoxClose()"
      :submitCallback="() => handleMessageBoxClose()"
    />
  </DialogWindow>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { useUpdateWgtSopFlow } from '@/hooks/useApi';
import CreateMessageBox from '@/components/parts/MessageBox/CreateMessageBox';
import { UpdateWgtSopFlow } from '@/types/HookUseApi/SopTypes';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { WgtSopSettingOption } from '@/types/SopDialogInterface';
import CONST from '@/constants/utils';
import { toNumberOrNull } from '@/utils';
import {
  sopWgtSettingDialogFormItems,
  sopWgtSettingDialogFormModel,
} from './sopWgtSettingDialog';

type Props = {
  dialogVisible: boolean;
  commonRequest: CommonRequestType;
  sopSetting: WgtSopSettingOption;
};
type DialogRefKey = 'singleButtonRef';

/**
 * 多言語
 */
const props = defineProps<Props>();
const { t } = useI18n();

const sopSettingDialogFormRef = ref<CustomFormType>({
  formItems: sopWgtSettingDialogFormItems,
  formModel: sopWgtSettingDialogFormModel,
});
const initialState: InitialDialogState<DialogRefKey> = {
  singleButtonRef: false,
};
const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
const emit = defineEmits(['update:dialogVisible', 'closeDialog']);
const handleMessageBoxClose = () => {
  closeDialog('singleButtonRef');
  emit('update:dialogVisible', false);
  emit('closeDialog');
};
const checkValidate = () => {
  const validate =
    props.sopSetting.sopFlowNmJp !== '' &&
    props.sopSetting.forcePrivGrpCd !== '';
  return validate;
};
/**
 * 実行ボタンクリックイベント
 */
const resolveClickHandler = async () => {
  const validate =
    sopSettingDialogFormRef.value.customForm !== undefined &&
    (await sopSettingDialogFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (!validate) {
    return false;
  }
  const pdfExtensions: string[] = ['pdf'];
  if (sopSettingDialogFormRef.value.formModel.helpBinPath !== '') {
    const helpPathFlg = pdfExtensions.some((extension: string) => {
      if (
        sopSettingDialogFormRef.value.formModel.helpBinPath
          .toString()
          .endsWith(`.${extension}`)
      ) {
        return true;
      }
      return false;
    });
    if (!helpPathFlg) {
      CreateMessageBox({
        dialogProps: {
          title: t('Cm.Chr.txtValidationError'),
          content: t('SOP.Msg.filePathSOPSettingValidationError'),
          type: 'error',
          isSingleBtn: true,
        },
      });
      return false;
    }
  }
  // 秤量前後SOPフローマスタ情報を更新する
  const apiRequestData: UpdateWgtSopFlow = {
    sopFlowNo: <string>sopSettingDialogFormRef.value.formModel.sopFlowNo,
    sopFlowNmJp: <string>sopSettingDialogFormRef.value.formModel.sopFlowNmJp,
    wgtSopCat: <string>sopSettingDialogFormRef.value.formModel.wgtSopCat,
    helpBinPath: <string>sopSettingDialogFormRef.value.formModel.helpBinPath,
    forcePrivGrpCd: <string>(
      sopSettingDialogFormRef.value.formModel.forcePrivGrpCd
    ),
    skipPrivGrpCd: <string>(
      sopSettingDialogFormRef.value.formModel.skipPrivGrpCd
    ),
    dspSeq: toNumberOrNull(sopSettingDialogFormRef.value.formModel.dspSeq),
    updDts: props.sopSetting.updDts,
  };
  const { responseRef, errorRef } = await useUpdateWgtSopFlow({
    ...props.commonRequest,
    btnId: 'btnDecision',
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    openDialog('singleButtonRef');
    messageBoxSingleButtonRef.value.type = 'error';
    return false;
  }
  if (responseRef.value?.data.rCode === CONST.API_STATUS_CODE.SUCCESS) {
    messageBoxSingleButtonRef.value.title = responseRef.value.data.rTitle;
    messageBoxSingleButtonRef.value.content = responseRef.value.data.rMsg;
    messageBoxSingleButtonRef.value.type = 'info';
    openDialog('singleButtonRef');
    return false;
  }
  return true;
};
defineExpose({ checkValidate });
</script>
<style lang="scss" scoped>
$namespace: 'order-addition-dialog';

.#{$namespace} {
  background-color: $white750;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
</style>
