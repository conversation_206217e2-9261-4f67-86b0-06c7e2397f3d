<template>
  <!-- 工程作業記録_製造記録修正ダイアログ -->
  <DialogWindow
    :title="$t('Prd.Chr.txtRecordModify')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 工程作業記録_製造記録修正情報 テキスト項目表示 -->
    <InfoShow
      :infoShowItems="detailInfoInfoShowRef.infoShowItems"
      :isLabelVertical="detailInfoInfoShowRef.isLabelVertical"
    />
    <!-- 工程作業記録_製造記録修正用コントロール -->
    <CustomForm
      class="Util_mt-16"
      :formModel="dialogFormRef.formModel"
      :formItems="dialogFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          dialogFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 工程作業記録_製造記録修正記録値判定のワーニング表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxDeviationCheckVisible"
    :dialogProps="messageBoxDeviationCheckPropsRef"
    :cancelCallback="() => closeDialog('messageBoxDeviationCheckVisible')"
    :submitCallback="
      () => {
        closeDialog('messageBoxDeviationCheckVisible');
        // チェックOKなら処理継続する。
        // 工程作業記録_製造記録修正確定API呼び出し
        requestApiModifyRecordInfo();
      }
    "
  />
  <!-- 工程作業記録_製造記録修正実行前の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPrcModifyVisible"
    :dialogProps="messageBoxPrcModifyPropsRef"
    :cancelCallback="() => closeDialog('messageBoxPrcModifyVisible')"
    :submitCallback="requestApiCheckRecordInfo"
  />
  <!-- 工程作業記録_製造記録修正実行の完了表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPrcModifyFinishedVisible"
    :dialogProps="messageBoxPrcModifyFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxPrcModifyFinished"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import CONST from '@/constants/utils';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import createMessageBoxForm from '@/utils/commentMessageBox';
import {
  useGetComboBoxDataStandard,
  useGetRecordInit,
  useModifyRecordInfo,
  useCheckRecordInfo,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  GetSopFlowInitData,
  GetRecordInitData,
  GetRecordInitRequestData,
  ModifyRecordInfoRequestData,
  CheckRecordInfoRequestData,
  COMBO_BOX_WHERE,
} from '@/types/HookUseApi/PrdTypes';
import {
  ComboBoxDataStandardReturnData,
  CommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import {
  getDialogFormItems,
  dialogFormModel,
  getInfoShowItems,
} from './prdPrcModify';

/**
 * 多言語
 */
const { t } = useI18n();

// 製造記録修正初期表示のレスポンス リスト部分
let initResponseData: GetRecordInitData = {
  cmtMain1: '', // 作業指示内容1
  cmtMain2: '', // 作業指示内容2
  cmtMain3: '', // 作業指示内容3
  instVal: '', // 指示値
  recVal1: '', // 記録値1
  recVal2: '', // 記録値2
  recVal3: '', // 記録値3
  recVal4: '', // 記録値4
  recVal5: '', // 記録値5
  recVal1Flg: '', // 記録値1フラグ
  recVal2Flg: '', // 記録値2フラグ
  recVal3Flg: '', // 記録値3フラグ
  recVal4Flg: '', // 記録値4フラグ
  recVal5Flg: '', // 記録値5フラグ
  updDts: '', // 更新日時
};

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxPrcModifyVisible'
  | 'messageBoxDeviationCheckVisible'
  | 'messageBoxPrcModifyFinishedVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxPrcModifyVisible: false,
  messageBoxDeviationCheckVisible: false,
  messageBoxPrcModifyFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const detailInfoInfoShowRef = ref<InfoShowType>({
  infoShowItems: getInfoShowItems(),
  isLabelVertical: true,
});

// コメントあり警告用フォーム設定
const messageBoxForm = createMessageBoxForm('message', 'cmtWarning'); // 第二引数が標準コンボボックス取得のキー名
// 工程作業記録_製造記録修正値判定 ワーニングのメッセージボックス
const messageBoxDeviationCheckPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 工程作業記録_製造記録修正実行の確認メッセージボックス
const messageBoxPrcModifyPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleProductionRecordEdit'),
  content: t('Prd.Msg.contentProductionRecordEdit'),
  type: 'question',
});

// 工程作業記録_製造記録修正実行の完了メッセージボックス
const messageBoxPrcModifyFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

// 入力付き確認メッセージボックスの入力を初期化
const clearInputMessageBoxForm = () => {
  if ('isPrompt' in messageBoxDeviationCheckPropsRef.value) {
    messageBoxDeviationCheckPropsRef.value.formItems.message.formModelValue =
      '';
  }
};

// 共通APIリクエストに渡すための、直前MessageBox表示テキストの保存情報
type MessageBoxInfo = {
  titleText: string;
  messageText: string;
  buttonText: string;
};

let messageBoxInfo: MessageBoxInfo = {
  titleText: '',
  messageText: '',
  buttonText: '',
};

// NOTE:前チェックAPIでワーニングの有無によって直前メッセージが変わるため、それを保存するための機構
// 直前MessageBox表示テキストの保存
const cacheMessageBoxInfo = (v: DialogProps) => {
  messageBoxInfo = {
    titleText: v.title,
    messageText: v.content,
    buttonText: t('Cm.Chr.btnOk'),
  };
};

const dialogFormRef = ref<CustomFormType>({
  formItems: getDialogFormItems(),
  formModel: dialogFormModel,
});

type Props = {
  selectedRowData: GetSopFlowInitData | null;
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;
const emit = defineEmits(['submit']);

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // 下記チェックを行い、チェックOKなら処理継続する。
  // 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    dialogFormRef.value.customForm !== undefined &&
    (await dialogFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return false;
  }

  // 以下のメッセージを表示
  // 工程作業記録_製造記録修正実行前の確認
  openDialog('messageBoxPrcModifyVisible');
  return false;
};

// 工程作業記録_製造記録修正実行完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxPrcModifyFinished = () => {
  // 再検索用に親に実行を通知
  emit('submit', props.privilegesBtnRequestData);

  // ダイアログウィンドウを閉じる
  closeDialog('messageBoxPrcModifyFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

// 型ガード関数を定義
// NOTE:テンプレートリテラルで書くとstring型と判定されて型推論できないためisを使用した型ガード関数を用意
function isKeyOfGetRecordInitData(key: string): key is keyof GetRecordInitData {
  return key in initResponseData;
}

/**
 * 工程作業記録_製造記録修正ダイアログの初期設定
 */
const prdPrcModifyInit = async () => {
  if (
    props.selectedRowData === null ||
    props.selectedRowData.sopFlowLnum === null ||
    props.selectedRowData.sopNodeLnum === null
  )
    return;
  showLoading();
  updateDialogChangeFlagRef(false);

  // 工程作業記録_製造記録修正初期表示のAPIを行う。
  const requestData: GetRecordInitRequestData = {
    sopFlowNo: props.selectedRowData.sopFlowNo,
    sopFlowLnum: props.selectedRowData.sopFlowLnum,
    sopNodeLnum: props.selectedRowData.sopNodeLnum,
  };

  // 工程作業記録_製造記録修正初期表示API実行
  const { responseRef, errorRef } = await useGetRecordInit({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // ダイアログ表示初期値として、レスポンス情報を格納
    initResponseData = responseRef.value.data.rData;

    // 工程作業記録_製造記録修正詳細情報レイアウト用初期値設定
    Object.entries(initResponseData).forEach(([key, value]) => {
      if (key in detailInfoInfoShowRef.value.infoShowItems) {
        detailInfoInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });
    // FormItems初期化
    dialogFormRef.value.formItems = getDialogFormItems();

    // 製造記録修正情報レイアウト用初期値設定
    setFormModelValueFromApiResponse(
      dialogFormRef,
      responseRef.value.data.rData,
    );

    // recValflag1~5 の値に基づいて disabled プロパティを更新
    for (let i = 1; i <= 5; i++) {
      const flagKey = `recVal${i}Flg`;
      const recKey = `recVal${i}`;
      if (
        isKeyOfGetRecordInitData(flagKey) &&
        recKey in dialogFormRef.value.formItems
      ) {
        const flagValue = initResponseData[flagKey];
        if (dialogFormRef.value.formItems[recKey].formRole === 'textBox') {
          if (flagValue === '1') {
            dialogFormRef.value.formItems[recKey].tags = []; // タグ削除
            dialogFormRef.value.formItems[recKey].props = { disabled: true }; // 非活性化
            dialogFormRef.value.formItems[recKey].rules = []; // ルール削除
          }
        }
      }
    }
  }

  // 標準コンボボックスデータ取得
  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'recModExpl', // 修正コメント
        condKey: 'm_sys_cmt',
        where: { cmt_cat: COMBO_BOX_WHERE.MODIFY.PRD_PRC_SOP_MOD },
      },
      {
        cmbId: 'cmtWarning',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: COMBO_BOX_WHERE.DEVIANT.COMMON },
      },
    ],
  });

  // 標準コンボボックスのデータが取得できていれば画面上に反映
  if (
    comboBoxDataStandardReturnData &&
    comboBoxDataStandardReturnData.rData.rList.length > 0
  ) {
    setCustomFormComboBoxOptionList(
      dialogFormRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }

  if (
    'isPrompt' in messageBoxDeviationCheckPropsRef.value &&
    comboBoxDataStandardReturnData
  ) {
    const resetData = createMessageBoxForm('message', 'cmtWarning');
    messageBoxDeviationCheckPropsRef.value.formItems = resetData.formItems;

    // NOTE:共通コンボボックス情報を元にコメント付き警告メッセージを設定
    setCustomFormComboBoxOptionList(
      messageBoxDeviationCheckPropsRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
  closeLoading();
};

// 工程作業記録_製造記録修正修正確定のAPIリクエスト処理
const requestApiModifyRecordInfo = async () => {
  closeDialog('messageBoxPrcModifyVisible');

  if (
    props.selectedRowData === null ||
    props.selectedRowData.sopFlowLnum === null ||
    props.selectedRowData.sopNodeLnum === null
  )
    return;

  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    // 署名ダイアログがキャンセルされた場合の処理
    clearInputMessageBoxForm(); // 入力フォームを初期化
    return;
  }

  showLoading();
  // 工程作業記録_製造記録修正確定APIを実行する。
  const requestData: ModifyRecordInfoRequestData = {
    prcNo: props.selectedRowData.prcNo,
    sopFlowNo: props.selectedRowData.sopFlowNo,
    sopFlowLnum: props.selectedRowData.sopFlowLnum,
    sopNodeNo: props.selectedRowData.sopNodeNo,
    sopNodeLnum: props.selectedRowData.sopNodeLnum,
    // 修正後のテキストボックスの内容を入力
    recModExpl:
      dialogFormRef.value.formItems.recModExpl.formModelValue.toString(),
    recVal1: dialogFormRef.value.formItems.recVal1.formModelValue.toString(),
    recVal2: dialogFormRef.value.formItems.recVal2.formModelValue.toString(),
    recVal3: dialogFormRef.value.formItems.recVal3.formModelValue.toString(),
    recVal4: dialogFormRef.value.formItems.recVal4.formModelValue.toString(),
    recVal5: dialogFormRef.value.formItems.recVal5.formModelValue.toString(),
    beforeRecVal1: initResponseData.recVal1,
    beforeRecVal2: initResponseData.recVal2,
    beforeRecVal3: initResponseData.recVal3,
    beforeRecVal4: initResponseData.recVal4,
    beforeRecVal5: initResponseData.recVal5,
    updDts: initResponseData.updDts,
    recDevExpl:
      'isPrompt' in messageBoxDeviationCheckPropsRef.value
        ? messageBoxDeviationCheckPropsRef.value.formItems.message.formModelValue.toString()
        : '',
  };

  // 工程作業記録_製造記録修正確定API実行
  const { responseRef, errorRef } = await useModifyRecordInfo({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxInfo.titleText,
    msgboxMsgTxt: messageBoxInfo.messageText,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    msgboxInputCmt:
      'isPrompt' in messageBoxDeviationCheckPropsRef.value
        ? messageBoxDeviationCheckPropsRef.value.formItems.message.formModelValue.toString()
        : '',
    ...requestData,
  });

  if (errorRef.value) {
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // ・データベースを更新した後、以下のメッセージを表示する。
    // 工程作業記録_製造記録修正確定完了
    messageBoxPrcModifyFinishedPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxPrcModifyFinishedPropsRef.value.content =
      responseRef.value.data.rMsg;

    openDialog('messageBoxPrcModifyFinishedVisible');
    closeLoading();
  }
};

// 製造記録修正の確認メッセージ'OK'押下時処理
// 工程作業記録_製造記録修正値判定のAPIリクエスト処理
const requestApiCheckRecordInfo = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxPrcModifyVisible');

  cacheMessageBoxInfo(messageBoxPrcModifyPropsRef.value);

  if (
    props.selectedRowData === null ||
    props.selectedRowData.sopFlowLnum === null ||
    props.selectedRowData.sopNodeLnum === null
  )
    return;

  showLoading();

  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 下記チェックを行い、チェックOKなら処理継続する。

  // 工程作業記録_製造記録修正値判定のAPIを行う。
  const requestData: CheckRecordInfoRequestData = {
    sopFlowNo: props.selectedRowData.sopFlowNo,
    sopFlowLnum: props.selectedRowData.sopFlowLnum,
    sopNodeLnum: props.selectedRowData.sopNodeLnum,
    recVal1: dialogFormRef.value.formItems.recVal1.formModelValue.toString(), // NOTE: 修正後記録値1のみを判定対象とする
  };

  // 工程作業記録_製造記録修正値判定API実行
  const { responseRef, errorRef } = await useCheckRecordInfo({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxPrcModifyPropsRef.value.title,
    msgboxMsgTxt: messageBoxPrcModifyPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    ...requestData,
  });

  if (errorRef.value) {
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      clearInputMessageBoxForm(); // 入力フォームを初期化
      // 工程作業記録_製造記録修正記録値判定 ワーニング用メッセージボックス起動
      messageBoxDeviationCheckPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxDeviationCheckPropsRef.value.content =
        errorRef.value.response.rMsg;
      openDialog('messageBoxDeviationCheckVisible');

      // ワーニングメッセージを保存
      cacheMessageBoxInfo(messageBoxDeviationCheckPropsRef.value);

      closeLoading();
      return; // ワーニングの場合、ワーニング用messageBox側で処理継続させる。
    }
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return; // レスポンスがない場合継続処理させない
  }

  requestApiModifyRecordInfo();
  closeLoading();
};

// ダイアログ起動条件の監視。isClickedの真偽値が切り替わりを監視
watch(() => props.isClicked, prdPrcModifyInit);
</script>
