import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';

const { t } = i18n.global;

// 入荷予定詳細ダイアログのアイテム定義
export const getAogPlanDetailsFormItems: () => CustomFormType['formItems'] =
  () => ({
    erpUnitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    unitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    planYmd: {
      label: { text: t('Aog.Chr.txtAogDate') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    aogPlanNo: {
      label: { text: t('Aog.Chr.txtAogNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    poDtlNo: {
      label: { text: t('Aog.Chr.txtPurchaseDetailOrderNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    matNo: {
      label: { text: t('Aog.Chr.txtItemCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    matNm: {
      label: { text: t('Aog.Chr.txtItemName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    mBpId: {
      label: { text: t('Aog.Chr.txtBusinessPartnerCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bpNm: {
      label: { text: t('Aog.Chr.txtBusinessPartnerName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    makerLotNo: {
      label: { text: t('Aog.Chr.txtBusinessPartnerLotNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    edNo: {
      label: { text: t('Aog.Chr.txtEditionNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    erpAogQty: {
      label: { text: t('Aog.Chr.txtErpAogAmount') },
      formModelValue: '',
      suffix: { formModelProp: 'erpUnitNm' },
      formRole: 'textBox',
      props: { disabled: true },
    },
    aogQty: {
      label: { text: t('Aog.Chr.txtAogQuantity') },
      formModelValue: '',
      suffix: { formModelProp: 'unitNm' },
      formRole: 'textBox',
      props: { disabled: true },
    },
    poApNo: {
      label: { text: t('Aog.Chr.txtPurchaseOrderAvailableToPromiseNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    poDtlExpl: {
      label: { text: t('Aog.Chr.txtPurchaseDetailOrderExpl') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    poApExpl: {
      label: { text: t('Aog.Chr.txtPurchaseOrderAvailableToPromiseExpl') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    aogAttBinList: {
      formModelValue: [],
      formRole: 'fileUpload',
      onClickHandler() {},
      props: {
        fileList: [],
        hideTriggerBtn: true,
      },
      label: { text: t('Aog.Chr.txtAttachments') },
    },
    planExpl: {
      label: { text: t('Aog.Chr.txtAogExpl') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
  });

// 入荷予定詳細ダイアログのモデル定義
export const aogArrivalPlanDetailsFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getAogPlanDetailsFormItems());
