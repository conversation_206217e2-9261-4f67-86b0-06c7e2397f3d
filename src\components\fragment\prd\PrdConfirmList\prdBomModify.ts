import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// 標準コンボボックスとカスタムフォームを紐づけるID
export const COMBINE_ID = {
  MOD_EXPL: 'prdBomModExpl',
} as const;

// 投入記録修正ダイアログのアイテム定義
export const getPrdBomModifyFormItems: () => CustomFormType['formItems'] =
  () => ({
    // 品目コード
    bomMatNo: {
      label: { text: t('Prd.Chr.txtMaterialCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 投入品名
    dspNmJp: {
      label: { text: t('Prd.Chr.txtBillOfMaterialsName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 単位
    unitNmJp: {
      label: { text: t('Prd.Chr.txtUnit') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 生産バッチ番号
    batchNo: {
      label: { text: t('Prd.Chr.txtProductBatchNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 管理番号
    lotNo: {
      label: { text: t('Prd.Chr.txtManageNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 投入番号
    bomMatSeq: {
      label: { text: t('Prd.Chr.txtBillOfOrder') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 投入ラベル
    lblSid: {
      label: { text: t('Prd.Chr.txtBillOfLabel') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 指図数量
    bomPlanQty: {
      label: { text: t('Prd.Chr.txtOrderQuantity') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 投入実績量
    rsltQty: {
      label: { text: t('Prd.Chr.txtBillOfMaterialsQuantity') },
      formModelValue: '',
      formRole: 'textBox',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.nonNegativeRealNumericOnly(),
      ],
      span: 12,
    },
    // 廃棄量
    disQty: {
      label: { text: t('Prd.Chr.txtDiscardQuantity') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 廃棄量内訳1
    disDtlQty1: {
      label: { text: t('Prd.Chr.txtDiscardQuantityItem1') },
      formModelValue: '',
      formRole: 'textBox',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.nonNegativeRealNumericOnly(),
      ],
      span: 24,
    },
    // 廃棄量内訳2
    disDtlQty2: {
      label: { text: t('Prd.Chr.txtDiscardQuantityItem2') },
      formModelValue: '',
      formRole: 'textBox',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.nonNegativeRealNumericOnly(),
      ],
      span: 24,
    },
    // 廃棄量内訳3
    disDtlQty3: {
      label: { text: t('Prd.Chr.txtDiscardQuantityItem3') },
      formModelValue: '',
      formRole: 'textBox',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.nonNegativeRealNumericOnly(),
      ],
      span: 24,
    },
    // 修正コメント入力
    modExpl: {
      formModelValue: '',
      label: { text: t('Prd.Chr.txtModifyCommentInput') },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('textBox'), rules.length(64)],
      formRole: 'textComboBox',
      selectOptions: [],
      cmbId: COMBINE_ID.MOD_EXPL,
      span: 24,
    },
  });

// 投入記録修正ダイアログのモデル定義
export const prdBomModifyFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getPrdBomModifyFormItems());
