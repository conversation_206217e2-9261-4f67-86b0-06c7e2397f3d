<template>
  <!-- 秤量前後SOP作業詳細ダイアログ -->
  <DialogWindow
    :title="$t('Wgt.Chr.txtWeightSopFlowInfo')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- SOPフロー情報 テキスト項目表示 -->
    <div class="weighing-sop-detail_info-show-container">
      <InfoShow
        :infoShowItems="detailInfoInfoShowRef.infoShowItems"
        :isLabelVertical="detailInfoInfoShowRef.isLabelVertical"
      />
    </div>
    <!-- 画面中央右側 ボタンエリア群 -->
    <div class="weighing-sop-detail_button-area Util_mt-8">
      <!-- 修正履歴ボタン -->
      <ButtonEx
        type="secondary"
        size="normal"
        :text="t('Wgt.Chr.btnSopRecordEditHistory')"
        :iconName="sopRecordEditHistoryButtonIconRef"
        @click="clickSopRecModHistoryBtn()"
      />
      <!-- 異状履歴ボタン -->
      <ButtonEx
        type="secondary"
        size="normal"
        :text="t('Wgt.Chr.btnSopRecordDeviationHistory')"
        :iconName="sopRecordDeviationHistoryButtonIconRef"
        @click="clickSopRecDevHistoryBtn()"
      />
    </div>
    <!-- 見出し SOPフロー一覧 -->
    <BaseHeading
      class="Util_mt-16"
      level="2"
      :text="$t('Wgt.Chr.txtWorkOperationRecord')"
      fontSize="24px"
    />
    <!-- SOPフロー一覧リストテーブル -->
    <TabulatorTable
      :propsData="tablePropsDataWeighingSOPConfirmInitListRef"
      :routerName="props.routerName"
      @selectRows="updateSelectedRows"
      @displayedAllRows="displayedAllRowsRef"
      @clickBtnColumn="updateEmitSelectedRow"
    />
    <!-- 画面下部エリア -->
    <div>
      <!-- 異状確認補足コメント(2行) -->
      <div class="weighing-sop-detail_cautionary-item">
        <div class="weighing-sop-detail_cautionary">
          <span>{{ $t('Wgt.Chr.txtRecordGuide') }}</span>
        </div>
        <div class="weighing-sop-detail_cautionary-space"></div>
        <div class="weighing-sop-detail_cautionary">
          <span>{{ $t('Wgt.Chr.contentDifferentConfirmComent') }}</span>
        </div>
      </div>
      <div>
        <CustomForm
          :formModel="dialogFormRef.formModel"
          :formItems="dialogFormRef.formItems"
          @visible="
            (v: CustomFormType['customForm']) => {
              dialogFormRef.customForm = v;
            }
          "
          @changeFormModel="updateCustomFormChangeFlag"
        />
      </div>
      <!-- SOP記録検印ボタン -->
      <div class="weighing-sop-detail_bottom-button">
        <ButtonEx
          type="primary"
          size="normal"
          :text="t('Wgt.Chr.btnSopRecordStamp')"
          :disabled="disabledRecordSealButtonRef"
          @click="handleSopRecordDetailStamp"
        />
      </div>
    </div>
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 秤量前後SOP作業詳細記録検印実行前確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxWeighingSOPDetailConfrimVisible"
    :dialogProps="msgBoxWeighingSOPDetailConfirmPropsRef"
    :cancelCallback="
      () => closeDialog('messageBoxWeighingSOPDetailConfrimVisible')
    "
    :submitCallback="() => openDialog('messageBoxWeighingSOPDetailVisible')"
  />
  <!-- 秤量前後SOP作業詳細記録検印の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxWeighingSOPDetailVisible"
    :dialogProps="messageBoxWeighingSOPDetailPropsRef"
    :cancelCallback="() => closeDialog('messageBoxWeighingSOPDetailVisible')"
    :submitCallback="requestApiModifyWeighingSOPRecordStamp"
  />
  <!-- 秤量前後SOP作業詳細記録検印実行完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxWeighingSOPDetailFinishedVisible"
    :dialogProps="messageBoxWeighingSOPDetailFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxModifyWeighingSOPDetailFinished"
  />
  <!-- 秤量前後SOP修正履歴ダイアログ -->
  <WgtHistoryWeighingSOPModify
    :sopFlowNo="cachePropsData.sopFlowNo"
    :sopFlowLnum="cachePropsData.sopFlowLnum"
    :isClicked="isClickedHistoryWeighingSOPModifyDialogRef"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
  />
  <!-- 秤量前後SOP異状履歴ダイアログ -->
  <WgtHistoryWeighingSOPDeviant
    :sopFlowNo="cachePropsData.sopFlowNo"
    :sopFlowLnum="cachePropsData.sopFlowLnum"
    :isClicked="isClickedHistoryWeighingSOPDeviantDialogRef"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
  />
  <!-- 秤量前後SOP記録修正ダイアログ -->
  <WgtEditWeighingSOPRecord
    :selectedRowData="emitSelectedRow"
    :isClicked="isClickedEditWeighingSOPRecordDialogRef"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    @submit="handleWeighingSOPDetailSubmit()"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import onValidateHandler from '@/utils/validateHandler';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import { rules } from '@/utils/validator';
import CONST from '@/constants/utils';
import {
  TabulatorTableIF,
  CustomOptionsData,
} from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import createMessageBoxForm from '@/utils/commentMessageBox';
import ButtonEx from '@/components/parts/ButtonEx.vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogProps } from '@/types/MessageBoxTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import { toNumberOrNull } from '@/utils/index';
import {
  useGetWeighingSOPDetailInit,
  useGetComboBoxDataStandard,
  useModifyWeighingSOPDetailRecordStamp,
} from '@/hooks/useApi';
import InfoShow from '@/components/parts/InfoShow.vue';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import { InfoShowType } from '@/types/InfoShowTypes';
import { IconTypes } from '@/types/IconTypes';
import {
  GetWeighingSOPListResListData,
  GetWeighingSOPDetailInitResListData,
  GetWeighingSOPDetailInitResList,
  GetWeighingSOPDetailInitRequestData,
  ModifyWeighingSOPDetailRecordStampRequestData,
} from '@/types/HookUseApi/WgtTypes';
import {
  CommonRequestType,
  ComboBoxDataStandardReturnData,
} from '@/types/HookUseApi/CommonTypes';
// 秤量前後SOP修正履歴ダイアログ
import WgtHistoryWeighingSOPModify from '@/components/fragment/wgt/wgtConfirmWeighingRecord/WgtHistoryWeighingSOPModify.vue';
// 秤量前後SOP異状履歴ダイアログ
import WgtHistoryWeighingSOPDeviant from '@/components/fragment/wgt/wgtConfirmWeighingRecord/WgtHistoryWeighingSOPDeviant.vue';
// 秤量前後SOP記録修正ダイアログ
import WgtEditWeighingSOPRecord from '@/components/fragment/wgt/wgtConfirmWeighingRecord/WgtEditWeighingSOPRecord.vue';
import {
  getInfoShowItems,
  getDialogFormItems,
  dialogFormModel,
} from './wgtWeighingSOPDetail';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxWeighingSOPDetailConfrimVisible'
  | 'messageBoxWeighingSOPDetailVisible'
  | 'messageBoxWeighingSOPDetailFinishedVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxWeighingSOPDetailConfrimVisible: false,
  messageBoxWeighingSOPDetailVisible: false,
  messageBoxWeighingSOPDetailFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  commonRejectHandler,
  updateDialogChangeFlagRef,
} = useDialog(initialState);

type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  routerName: string;
  selectedRowData: GetWeighingSOPListResListData | null;
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

const emit = defineEmits(['submit']);

// 前画面の選択行情報 型ガード毎回書かなくて済むように初期化時にキャッシュして使いまわす
let cachePropsData: {
  sopFlowNo: string;
  sopFlowLnum: number;
  wgtInstGrpNo: string;
} = {
  sopFlowNo: '',
  sopFlowLnum: 0,
  wgtInstGrpNo: '',
};

// 活性チェックボックス数
let enableCheckboxCount = 0;
// スクロールバー最下部検知
const isDisplayedAllRowsRef = ref<boolean>(false);

// 検印ボタン活性・非活性切り替え
const disabledRecordSealButtonRef = ref<boolean>(true);

const detailInfoInfoShowRef = ref<InfoShowType>({
  infoShowItems: getInfoShowItems(),
  isLabelVertical: true,
});

// 秤量前後SOP作業詳細初期表示のレスポンス リスト部分
let initResponseData: GetWeighingSOPDetailInitResList = {
  wgtSopDetail: [],
  sopFlowNmJp: '',
  cmtFlowDev: 0,
  recConfirmFlgDsp: '',
  sopFlowStsDsp: '',
  stDts: '',
  edDts: '',
  wgtSopsetKey: '',
  wgtRoomNmJp: '',
  deviceNmJp: '',
  modExistFlg: '',
  devExistFlg: '',
  devCorrLvFlg: false,
  recConfirmBtnFlg: false,
  updDts: '',
};

// カスタムフォーム変更状態 ダイアログ終了チェック用
let customFormChangeFlag: boolean = false;
// 選択行変更状態 ダイアログ終了チェック用
let selectedRowChangeFlag: boolean = false;
// 管理している状態フラグ全てを見てupdateDialogChangeFlagRefを更新する
const checkDialogChangeFlag = () => {
  updateDialogChangeFlagRef(customFormChangeFlag || selectedRowChangeFlag);
};
// カスタムフォーム状態更新
const updateCustomFormChangeFlag = (flag: boolean = false) => {
  customFormChangeFlag = flag;
  checkDialogChangeFlag();
};
// 選択行変更状態更新
const updateSelectedRowChangeFlag = (flag: boolean = false) => {
  selectedRowChangeFlag = flag;
  checkDialogChangeFlag();
};

// TabulatorTableの行情報定義
type SOPDetailTableRowData = GetWeighingSOPDetailInitResListData & {
  uniqueKey: string; // テーブル用主キー
};
// 選択行情報(複数)の格納
let selectedRows: SOPDetailTableRowData[] = [];

/**
 * 指図記録 検印ボタン活性・非活性切り替え
 */
const switchDisabledPrdRecSealButton = () => {
  // NOTE: 20250616時点、全チェックボタンの機能を利用すると無効化されたチェックボックスもカウントされる。一致だけで判定しないようにして対処する。
  const isAllChecked: boolean = enableCheckboxCount <= selectedRows.length;
  if (
    isDisplayedAllRowsRef.value &&
    isAllChecked &&
    // 全行スクロール済みかつ、全行チェックボックスが選択されている場合、検印ボタンを活性化
    // recApprovBtnFlgがfalseの場合、ボタンを活性化する
    !initResponseData.recConfirmBtnFlg
  ) {
    disabledRecordSealButtonRef.value = false;
  } else {
    disabledRecordSealButtonRef.value = true;
  }
};

// チェックボックス選択時処理
const updateSelectedRows = (v: SOPDetailTableRowData[]) => {
  selectedRows = v;
  // 活性化非活性化の切り替え処理
  switchDisabledPrdRecSealButton();

  if (v.length > 0) {
    updateSelectedRowChangeFlag(true);
  } else {
    updateSelectedRowChangeFlag(false);
  }
};

// スクロールバー最下部検知時処理
const displayedAllRowsRef = () => {
  isDisplayedAllRowsRef.value = true;
  switchDisabledPrdRecSealButton();
};

const dialogFormRef = ref<CustomFormType>({
  formItems: getDialogFormItems(),
  formModel: dialogFormModel,
});

let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;

const messageBoxForm = createMessageBoxForm('message', 'cmtWarning');
// SOP記録検印押下後の警告ダイアログ（コメントあり）
const messageBoxWeighingSOPDetailPropsRef = ref<DialogProps>({
  title: t('Wgt.Msg.titleWeightSopRecordConfirm'),
  content: t('Wgt.Msg.contentWeightSopRecordConfirm'),
  isPrompt: true,
  isSkipValidation: true, // 任意入力とする
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});
// SOP記録検印押下後の確認メッセージ
const msgBoxWeighingSOPDetailConfirmPropsRef = ref<DialogProps>({
  title: t('Wgt.Msg.titleWeightSopRecordConfirm'),
  content: t('Wgt.Msg.contentWeightSopRecordStamp'),
  type: 'info',
});
// SOP記録検印の完了メッセージボックス
const messageBoxWeighingSOPDetailFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});
// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 入力付き確認メッセージボックスの入力を初期化
const clearInputMessageBoxForm = () => {
  if ('isPrompt' in messageBoxWeighingSOPDetailPropsRef.value) {
    messageBoxWeighingSOPDetailPropsRef.value.formItems.message.formModelValue =
      '';
  }
};

// ボタンアイコン指定値
const iconYellow = 'icon_yellow';
const iconGray = 'icon_gray';
// SOP修正履歴ボタンアイコン
const sopRecordEditHistoryButtonIconRef = ref<IconTypes>(iconYellow);
// SOP異状履歴ボタンアイコン
const sopRecordDeviationHistoryButtonIconRef = ref<IconTypes>(iconYellow);
/**
 * ボタンステータスの初期化処理
 */
const resetButtonStatus = () => {
  sopRecordEditHistoryButtonIconRef.value = iconYellow;
  sopRecordDeviationHistoryButtonIconRef.value = iconYellow;
};

// '修正履歴' クリック 秤量前後SOP修正履歴ダイアログへ遷移
const isClickedHistoryWeighingSOPModifyDialogRef = ref<boolean>(false);
// '異状履歴' クリック 秤量前後SOP異状履歴ダイアログへ遷移
const isClickedHistoryWeighingSOPDeviantDialogRef = ref<boolean>(false);
// '記録修正' クリック 秤量前後SOP記録修正ダイアログへ遷移
const isClickedEditWeighingSOPRecordDialogRef = ref<boolean>(false);
/**
 * SOP修正履歴ボタン押下時
 */
const clickSopRecModHistoryBtn = () => {
  isClickedHistoryWeighingSOPModifyDialogRef.value =
    !isClickedHistoryWeighingSOPModifyDialogRef.value;
};
/**
 * SOP異状履歴ボタン押下時
 */
const clickSopRecDevHistoryBtn = () => {
  isClickedHistoryWeighingSOPDeviantDialogRef.value =
    !isClickedHistoryWeighingSOPDeviantDialogRef.value;
};
/**
 * 記録修正ボタン押下時
 */
const clickBtnColumn = () => {
  isClickedEditWeighingSOPRecordDialogRef.value =
    !isClickedEditWeighingSOPRecordDialogRef.value;
};

// 選択行情報(単体)の格納
let emitSelectedRow: GetWeighingSOPDetailInitResListData | null = null;
// 記録修正ボタン押下時処理
const updateEmitSelectedRow = (
  v: GetWeighingSOPDetailInitResListData | null,
) => {
  emitSelectedRow = v;
  // 秤量前後SOP記録修正ダイアログ表示
  clickBtnColumn();
};

// 秤量前後SOP作業詳細用テーブル設定
const tablePropsDataWeighingSOPConfirmInitListRef = ref<TabulatorTableIF>({
  pageName: 'WgtSOPDetail',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'uniqueKey', // 主キー。ユニークになるものを設定。
  tableBtns: [],
  showCheckbox: {
    show: true,
    condition: 'checkboxFlg', // チェックボックス非活性フラグ
    conditionValue: 0, // 0…活性、1…非活性
    allAllowed: false,
  },
  btnColumn: {
    frozen: true, // 凍結列
    columnWidth: 70, // ボタン列幅
    condition: 'recModBtnFlg', // 記録修正ボタン非活性フラグ
    conditionValue: 0, // 0…活性、1…非活性
    btnProps: {
      text: t('Wgt.Chr.btnRecordEdit'),
      type: 'secondary',
      size: 'tabulator',
    },
  },
  selectRowsData: [], // 選択行情報

  column: [
    // SOPフローNo 隠しカラム
    { title: '', field: 'sopFlowNo', hidden: true },
    // SOPフロー実行ログ番号 隠しカラム
    { title: '', field: 'sopFlowLnum', hidden: true },
    // SOPノード実行ログ番号 隠しカラム
    { title: '', field: 'sopNodeLnum', hidden: true },
    // 実行数
    {
      title: 'Wgt.Chr.txtSopNodeTimes',
      field: 'sopNodeTimes',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.SOP_NODE_TIMES,
    },
    // 異状レベル
    {
      title: 'Wgt.Chr.txtDeviationLevel',
      field: 'devCorrLv',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.DEV_LV,
    },
    // 作業指示内容
    {
      title: 'Wgt.Chr.txtWorkInstructionDetail',
      field: 'cmtMain',
      width: COLUMN_WIDTHS.WGT.CMT_MAIN,
    },
    // 指示値
    {
      title: 'Wgt.Chr.txtInstructionValue',
      field: 'instVal',
      width: COLUMN_WIDTHS.INST_VAL,
    },
    // 修正回数
    {
      title: 'Wgt.Chr.txtModifyCount',
      field: 'recModTimes',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT.CMT_TIMES,
    },
    // 単位
    {
      title: 'Wgt.Chr.txtUnit',
      field: 'unitNmJp',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    // 記録値1
    {
      title: 'Wgt.Chr.txtRecordValue1',
      field: 'recVal1',
      width: COLUMN_WIDTHS.WGT.REC_VAL,
    },
    // 記録値2
    {
      title: 'Wgt.Chr.txtRecordValue2',
      field: 'recVal2',
      width: COLUMN_WIDTHS.WGT.REC_VAL,
    },
    // 記録値3
    {
      title: 'Wgt.Chr.txtRecordValue3',
      field: 'recVal3',
      width: COLUMN_WIDTHS.WGT.REC_VAL,
    },
    // 記録値4
    {
      title: 'Wgt.Chr.txtRecordValue4',
      field: 'recVal4',
      width: COLUMN_WIDTHS.WGT.REC_VAL,
    },
    // 記録値5
    {
      title: 'Wgt.Chr.txtRecordValue5',
      field: 'recVal5',
      width: COLUMN_WIDTHS.WGT.REC_VAL,
    },
    // 異状下限
    {
      title: 'Wgt.Chr.txtDeviationLowerLimit',
      field: 'thValLlmt',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT.VAL_LMT,
    },
    // 異状上限
    {
      title: 'Wgt.Chr.txtDeviationUpperLimit',
      field: 'thValUlmt',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT.VAL_LMT,
    },
    // 参考値1
    {
      title: 'Wgt.Chr.txtReferenceValue1',
      field: 'refVal1',
      width: COLUMN_WIDTHS.WGT.REC_VAL,
    },
    // 参考値2
    {
      title: 'Wgt.Chr.txtReferenceValue2',
      field: 'refVal2',
      width: COLUMN_WIDTHS.WGT.REC_VAL,
    },
    // 参考値3
    {
      title: 'Wgt.Chr.txtReferenceValue3',
      field: 'refVal3',
      width: COLUMN_WIDTHS.WGT.REC_VAL,
    },
    // 参考値4
    {
      title: 'Wgt.Chr.txtReferenceValue4',
      field: 'refVal4',
      width: COLUMN_WIDTHS.WGT.REC_VAL,
    },
    // 参考値5
    {
      title: 'Wgt.Chr.txtReferenceValue5',
      field: 'refVal5',
      width: COLUMN_WIDTHS.WGT.REC_VAL,
    },
    // SOPヘルプ表示日時
    {
      title: 'Wgt.Chr.txtSopHelpDts',
      field: 'helpDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
    // 記録日時
    {
      title: 'Wgt.Chr.txtRecordDate',
      field: 'recDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
    // 記録者
    {
      title: 'Wgt.Chr.txtRecordUser',
      field: 'recUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // D記録
    {
      title: 'Wgt.Chr.txtDRecord',
      field: 'dcheckVal',
      width: COLUMN_WIDTHS.WGT.D_CHK_VAL,
    },
    // D記録日時
    {
      title: 'Wgt.Chr.txtDRecordDate',
      field: 'dcheckDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
    // D記録者
    {
      title: 'Wgt.Chr.txtDRecordUser',
      field: 'dcheckUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 複数作業者
    {
      title: 'Wgt.Chr.txtMultipleWorkUser',
      field: 'workUsr',
      width: COLUMN_WIDTHS.WGT.DATA_EXIST,
    },
    // 異状コメント
    {
      title: 'Wgt.Chr.txtDeviationComment',
      field: 'devExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 作業コメント
    {
      title: 'Wgt.Chr.txtWorkComment',
      field: 'msgExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 修正コメント
    {
      title: 'Wgt.Chr.txtModifyComment',
      field: 'modExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    {
      title: '',
      field: 'operations',
      formatter: 'btn',
      hozAlign: 'center',
    },
    // 背景色指定隠しカラムとして運用
    {
      title: '',
      field: 'backgroundColor',
      hidden: true,
    },
    // チェックボックス活性・非活性判定の隠しカラム
    {
      title: '',
      field: 'checkboxFlg',
      hidden: true,
    },
    // 記録修正ボタン活性・非活性フラグの隠しカラム
    {
      title: '',
      field: 'recModBtnFlg',
      hidden: true,
    },
  ],
  rowColor: {
    useColor: true,
    colorColumn: 'backgroundColor',
  },
  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
  tableId: 'detail-table', // tableId:displayedAllRowsのイベント呼び出しに必要
  textWrapColumns: ['cmtMain'],
  rowHeight: 60,
});

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    clickHandler: () => commonRejectHandler(),
  },
];

/**
 * 作業指示コメント結合
 */
const joinCmtMain = (res: CustomOptionsData) => {
  const comments = [res.cmtMain1, res.cmtMain2, res.cmtMain3].filter(Boolean);
  res.cmtMain = comments.join('<br>');
};

/**
 * テーブルデータチェック
 */
const checkTableData = (tableData: CustomOptionsData[]) => {
  // 活性コンボボックス数初期化
  enableCheckboxCount = 0;
  // 異状件数数初期化
  tableData.forEach((res) => {
    res.recModBtnFlg = toNumberOrNull(res.recModBtnFlg) ?? 1;
    res.checkboxFlg = toNumberOrNull(res.checkboxFlg) ?? 1;
    if (res.checkboxFlg === 0) {
      enableCheckboxCount += 1;
    }
    // 作業指示コメント結合
    joinCmtMain(res);
  });
};

/**
 * 画面状態の初期化
 */
const resetStatus = () => {
  // 活性チェックボックス数
  enableCheckboxCount = 0;
  // スクロールバー最下部検知
  isDisplayedAllRowsRef.value = false;
  // 指図記録 検印ボタン活性・非活性切り替え
  disabledRecordSealButtonRef.value = true;
  // 選択行情報(複数)
  selectedRows = [];
  resetButtonStatus();
  updateDialogChangeFlagRef(false);
  customFormChangeFlag = false;
  selectedRowChangeFlag = false;
};

/**
 * 秤量前後SOP作業詳細初期表示の呼び出しと反映
 * 内部でshowLoading、closeLoadingを行うため、呼び出し元では不要
 */
const requestApiGetWeighingSOPDetailInit = async () => {
  showLoading();

  // 秤量前後SOP作業詳細初期表示のAPIを行う。
  const requestData: GetWeighingSOPDetailInitRequestData = {
    sopFlowNo: cachePropsData.sopFlowNo,
    sopFlowLnum: cachePropsData.sopFlowLnum,
    wgtInstGrpNo: cachePropsData.wgtInstGrpNo,
  };

  // 秤量前後SOP作業詳細初期表示API実行
  const { responseRef, errorRef } = await useGetWeighingSOPDetailInit({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return false; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return false;
  }

  initResponseData = responseRef.value.data.rData;

  if (!initResponseData.devCorrLvFlg) {
    // 必須外す設定
    dialogFormRef.value.formItems.devExpl.rules = [
      rules.length(64, t('Cm.Chr.txtLength', [64])),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ];
  } else {
    // 必須付与設定
    dialogFormRef.value.formItems.devExpl.rules = [
      rules.required('textComboBox'), // 必須
      rules.length(64, t('Cm.Chr.txtLength', [64])),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ];
  }

  // 再検索時にバリデーション変更の可能性があるため状態初期化
  if (dialogFormRef.value.customForm) {
    dialogFormRef.value.customForm.clearValidate('devExpl');
  }

  const tableData: SOPDetailTableRowData[] = [];
  // 行情報にユニークキーを追加して格納
  initResponseData.wgtSopDetail.forEach((res) => {
    const tableRowData: SOPDetailTableRowData = {
      ...res,
      uniqueKey: `${res.sopFlowNo}-${res.sopFlowLnum}-${res.sopNodeLnum}`,
    };
    tableData.push(tableRowData);
  });
  // ダイアログ表示初期値として、レスポンス情報を格納
  tablePropsDataWeighingSOPConfirmInitListRef.value.tableData = tableData;

  checkTableData(tablePropsDataWeighingSOPConfirmInitListRef.value.tableData);
  // SOPフロー情報レイアウト用初期値設定
  Object.entries(initResponseData).forEach(([key, value]) => {
    if (key in detailInfoInfoShowRef.value.infoShowItems) {
      detailInfoInfoShowRef.value.infoShowItems[key].infoShowModelValue =
        value?.toString() ?? '';
    }
  });

  // 修正履歴ボタンのアイコン設定(0:黄色、1:灰色)
  // TODO:フラグのbool化
  if (initResponseData.modExistFlg === '0') {
    sopRecordEditHistoryButtonIconRef.value = iconGray;
  }
  // 異状履歴ボタンのアイコン設定(0:黄色、1:灰色)
  // TODO:フラグのbool化
  if (initResponseData.devExistFlg === '0') {
    sopRecordDeviationHistoryButtonIconRef.value = iconGray;
  }

  closeLoading();
  return true;
};

/**
 * 秤量前後SOP作業詳細ダイアログの再検索処理
 */
const wgtWeighingSOPDetailRedisplayInit = async () => {
  // NOTE:後続処理がないため、戻り値を利用していない。仕様追加時に注意。
  // 初期表示APIを行う
  await requestApiGetWeighingSOPDetailInit();
};

/**
 * 秤量前後SOP作業詳細ダイアログの初期設定
 */
const wgtWeighingSOPDetailInit = async () => {
  if (props.selectedRowData === null) return;
  if (props.selectedRowData.sopFlowLnum === null) return;
  if (
    props.selectedRowData.sopFlowNo === '' ||
    props.selectedRowData.sopFlowLnum === 0
  ) {
    return;
  }

  // FormItems初期化
  dialogFormRef.value.formItems = getDialogFormItems();

  // キャンセルボタン押下時のチェックボックス選択情報が残っている可能性があるため解除
  tablePropsDataWeighingSOPConfirmInitListRef.value.selectRowsData = [];
  // 画面状態の初期化
  resetStatus();

  // 選択行情報のキャッシュ
  cachePropsData = {
    sopFlowNo: props.selectedRowData.sopFlowNo,
    sopFlowLnum: props.selectedRowData.sopFlowLnum,
    wgtInstGrpNo: props.selectedRowData.wgtInstGrpNo,
  };

  // 初期表示APIを行う
  const isSuccess = await requestApiGetWeighingSOPDetailInit();
  if (!isSuccess) {
    return; // エラーの場合は処理を中断
  }

  showLoading();

  // 標準コンボボックスデータ取得
  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'devExpl', // 異状確認コメント
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_SOP_DEV_CONF' },
      },
      {
        cmbId: 'cmtWarning',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_SOP_FLOW_CONF' },
      },
    ],
  });

  // 標準コンボボックスのデータが取得できていれば画面上に反映
  if (
    comboBoxDataStandardReturnData &&
    comboBoxDataStandardReturnData.rData.rList.length > 0
  ) {
    setCustomFormComboBoxOptionList(
      dialogFormRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }

  if (
    'isPrompt' in messageBoxWeighingSOPDetailPropsRef.value &&
    comboBoxDataStandardReturnData
  ) {
    const resetData = createMessageBoxForm('message', 'cmtWarning');
    messageBoxWeighingSOPDetailPropsRef.value.formItems = resetData.formItems;

    // NOTE:共通コンボボックス情報を元にコメント付き警告メッセージを設定
    setCustomFormComboBoxOptionList(
      messageBoxWeighingSOPDetailPropsRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }

  closeLoading();
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

// 決定操作時処理
const handleWeighingSOPDetailSubmit = () => {
  tablePropsDataWeighingSOPConfirmInitListRef.value.selectRowsData = []; // 選択行情報を初期化
  // 選択状態を復元
  if (selectedRows.length > 0) {
    // ユニークキーを取得して格納
    tablePropsDataWeighingSOPConfirmInitListRef.value.selectRowsData =
      selectedRows.map((row) => row.uniqueKey);
  }

  // 再検索
  wgtWeighingSOPDetailRedisplayInit();
};

// SOP記録検印ボタンクリック時処理
const handleSopRecordDetailStamp = async () => {
  // 下記チェックを行い、チェックOKなら処理継続する。
  // 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  // NOTE: 異状確認レベルフラグが1の場合、カスタムフォームのバリデーションを行う
  const validate =
    dialogFormRef.value.customForm !== undefined &&
    (await dialogFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return;
  }

  // NOTE:「確認→コメント」の順序で開く
  // 次のダイアログを表示
  clearInputMessageBoxForm();
  openDialog('messageBoxWeighingSOPDetailConfrimVisible');
};

// 秤量前後SOP作業詳細記録検印実行のAPIリクエスト処理
const requestApiModifyWeighingSOPRecordStamp = async () => {
  closeDialog('messageBoxWeighingSOPDetailConfrimVisible');
  closeDialog('messageBoxWeighingSOPDetailVisible');

  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    // 署名ダイアログがキャンセルされた場合の処理
    console.error('署名ダイアログがキャンセルされました', error);
    return;
  }

  // NOTE:型ガード用のチェック。
  if (!('isPrompt' in messageBoxWeighingSOPDetailPropsRef.value)) return;

  showLoading();

  // 秤量前後SOP作業詳細記録検印APIを実行する。
  const requestData: ModifyWeighingSOPDetailRecordStampRequestData = {
    sopFlowNo: cachePropsData.sopFlowNo,
    sopFlowLnum: cachePropsData.sopFlowLnum,
    // 修正後のテキストボックスの内容を入力
    devExpl: dialogFormRef.value.formItems.devExpl.formModelValue.toString(),
    sopExpl:
      messageBoxWeighingSOPDetailPropsRef.value.formItems.message.formModelValue.toString(),
    updDts: initResponseData.updDts,
  };

  // NOTE:直前メッセージをキャッシュせず直書きしている。仕様変更等でメッセージ順序を入れ替えた際に意図しないものを送る可能性あり。要注意。
  const { responseRef, errorRef } = await useModifyWeighingSOPDetailRecordStamp(
    {
      ...props.privilegesBtnRequestData,
      msgboxTitleTxt: messageBoxWeighingSOPDetailPropsRef.value.title,
      msgboxMsgTxt: messageBoxWeighingSOPDetailPropsRef.value.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      msgboxInputCmt:
        messageBoxWeighingSOPDetailPropsRef.value.formItems.message.formModelValue.toString(),
      ...requestData,
    },
  );

  if (errorRef.value) {
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // ・データベースを更新した後、以下のメッセージを表示する。
  // 秤量前後SOP作業詳細記録検印実行完了
  messageBoxWeighingSOPDetailFinishedPropsRef.value.title =
    responseRef.value.data.rTitle;
  messageBoxWeighingSOPDetailFinishedPropsRef.value.content =
    responseRef.value.data.rMsg;

  closeLoading();
  openDialog('messageBoxWeighingSOPDetailFinishedVisible');
};

// 秤量前後SOP作業詳細記録検印完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxModifyWeighingSOPDetailFinished = () => {
  emit('submit', props.privilegesBtnRequestData);
  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxWeighingSOPDetailFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

// ダイアログ起動条件の監視。isClickedの真偽値が切り替わりを監視
watch(
  () => props.isClicked,
  async () => {
    wgtWeighingSOPDetailInit();
  },
);
</script>
<style lang="scss" scoped>
$namespace: 'weighing-sop-detail';
.#{$namespace} {
  &_info-show-container {
    // NOTE:スクロール範囲は各画面単位で調節するため直値で指定。コピペで使いまわさないこと。
    height: 194px; // NOTE:見出しが半分見えるぐらいの高さ。スクロール有無を分かりやすくするため。
    overflow-y: auto;
  }
  // 画面上部 右側ボタンエリア
  &_button-area {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
  // 警告文
  &_cautionary {
    font-size: 14px;
    color: $red330;
  }
  // 警告文 行間調整用
  &_cautionary-space {
    padding-top: 2px;
  }
  // 画面下部要素 ボタン(記録検印)
  &_bottom-button {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
