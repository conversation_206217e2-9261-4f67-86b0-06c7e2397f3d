import { ref } from 'vue';
import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules, check } from '@/utils/validator';
import CONST from '@/constants/utils';

const { t } = i18n.global;

// アイテム定義
export const sopPrcSettingDialogFormItems: CustomFormType['formItems'] = {
  PrcNm: {
    formModelValue: '',
    label: { text: t('SOP.Chr.SOPPrcSetting.txtPrcNm') },
    formRole: 'textBox',
    props: { disabled: true },
  },
  sopFlowNo: {
    formModelValue: '',
    label: { text: t('SOP.Chr.txtSopFlowNo') },
    formRole: 'textBox',
    props: { disabled: true },
  },
  sopFlowNmJp: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    formModelValue: '',
    label: { text: t('SOP.Chr.txtSopFlowNm') },
    formRole: 'textBox',
    rules: [
      rules.length(32),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      rules.required('textBox'),
    ],
  },
  stYmd: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    label: { text: t('SOP.Chr.SOPPrcListSetting.txtStYMD') },
    formModelValue: '',
    rules: [rules.required('date'), rules.futureDate(), rules.fromToDate()],
    formRole: 'date',
    props: { modelValue: '', type: 'date' },
  },
  edYmd: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    label: { text: t('SOP.Chr.SOPPrcListSetting.txtEdYMD') },
    formModelValue: '',
    rules: [rules.required('date'), rules.futureDate(), rules.fromToDate()],
    formRole: 'date',
    props: { modelValue: '', type: 'date' },
  },
  recApprovFlg: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    formModelValue: '',
    rules: [rules.required('selectComboBox')],
    label: { text: t('SOP.Chr.SOPPrcListSetting.txtRecApprovFlg') },
    formRole: 'selectComboBox',
    selectOptions: [],
    props: {
      filterable: true,
      clearable: true,
      optionsData: [],
    },
    cmbId: 'recApprovFlg',
  },
  helpBinPath: {
    formModelValue: '',
    label: { text: t('SOP.Chr.txtHelpSetting') },
    formRole: 'textBox',
    rules: [rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS_PATH])],
  },
  forcePrivGrpCd: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    formModelValue: '',
    label: { text: t('SOP.Chr.txtApproveGroup') },
    rules: [rules.required('selectComboBox')],
    formRole: 'selectComboBox',
    selectOptions: [],
    props: { filterable: true, clearable: true, optionsData: [] },
    cmbId: 'privGroup',
  },
  skipPrivGrpCd: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    formModelValue: '',
    label: { text: t('SOP.Chr.txtSkipGroup') },
    rules: [rules.required('selectComboBox')],
    formRole: 'selectComboBox',
    selectOptions: [],
    props: { filterable: true, clearable: true, optionsData: [] },
    cmbId: 'privGroup',
  },
  dspSeq: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    formModelValue: '',
    label: { text: t('SOP.Chr.txtDspSeq') },
    formRole: 'textBox',
    rules: [
      rules.required('textBox'),
      rules.placesOfNumeric({ int: 6 }),
      rules.naturalNumericOnly(),
    ],
  },
};
// モデル定義
export const sopPrcSettingDialogFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(sopPrcSettingDialogFormItems);

export const sopPrcSettingDialogFormRef = ref<CustomFormType>({
  formItems: sopPrcSettingDialogFormItems,
  formModel: sopPrcSettingDialogFormModel,
});

/**
 * カレンダー前後チェック
 * @param {string} rule.field - フィールド
 * @param {string} rule.message - メッセージ
 * @param {string} rule.dataId -  データID
 * @param {string} value - データ
 * @param {void} callback -コールバック
 */
export const fromToDate = (
  rule: { field: string; message: string; dataId: string },
  value: string,
  callback: (val?: Error) => void,
) => {
  if (rule.field !== '') {
    const startDate = sopPrcSettingDialogFormModel[rule.dataId]?.toString();
    if (check.fromToDate(startDate, value)) {
      const errorVal = new Error(rule.message);
      callback(errorVal);
    } else {
      callback();
    }
  }
};
