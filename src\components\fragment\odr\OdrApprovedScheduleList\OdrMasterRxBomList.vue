<template>
  <!-- 構成品予定詳細ダイアログ -->
  <!-- 見出し 構成品予定詳細 -->
  <DialogWindow
    :title="$t('Odr.Chr.txtBillOfMaterialsPlanDetail')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 見出し 小日程計画詳細 -->
    <BaseHeading
      level="2"
      :text="$t('Odr.Chr.txtScheduleDetail')"
      fontSize="24px"
    />
    <!-- 小日程計画詳細の見出し+テキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="orderDetailInfoShowRef.infoShowItems"
      :isLabelVertical="orderDetailInfoShowRef.isLabelVertical"
    />

    <!-- 見出し 構成品一覧 -->
    <BaseHeading
      level="2"
      :text="$t('Odr.Chr.txtComponentList')"
      fontSize="24px"
      class="Util_mt-16"
    />
    <!-- 構成品一覧テーブル -->
    <TabulatorTable
      :propsData="tablePropsDataBillOfMaterialsListRef"
      :routerName="props.routerName"
      @selectRow="updateSelectedRow"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 版変更削除の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxEditionChangeDeleteVisible"
    :dialogProps="messageBoxEditionChangeDeletePropsRef"
    :cancelCallback="() => closeDialog('messageBoxEditionChangeDeleteVisible')"
    :submitCallback="requestApiDeleteScheduleBillOfMaterials"
  />
  <!-- 版変更削除の完了表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxEditionChangeDeleteFinishedVisible"
    :dialogProps="messageBoxEditionChangeDeleteFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxEditionChangeDeleteFinished"
  />
  <!-- 版変更ダイアログ -->
  <OdrChangeEditNo
    :isClicked="isClickedShowChangeEditNoDialogRef"
    :scheduleNo="props.skdNo"
    :processSequence="getProcessSequence()"
    :selectedRowData="selectedRow"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    @submit="requestApiGetMasterPrescriptionBillOfMaterialsList"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import {
  useGetMasterPrescriptionBillOfMaterialsList,
  useDeleteScheduleBillOfMaterials,
} from '@/hooks/useApi';
import {
  GetMasterPrescriptionBillOfMaterialsListResData,
  GetMasterPrescriptionBillOfMaterialsListResBillOfMaterialsData,
  GetMasterPrescriptionBillOfMaterialsListRequestData,
  DeleteScheduleBillOfMaterialsRequestData,
} from '@/types/HookUseApi/OdrTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import OdrChangeEditNo from '@/components/fragment/odr/OdrApprovedScheduleList/OdrChangeEditNo.vue';
import getOrderDetailInfoShowItems from './odrMasterRxBomList';

/**
 * 多言語
 */
const { t } = useI18n();

// 選択行情報の格納
let selectedRow: GetMasterPrescriptionBillOfMaterialsListResBillOfMaterialsData | null =
  null;
// 構成品予定詳細一覧取得APIのレスポンス
let listResponseData: GetMasterPrescriptionBillOfMaterialsListResData = {
  skdNo: '', // オーダー番号
  dspNmJp: '', // 品名
  rxNmJp: '', // 処方名
  stdPrdQty: '', // 標準生産量
  unitNmJp: '', // 単位
  rxBomList: [],
};

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxEditionChangeDeleteVisible'
  | 'messageBoxEditionChangeDeleteFinishedVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxEditionChangeDeleteVisible: false,
  messageBoxEditionChangeDeleteFinishedVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 版変更削除の確認メッセージボックス
const messageBoxEditionChangeDeletePropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titleEditionChangeDelete'),
  content: t('Odr.Msg.contentEditionChangeDelete'),
  type: 'question',
});

// 版変更削除の完了メッセージボックス
const messageBoxEditionChangeDeleteFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

const orderDetailInfoShowRef = ref<InfoShowType>({
  infoShowItems: getOrderDetailInfoShowItems(),
  isLabelVertical: true,
});

// '版変更' クリック
const isClickedShowChangeEditNoDialogRef = ref<boolean>(false);

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  skdNo: string; // オーダー番号
  privilegesBtnRequestData: CommonRequestType;
  routerName: string; // TabulatorTable権限 未選択チェック用
};

const props = defineProps<Props>();

// 構成品一覧用テーブル設定
const tablePropsDataBillOfMaterialsListRef = ref<TabulatorTableIF>({
  pageName: 'ProcessList',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'uniqueKey', // 主キー。ユニークになるものを設定。
  showRadio: true, // ラジオボタンとして使用。

  column: [
    // 製造工程順
    {
      title: 'Odr.Chr.txtOrderProcessSequence',
      field: 'prcSeq',
      width: COLUMN_WIDTHS.ODR.PRC_SEQ,
    },
    // 投入番号
    {
      title: 'Odr.Chr.txtPrescriptionBillOfMaterialsMaterialSequence',
      field: 'bomMatSeq',
      width: COLUMN_WIDTHS.ODR.BOM_MAT_SEQ,
    },
    // 構成品目コード
    {
      title: 'Odr.Chr.txtBillOfMaterialsCode',
      field: 'bomMatNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    // 構成品名
    {
      title: 'Odr.Chr.txtBillOfMaterialsName',
      field: 'bomDspNmJp',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    // 版管理種別
    {
      title: 'Odr.Chr.txtEditionManagementType',
      field: 'edMgtType',
      width: COLUMN_WIDTHS.ODR.ED_MGT_TYPE,
    },
    // 版番号
    {
      title: 'Odr.Chr.txtEditionNo',
      field: 'edNo',
      width: COLUMN_WIDTHS.ED_NO,
    },
    // 版指定
    {
      title: 'Odr.Chr.txtHasEdtion',
      field: 'hasSkdBom',
      width: COLUMN_WIDTHS.ODR.SKD_BOM_EXIST,
    },
    // 標準仕込量
    {
      title: 'Odr.Chr.txtStandardBillOfMaterialsQuantity',
      field: 'stdBomQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // バッチ指示重量
    {
      title: 'Odr.Chr.txtBatchWeight',
      field: 'batchWgt',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 単位
    {
      title: 'Odr.Chr.txtUnit',
      field: 'bomUnitNmJp',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    // レスポンスにユニークなデータが存在しないため、自前で隠しカラムでユニーク情報生成
    { title: '', field: 'uniqueKey', hidden: true },
  ],
  // NOTE:権限が親からprops経由で来るため、Init関数内でonSelectBtnsを生成する
  onSelectBtns: [],
  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
});

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
];

// 選択行情報の更新
const updateSelectedRow = (
  v: GetMasterPrescriptionBillOfMaterialsListResBillOfMaterialsData | null,
) => {
  selectedRow = v;
};

// 版変更削除の確認メッセージ'OK'押下時処理
// 版変更削除のAPIリクエスト処理
const requestApiDeleteScheduleBillOfMaterials = async () => {
  console.log('requestApiDeleteScheduleBillOfMaterials');

  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxEditionChangeDeleteVisible');

  // NOTE:ボタン押下時にチェック済みだが、型ガード用に再チェック
  if (selectedRow === null) return;
  // eslintエラー回避目的の型ガード
  if (selectedRow.prcSeq === null) return;

  showLoading();

  // 版変更削除のAPIを行う。
  const requestData: DeleteScheduleBillOfMaterialsRequestData = {
    skdNo: props.skdNo,
    prcSeq: selectedRow.prcSeq,
    bomMatNo: selectedRow.bomMatNo,
    updDts: selectedRow.updDts,
  };
  const { errorRef, responseRef } = await useDeleteScheduleBillOfMaterials({
    ...props.privilegesBtnRequestData,
    ...requestData,
    msgboxTitleTxt: messageBoxEditionChangeDeletePropsRef.value.title,
    msgboxMsgTxt: messageBoxEditionChangeDeletePropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
  });
  if (errorRef.value) {
    closeLoading();

    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  closeLoading();

  if (responseRef.value) {
    // ・データベースを更新した後、以下のメッセージを表示する。
    // 版変更解除完了
    messageBoxEditionChangeDeleteFinishedPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxEditionChangeDeleteFinishedPropsRef.value.content =
      responseRef.value.data.rMsg;

    openDialog('messageBoxEditionChangeDeleteFinishedVisible');
  }
};

// 初回表示、再検索で呼び出される
// 構成品予定詳細取得API呼び出し
const requestApiGetMasterPrescriptionBillOfMaterialsList = async () => {
  showLoading();

  // 構成品予定詳細取得のAPIを行う。
  const requestData: GetMasterPrescriptionBillOfMaterialsListRequestData = {
    skdNo: props.skdNo,
  };
  const { responseRef, errorRef } =
    await useGetMasterPrescriptionBillOfMaterialsList({
      ...props.privilegesBtnRequestData,
      ...requestData,
    });
  if (errorRef.value) {
    closeLoading();

    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return Promise.reject(); // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // ダイアログの引数として、レスポンス情報を格納
    listResponseData = responseRef.value.data.rData;

    // 小日程計画詳細レイアウト用初期値設定
    Object.entries(listResponseData).forEach(([key, value]) => {
      if (key in orderDetailInfoShowRef.value.infoShowItems) {
        orderDetailInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });

    // テーブル設定
    tablePropsDataBillOfMaterialsListRef.value.tableData =
      listResponseData.rxBomList;
    // レスポンスに存在しないユニークキーを独自に設定して隠しカラムに仕込む対応
    tablePropsDataBillOfMaterialsListRef.value.tableData.forEach((value) => {
      const tableData = value;
      tableData.uniqueKey = `${value.prcSeq}-${value.bomMatNo}-${value.bomMatSeq}`;
    });
  }

  closeLoading();
  return Promise.resolve();
};

// 版変更削除に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxEditionChangeDeleteFinished = () => {
  // 自身を閉じる
  closeDialog('messageBoxEditionChangeDeleteFinishedVisible');

  // 再検索
  requestApiGetMasterPrescriptionBillOfMaterialsList();
};

// selectedRow.prcSeqをnumber固定で取得
const getProcessSequence = () => {
  // NOTE:計画指図はBEからnumberがnull来ることは有り得ないが、記述ルールとしてnull許容になっているので外す
  if (selectedRow === null || selectedRow.prcSeq === null) return 0;
  return selectedRow.prcSeq;
};

/**
 * 構成品予定詳細ダイアログの初期設定
 */
const odrMasterRxBomListInit = async () => {
  // 自身の選択行情報を初期化
  selectedRow = null;

  // InfoShow初期化
  orderDetailInfoShowRef.value.infoShowItems = getOrderDetailInfoShowItems();

  // 構成品予定詳細取得のAPI呼び出しと反映
  try {
    await requestApiGetMasterPrescriptionBillOfMaterialsList();
  } catch (error) {
    return;
  }

  // NOTE:ボタンに権限付与は親から渡されたものを設定するため、初期化はここで行う。
  tablePropsDataBillOfMaterialsListRef.value.onSelectBtns = [
    // 版変更解除
    {
      text: 'Odr.Chr.btnDeleteEditionChange',
      type: 'dangerSecond',
      // NOTE:選択チェック用に権限付与。
      tabulatorActionId: props.privilegesBtnRequestData.btnId,
      clickHandler: async () => {
        // NOTE:未選択なら処理させない。エラーメッセージはTabulatorTable側で出してくれる。
        if (selectedRow === null) {
          return;
        }

        // 版変更削除の確認メッセージダイアログ表示開始
        openDialog('messageBoxEditionChangeDeleteVisible');
      },
    },
    // 版変更
    {
      text: 'Odr.Chr.btnEditionChange',
      // NOTE:選択チェック用に権限付与。
      tabulatorActionId: props.privilegesBtnRequestData.btnId,
      clickHandler() {
        // 版変更ダイアログを表示開始
        // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
        isClickedShowChangeEditNoDialogRef.value =
          !isClickedShowChangeEditNoDialogRef.value;
      },
    },
  ];

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await odrMasterRxBomListInit();
  },
);
</script>
