import CONST from '@/constants/utils';
import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// 計画番号の固定文字数
export const PLAN_PRC_NO_FIXED_CHR_LENGTH = 9;

// 計画追加ダイアログのアイテム定義
export const getOdrAddScheduleFormItems: () => CustomFormType['formItems'] =
  () => ({
    unitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    planPrcNo: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPlanProcedureNo') }, // 計画番号
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [
        rules.required('textBox'),
        rules.singleByteNumber(),
        rules.fixedChrLength(PLAN_PRC_NO_FIXED_CHR_LENGTH),
      ],
      formRole: 'textBox',
      props: { disabled: false },
    },
    matAttr: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtMaterial') }, // 品目
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'matAttrOdrAdd', // レスポンスの同名cmbIdと紐づけされて格納される
    },
    matNo: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtMaterialCode') }, // 品目コード
      formRole: 'textBox',
      props: { disabled: true },
    },
    matNm: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtMaterialName') }, // 品名
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 処方選択ダイアログを開くためのボタン
    buttonOdrSelectRx: {
      formModelValue: '',
      formRole: 'button',
      onClickHandler() {}, // vueで上書きします
      props: {
        text: '処方選択',
      },
    },
    prescriptionNm: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPrescriptionName') }, // 処方名
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('textBox')],
      formRole: 'textBox',
      props: { disabled: true },
    },
    masterBatchRecordNo: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtMasterBatchRecordNo') }, // MBR番号
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('textBox')],
      formRole: 'textBox',
      props: { disabled: true },
    },
    prescriptionDts: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPrescriptionDeadlineDate') }, // 処方有効期限
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('textBox')],
      formRole: 'textBox',
      props: { disabled: true },
    },
    stdPrdQty: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtStandardProductionQuantity') }, // 標準生産量
      suffix: { formModelProp: 'unitNm' },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('textBox')],
      formRole: 'textBox',
      props: { disabled: true },
    },
    planQty: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtOrderQuantity') }, // 計画生産量
      suffix: { formModelProp: 'unitNm' },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.positiveRealNumericOnly(),
      ],
      formRole: 'textBox',
    },
    odrDts: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtOrderDate') }, // 製造開始予定日
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('date')],
      formRole: 'date',
      props: { modelValue: '', type: 'date' },
    },
    skdAddExpl: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPlanComment') }, // 計画コメント
      rules: [
        rules.length(64),
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      ],
      formRole: 'textBox',
    },
  });

// 計画追加ダイアログのモデル定義
export const odrAddScheduleFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getOdrAddScheduleFormItems());
