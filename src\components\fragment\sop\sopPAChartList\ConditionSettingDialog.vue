<!-- コンボボックスを内包したダイアログの動作確認用サンプル -->
<template>
  <!-- コンボボックスダイアログ -->
  <DialogWindow
    :title="'指図SOPフロー検索'"
    :dialogVisible="props.dialogVisible"
    :onReject="() => console.log('onReject selectComboBoxDialog')"
    :onResolve="
      // 実行時にバリデーションチェックを行うなら以下の記載。
      async () => {
        // ログ出力は不要なら消してください
        console.log('onResolve selectComboBoxDialog');
        emit('selectedCondition', selectComboBoxDialogFormRef);
        return (
          selectComboBoxDialogFormRef.customForm !== undefined &&
          (await selectComboBoxDialogFormRef.customForm.validate((isValid) =>
            onValidateHandler(isValid),
          ))
        );
      }
    "
  >
    <!-- カスタムフォームを利用して内容表示を行う -->
    <CustomForm
      :formModel="selectComboBoxDialogFormRef.formModel"
      :formItems="selectComboBoxDialogFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          selectComboBoxDialogFormRef.customForm = v;
        }
      "
    />
  </DialogWindow>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import {
  selectComboBoxDialogFormItems,
  selectComboBoxDialogFormModel,
} from './conditionSettingDialog';

// カスタムフォームの情報まとめ。設定はsampleSelectComboBoxDialog.tsに記載している
const selectComboBoxDialogFormRef = ref<CustomFormType>({
  formItems: selectComboBoxDialogFormItems,
  formModel: selectComboBoxDialogFormModel,
});

// 親から渡す情報群
type Props = {
  dialogVisible: boolean; // ダイアログ表示条件。利用先でDialogRefKeyを入れてください
};

const props = defineProps<Props>();
const emit = defineEmits(['selectedCondition']);
</script>
<style lang="scss" scoped>
$namespace: 'sample-select-combobox-dialog';

.#{$namespace} {
  background-color: $white750;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
</style>
