<template>
  <!-- 入荷予定詳細 -->
  <DialogWindow
    :title="$t('Aog.Chr.txtAogDetails')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    :buttons="[
      {
        text: $t('Cm.Chr.btnCancel'),
        type: 'secondary',
        size: 'normal',
        clickHandler: () => {
          closeDialog('fragmentDialogVisible');
        },
      },
    ]"
  >
    <CustomForm
      :formModel="aogArrivalPlanDetailsFormRef.formModel"
      :formItems="aogArrivalPlanDetailsFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          aogArrivalPlanDetailsFormRef.customForm = v;
        }
      "
    />
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import { AogInstructionCreateListData } from '@/types/HookUseApi/AogTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { handleValidationBySingleRowSelect } from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { useGetAogPlan } from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  aogArrivalPlanDetailsFormModel,
  getAogPlanDetailsFormItems,
} from './AogArrivalPlanDetails';

const aogArrivalPlanDetailsFormRef = ref<CustomFormType>({
  formItems: getAogPlanDetailsFormItems(),
  formModel: aogArrivalPlanDetailsFormModel,
});

type Props = {
  selectedRowData: AogInstructionCreateListData | null;
  selectedRowsData: AogInstructionCreateListData[];
  isClicked: boolean;
  commonActionRequestData: CommonRequestType;
};
const props = defineProps<Props>();

const FILE_AOG_ATT_BIN = {
  MODEL_KEY: 'aogAttBinList',
  NAME_KEY: 'attBinFileNm',
  UNIQUE_KEY: 'attBinNo',
} as const;

type DialogRefKey =
  | 'singleButton'
  | 'messageBoxSingleButtonRef'
  | 'fragmentDialogVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  messageBoxSingleButtonRef: false,
  fragmentDialogVisible: false,
};
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

/**
 * 初期設定
 */
const aogArrivalPlanDetailsInit = async () => {
  // 単一選択されていなかった場合はエラー表示とする
  try {
    await handleValidationBySingleRowSelect(props.selectedRowsData);
  } catch (e) {
    console.log(e);
    return;
  }

  if (!props.selectedRowData) return;
  showLoading();
  aogArrivalPlanDetailsFormRef.value.formItems = getAogPlanDetailsFormItems();
  // 入荷予定詳細データ取得
  const { responseRef, errorRef } = await useGetAogPlan({
    ...props.commonActionRequestData,
    aogPlanNo: props.selectedRowData.aogPlanNo,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    setFormModelValueFromApiResponse(
      aogArrivalPlanDetailsFormRef,
      responseRef.value.data.rData,
      {
        fileKeys: [
          {
            formModelKey: FILE_AOG_ATT_BIN.MODEL_KEY,
            fileNameKey: FILE_AOG_ATT_BIN.NAME_KEY,
            fileKeyPropName: FILE_AOG_ATT_BIN.UNIQUE_KEY,
          },
        ],
        commonRequestData: props.commonActionRequestData,
      },
    );
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, aogArrivalPlanDetailsInit);
</script>
<style lang="scss" scoped>
$namespace: 'order-addition-dialog';

.#{$namespace} {
  background-color: $white750;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
</style>
