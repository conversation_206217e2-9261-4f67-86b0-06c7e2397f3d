<template>
  <!--
   NOTE:2025/07/28
   呼び出し元が存在しなくなったため、利用されなくなるvueです。
   削除せずに放置します。
   他修正によりこのvueでエラーが出た場合は、コメントアウト等でメンテナンスコストを最小限にしてください。
  -->
  <!-- 秤量前後SOP記録ダイアログ -->
  <DialogWindow
    :title="$t('Wgt.Chr.txtWeightSopRecord')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 見出し SOPフロー一覧 -->
    <BaseHeading
      level="2"
      :text="$t('Wgt.Chr.txtWeightSopFlowList')"
      fontSize="24px"
    />
    <!-- SOPフロー一覧リストテーブル -->
    <TabulatorTable
      :propsData="tablePropsDataWeighingSOPConfirmInitListRef"
      :routerName="props.routerName"
      @selectRow="updateSelectedRow"
    />
  </DialogWindow>
  <!-- 秤量前後SOP作業詳細ダイアログ -->
  <WgtWeighingSOPDetail
    :isClicked="isClickedShowWgtWeighingSOPDetailDialogRef"
    :selectedRowData="null"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    :routerName="props.routerName"
    @submit="wgtConfirmWeighingSOPInit"
  />
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 秤量指示書確定の確認表示 -->
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogProps } from '@/types/MessageBoxTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import { useGetWeighingSOPConfirmInit } from '@/hooks/useApi';
import {
  GetWeighingSOPConfirmInitResListData,
  GetWeighingSOPConfirmInitRequestData,
} from '@/types/HookUseApi/WgtTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import WgtWeighingSOPDetail from './WgtWeighingSOPDetail.vue';

/**
 * 多言語
 */
const { t } = useI18n();

type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  wgtInstGrpNo: string;
  routerName: string;
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

// ダイアログの表示切替用定義
type DialogRefKey = 'fragmentDialogVisible' | 'messageBoxApiErrorVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// NOTE:ビルドエラー解消。メンテコスト削減のためそのままコメントアウト
// // 作業詳細に渡すための選択番号情報
// let cacheSopFlowNo: string = '';
// let cacheSopFlowLnum: number = 0;

const emit = defineEmits(['submit']);

// '作業詳細' クリック 秤量前後SOP作業詳細ダイアログへ遷移
const isClickedShowWgtWeighingSOPDetailDialogRef = ref<boolean>(false);

// 選択行情報の格納
let selectedRow: GetWeighingSOPConfirmInitResListData | null = null;

// 選択行情報の更新
const updateSelectedRow = (v: GetWeighingSOPConfirmInitResListData | null) => {
  selectedRow = v;

  // NOTE:ビルドエラー解消。メンテコスト削減のためそのままコメントアウト
  // if (selectedRow === null || selectedRow.sopFlowLnum === null) {
  //   return;
  // }
  // // 作業詳細に渡すため、情報をキャッシュ
  // cacheSopFlowNo = selectedRow.sopFlowNo;
  // cacheSopFlowLnum = selectedRow.sopFlowLnum;
};

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    clickHandler: () => {
      emit('submit', props.privilegesBtnRequestData);
    },
  },
];

// 秤量指示明細用テーブル設定
const tablePropsDataWeighingSOPConfirmInitListRef = ref<TabulatorTableIF>({
  pageName: 'ConfWgtSOP',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'uniqueKey', // 主キー。ユニークになるものを設定。
  showRadio: true,

  column: [
    // ユニークキー 隠しカラム
    { title: '', field: 'uniqueKey', hidden: true },
    // SOPフローNo 隠しカラム
    { title: '', field: 'sopFlowNo', hidden: true },
    // SOPフロー実行ログ番号 隠しカラム
    { title: '', field: 'sopFlowLnum', hidden: true },
    // 確認状態
    {
      title: 'Wgt.Chr.txtStatus',
      field: 'recConfirmFlgDsp',
      width: COLUMN_WIDTHS.WGT.REC_CONFIRM_FLG,
    },
    // フロー状態
    {
      title: 'Wgt.Chr.txtFlowStatus',
      field: 'sopFlowStsDsp',
      width: COLUMN_WIDTHS.WGT.SOP_FLOW_STS,
    },
    // 異状レベル
    {
      title: 'Wgt.Chr.txtDeviationLevel',
      field: 'devCorrLv',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.DEV_LV,
    },
    // SOPフロー名
    {
      title: 'Wgt.Chr.txtSopFlowName',
      field: 'sopFlowNmJp',
      width: COLUMN_WIDTHS.SOP_FLOW_NM,
    },
    // SOPフロー開始日時
    {
      title: 'Wgt.Chr.txtSOPFlowStartDate',
      field: 'stDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // SOPフロー終了日時
    {
      title: 'Wgt.Chr.txtSOPFlowEndDate',
      field: 'edDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // SOPフロー記録検印日時
    {
      title: 'Wgt.Chr.txtSOPFlowRecordStampDate',
      field: 'recConfirmDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // 異状コメント
    {
      title: 'Wgt.Chr.txtDeviationComment',
      field: 'devExplDsp',
      width: COLUMN_WIDTHS.WGT.DATA_EXIST,
    },
    // 作業コメント
    {
      title: 'Wgt.Chr.txtWorkComment',
      field: 'msgExplDsp',
      width: COLUMN_WIDTHS.WGT.DATA_EXIST,
    },
    // 修正コメント
    {
      title: 'Wgt.Chr.txtModifyComment',
      field: 'modExplDsp',
      width: COLUMN_WIDTHS.WGT.DATA_EXIST,
    },
    // 異状確認コメント
    {
      title: 'Wgt.Chr.txtDeviationCheckComment',
      field: 'recConfirmDevExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 記録確認コメント
    {
      title: 'Wgt.Chr.txtRecordCheckComment',
      field: 'recConfirmExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 秤量セットキー
    {
      title: 'Wgt.Chr.txtWeightSetKey',
      field: 'wgtSopsetKey',
      width: COLUMN_WIDTHS.WGT.WGT_SOPSET_KEY,
    },
    // 秤量室
    {
      title: 'Wgt.Chr.txtWeightRoom',
      field: 'wgtRoomNmJp',
      width: COLUMN_WIDTHS.WGT.WGT_ROOM_NM,
    },
    // 計量器
    {
      title: 'Wgt.Chr.txtWeightDevice',
      field: 'deviceNmJp',
      width: COLUMN_WIDTHS.WGT.DEVICE_NM,
    },
    // 隠しカラムとして運用
    { title: '', field: 'backgroundColor', hidden: true },
  ],
  rowColor: {
    useColor: true,
    colorColumn: 'backgroundColor',
  },
  tableData: [],
  // NOTE:権限が親からprops経由で来るため、Init関数内でonSelectBtnsを生成する
  onSelectBtns: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
});

// SOPフロー一覧取得API呼び出し
const requestApiGetWeighingSOPConfirmInit = async () => {
  if (props.wgtInstGrpNo === '') {
    return Promise.reject();
  }

  showLoading();

  // 秤量前後SOP記録初期表示のAPIを行う。
  const requestData: GetWeighingSOPConfirmInitRequestData = {
    wgtInstGrpNo: props.wgtInstGrpNo,
  };

  // 秤量前後SOP記録初期表示API実行
  const { responseRef, errorRef } = await useGetWeighingSOPConfirmInit({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return Promise.reject(); // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // ダイアログ表示初期値として、レスポンス情報を格納
    // SOPフロー一覧のテーブル作成
    tablePropsDataWeighingSOPConfirmInitListRef.value.tableData =
      responseRef.value.data.rData.wgtSopList;
  }

  closeLoading();
  return Promise.resolve();
};

/**
 * 秤量前後SOP記録ダイアログの初期設定
 */
const wgtConfirmWeighingSOPInit = async () => {
  // 選択行情報を初期化
  selectedRow = null;

  // SOPフロー一覧取得のAPI呼び出しと反映
  try {
    await requestApiGetWeighingSOPConfirmInit();
  } catch {
    return;
  }
  // NOTE:ボタンに権限付与は親から渡されたものを設定するため、初期化はここで行う。
  tablePropsDataWeighingSOPConfirmInitListRef.value.onSelectBtns = [
    {
      type: 'primary',
      text: 'Wgt.Chr.btnFlowDetail', // 作業詳細
      // NOTE:選択チェック用に権限付与。
      tabulatorActionId: props.privilegesBtnRequestData.btnId,
      clickHandler: () => {
        if (selectedRow === null || selectedRow.sopFlowLnum === null) {
          return;
        }

        isClickedShowWgtWeighingSOPDetailDialogRef.value =
          !isClickedShowWgtWeighingSOPDetailDialogRef.value;
      },
    },
  ];

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

// ダイアログ起動条件の監視。isClickedの真偽値が切り替わりを監視
watch(() => props.isClicked, wgtConfirmWeighingSOPInit);
</script>
