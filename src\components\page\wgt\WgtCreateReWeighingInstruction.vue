<template>
  <!-- 再秤量指示作成ページ -->
  <el-card class="re-weighing-instruction_search-panel">
    <!-- 見出し：秤量指示書スキャン -->
    <BaseHeading
      level="2"
      fontSize="24px"
      :text="$t('Wgt.Chr.txtWeightInstructionQRScan')"
    />
    <div class="re-weighing-instruction_search-condition">
      <CustomForm
        class="Util_mt-16"
        :triggerRendering="customFormRenderingTriggerRef"
        :formModel="getSearchFormRef.formModel"
        :formItems="getSearchFormRef.formItems"
        width="100%"
        @visible="
          (v: CustomFormType['customForm']) => {
            getSearchFormRef.customForm = v;
          }
        "
        @keyup.enter="keyupEnterHandler"
      />
    </div>
  </el-card>
  <el-card
    v-if="tablePropsDataRef.tableData.length !== 0"
    class="re-weighing-instruction_main-panel"
  >
    <el-row class="re-weighing-instruction_el-row">
      <!-- 秤量指示書番号の見出し+テキスト項目表示 -->
      <div class="re-weighing-instruction_animated-col">
        <InfoShow
          :infoShowItems="processDataInfoShowRef.infoShowItems"
          :isLabelVertical="processDataInfoShowRef.isLabelVertical"
        />
        <BaseHeading
          class="Util_mt-8"
          level="2"
          :text="pageTitle"
          fontSize="24px"
        />
        <!-- 共通のテーブル -->
        <TabulatorTable
          :propsData="tablePropsDataRef"
          :routerName="props.routerInfo.name"
          @selectRow="updateSelectedRow"
        />
        <!-- 小分け登録ダイアログ -->
        <WgtReWeighingInstructionWrapping
          v-if="props.privilegesBtnRequestData[BUTTON_ID.WRAPPING]"
          :isClicked="isClickedReWeighingInstructionWrappingDialogRef"
          :privilegesBtnRequestData="
            props.privilegesBtnRequestData[BUTTON_ID.WRAPPING]
          "
          :selectedRow="selectedRow"
          @submit="getReWeighingInstructionInfo"
        />
        <!-- 明細登録ダイアログ -->
        <WgtReWeighingInstructionDetail
          v-if="props.privilegesBtnRequestData[BUTTON_ID.DETAIL]"
          :isClicked="isClickedReWeighingInstructionDetailDialogRef"
          :privilegesBtnRequestData="
            props.privilegesBtnRequestData[BUTTON_ID.DETAIL]
          "
          :selectedRow="selectedRow"
          @submit="getReWeighingInstructionInfo"
        />
      </div>
    </el-row>
  </el-card>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { onMounted, ref, nextTick } from 'vue';
import {
  GetReWeighingInstructionResList,
  GetReWeighingInstructionResListData,
} from '@/types/HookUseApi/WgtTypes';
import { useGetReWeighingInstruction, useGetUsrGrid } from '@/hooks/useApi';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogProps } from '@/types/MessageBoxTypes';
// 再秤量指示登録(小分け)
import WgtReWeighingInstructionWrapping from '@/components/fragment/wgt/wgtCreateReWeighingInstruction/WgtReWeighingInstructionWrapping.vue';
// 再秤量指示詳細(明細)
import WgtReWeighingInstructionDetail from '@/components/fragment/wgt/wgtCreateReWeighingInstruction/WgtReWeighingInstructionDetail.vue';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { CustomFormType } from '@/types/CustomFormTypes';
import InfoShow from '@/components/parts/InfoShow.vue';
import { InfoShowType } from '@/types/InfoShowTypes';
import { RouterInfoType } from '@/types/RouterTypes';
import { initCommonRequestFromPrivilegesType } from '@/hooks/useApi/util';
import { closeLoading, showLoading } from '@/utils/dialog';
import {
  BUTTON_ID,
  wgtInstGrpNoRules,
  getSearchFormItems,
  getSearchFormModel,
  getProcessDataInfoShowItems,
} from './wgtCreateReWeighingInstruction';

const getSearchFormRef = ref<CustomFormType>({
  formItems: getSearchFormItems(),
  formModel: getSearchFormModel,
});

/**
 * 多言語
 */
const { t } = useI18n();
const pageTitle = t('Wgt.Chr.txtWeightInstructionsDetailList');
const customFormRenderingTriggerRef = ref(false);
// ダイアログの表示切替用定義
const initialState: InitialDialogState<'messageBoxApiErrorVisible'> = {
  messageBoxApiErrorVisible: false,
};
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
// 小分け登録ダイアログ開始' クリック
const isClickedReWeighingInstructionWrappingDialogRef = ref<boolean>(false);
// 明細登録ダイアログ開始' クリック
const isClickedReWeighingInstructionDetailDialogRef = ref<boolean>(false);

// 選択行情報の格納
let selectedRow: GetReWeighingInstructionResListData | null = null;

let reWeighingInstructionData: GetReWeighingInstructionResList = {
  wgtDetailList: [],
  wgtPlanYmd: '',
  wgtInstGrpStsDsp: '',
};

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

const props = defineProps<RouterInfoType>();
const commonActionRequestData = initCommonRequestFromPrivilegesType({
  menu2Scn: props.routerInfo.name.slice(0, -1),
  menu3Act: props.routerInfo.name.at(-1),
  signType: '',
});

const processDataInfoShowRef = ref<InfoShowType>({
  infoShowItems: getProcessDataInfoShowItems(),
  isLabelVertical: false,
});

// 検索用リクエストデータの格納
let wgtInstGrpNoDsp = '';

// 選択行情報の更新
const updateSelectedRow = (v: GetReWeighingInstructionResListData | null) => {
  // 選択行情報を保存
  selectedRow = v;
};

// 秤量指示明細用テーブル設定
const tablePropsDataRef = ref<TabulatorTableIF>({
  pageName: 'Rewgtinst',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'wgtInstNo', // 主キー。ユニークになるものを設定。
  showRadio: true,
  tableBtns: [],
  onSelectBtns: [
    {
      type: 'secondary',
      tabulatorActionId: BUTTON_ID.WRAPPING,
      text: 'Wgt.Chr.btnRegWrapping', // 小分け登録
      clickHandler: () => {
        // 再秤量指示登録(小分け)ダイアログを表示開始
        // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
        isClickedReWeighingInstructionWrappingDialogRef.value =
          !isClickedReWeighingInstructionWrappingDialogRef.value;
      },
    },
    {
      type: 'secondary',
      tabulatorActionId: BUTTON_ID.DETAIL,
      text: 'Wgt.Chr.btnRegDetail', // 明細登録
      clickHandler: () => {
        // 再秤量指示詳細(明細)ダイアログを表示開始
        // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
        isClickedReWeighingInstructionDetailDialogRef.value =
          !isClickedReWeighingInstructionDetailDialogRef.value;
      },
    },
  ],

  column: [
    // 秤量指示明細番号
    {
      title: 'Wgt.Chr.txtWeightInstructionDetailNo',
      field: 'wgtInstNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    // 秤量指示明細状態
    {
      title: 'Wgt.Chr.txtWeightInstructionDetailStatus',
      field: 'wgtInstStsDsp',
      width: COLUMN_WIDTHS.WGT.WGT_INST_STS,
    },
    // 製造品目コード
    {
      title: 'Wgt.Chr.txtMaterialCode',
      field: 'matNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    // 製造品名
    {
      title: 'Wgt.Chr.txtMaterialName',
      field: 'dspNmJp',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    // 製造番号
    {
      title: 'Wgt.Chr.txtLotNo',
      field: 'lotNo',
      width: COLUMN_WIDTHS.LOT_NO,
    },
    // 製造指図番号
    {
      title: 'Wgt.Chr.txtOrderNo',
      field: 'odrNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    // 製造工程
    {
      title: 'Wgt.Chr.txtOrderProcess',
      field: 'prcNmJp',
      width: COLUMN_WIDTHS.PRC_NM,
    },
    // 秤量セットキー
    {
      title: 'Wgt.Chr.txtWeightSetKey',
      field: 'wgtSopsetKey',
      width: COLUMN_WIDTHS.WGT.WGT_SOPSET_KEY,
    },
    // 秤量セット名
    {
      title: 'Wgt.Chr.txtWeightSetName',
      field: 'wgtSopsetNmJp',
      width: COLUMN_WIDTHS.WGT.WGT_ROOM_NM,
    },
    // 秤量室
    {
      title: 'Wgt.Chr.txtWeightRoom',
      field: 'wgtRoomNmJp',
      width: COLUMN_WIDTHS.WGT.WGT_ROOM_NM,
    },
    // 秤量前SOPフローNo
    {
      title: 'Wgt.Chr.txtBeforeWeightSOPFlowNo',
      field: 'wgtbSopFlowNo',
      width: COLUMN_WIDTHS.WGT.WGT_SOP_FLOW_NO,
    },
    // 秤量前SOPフロー名
    {
      title: 'Wgt.Chr.txtBeforeWeightSOPFlowName',
      field: 'wgtbSopFlowNmJp',
      width: COLUMN_WIDTHS.SOP_FLOW_NM,
    },
    // 秤量後SOPフローNo
    {
      title: 'Wgt.Chr.txtAfterWeightSOPFlowNo',
      field: 'wgtaSopFlowNo',
      width: COLUMN_WIDTHS.WGT.WGT_SOP_FLOW_NO,
    },
    // 秤量後SOPフローNo
    {
      title: 'Wgt.Chr.txtAfterWeightSOPFlowName',
      field: 'wgtaSopFlowNmJp',
      width: COLUMN_WIDTHS.SOP_FLOW_NM,
    },
    // 秤量指示書フォーマット
    {
      title: 'Wgt.Chr.txtWeightInstructionFormat',
      field: 'docWgtFormat',
      width: COLUMN_WIDTHS.WGT.DOC_WGT_FORMAT,
    },
    // 計量器番号
    {
      title: 'Wgt.Chr.txtWeightDeviceNo',
      field: 'wgtDeviceNo',
      width: COLUMN_WIDTHS.WGT.DEVICE_NO,
    },
    // 計量器名
    {
      title: 'Wgt.Chr.txtWeightDeviceName',
      field: 'deviceNmJp',
      width: COLUMN_WIDTHS.WGT.DEVICE_NM,
    },
    // 秤量方法
    {
      title: 'Wgt.Chr.txtWeightMethod',
      field: 'wgtMtdNmJp',
      width: COLUMN_WIDTHS.WGT.WGT_MTD_NM,
    },
    // バッチ番号
    {
      title: 'Wgt.Chr.txtBatchNo',
      field: 'batchNo',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.BAT_NO,
    },
    // 秤量品目コード
    {
      title: 'Wgt.Chr.txtWeightMaterialCode',
      field: 'bomMatNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    // 秤量品名
    {
      title: 'Wgt.Chr.txtWeightMaterialName',
      field: 'wgtDspNmJp',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    // 指図指示量
    {
      title: 'Wgt.Chr.txtOrderInstructionValue',
      field: 'bomPlanQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.INST_QTY,
    },
    // 単位
    {
      title: 'Wgt.Chr.txtUnit',
      field: 'unitNmJp',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    // 秤量順
    {
      title: 'Wgt.Chr.txtWeightSequence',
      field: 'wgtSeq',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT.WGT_SEQ,
    },
    // 秤量品投入番号
    {
      title: 'Wgt.Chr.txtWeightMaterialSequence',
      field: 'bomMatSeq',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT.BOM_MAT_SEQ,
    },
    // 補正計算
    {
      title: 'Wgt.Chr.txtWeightTiterKey',
      field: 'wgtTiterKeyDsp',
      width: COLUMN_WIDTHS.WGT.WGT_TITER_KEY,
    },
    // 製造記録確認済フラグ
    {
      title: 'Wgt.Chr.txtPrcRecConfirmFlag',
      field: 'recConfirmFlgDsp',
      width: COLUMN_WIDTHS.WGT.REC_CONFIRM_FLG,
    },
    // 指図中止フラグ
    {
      title: 'Wgt.Chr.txtLotoutFlag',
      field: 'lotoutFlgDsp',
      width: COLUMN_WIDTHS.WGT.LOTOUT_FLG,
    },
  ],
  tableData: [],
  usrGridInfo: {
    colSort: [],
    colHide: [],
  },
  title: pageTitle, // csv出力名
  showConditionSearch: true, // カラム編集、CSV出力表示に必要
  noUseConditionSearch: false, // ConditionSearch関連を出す
  hideSearchHeader: true, // 絞り込み不要
});

// 検索用フォームのフォーカス状態の設定
// isFocus: trueでフォーカス、falseでフォーカスを外す
const setFocusStateSearchForm = (isFocus: boolean) => {
  if (getSearchFormRef.value.customForm === undefined) {
    return;
  }

  if (isFocus) {
    getSearchFormRef.value.customForm.$el.querySelector('input').focus();
  } else {
    getSearchFormRef.value.customForm.$el.querySelector('input').blur();
  }
};

/**
 * 再秤量指示作成の初期設定
 */
const wgtCreateReWeighingInstructionInit = async () => {
  showLoading();

  const usrGridResData = await useGetUsrGrid(props.pageCommonRequest);
  if (usrGridResData) {
    tablePropsDataRef.value.usrGridInfo!.colSort = usrGridResData.colSort;
    tablePropsDataRef.value.usrGridInfo!.colHide = usrGridResData.colHide;
  }

  // QRコード読み取りのため、秤量指示書番号の入力欄をアクティブの状態にする。
  setFocusStateSearchForm(true);

  closeLoading();
};

/**
 * 再秤量指示情報取得
 */
const getReWeighingInstructionInfo = async (requestData: CommonRequestType) => {
  // NOTE:再検索用初期化処理 エラー時に前のテーブルデータが残ってしまう問題があるため。
  tablePropsDataRef.value.tableData = [];

  showLoading();
  const { responseRef, errorRef } = await useGetReWeighingInstruction({
    wgtInstGrpNo: wgtInstGrpNoDsp,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();

    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return false;
  }
  if (responseRef.value) {
    // テーブル表示データ
    tablePropsDataRef.value.tableData =
      responseRef.value.data.rData.wgtDetailList;

    reWeighingInstructionData = responseRef.value.data.rData;
    Object.keys(reWeighingInstructionData).forEach((key) => {
      if (
        processDataInfoShowRef.value.infoShowItems &&
        key in processDataInfoShowRef.value.infoShowItems
      ) {
        processDataInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          reWeighingInstructionData[
            key as keyof GetReWeighingInstructionResList
          ]?.toString() ?? '';
      }
    });
  }
  closeLoading();
  return true;
};

/**
 * 全ての秤量指示書明細一覧を取得
 */
const keyupEnterHandler = async () => {
  // フォームが存在しない場合は処理を終了
  if (getSearchFormRef.value.customForm === undefined) {
    return;
  }
  // フォームのバリデーション
  await getSearchFormRef.value.customForm.validate(async (isValid) => {
    if (!isValid) {
      return;
    }
    // 検索データを格納(再検索処理用)
    wgtInstGrpNoDsp =
      getSearchFormRef.value.formItems.wgtInstGrpNo.formModelValue.toString();
    // 再秤量指示情報取得
    const infoRes = await getReWeighingInstructionInfo({
      ...commonActionRequestData,
      btnId: BUTTON_ID.SCAN,
    });
    // 成功した場合の処理
    if (infoRes) {
      // 追加処理: 秤量指示書番号とフォームの更新
      getSearchFormRef.value.formItems.wgtInstGrpNoDsp.formModelValue =
        getSearchFormRef.value.formItems.wgtInstGrpNo.formModelValue;
      getSearchFormRef.value.formItems.wgtInstGrpNo.formModelValue = '';
      getSearchFormRef.value.formItems.wgtInstGrpNo.tags = [
        { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
      ];
      // 検索後も秤量指示書番号の入力条件を維持
      getSearchFormRef.value.formItems.wgtInstGrpNo.rules = wgtInstGrpNoRules;
      customFormRenderingTriggerRef.value =
        !customFormRenderingTriggerRef.value;

      // 入力欄のフォーカスを外す
      setFocusStateSearchForm(false);
    }
  });
};

onMounted(async () => {
  // NOTE:nextTickは無くても動作するが他ソースで同様の実装があり、統一のため記載
  await nextTick();
  wgtCreateReWeighingInstructionInit();
});
</script>
<style lang="scss" scoped>
$namespace: 're-weighing-instruction';

.#{$namespace} {
  &_search-panel {
    background-color: $white750;
    flex-shrink: 0;
  }

  &_main-panel {
    background-color: $white750;
    display: flex;
    flex-direction: column;
    flex-grow: 1;

    /* element plus */
    :deep(.el-card__body) {
      padding-top: 32px;
      padding-bottom: 0;
      padding-inline: 16px;
      display: flex;
      flex-direction: column;
      flex-grow: 1;
    }
  }
  &_search-condition {
    display: flex;
    flex-wrap: nowrap;
    position: relative;
    box-sizing: border-box;
    :deep(.custom-form_suffix) {
      font-size: 14px;
      margin-left: 16px;
      position: unset;
    }
  }
  &_search-text {
    margin-top: 32px;
    margin-left: 16px;
    height: 32px;
    line-height: 32px;
  }
  &_el-row {
    flex-grow: 1;
  }

  &_suffix {
    font-size: 10px;
    padding-left: 5px;
  }
  &_animated-col {
    display: flex;
    flex-direction: column;
    width: 100%;
    .bottom-action-btn {
      &_wrap {
        display: flex;
        flex-wrap: wrap-reverse;
        justify-content: end;
        gap: 16px 40px;
        margin-top: auto;
        padding-bottom: 16px;
        flex-shrink: 0;
      }
      &_list {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        min-width: 0;
      }
    }
  }
}
</style>
