<template>
  <!-- 照査完了確認ダイアログ -->
  <!-- 見出し 照査完了確認 -->
  <DialogWindow
    :title="$t('Sjg.Chr.txtConfirmInspectionCompletion')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    :class="'odr-regist-rebatch-instruction'"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    width="1400"
  >
    <div>
      <!-- 照査完了確認の見出し+テキスト項目表示 -->
      <BaseHeading
        level="2"
        :text="$t('Sjg.Chr.txtInfoItem')"
        fontSize="24px"
      />
      <InfoShow
        class="Util_mt-16"
        :infoShowItems="processDataInfoShowRef.infoShowItems"
        :isLabelVertical="processDataInfoShowRef.isLabelVertical"
        fontSizeLabel="12px"
        fontSizeContent="16px"
      />
    </div>
    <!-- 照査結果一覧 -->
    <BaseHeading
      level="2"
      :text="$t('Sjg.Chr.btnInspectionResults')"
      fontSize="24px"
      class="Util_mt-48"
    />
    <!--  照査結果一覧 テーブル -->
    <TabulatorTable :propsData="tablePropsDataVerifyResultListRef" />

    <!-- 見出し GMP確認結果詳細 -->
    <BaseHeading
      level="2"
      :text="$t('Sjg.Chr.txtGMPInformationResults')"
      fontSize="24px"
      class="Util_mt-24"
    />
    <!-- GMP確認結果詳細テーブル -->
    <TabulatorTable :propsData="tablePropsDataGMPInfoListRef" />

    <CustomForm
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="getCustomFormRef.formModel"
      :formItems="getCustomFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          getCustomFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />

  <!-- 照査完了確認のメッセージボックス -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxConfirm"
    :dialogProps="messageBoxConfirmProps"
    :cancelCallback="() => closeDialog('messageBoxConfirm')"
    :submitCallback="
      () => {
        closeDialog('messageBoxConfirm');
        clickConfirmBtn(messageBoxConfirmProps);
      }
    "
  />
  <MessageBox
    v-if="dialogVisibleRef.sjgInfoValidCheckError"
    :dialogProps="messageBoxSjgInfoValidCheckErrorRef"
    :submitCallback="() => closeSjgInfoValidCheckError()"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { ref, watch } from 'vue';
import { rules } from '@/utils/validator';
import SCREENID from '@/constants/screenId';
import onValidateHandler from '@/utils/validateHandler';
import BaseHeading from '@/components/base/BaseHeading.vue';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import CustomForm from '@/components/parts/CustomForm.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import {
  useGetVerifyResult,
  useGetComboBoxDataStandard,
  useCheckInitial,
  useModifyVerifyFinish,
  useCheckFinAll,
  useCheckValidVerify,
} from '@/hooks/useApi';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import { CustomFormType } from '@/types/CustomFormTypes';
import { SelectInspectionItemData } from '@/types/HookUseApi/SjgTypes';
import {
  CommonRequestType,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import {
  tablePropsDataVerifyResultList,
  tablePropsDataGMPInfoList,
  getProcessDataInfoShowItems,
  inspectionCompletionFormModel,
  getInspectionCompletionFormItems,
} from './sjgInspectionCompletionConfirmation';

// [W181310]照査完了確認
/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxConfirm'
  | 'sjgInfoValidCheckError';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxConfirm: false,
  sjgInfoValidCheckError: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 照査完了確認のメッセージボックス
const messageBoxConfirmProps: DialogProps = {
  title: t('Sjg.Msg.titleConfirmCompletion'), // レスポンスで上書きする
  content: t('Sjg.Msg.contentConfirmCompletion'), // レスポンスで上書きする
  type: 'question',
};

// 照査データ有効チェックエラーのメッセージ
const messageBoxSjgInfoValidCheckErrorRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});
const getCustomFormRef = ref<CustomFormType>({
  formItems: getInspectionCompletionFormItems(),
  formModel: inspectionCompletionFormModel,
});

const processDataInfoShowRef = ref<InfoShowType>({
  infoShowItems: getProcessDataInfoShowItems(),
  isLabelVertical: true,
});
const customFormRenderingTriggerRef = ref(false);
// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  privilegesBtnRequestData: CommonRequestType;
  selectedInspectionItemData: SelectInspectionItemData | null;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);
const router = useRouter();
// 照査結果一覧テーブル設定
const tablePropsDataVerifyResultListRef = ref<TabulatorTableIF>({
  ...tablePropsDataVerifyResultList,
});

// GMP確認結果詳細テーブル設定
const tablePropsDataGMPInfoListRef = ref<TabulatorTableIF>({
  ...tablePropsDataGMPInfoList,
});

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
    clickHandler: commonRejectHandler,
  },
  {
    text: t('Sjg.Chr.txtInspectionCompletion'),
    type: 'primary',
    size: 'normal',
    clickHandler: async () => {
      const validate =
        getCustomFormRef.value.customForm !== undefined &&
        (await getCustomFormRef.value.customForm.validate((isValid) => {
          onValidateHandler(isValid);
        }));
      if (validate) {
        openDialog('messageBoxConfirm');
      }
      return false;
    },
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
];

// 初期処理で呼び出される
// 照査完了確認取得APIリクエストとレスポンス情報を格納
const requestApiGetVerifyResult = async () => {
  // 照査完了確認取得のAPIを行う。
  const { responseRef, errorRef } = await useGetVerifyResult({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
    addBomFlg: '1',
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return false; // エラーの場合継続処理させない
  }
  if (responseRef.value) {
    // ダイアログ表示初期値として、レスポンス情報を格納
    const initResponseData = responseRef.value.data.rData;
    // テーブル設定
    tablePropsDataVerifyResultListRef.value.tableData =
      initResponseData.verifyRsltList;

    // コメント必須チェック設定
    if (initResponseData.reqCommentFlg === '0') {
      getCustomFormRef.value.formItems.verifyExpl.rules = [
        rules.length(64, t('Cm.Chr.txtLength', [64])),
      ];
      getCustomFormRef.value.formItems.verifyExpl.tags = [];
      customFormRenderingTriggerRef.value =
        !customFormRenderingTriggerRef.value;
    }

    tablePropsDataGMPInfoListRef.value.tableData = initResponseData.gmpInfoList;
  }
  return true;
};

/**
 * 照査完了確認の完了
 */
const modifyVerifyFinishAPI = async () => {
  // 照査完了確認の完了チェック
  const { responseRef, errorRef } = await useModifyVerifyFinish({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxConfirmProps.title,
    msgboxMsgTxt: messageBoxConfirmProps.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    lotSid: props.selectedInspectionItemData!.lotSid,
    verifyExpl: getCustomFormRef.value.formModel.verifyExpl.toString(),
  });
  if (errorRef.value) {
    closeLoading();
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }
  if (responseRef.value) {
    // 照査完了確認ダイアログを閉める
    closeDialog('fragmentDialogVisible');
    emit('submit', props.privilegesBtnRequestData);
  }
};

const checkValidVerifyHandler = async (messageBoxProps: DialogProps) => {
  const apiRequestData: ExtendCommonRequestType<{ lotSid: string }> = {
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxProps.title,
    msgboxMsgTxt: messageBoxProps.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    lotSid: props.selectedInspectionItemData!.lotSid,
  };
  // 照査情報有効チェック
  const { errorRef } = await useCheckValidVerify(apiRequestData);
  if (errorRef.value) {
    closeLoading();
    messageBoxSjgInfoValidCheckErrorRef.value.title =
      errorRef.value.response.rTitle;
    messageBoxSjgInfoValidCheckErrorRef.value.content =
      errorRef.value.response.rMsg;
    openDialog('sjgInfoValidCheckError');
    return false;
  }
  return true;
};

/**
 * 照査完了ボタンの動作
 */
const clickConfirmBtn = async (messageBoxProps: DialogProps) => {
  showLoading();
  if (!(await checkValidVerifyHandler(messageBoxProps))) {
    return;
  }
  // 照査未実施チェック
  const { responseRef, errorRef } = await useCheckInitial({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxProps.title,
    msgboxMsgTxt: messageBoxProps.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    lotSid: props.selectedInspectionItemData!.lotSid,
  });
  if (errorRef.value) {
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    // 顔認証を行うための画面(W99A200)から顔認証を行う必要がある
    // 認証が成功した場合、下記の処理を行う
    try {
      // 署名ダイアログを表示
      await showSignDialog({
        commonRequestParam: {
          ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
        },
      });
    } catch (error) {
      return;
    }
    await modifyVerifyFinishAPI();
  }
  closeLoading();
};

const closeSjgInfoValidCheckError = async () => {
  closeDialog('sjgInfoValidCheckError');
  router.push({
    name: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
    state: {
      routerName: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
      searchConditionData: window.history.state.conditionData,
      tableSearchData: window.history.state.tableSearchData,
    },
  });
};

/**
 * 照査完了確認ダイアログの初期設定
 */
const sjgConfirmInspectionCompletionInit = async () => {
  if (!props.selectedInspectionItemData) return;
  showLoading();
  updateDialogChangeFlagRef(false);
  // 照査完了確認情報レイアウト用初期値設定
  getCustomFormRef.value.formItems = getInspectionCompletionFormItems();
  processDataInfoShowRef.value.infoShowItems = getProcessDataInfoShowItems();
  Object.entries(props.selectedInspectionItemData).forEach(([key, value]) => {
    if (key in processDataInfoShowRef.value.infoShowItems) {
      processDataInfoShowRef.value.infoShowItems[key].infoShowModelValue =
        value?.toString() ?? '';
    }
  });
  // 照査完了全チェック
  const { responseRef, errorRef } = await useCheckFinAll({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }
  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }
  // 標準コンボボックスデータ取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtSjgVeriFin',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'SJG_VERIFY_FIN' },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setCustomFormComboBoxOptionList(
      getCustomFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  // 照査完了確認取得APIリクエストとレスポンス情報を格納
  const result = await requestApiGetVerifyResult();
  if (!result) {
    return;
  }
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
  closeLoading();
};
watch(() => props.isClicked, sjgConfirmInspectionCompletionInit);
</script>
<style lang="scss" scoped></style>
