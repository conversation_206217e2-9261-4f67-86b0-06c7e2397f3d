import CONST from '@/constants/utils';
import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// 標準コンボボックスとカスタムフォームを紐づけるID
export const COMBINE_ID = {
  REC_MOD_EXPL: 'prdConfSopModExpl',
} as const;

// 縦並び項目定義
export const getInfoShowItems: () => InfoShowType['infoShowItems'] = () => ({
  // 作業指示内容
  cmtMain1: {
    label: { text: t('Prd.Chr.txtWorkInstructionDetail') },
    infoShowModelValue: '',
    infoShowRole: 'text',
    span: 24,
  },
  cmtMain2: {
    infoShowModelValue: '',
    infoShowRole: 'text',
    span: 24,
  },
  cmtMain3: {
    infoShowModelValue: '',
    infoShowRole: 'text',
    span: 24,
  },
  // 指示値
  instVal: {
    label: { text: t('Prd.Chr.txtInstructionValue') },
    infoShowModelValue: '',
    infoShowRole: 'text',
    span: 24,
  },
});

// ダイアログのカスタムフォーム定義
export const getDialogFormItems: () => CustomFormType['formItems'] = () => ({
  // 記録値1
  recVal1: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    label: { text: t('Prd.Chr.txtRecordValue1') },
    formModelValue: '',
    rules: [
      rules.required('textBox'),
      rules.length(64, t('Cm.Chr.txtLength', [64])),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ],
    formRole: 'textBox',
    span: 24,
  },
  // 記録値2
  recVal2: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    label: { text: t('Prd.Chr.txtRecordValue2') },
    formModelValue: '',
    rules: [
      rules.required('textBox'),
      rules.length(64, t('Cm.Chr.txtLength', [64])),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ],
    formRole: 'textBox',
    span: 24,
  },
  // 記録値3
  recVal3: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    label: { text: t('Prd.Chr.txtRecordValue3') },
    formModelValue: '',
    rules: [
      rules.required('textBox'),
      rules.length(64, t('Cm.Chr.txtLength', [64])),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ],
    formRole: 'textBox',
    span: 24,
  },
  // 記録値4
  recVal4: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    label: { text: t('Prd.Chr.txtRecordValue4') },
    formModelValue: '',
    rules: [
      rules.required('textBox'),
      rules.length(64, t('Cm.Chr.txtLength', [64])),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ],
    formRole: 'textBox',
    span: 24,
  },
  // 記録値5
  recVal5: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    label: { text: t('Prd.Chr.txtRecordValue5') },
    formModelValue: '',
    rules: [
      rules.required('textBox'),
      rules.length(64, t('Cm.Chr.txtLength', [64])),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ],
    formRole: 'textBox',
    span: 24,
  },
  // 修正コメント入力
  recModExpl: {
    formModelValue: '',
    label: { text: t('Prd.Chr.txtModifyCommentInput') },
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    rules: [rules.required('textBox'), rules.length(64)],
    formRole: 'textComboBox',
    selectOptions: [],
    cmbId: COMBINE_ID.REC_MOD_EXPL,
    span: 24,
  },
});

// ダイアログのカスタムフォームモデル定義
export const dialogFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getDialogFormItems());
