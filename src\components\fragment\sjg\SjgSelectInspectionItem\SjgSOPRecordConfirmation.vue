<template>
  <!-- SOP記録確認ダイアログ -->
  <!-- 見出し SOP記録確認 -->
  <DialogWindow
    :title="dialogConfigTitle"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    width="1776"
  >
    <!--  品目情報 -->
    <BaseHeading level="2" :text="$t('Sjg.Chr.txtInfoItem')" fontSize="24px" />
    <!-- 照査情報の見出し+テキスト項目表示 -->
    <div class="Util_mt-16">
      <InfoShow
        :infoShowItems="processDataInfoShowRef.infoShowItems"
        :isLabelVertical="processDataInfoShowRef.isLabelVertical"
        fontSizeLabel="12px"
        fontSizeContent="16px"
      />
    </div>

    <!-- SOP記録 -->
    <BaseHeading
      level="2"
      :text="$t('Sjg.Chr.txtSjgSOPRecord')"
      fontSize="24px"
      class="Util_mt-48"
    />
    <p>{{ $t('Sjg.Chr.txtSjgSOPRecordConfirmationSupplement') }}</p>
    <!--  SOP記録 テーブル -->
    <TabulatorTable
      :propsData="tablePropsDataVerifyResultListRef"
      @clickBtnColumn="clickListItemDetail"
      @selectRows="updateSelectedRows"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <MessageBox
    v-if="dialogVisibleRef.sjgInfoValidCheckError"
    :dialogProps="messageBoxSjgInfoValidCheckErrorRef"
    :submitCallback="() => closeSjgInfoValidCheckError()"
  />
  <!-- SOP作業詳細ダイアログ -->
  <SjgSOPOperationDetails
    :detailLogList="selectedRow"
    :isClicked="isClickedShowSOPOperationDetailsRef"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { ref, watch } from 'vue';
import SCREENID from '@/constants/screenId';
import BaseHeading from '@/components/base/BaseHeading.vue';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import SjgSOPOperationDetails from '@/components/fragment/sjg/SjgSelectInspectionItem/SjgSOPOperationDetails.vue';
import {
  useGetSopRecordList,
  useModifySopRecordVerifyFinish,
  useCheckValidVerify,
} from '@/hooks/useApi';
import {
  VerifyItemList,
  SopRecListData,
  SelectInspectionItemData,
} from '@/types/HookUseApi/SjgTypes';
import {
  CommonRequestType,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import {
  ColorMapping,
  tablePropsDataVerifyResultList,
  getProcessDataInfoShowItems,
} from './sjgSOPRecordConfirmation';

// [W181610]SOP記録確認
/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxRequired'
  | 'sjgInfoValidCheckError';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxRequired: false,
  sjgInfoValidCheckError: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);
const router = useRouter();

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 照査データ有効チェックエラーのメッセージ
const messageBoxSjgInfoValidCheckErrorRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const processDataInfoShowRef = ref<InfoShowType>({
  infoShowItems: getProcessDataInfoShowItems(),
  isLabelVertical: true,
});
const isClickedShowSOPOperationDetailsRef = ref<boolean>(false);
// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  screenId: string;
  privilegesBtnRequestData: CommonRequestType;
  selectedInspectionItemData: SelectInspectionItemData | null;
  selectedRowData: VerifyItemList | null;
  saveFlag: boolean;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 照査結果一覧テーブル設定
const tablePropsDataVerifyResultListRef = ref<TabulatorTableIF>({
  ...tablePropsDataVerifyResultList,
  onSelectBtns: [],
});

type SopRecListDataWithUniqueKey = SopRecListData & {
  uniqueKey: string;
};

let selectedRow: SopRecListDataWithUniqueKey | null = null;
let selectedRows: SopRecListDataWithUniqueKey[] = [];
let newVerifyRsltList: SopRecListDataWithUniqueKey[] = [];
const dialogConfigTitle = ref<string>('');

const checkValidVerifyHandler = async () => {
  const apiRequestData: ExtendCommonRequestType<{ lotSid: string }> = {
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
  };
  // 照査情報有効チェック
  const { errorRef } = await useCheckValidVerify(apiRequestData);
  if (errorRef.value) {
    closeLoading();
    messageBoxSjgInfoValidCheckErrorRef.value.title =
      errorRef.value.response.rTitle;
    messageBoxSjgInfoValidCheckErrorRef.value.content =
      errorRef.value.response.rMsg;
    openDialog('sjgInfoValidCheckError');
    return false;
  }
  return true;
};
/**
 * 保存ボタンの動作
 */
const clickConfirmBtn = async () => {
  // 全てにチェックがついていない場合はチェックNG
  if (
    selectedRows.length !==
    tablePropsDataVerifyResultListRef.value.tableData.length
  ) {
    messageBoxApiErrorPropsRef.value.title = t(
      'Sjg.Msg.titleRecordConfirmationCheckBox',
    );
    messageBoxApiErrorPropsRef.value.content = t(
      'Sjg.Msg.contentRecordConfirmationCheckBox',
    );
    openDialog('messageBoxApiErrorVisible');
    return;
  }

  showLoading();
  if (!(await checkValidVerifyHandler())) {
    return;
  }
  // SOP記録確認完了
  const { errorRef } = await useModifySopRecordVerifyFinish({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
  });
  if (errorRef.value) {
    closeLoading();
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    messageBoxApiErrorPropsRef.value.type = 'error';
    openDialog('messageBoxApiErrorVisible');
    return;
  }
  closeLoading();
  closeDialog('fragmentDialogVisible');
  emit('submit');
};

const buttonsList: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    disabled: false,
    clickHandler: async () => {
      if (props.screenId === SCREENID.SJG_SOP_RECORD_CONFIRMATION) {
        if (await commonRejectHandler()) {
          closeDialog('fragmentDialogVisible');
          emit('submit', props.privilegesBtnRequestData);
        }
      } else {
        closeDialog('fragmentDialogVisible');
      }
      return true;
    },
  },
  {
    text: t('Cm.Chr.btnTemporary'),
    type: 'primary',
    size: 'normal',
    clickHandler: () => {
      clickConfirmBtn();
      return false;
    },
  },
];
// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons = ref<DialogWindowProps['buttons']>(buttonsList);
// フッターの確認完了ボタンを非表示にする。
const setFootBtnDisable = () => {
  // 照査完了後の確認の場合
  let dialogButtonsTemp: DialogWindowProps['buttons'] = [];
  if (props.selectedRowData?.verifyCatSts === 'FN') {
    dialogButtonsTemp = buttonsList.slice(0, 1);
  } else {
    // YES
    dialogButtonsTemp = buttonsList;
  }
  dialogButtons.value = dialogButtonsTemp;
};

const closeSjgInfoValidCheckError = async () => {
  closeDialog('sjgInfoValidCheckError');
  router.push({
    name: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
    state: {
      routerName: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
      searchConditionData: window.history.state.conditionData,
      tableSearchData: window.history.state.tableSearchData,
    },
  });
};

const clickListItemDetail = (v: SopRecListDataWithUniqueKey) => {
  selectedRow = v;
  if (
    props.saveFlag ||
    props.screenId === SCREENID.SJG_SOP_RECORD_CONFIRMATION
  ) {
    if (selectedRows.length > 0) {
      const uniqueKeyArray: string[] = selectedRows.map((obj) => obj.uniqueKey);
      tablePropsDataVerifyResultListRef.value.selectRowsData = uniqueKeyArray;
    }
    const selectRowsDataMap = new Set(
      tablePropsDataVerifyResultListRef.value.selectRowsData,
    );
    if (!selectRowsDataMap.has(v.uniqueKey)) {
      tablePropsDataVerifyResultListRef.value.selectRowsData?.push(v.uniqueKey);
    }
    tablePropsDataVerifyResultListRef.value.selectRowData = v.uniqueKey;
    tablePropsDataVerifyResultListRef.value.tableData = JSON.parse(
      JSON.stringify(newVerifyRsltList),
    );
  }
  //  SOP作業詳細(W181910)ダイアログを表示する。
  isClickedShowSOPOperationDetailsRef.value =
    !isClickedShowSOPOperationDetailsRef.value;
};
// 選択行情報の更新
const updateSelectedRows = (v: SopRecListDataWithUniqueKey[]) => {
  // 選択複数行情報を保存
  selectedRows = v;
  if (v.length > 0) {
    updateDialogChangeFlagRef(true);
  } else {
    updateDialogChangeFlagRef(false);
  }
};

const getSopRecordList = async () => {
  // SOP記録一覧取得のAPIを行う。
  const { responseRef, errorRef } = await useGetSopRecordList({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
  });
  if (errorRef.value) {
    closeLoading();
    newVerifyRsltList = [];
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }
  if (responseRef.value) {
    // テーブル設定
    // テーブル設定
    const tableData = responseRef.value.data.rData.sopRecList;
    newVerifyRsltList = tableData.map((item, i) => {
      let backgroundColor = '';
      const detailsBtn = 0;
      switch (item.devCorrLv) {
        case '1':
          backgroundColor = ColorMapping.YELLOW;
          break;
        case '2':
          backgroundColor = ColorMapping.ORANGE;
          break;
        case '3':
          backgroundColor = ColorMapping.RED;
          break;
        default:
          backgroundColor = '';
          break;
      }
      const cmtMainWithBr = item.cmtMain?.replace(/\n/g, '<br>') ?? '';
      return {
        ...item,
        cmtMain: cmtMainWithBr,
        backgroundColor,
        detailsBtn,
        uniqueKey: `${item.odrNo}-${i}`,
      };
    });
    tablePropsDataVerifyResultListRef.value.tableData = newVerifyRsltList;
  }
};

/**
 * SOP記録確認ダイアログの初期設定
 */
const sjgSOPRecordConfirmationInit = async () => {
  if (!props.selectedRowData) return;
  if (!props.selectedInspectionItemData) return;
  showLoading();
  // 選択行情報初期化
  tablePropsDataVerifyResultListRef.value.tableData = [];
  tablePropsDataVerifyResultListRef.value.selectRowsData = [];
  selectedRows = [];
  await getSopRecordList();

  updateDialogChangeFlagRef(false);
  processDataInfoShowRef.value.infoShowItems = getProcessDataInfoShowItems();
  if (
    props.selectedInspectionItemData &&
    props.selectedInspectionItemData.lotSid !== ''
  ) {
    Object.entries(props.selectedInspectionItemData).forEach(([key, value]) => {
      if (key in processDataInfoShowRef.value.infoShowItems) {
        processDataInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });
  }
  setFootBtnDisable();
  // SOP記録確認(確認)ダイアログ(W181611)
  if (props.screenId === SCREENID.SJG_SOP_RECORD_CONFIRMATION_CONFIRMATION) {
    dialogButtons.value = buttonsList.slice(0, 1);
    dialogConfigTitle.value = t(
      'Sjg.Chr.txtSjgSOPRecordConfirmationConfirmation',
    );
    if (props.selectedRowData?.verifyCatSts === 'FN' || props.saveFlag) {
      const uniqueKeyArray: string[] =
        tablePropsDataVerifyResultListRef.value.tableData
          .map((obj) => obj.uniqueKey)
          .filter(
            (key): key is string => typeof key === 'string' && key !== '',
          ) ?? [];

      tablePropsDataVerifyResultListRef.value.selectRowsData = uniqueKeyArray;
    }
  } else {
    dialogConfigTitle.value = t('Sjg.Chr.txtSjgSOPRecordConfirmation');
  }
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, sjgSOPRecordConfirmationInit);
</script>
<style lang="scss" scoped></style>
