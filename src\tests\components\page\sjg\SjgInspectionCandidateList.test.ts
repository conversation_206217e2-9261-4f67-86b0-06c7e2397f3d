import { ref } from 'vue';
import { beforeEach, describe, expect, it, vi, Mock, afterEach } from 'vitest';
import { flushPromises, mount, VueWrapper } from '@vue/test-utils';
import SjgInspectionCandidateList from '@/components/page/sjg/SjgInspectionCandidateList.vue';
import { createP<PERSON>, setActivePinia } from 'pinia';
import stores from '@/store';
import { createRouter, createWebHistory, Router } from 'vue-router';
import { loginData } from '@/tests/helpers/mockData/requestData';
import {
  useGetVerifyList,
  useAddLotVerify,
  useGetComboBoxDataStandard,
  useGetUsrGrid,
} from '@/hooks/useApi';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import ConditionSearch from '@/components/model/ConditionSearch/ConditionSearch.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import mountOption from '@/tests/helpers/mountOption';
import resizeObserverMock from '@/tests/helpers/mockData/resizeObserverMock';
import routeMock from '@/tests/helpers/mockData/routeMock';
import {
  mockTableData1,
  mockSelectedRowData,
  mockGetVerifyList,
  mockAddLotVerify,
  mockRCode1000,
} from '@/tests/helpers/mockData/sjgInspectionCandidateListMockData';
import {
  conditionSearchBtnClickTrigger,
  TabulatorTableBtnClickTrigger,
  btnClickTrigger,
} from '@/tests/helpers/trigger';
import MESSAGE_BOX_SELECTOR from '@/tests/helpers/constants/messageBox';
import { mockCommonComboDataStandard } from '@/tests/helpers/mockData/commonComboDataStd';
import {
  createMockPageCommonRequest,
  createMockPrivilegesBtnRequestData,
} from '@/tests/helpers/createCommonRequestData';
import { mockUseGetUsrGridData } from '@/tests/helpers/mockData/tabulatorTableMockData';

resizeObserverMock();

let wrapper: VueWrapper<InstanceType<typeof SjgInspectionCandidateList>>;
let router: Router;

beforeEach(async () => {
  setActivePinia(createPinia());
  stores.usr().privileges = loginData.rData.privilegeList;
  router = createRouter({
    history: createWebHistory(),
    routes: routeMock,
  });
  (useGetUsrGrid as Mock).mockResolvedValue({
    responseRef: ref({ data: mockUseGetUsrGridData }),
    errorRef: ref(undefined),
  });
});

afterEach(() => {
  if (wrapper) wrapper.unmount();
});

const getMountComponent = (screenId: string) =>
  mount(SjgInspectionCandidateList, {
    global: {
      plugins: [...mountOption.global.plugins, router],
    },
    props: {
      routerInfo: {
        name: screenId,
        title: 'Cm.Chr.Menu.txtSjgInspectionCandidateList',
      },
      pageCommonRequest: createMockPageCommonRequest(screenId),
      privilegesBtnRequestData: createMockPrivilegesBtnRequestData(screenId, [
        'btnVerify',
      ]),
    },
  });

vi.mock('@/hooks/useApi', () => ({
  useGetComboBoxDataStandard: vi.fn(),
  useGetVerifyList: vi.fn(),
  useAddLotVerify: vi.fn(),
  useGetUsrGrid: vi.fn(),
}));

describe('InvInventoryListInit', () => {
  it('Should render correctly conditionSearch in case of response success code 200', async () => {
    (useGetComboBoxDataStandard as Mock).mockResolvedValue(
      mockCommonComboDataStandard,
    );
    wrapper = getMountComponent('81100');
    await flushPromises();

    const conditionSearch = wrapper.findComponent(ConditionSearch);
    expect(conditionSearch.exists()).toBe(true);

    const conditionSearchTitle = conditionSearch.findAll(
      '.condition-search_title',
    );
    expect(conditionSearchTitle.length).toEqual(4);
    expect(conditionSearchTitle[0].text()).toEqual('品目コード');
    expect(
      conditionSearch.props().conditionData[3].children![0].defaultValue,
    ).toBe('IC');
  });

  it('Should be set conditionSearch when transitioning from SjgInspectionCandidateList', async () => {
    (useGetComboBoxDataStandard as Mock).mockResolvedValue(
      mockCommonComboDataStandard,
    );
    (useGetVerifyList as Mock).mockResolvedValue({
      responseRef: ref({ data: mockGetVerifyList }),
      errorRef: ref(undefined),
    });
    const mockState = {
      routerName: '81200',
      tableSearchData: '[]',
      searchConditionData:
        '{"matNo":"1000001","lotNo":"240401001","lotoutFlg":"0"}',
      selectedData: {},
    };
    Object.defineProperty(window.history, 'state', {
      value: mockState,
      writable: true,
    });
    wrapper = getMountComponent('81200');
    await flushPromises();
    await wrapper.vm.$nextTick();
    const conditionSearch = wrapper.findComponent(ConditionSearch);
    expect(conditionSearch.exists()).toBe(true);
    expect(
      conditionSearch.props().conditionData[0].children![0].defaultValue,
    ).toBe('1000001');
    expect(
      conditionSearch.props().conditionData[1].children![0].defaultValue,
    ).toBe('240401001');
    expect(
      conditionSearch.props().conditionData[2].children![0].defaultValue,
    ).toBe('0');
  });
});

describe('SearchClickHandler', () => {
  beforeEach(async () => {
    (useGetComboBoxDataStandard as Mock).mockResolvedValue(
      mockCommonComboDataStandard,
    );
    wrapper = getMountComponent('81100');
    await flushPromises();
  });

  it('Should render correctly TabulatorTable with verifyStsInitial data in case of response success code 200', async () => {
    (useGetVerifyList as Mock).mockResolvedValue({
      responseRef: ref({ data: mockGetVerifyList }),
      errorRef: ref(undefined),
    });
    await conditionSearchBtnClickTrigger(
      wrapper,
      '.sjg-inspection-candidate-list',
    );
    const propsData = wrapper.findComponent(TabulatorTable).props('propsData');
    expect(wrapper.find('.tabulator-table-body').exists()).toBe(true);
    expect(propsData?.tableData).toEqual(mockTableData1);
  });

  it('Should render correctly MessageBox in case of response error', async () => {
    (useGetVerifyList as Mock).mockResolvedValue({
      responseRef: ref(undefined),
      errorRef: ref({ response: mockRCode1000 }),
    });
    await conditionSearchBtnClickTrigger(
      wrapper,
      '.sjg-inspection-candidate-list',
    );

    const singleButtonMessageBox = wrapper.findComponent(MessageBox);
    expect(singleButtonMessageBox.exists()).toBe(true);

    expect(
      singleButtonMessageBox.find(MESSAGE_BOX_SELECTOR.TITLE).text(),
    ).toEqual('APIレスポンスタイトル');
    expect(
      singleButtonMessageBox.find(MESSAGE_BOX_SELECTOR.MESSAGE).text(),
    ).toEqual('1000エラー');
    await btnClickTrigger(
      singleButtonMessageBox,
      MESSAGE_BOX_SELECTOR.SINGLE_BTN,
    );

    await flushPromises();
    expect(singleButtonMessageBox.exists()).toBe(false);
  });
});

describe('TabulatorTableBtnClickHandler', () => {
  beforeEach(async () => {
    (useGetComboBoxDataStandard as Mock).mockResolvedValue(
      mockCommonComboDataStandard,
    );
    (useGetVerifyList as Mock).mockResolvedValue({
      responseRef: ref({ data: mockGetVerifyList }),
      errorRef: ref(undefined),
    });
    wrapper = getMountComponent('81100');
    await flushPromises();
  });

  it('Should return when the verify button is clicked no row selected', async () => {
    (useAddLotVerify as Mock).mockResolvedValue({
      responseRef: ref({ data: mockAddLotVerify }),
      errorRef: ref(undefined),
    });
    await TabulatorTableBtnClickTrigger(
      wrapper,
      '照査',
      '.sjg-inspection-candidate-list',
    );
    await flushPromises();
    await router.isReady();

    expect(useAddLotVerify).not.toHaveBeenCalled();
  });

  it('Should execute correctly router.push when the verify button is clicked with a row selected', async () => {
    (useAddLotVerify as Mock).mockResolvedValue({
      responseRef: ref({ data: mockAddLotVerify }),
      errorRef: ref(undefined),
    });

    const pushSpy = vi.spyOn(router, 'push');
    const mockState = {
      routerName: '81200',
      tableSearchData: '[]',
      conditionData:
        '{"matNo":"","lotNo":"","lotoutFlg":"","verifyCompFlg":""}',
      selectedData: {
        expiryYmd: '2025/02/10',
        limsVerifyRecvMatFlg: '0',
        lotNo: '240401001',
        lotSid: '241010-001$001',
        matNm: 'アミノ酸',
        matNo: '1000001',
        rsltYmd: '2025/02/10',
        shtNm: 'CTJ',
        verifyReasonNm: undefined,
      },
    };
    await TabulatorTableBtnClickTrigger(
      wrapper,
      '照査',
      '.sjg-inspection-candidate-list',
      { emitSelectRowData: mockSelectedRowData },
    );
    await flushPromises();
    await router.isReady();

    expect(pushSpy).toHaveBeenCalledWith({
      name: '81200',
      state: mockState,
    });
  });
  it('Should render error messageBox when useAddLotVerify response error', async () => {
    (useAddLotVerify as Mock).mockResolvedValue({
      responseRef: ref(undefined),
      errorRef: ref({ response: mockRCode1000 }),
    });

    await TabulatorTableBtnClickTrigger(
      wrapper,
      '照査',
      '.sjg-inspection-candidate-list',
      { emitSelectRowData: mockSelectedRowData },
    );
    await flushPromises();
    const singleButtonMessageBox = wrapper.findComponent(MessageBox);
    expect(singleButtonMessageBox.exists()).toBe(true);

    expect(
      singleButtonMessageBox.find(MESSAGE_BOX_SELECTOR.TITLE).text(),
    ).toEqual('APIレスポンスタイトル');
    expect(
      singleButtonMessageBox.find(MESSAGE_BOX_SELECTOR.MESSAGE).text(),
    ).toEqual('1000エラー');
    await btnClickTrigger(
      singleButtonMessageBox,
      MESSAGE_BOX_SELECTOR.SINGLE_BTN,
    );
  });
});

it('Should render correctly conditionSearch when showCondition btn click', async () => {
  (useGetComboBoxDataStandard as Mock).mockResolvedValue(
    mockCommonComboDataStandard,
  );
  wrapper = getMountComponent('81100');
  await flushPromises();
  const tabulatorTable = wrapper.findComponent(TabulatorTable);
  expect(tabulatorTable.props('propsData')?.showConditionSearch).toBe(true);
  await TabulatorTableBtnClickTrigger(
    wrapper,
    '絞り込み',
    '.sjg-inspection-candidate-list',
  );

  expect(tabulatorTable.props('propsData')?.showConditionSearch).toBe(false);

  await TabulatorTableBtnClickTrigger(
    wrapper,
    '絞り込み',
    '.sjg-inspection-candidate-list',
  );
  expect(tabulatorTable.props('propsData')?.showConditionSearch).toBe(true);
});
