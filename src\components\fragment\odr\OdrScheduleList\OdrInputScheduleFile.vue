<template>
  <!-- 計画取込ダイアログ -->
  <DialogWindow
    :title="$t('Odr.Chr.txtPlanImport')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <div class="top-wide">
      <el-row>
        <el-col>
          <ButtonEx
            id="fileInput"
            type="secondary"
            size="small"
            :text="$t('Odr.Chr.btnCSVImport')"
            @click="commDeviceCsvImport"
          />
        </el-col>
      </el-row>
    </div>
    <input
      ref="fileInput"
      type="file"
      @change="importCSV"
      accept=".csv"
      style="display: none"
      aria-labelledby="fileInput"
    />
    <!--CSVファイル明細テーブル -->
    <TabulatorTable :propsData="tablePropsDataOrderInputScheduleFileRef" />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- CSVインポート時のエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxImportErrorVisible"
    :dialogProps="messageBoxImportErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxImportErrorVisible')"
  />
  <!-- 実行ボタン押下時のCSV未インポートエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxNotImportErrorVisible"
    :dialogProps="messageBoxNotImportErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxNotImportErrorVisible')"
  />
  <!-- 計画取込の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxOrderInputScheduleFileVisible"
    :dialogProps="messageBoxOrderInputScheduleFilePropsRef"
    :cancelCallback="
      () => closeDialog('messageBoxOrderInputScheduleFileVisible')
    "
    :submitCallback="requestApiAddScheduleImport"
  />
  <!-- 計画取込完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxOrderInputScheduleFileFinishedVisible"
    :dialogProps="messageBoxOrderInputScheduleFileFinishedPropsRef"
    :submitCallback="
      closeDialogFromMessageBoxModifyOrderInputScheduleFileFinished
    "
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import { DialogProps } from '@/types/MessageBoxTypes';
import { AddScheduleImportRequestData } from '@/types/HookUseApi/OdrTypes';
import { useAddScheduleImport } from '@/hooks/useApi';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import ButtonEx from '@/components/parts/ButtonEx.vue';
import {
  CSV_COLUMN_NAMES,
  tablePropsDataOrderInputScheduleFileRef,
} from './odrInputScheduleFile';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxImportErrorVisible'
  | 'messageBoxNotImportErrorVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxOrderInputScheduleFileVisible'
  | 'messageBoxOrderInputScheduleFileFinishedVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxImportErrorVisible: false,
  messageBoxNotImportErrorVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxOrderInputScheduleFileVisible: false,
  messageBoxOrderInputScheduleFileFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// インポート時のエラーメッセージボックス
const messageBoxImportErrorPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

// 実行ボタン押下時のCSV未インポートのエラーメッセージボックス
const messageBoxNotImportErrorPropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titleCsvNotImported'),
  content: t('Odr.Msg.contentCsvNotImported'),
  isSingleBtn: true,
  type: 'error',
});

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 計画取込の確認メッセージボックス
const messageBoxOrderInputScheduleFilePropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titleOrderInputScheduleFile'),
  content: t('Odr.Msg.contentOrderInputScheduleFile'),
  type: 'question',
});

// 計画取込の完了メッセージボックス
const messageBoxOrderInputScheduleFileFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  privilegesBtnRequestData: CommonRequestType;
};
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

const fileInput = ref();

// CSVファイルのカラム数
const CSV_COLUMN_COUNT = CSV_COLUMN_NAMES.length;

// CSVカラム名のインデックス
const CSV_COLUMN_INDEX: { [key: string]: number } = {};
CSV_COLUMN_NAMES.forEach((col, index) => {
  CSV_COLUMN_INDEX[col] = index;
});

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // CSVファイル未インポートならエラー表示
  if (tablePropsDataOrderInputScheduleFileRef.value.tableData.length === 0) {
    openDialog('messageBoxNotImportErrorVisible');
    return false;
  }

  // 2.以下のメッセージを表示
  // 計画取込の確認
  openDialog('messageBoxOrderInputScheduleFileVisible');
  return false;
};

/**
 * CSVファイルのインポート
 * @param e イベント
 */
const importCSV = async (e: Event) => {
  const file = (e.target as HTMLInputElement).files?.[0];

  // CSVファイル拡張子チェック
  if (!(file && file.name.endsWith('.csv'))) {
    messageBoxImportErrorPropsRef.value.title = `${t('Cm.Chr.txtCSVErrorImport')}`;
    messageBoxImportErrorPropsRef.value.content = `${t('Cm.Chr.txtCSVFileSelect')}`;
    openDialog('messageBoxImportErrorVisible');
    return;
  }

  const text = await file.text();

  // CSVの改行コード判定
  let brCd = '\n';
  if (text.match(/\r\n/)) {
    brCd = '\r\n';
  } else if (text.match(/\r/)) {
    brCd = '\r';
  }

  const rows = text.split(brCd).filter((row) => row.trim() !== '');

  let headers: string[] = [];
  let dataRows: string[] = [];
  headers = rows[0].split(',').map((header) => header.trim());
  dataRows = rows.slice(1).map((dataRow) => dataRow.replace(/"/g, ''));

  // データなしチェック
  if (dataRows.length === 0) {
    messageBoxImportErrorPropsRef.value.title = `${t('Cm.Chr.txtCSVErrorImport')}`;
    messageBoxImportErrorPropsRef.value.content = `${t('Cm.Chr.txtCSVFileEmpty')}`;
    openDialog('messageBoxImportErrorVisible');
    return;
  }

  // ヘッダーカラム数チェック
  if (headers.length !== CSV_COLUMN_COUNT) {
    messageBoxImportErrorPropsRef.value.title = `${t('Cm.Chr.txtCSVErrorImport')}`;
    messageBoxImportErrorPropsRef.value.content = `${t('Cm.Chr.txtCSVFomatError')}`;
    openDialog('messageBoxImportErrorVisible');
    return;
  }

  // カラム名チェック
  const missingColumn = CSV_COLUMN_NAMES.filter(
    (col) => !headers.includes(col),
  );
  if (missingColumn.length > 0) {
    messageBoxImportErrorPropsRef.value.title = `${t('Cm.Chr.txtCSVErrorImport')}`;
    messageBoxImportErrorPropsRef.value.content = `${t('Cm.Chr.txtCSVFomatError')}`;
    openDialog('messageBoxImportErrorVisible');
    return;
  }

  // データ行チェック
  let hasError = false;
  dataRows.some((row, index) => {
    const cols = row.split(',').map((col) => col.trim());

    // データ行カラム数チェック
    if (cols.length !== CSV_COLUMN_COUNT) {
      messageBoxImportErrorPropsRef.value.title = `${t('Cm.Chr.txtCSVErrorImport')}`;
      messageBoxImportErrorPropsRef.value.content = `${t('Cm.Chr.txtCSVFomatError')}`;
      openDialog('messageBoxImportErrorVisible');
      hasError = true;
      return true;
    }

    // 着手予定日未入力チェック
    if (cols[CSV_COLUMN_INDEX.schd_prod_st_dt] === '') {
      messageBoxImportErrorPropsRef.value.title = `${t('Odr.Msg.titleNotInputSkdYmd')}`;
      messageBoxImportErrorPropsRef.value.content = `${t('Odr.Msg.contentNotInputSkdYmd', [index + 1])}`;
      openDialog('messageBoxImportErrorVisible');
      hasError = true;
      return true;
    }

    // 着手予定日の桁数チェック
    // 着手予定日が8桁数値文字列でない場合チェックNG
    if (cols[CSV_COLUMN_INDEX.schd_prod_st_dt].match(/[0-9]{8}/) === null) {
      messageBoxImportErrorPropsRef.value.title = `${t('Odr.Msg.titleFormatErrorSkdYmd')}`;
      messageBoxImportErrorPropsRef.value.content = `${t('Odr.Msg.contentFormatErrorSkdYmd', [index + 1])}`;
      openDialog('messageBoxImportErrorVisible');
      hasError = true;
      return true;
    }

    // MES品目コード未入力チェック
    if (cols[CSV_COLUMN_INDEX.itm_cd] === '') {
      messageBoxImportErrorPropsRef.value.title = `${t('Odr.Msg.titleNotInputMesMatNo')}`;
      messageBoxImportErrorPropsRef.value.content = `${t('Odr.Msg.contentNotInputMesMatNo', [index + 1])}`;
      openDialog('messageBoxImportErrorVisible');
      hasError = true;
      return true;
    }

    return false;
  });
  if (hasError) {
    return;
  }

  const csvData = dataRows.map((line) => line.split(','));

  // テーブルデータを更新
  tablePropsDataOrderInputScheduleFileRef.value.tableData = csvData.map(
    (row) => {
      const rowData = {
        erpifMesId: row[CSV_COLUMN_INDEX.mes_id],
        skdNo: row[CSV_COLUMN_INDEX.itm_ord_no],
        mesMatNo: row[CSV_COLUMN_INDEX.itm_cd],
        erpMfgZoneNo: row[CSV_COLUMN_INDEX.prod_loc_cd],
        erpStrZoneNo: row[CSV_COLUMN_INDEX.strg_loc_cd],
        erpUnitCd: row[CSV_COLUMN_INDEX.inpt_unit_cd],
        skdQty: row[CSV_COLUMN_INDEX.inst_qty],
        skdYmd: row[CSV_COLUMN_INDEX.schd_prod_st_dt],
        avlYmd: row[CSV_COLUMN_INDEX.schd_avl_dt],
        arrgtNo1: row[CSV_COLUMN_INDEX.sno1],
        arrgtNo2: row[CSV_COLUMN_INDEX.sno2],
        arrgtNo3: row[CSV_COLUMN_INDEX.sno3],
        arrgtNo4: row[CSV_COLUMN_INDEX.sno4],
        erpExpl: row[CSV_COLUMN_INDEX.rmrks],
      };
      return rowData;
    },
  );

  // インポートが0件でなければ、ダイアログ終了チェックをtrueにする
  if (tablePropsDataOrderInputScheduleFileRef.value.tableData.length > 0) {
    updateDialogChangeFlagRef(true);
  } else {
    updateDialogChangeFlagRef(false);
  }
};

// 計画取込の確認メッセージ'OK'押下時処理
// 計画取込のAPIリクエスト処理
const requestApiAddScheduleImport = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxOrderInputScheduleFileVisible');

  showLoading();

  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する。
  const requestData: AddScheduleImportRequestData = {
    importSkdList: [],
  };
  tablePropsDataOrderInputScheduleFileRef.value.tableData.forEach((row) => {
    requestData.importSkdList.push({
      erpifMesId: row.erpifMesId?.toString() ?? '',
      skdNo: row.skdNo?.toString() ?? '',
      mesMatNo: row.mesMatNo?.toString() ?? '',
      erpMfgZoneNo: row.erpMfgZoneNo?.toString() ?? '',
      erpStrZoneNo: row.erpStrZoneNo?.toString() ?? '',
      erpUnitCd: row.erpUnitCd?.toString() ?? '',
      skdQty: row.skdQty?.toString() ?? '',
      skdYmd: row.skdYmd?.toString() ?? '',
      avlYmd: row.avlYmd?.toString() ?? '',
      arrgtNo1: row.arrgtNo1?.toString() ?? '',
      arrgtNo2: row.arrgtNo2?.toString() ?? '',
      arrgtNo3: row.arrgtNo3?.toString() ?? '',
      arrgtNo4: row.arrgtNo4?.toString() ?? '',
      erpExpl: row.erpExpl?.toString() ?? '',
    });
  });

  const { responseRef, errorRef } = await useAddScheduleImport({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxOrderInputScheduleFilePropsRef.value.title,
    msgboxMsgTxt: messageBoxOrderInputScheduleFilePropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // 4．計画取込
  // ・データベースを更新した後、以下のメッセージを表示する。
  // 計画取込完了
  messageBoxOrderInputScheduleFileFinishedPropsRef.value.title =
    responseRef.value.data.rTitle;
  messageBoxOrderInputScheduleFileFinishedPropsRef.value.content =
    responseRef.value.data.rMsg;

  closeLoading();

  openDialog('messageBoxOrderInputScheduleFileFinishedVisible');
};

// 計画取込完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxModifyOrderInputScheduleFileFinished = () => {
  // 再検索用に親に実行を通知
  emit('submit', props.privilegesBtnRequestData);

  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxOrderInputScheduleFileFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

/**
 * ダイアログの初期設定
 */
const odrInputScheduleFileInit = async () => {
  // ダイアログ終了チェック初期化
  updateDialogChangeFlagRef(false);

  // テーブルデータ初期化
  tablePropsDataOrderInputScheduleFileRef.value.tableData = [];

  // ダイアログ表示
  openDialog('fragmentDialogVisible');
};

// ダイアログ起動条件の監視
watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await odrInputScheduleFileInit();
  },
);
// /**
//  * [CSVインポート] ボタン クリックイベント
//  */
const commDeviceCsvImport = () => {
  fileInput.value.click();
  fileInput.value.value = '';
};
</script>
