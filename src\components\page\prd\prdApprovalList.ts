import CONST from '@/constants/utils';
import i18n from '@/constants/lang';
import { rules } from '@/utils/validator';
import { ConditionData } from '@/types/ConditionSearchTypes';
import { SearchDataModel } from '@/hooks/useSearch';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';

const { t } = i18n.global;

// 製造記録承認画面の権限に紐づくボタン定義
export const BUTTON_ID = {
  SEARCH: 'btnSearch', // 検索
  DETAIL: 'btnRecordDetail', // 記録詳細
} as const;

// ConditionSearchとRequestDataを紐づけるモデル定義
export const searchDataModel = {
  odrNo: { odrNo: 'inputBox' },
  matNo: { matNo: 'selectData' },
  lotNo: { lotNo: 'inputBox' },
  odrStYmdFrom: { odrStYmd: 'startDate' },
  odrStYmdTo: { odrStYmd: 'endDate' },
  odrEdYmdFrom: { odrEdYmd: 'startDate' },
  odrEdYmdTo: { odrEdYmd: 'endDate' },
  recConfirmDtsFrom: { odrConfYmd: 'startDate' },
  recConfirmDtsTo: { odrConfYmd: 'endDate' },
} as const satisfies SearchDataModel;

/**
 * 条件検索設定
 * @type {ConditionData[]}
 */
export const conditionData: ConditionData[] = [
  {
    label: ['Prd.Chr.txtOrderNo'], // 製造指図番号
    id: 'odrNoTitle',
    children: [
      {
        type: 'input',
        id: 'odrNo',
        rules: [
          rules.length(13),
          rules.upperCaseSingleByteAlphanumeric({ isSearch: true }), // あいまい検索でアスタリスク使用
        ],
      },
    ],
  },
  {
    label: ['Prd.Chr.txtMaterialCode'], // 品目コード
    id: 'matNoTitle',
    children: [
      {
        type: 'autoComplete',
        id: 'matNo',
        selectOption: [],
      },
    ],
  },
  {
    label: ['Prd.Chr.txtLotNo'], // 製造番号
    id: 'lotNoTitle',
    children: [
      {
        type: 'input',
        id: 'lotNo',
        rules: [
          rules.length(15),
          rules.upperCaseSingleByteAlphanumericNumberCharacters({
            isSearch: true,
          }), // あいまい検索でアスタリスク使用
        ],
      },
    ],
  },
  {
    label: ['Prd.Chr.txtOrderStartDate'], // 製造開始予定日
    id: 'prdStYmdTitle',
    children: [
      {
        type: 'date',
        label: ['Cm.Chr.txtFrom', 'Cm.Chr.txtTo'],
        defaultValue: ['', ''], // NOTE:初期値入力無し
        id: 'odrStYmd',
        rules: [rules.fromToDate()],
      },
    ],
  },
  {
    label: ['Prd.Chr.txtOrderEndDate'], // 製造終了予定日
    id: 'prdEdYmdTitle',
    children: [
      {
        type: 'date',
        label: ['Cm.Chr.txtFrom', 'Cm.Chr.txtTo'],
        id: 'odrEdYmd',
        rules: [rules.fromToDate()],
        defaultValue: ['', ''], // NOTE:初期値入力無し
      },
    ],
  },
  {
    label: ['Prd.Chr.txtRecordConfirmDate'], // 記録確認日
    id: 'prdConfYmdTitle',
    children: [
      {
        type: 'date',
        label: ['Cm.Chr.txtFrom', 'Cm.Chr.txtTo'],
        id: 'odrConfYmd',
        rules: [rules.required('date'), rules.fromToDate()],
        defaultValue: ['0', '1'], // NOTE:初期値はFromが今日。Toが1日後。相対値で設定する。
      },
    ],
  },
];

export const getSearchFormItems: () => CustomFormType['formItems'] = () => ({
  qrCode: {
    formModelValue: '',
    formRole: 'suffix',
  },
  odrNo: {
    tags: [],
    label: { text: t('Prd.Msg.contentQrCodeRead') },
    formModelValue: '',
    formRole: 'textBox',
    suffix: { formModelProp: 'qrCode' },
    props: { clearable: true, size: 'small', isTooltipDisabled: true },
    rules: [rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS])],
  },
});

export const searchFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getSearchFormItems());
