<template>
  <!-- マスタ承認ダイアログ -->
  <DialogWindow
    :title="$t('Mst.Chr.txtApproveMaster')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    :class="'approve-master'"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    width="98vw"
  >
    <!-- 見出し マスタ名 -->
    <BaseHeading level="2" :text="props.masterName" fontSize="20px" />
    <!-- 見出し 検証環境 -->
    <BaseHeading
      level="2"
      :text="$t('Mst.Chr.txtVerificationEnvironment')"
      fontSize="20px"
      fontWeight="normal"
      class="Util_mt-24"
    />
    <!-- 検証環境一覧テーブル -->
    <TabulatorTable
      :propsData="tablePropsDataVerificationEnvironmentRef"
      :routerName="props.routerName"
      @selectRows="updateSelectedRows"
    />
    <!-- 見出し 本番環境 -->
    <BaseHeading
      level="2"
      :text="$t('Mst.Chr.txtProductionEnvironment')"
      fontSize="20px"
      fontWeight="normal"
    />
    <!-- 本番環境一覧テーブル -->
    <TabulatorTable
      :propsData="tablePropsDataProductionEnvironmentRef"
      :routerName="props.routerName"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- マスタ承認の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApproveConfirmVisible"
    :dialogProps="messageBoxApproveConfirmPropsRef"
    :cancelCallback="() => closeDialog('messageBoxApproveConfirmVisible')"
    :submitCallback="requestApiApproveMaster"
  />
  <!-- マスタ承認完了の表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApproveCompleteVisible"
    :dialogProps="messageBoxApproveCompletePropsRef"
    :submitCallback="requestApiApproveMasterFinished"
  />
  <div style="visibility: hidden">
    <span ref="hiddenSpan"></span>
  </div>
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { ColumnLayout } from '@/components/model/common/TabulatorTable/TabulatorTable';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  GetConfirmMasterListReq,
  ConfirmMasterListData,
  ConfirmMasterFieldsData,
  EntryMasterFieldsData,
  EntryMasterRecordsData,
} from '@/types/HookUseApi/MstTypes';
import {
  useGetConfirmMasterList,
  useModifyMasterApprove,
} from '@/hooks/useApi';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import {
  tablePropsDataVerificationEnvironmentRef,
  tablePropsDataProductionEnvironmentRef,
} from './mstApproveMaster';

/**
 * 多言語
 */
const { t } = useI18n();
const hiddenSpan = ref<HTMLDivElement | null>(null);

// 選択行情報の格納
let selectedRowsRef: ConfirmMasterListData[] = [];

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxApproveConfirmVisible'
  | 'messageBoxApproveCompleteVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxApproveConfirmVisible: false,
  messageBoxApproveCompleteVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// マスタ承認の確認メッセージボックス
const messageBoxApproveConfirmPropsRef = ref<DialogProps>({
  title: t('Mst.Msg.titleApprovalConfirm'),
  content: t('Mst.Msg.contentApprovalConfirm'),
  type: 'question',
});

// マスタ承認完了のメッセージボックス
const messageBoxApproveCompletePropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
  {
    text: t('Mst.Chr.btnApproval'), // 承認
    type: 'primary',
    size: 'normal',
    clickHandler: () => {
      // NOTE:未選択なら処理させない。エラーメッセージはここで出す。
      if (selectedRowsRef.length === 0) {
        messageBoxApiErrorPropsRef.value.title = t('Cm.Chr.txtUnselectedData');
        messageBoxApiErrorPropsRef.value.content = t(
          'Cm.Msg.unselectedCheckBoxData',
        );
        openDialog('messageBoxApiErrorVisible');
        return false;
      }

      // マスタ承認の確認メッセージ表示
      openDialog('messageBoxApproveConfirmVisible');

      return false;
    },
  },
];

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  masterName: string; // マスタ名
  tableName: string; // テーブル名
  cdId: string; // コードID
  privilegesBtnRequestData: CommonRequestType;
  routerName: string; // TabulatorTable権限 未選択チェック用
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

const verificationEnvironmentFields = ref<ConfirmMasterFieldsData[]>([]);

/**
 * コンテンツの幅を計算
 */
const getTextWidth = (text: string) => {
  hiddenSpan.value!.textContent = text;
  hiddenSpan.value!.style.fontSize = `14px`;
  return hiddenSpan.value!.offsetWidth;
};

/**
 * 一番広い列の幅を取得
 */
const getMaxLength = (text: string[]) => {
  let maxLength = 0;
  // 各要素の幅を計算し格納
  text.forEach((name: string) => {
    if (name) {
      const offsetWidth = getTextWidth(name) + 60;
      if (offsetWidth) {
        maxLength = Math.max(maxLength, offsetWidth);
      }
    }
  });
  return maxLength;
};

// 初回表示、再検索で呼び出される
// マスタ確認結果一覧取得API呼び出し
const requestApiGetConfirmMasterList = async (
  requestData: GetConfirmMasterListReq,
) => {
  showLoading();

  // マスタ確認結果一覧を取得
  const { responseRef, errorRef } = await useGetConfirmMasterList({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });

  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return Promise.reject(); // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // 検証環境一覧の各列の幅を求める
    const values = new Map();
    responseRef.value.data.rData.verificationEnvironmentColumns.forEach(
      (column) => values.set(column.field, [column.title]),
    );
    responseRef.value.data.rData.verificationEnvironmentList.forEach(
      (environment) =>
        Object.keys(environment).forEach((field) => {
          if (values.has(field)) {
            values.get(field).push(environment[field]);
          }
        }),
    );
    const columns: ColumnLayout[] =
      responseRef.value.data.rData.verificationEnvironmentColumns.map(
        (column) => ({
          title: column.title,
          field: column.field,
          hidden: column.hidden,
          width: getMaxLength(values.get(column.field)),
        }),
      );
    columns.push({
      title: '',
      field: 'uniqueKey',
      hidden: true,
      width: 5,
    });

    // 各セルの文字色を設定する
    const datas: ConfirmMasterListData[] =
      responseRef.value.data.rData.verificationEnvironmentList.map(
        (environment) => {
          const updatedEnvironment = { ...environment }; // 新しいオブジェクトを作成
          const fields = Object.keys(environment);
          fields.forEach((field) => {
            const fieldMod = `${field}Mod`;
            if (fieldMod in environment) {
              const modValue = environment[fieldMod] as string;
              const fieldValue = environment[field] ?? '';
              let style = '';
              switch (modValue?.toUpperCase()) {
                case 'A':
                  style = `style='color: ${t('Mst.Chr.txtAdditionColor')}'`;
                  break;
                case 'M':
                  style = `style='color: ${t('Mst.Chr.txtModificationColor')}'`;
                  break;
                case 'D':
                  style = `style='color: ${t('Mst.Chr.txtDeletionColor')}'`;
                  break;
                default:
                  break;
              }
              // NOTE: 文字色を変更するため <span>タグ を追加
              // NOTE: ソート順を維持するため、data-value属性に元の値をセット
              updatedEnvironment[field] =
                `<span data-value='${fieldValue}' ${style}>${fieldValue}</span>`;
              // NOTE: field には <span>タグ をセットするので fieldMod にバックエンドから受け取った値をセット
              updatedEnvironment[fieldMod] = environment[field];
            }
          });
          return updatedEnvironment; // 更新されたオブジェクトを返す
        },
      );

    // 主キーを取得
    const keys: string[] = [];
    responseRef.value.data.rData.verificationEnvironmentFields.forEach(
      (field) => {
        if (field.isKey) {
          keys.push(field.field);
        }
      },
    );

    // 検証環境カラム一覧をカラムに反映
    tablePropsDataVerificationEnvironmentRef.value.column = columns;

    // 検証環境フィールド一覧をフィールド一覧に退避
    verificationEnvironmentFields.value =
      responseRef.value.data.rData.verificationEnvironmentFields;

    // 検証環境一覧をテーブルに反映
    tablePropsDataVerificationEnvironmentRef.value.tableData = datas;

    // 本番環境カラム一覧をカラムに反映
    tablePropsDataProductionEnvironmentRef.value.column =
      responseRef.value.data.rData.productionEnvironmentColumns;

    // 本番環境一覧をテーブルに反映
    tablePropsDataProductionEnvironmentRef.value.tableData =
      responseRef.value.data.rData.productionEnvironmentList;

    // 主キーをユニークキーとして隠しカラムに設定
    tablePropsDataVerificationEnvironmentRef.value.tableData.forEach(
      (value) => {
        const fields = Object.keys(value);
        let key = '';
        fields.forEach((field) => {
          if (keys.includes(field)) {
            const fieldMod = `${field}Mod`;
            key += `${value[fieldMod]}|`;
          }
        });
        const tableData = value;
        tableData.uniqueKey = key;
      },
    );

    // 変更がある場合は承認ボタンを有効化、ない場合は無効化
    dialogButtons[1].disabled = true;
    responseRef.value.data.rData.verificationEnvironmentList.forEach(
      (environment) => {
        if (
          environment.modificationType !== null &&
          environment.modificationType !== undefined &&
          environment.modificationType !== ''
        ) {
          dialogButtons[1].disabled = false;
        }
      },
    );
  }

  closeLoading();

  return Promise.resolve();
};

/**
 * マスタ承認処理
 */
const requestApiApproveMaster = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxApproveConfirmVisible');

  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    return;
  }

  showLoading();

  const records: EntryMasterRecordsData[] = [];
  selectedRowsRef.forEach((row) => {
    const modificationType: string = row.modificationType as string;
    if (modificationType === '') {
      return;
    }
    const fields: EntryMasterFieldsData[] = [];
    verificationEnvironmentFields.value.forEach((field) => {
      fields.push({
        name: field.name,
        isKey: field.isKey,
        // NOTE: バックエンドへ渡す値は fieldMod から取得する
        value: row[`${field.field}Mod`],
      });
    });
    records.push({ modificationType, fields });
  });

  const entryMasterData = {
    ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
    msgboxTitleTxt: messageBoxApproveConfirmPropsRef.value.title,
    msgboxMsgTxt: messageBoxApproveConfirmPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    tableName: props.tableName,
    cdId: props.cdId,
    records,
  };

  const { responseRef, errorRef } =
    await useModifyMasterApprove(entryMasterData);

  closeLoading();

  if (errorRef.value) {
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // マスタ承認完了
    messageBoxApproveCompletePropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxApproveCompletePropsRef.value.content =
      responseRef.value.data.rMsg;
    openDialog('messageBoxApproveCompleteVisible');
  }
};

/**
 * マスタ承認完了後の処理
 */
const requestApiApproveMasterFinished = async () => {
  // メッセージを閉じる
  closeDialog('messageBoxApproveCompleteVisible');

  // ダイアログを閉じる
  closeDialog('fragmentDialogVisible');
  emit(
    'submit',
    props.privilegesBtnRequestData,
    `${props.tableName}|${props.cdId}`,
  );
};

/**
 * マスタ承認ダイアログの初期設定
 */
const mstApproveMasterInit = async () => {
  // マスタ確認結果一覧取得のAPI呼び出しと反映
  try {
    const requestData: GetConfirmMasterListReq = {
      tableName: props.tableName,
      cdId: props.cdId,
    };
    await requestApiGetConfirmMasterList(requestData);
  } catch {
    return;
  }

  // 選択行情報の初期化
  selectedRowsRef = [];

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await mstApproveMasterInit();
  },
);

/**
 * 行選択時
 */
const updateSelectedRows = (v: ConfirmMasterListData[]) => {
  selectedRowsRef = v;
};
</script>
