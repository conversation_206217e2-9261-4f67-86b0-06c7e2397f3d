import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';
import CONST from '@/constants/utils';

const { t } = i18n.global;

// アイテム定義
export const sopSettingDialogFormItems: CustomFormType['formItems'] = {
  matNm: {
    formModelValue: '',
    label: { text: t('SOP.Chr.txtMatNm') },
    formRole: 'textBox',
    props: { disabled: true },
  },
  rxNm: {
    formModelValue: '',
    label: { text: t('SOP.Chr.txtRxNm') },
    formRole: 'textBox',
    props: { disabled: true },
  },
  sopFlowNo: {
    formModelValue: '',
    label: { text: t('SOP.Chr.txtSopFlowNo') },
    formRole: 'textBox',
    props: { disabled: true },
  },
  sopFlowNmJp: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    formModelValue: '',
    label: { text: t('SOP.Chr.txtSopFlowNm') },
    formRole: 'textBox',
    rules: [
      rules.length(32),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      rules.required('textBox'),
    ],
  },
  sopBatchType: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    formModelValue: '',
    label: { text: t('SOP.Chr.txtSopBatchType') },
    formRole: 'selectComboBox',
    rules: [rules.required('selectComboBox')],
    selectOptions: [],
    props: { filterable: true, clearable: true, optionsData: [] },
    cmbId: 'sopBatchType',
  },
  helpBinPath: {
    formModelValue: '',
    label: { text: t('SOP.Chr.txtHelpSetting') },
    formRole: 'textBox',
    rules: [rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS_PATH])],
  },
  forcePrivGrpCd: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    formModelValue: '',
    label: { text: t('SOP.Chr.txtApproveGroup') },
    rules: [rules.required('selectComboBox')],
    formRole: 'selectComboBox',
    selectOptions: [],
    props: { filterable: true, clearable: true, optionsData: [] },
    cmbId: 'privGroup',
  },
  skipPrivGrpCd: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    formModelValue: '',
    label: { text: t('SOP.Chr.txtSkipGroup') },
    rules: [rules.required('selectComboBox')],
    formRole: 'selectComboBox',
    selectOptions: [],
    props: { filterable: true, clearable: true, optionsData: [] },
    cmbId: 'privGroup',
  },
  dspSeq: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    formModelValue: '',
    label: { text: t('SOP.Chr.txtDspSeq') },
    formRole: 'textBox',
    rules: [
      rules.required('textBox'),
      rules.placesOfNumeric({ int: 6 }),
      rules.naturalNumericOnly(),
    ],
  },
};
// モデル定義
export const sopSettingDialogFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(sopSettingDialogFormItems);
