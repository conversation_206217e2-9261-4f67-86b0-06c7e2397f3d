import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// ロットロックダイアログのアイテム定義
export const getInventoryLockLotFormItems: () => CustomFormType['formItems'] =
  () => ({
    matNo: {
      label: { text: t('Inv.Chr.txtItemCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    matNm: {
      label: { text: t('Inv.Chr.txtItemName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    lotNo: {
      label: { text: t('Inv.Chr.txtManageNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    lotLockExpl: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtComment') },
      formModelValue: '',
      rules: [
        rules.required('textComboBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
      ],
      formRole: 'textComboBox',
      props: {
        clearable: true,
        size: 'custom',
        width: '100%',
      },
      selectOptions: [],
      cmbId: 'cmtCatInvLotLockOn',
    },
  });

// ロットロックダイアログのモデル定義
export const inventoryLockLotFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getInventoryLockLotFormItems());
