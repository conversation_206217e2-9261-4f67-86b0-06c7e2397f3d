import { checkPartCode } from '@/components/model/SopPA/SopChartSetting';
import SOP_PARTS_VARIABLES from '@/constants/sopPartsVariables';
import {
  SopBlockPartOption,
  SopControlPartOption,
  SopConditionProps,
  PartButtonBranchProps,
  PartSystemBranchProps,
  PartInstructionConfirmProps,
  PartSopTimerProps,
  PartExternalDeviceProps,
  PartWeightCalibrationProps,
  SopFlowGetDataOption,
} from '@/types/SopDialogInterface';
import { Graph } from '@antv/x6';

/**
 * マージ処理用 型定義
 */
export type Matrix = string[][];
export type BranchList = { [branchId: string]: string[] }; // 分岐親ID: 所属ノードIDリスト
export type NodeAttr = Record<string, unknown>;
export type BranchListResult = {
  branchList: BranchList;
  parentToConfluenceMap: Record<string, string>;
};
/**
 * 現在のグラフ上のノードから「ID→属性」マップを作成する
 * @returns {Record<string, any>} ノードIDをキー、属性（toJSON()の内容）を値とするマップ
 */
export const createNodeIdToAttrMapFromDB = (
  nodeLists: SopBlockPartOption[],
): Record<string, SopControlPartOption> => {
  const map: Record<string, SopControlPartOption> = {};
  // const allNodes = state.localeSelectGraph!.getNodes();
  nodeLists.forEach((node) => {
    // ディープコピーで独立した値を保存
    const nodeData = JSON.parse(JSON.stringify(node));
    // (nodeData as Record<string, unknown>).outgoingIds = state.localeSelectGraph!.getNeighbors(node, { outgoing: true }).map(n => n.id);
    map[node.id || ''] = nodeData;
  });
  return map;
};

/**
 * ノードのY座標を計算する
 *
 * @param sopPartsCD - ノード種別CD
 * @param yIdx - ノードのY方向インデックス（行番号）
 * @param allNodesMatrix - ノードIDの2次元配列（行: Y方向, 列: X方向）
 * @param nodeAttrMap - ノードIDをキーとした属性情報マップ（位置・種別など）
 * @param sopPartConst - パーツ定数（高さ・マージン等）
 * @param options - オプション（join/branch/confluence/収束ノードのオフセット等）
 * @returns Y座標（px）
 *
 * 概要:
 * - join/branchはmarginYで等間隔、confluenceはmarginY＋オフセットで等間隔
 * - いずれも必ずyIdxを利用して計算
 * - confluenceのオフセットが指定されている場合、それ以降のjoin/branch/confluenceも高さ調整
 * - join/branch/confluence間の通常パーツは、前後のspecialノードの中央に配置
 * - スタート直後のjoinは特別な調整位置が可能
 * - エンドノードは直前のjoin/confluenceからのオフセット指定が可能
 */
export const calcNodePosY = (
  sopPartsCD: string,
  yIdx: number,
  allNodesMatrix: Matrix,
  nodeAttrMap: Record<string, unknown>,
  options?: {
    marginY?: number; // マージン（Y間隔）
    baseY?: number; // ベースY
  },
): number => {
  const { baseY = 0, marginY = 0 } = options || {};

  // marginY:confluenceOffsetY の比率を維持して計算
  // 例: 104:96 → 96/104 = 0.923...
  const CONFLUENCE_OFFSET_RATIO = 96 / 104;
  const confluenceOffsetY = Math.round(marginY * CONFLUENCE_OFFSET_RATIO);

  // join/branch/confluence種別判定
  const isJoin = (cd: string) =>
    cd === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME ||
    cd === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD;
  const isBranch = (cd: string) =>
    cd === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME ||
    cd === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD;
  const isConfluence = (cd: string) =>
    cd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME ||
    cd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD;

  // オフセット合計を計算
  const calcOffsetSum = (toYIdx: number) => {
    let offsetSum = 0;
    for (let i = 1; i <= toYIdx; i++) {
      const prevRow = allNodesMatrix[i - 1];
      if (prevRow) {
        for (let px = 0; px < prevRow.length; px++) {
          const prevId = prevRow[px];
          if (
            prevId &&
            nodeAttrMap &&
            nodeAttrMap[prevId] &&
            // @ts-expect-error sopPartsCD属性ある
            nodeAttrMap[prevId].sopPartsCD !== undefined &&
            // @ts-expect-error sopPartsCD属性ある
            isConfluence(nodeAttrMap[prevId].sopPartsCD) &&
            confluenceOffsetY
          ) {
            offsetSum += confluenceOffsetY;
            break;
          }
        }
      }
    }
    return offsetSum;
  };

  // --- 1. スタート ---
  if (
    sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_START_NAME ||
    sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_START_CD
  ) {
    return baseY;
  }

  // --- 2. join/branch ---
  if (isJoin(sopPartsCD) || isBranch(sopPartsCD)) {
    return baseY + yIdx * marginY + calcOffsetSum(yIdx);
  }

  // --- 3. confluence ---
  if (isConfluence(sopPartsCD)) {
    return baseY + yIdx * marginY + confluenceOffsetY + calcOffsetSum(yIdx);
  }

  // --- 4. End ---
  if (
    sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_END_NAME ||
    sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_END_CD
  ) {
    return baseY + yIdx * marginY + calcOffsetSum(yIdx);
  }

  // --- 5. 通常パーツ ---
  // TODO: 通常パーツも同じ比率で間隔を計算
  return baseY + yIdx * marginY + calcOffsetSum(yIdx) - 20;
  // let prevSpecialY: number | null = null;
  // let nextSpecialY: number | null = null;
  // // 前のspecialノードのY
  // for (let i = yIdx - 1; i >= 0 && prevSpecialY === null; i--) {
  //   const prevRow = allNodesMatrix[i];
  //   if (prevRow) {
  //     for (let px = 0; px < prevRow.length; px++) {
  //       const prevId = prevRow[px];
  //       if (
  //         prevId &&
  //         nodeAttrMap &&
  //         isSpecial(nodeAttrMap[prevId].sopPartsCD)
  //       ) {
  //         prevSpecialY = nodeAttrMap[prevId]?.position?.y ?? (baseY + i * marginY);
  //         break;
  //       }
  //     }
  //   }
  // }
  // // 次のspecialノードのY
  // for (let i = yIdx + 1; i < allNodesMatrix.length && nextSpecialY === null; i++) {
  //   const nextRow = allNodesMatrix[i];
  //   if (nextRow) {
  //     for (let nx = 0; nx < nextRow.length; nx++) {
  //       const nextId = nextRow[nx];
  //       if (
  //         nextId &&
  //         nodeAttrMap &&
  //         isSpecial(nodeAttrMap[nextId].sopPartsCD)
  //       ) {
  //         if (isConfluence(nodeAttrMap[nextId].sopPartsCD)) {
  //           nextSpecialY = nodeAttrMap[nextId]?.position?.y ?? (baseY + i * marginY + confluenceOffsetY);
  //         } else {
  //           nextSpecialY = nodeAttrMap[nextId]?.position?.y ?? (baseY + i * marginY);
  //         }
  //         break;
  //       }
  //     }
  //   }
  // }
  // if (prevSpecialY !== null && nextSpecialY !== null) {
  //   return (prevSpecialY + nextSpecialY) / 2 - partHeight / 2 + addPartHeight / 2;
  // }
  // return baseY + yIdx * marginY;
};

/**
 * ノード種別を判定する関数例
 */
export const getEdgeNodeType = (partsCd: string, partCds: string[]): string => {
  if (!partsCd) return 'normal';

  if (checkPartCode(partCds, partsCd)) return 'normal';
  if (partsCd.startsWith(SOP_PARTS_VARIABLES.PART_SPEC_START_CD))
    return 'normal';
  if (partsCd.startsWith(SOP_PARTS_VARIABLES.PART_SPEC_END_CD)) return 'normal';
  if (partsCd.startsWith(SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD))
    return SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME;
  if (partsCd.startsWith(SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD))
    return SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME;
  if (partsCd.startsWith(SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD))
    return SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME;
  return partsCd;
};

export const findNearParentBranchId = (
  branchList: BranchList,
  nodeId: string,
): string | null => {
  let foundBranchId: string | null = null;
  // キーを昇順または降順でソート
  const branchIds = Object.keys(branchList).sort((a, b) => b.localeCompare(a)); // 降順
  branchIds.some((branchId) => {
    if (
      JSON.stringify(branchList[branchId]) !== '{}' &&
      branchList[branchId].includes(nodeId)
    ) {
      foundBranchId = branchId;
      return true;
    }
    return false;
  });
  return foundBranchId;
};

export const findNodeRow = (matrix: Matrix, nodeId: string): number =>
  matrix.findIndex((row) => row.includes(nodeId));

export const getIndividualPara = <T>(attr: unknown): T | undefined => {
  if (attr && typeof attr === 'object' && 'individualPara' in attr) {
    return (attr as { individualPara?: unknown }).individualPara as T;
  }
  return undefined;
};

export const getConditionProps = (
  attr: unknown,
): SopConditionProps | undefined => {
  if (attr && typeof attr === 'object' && 'conditionProps' in attr) {
    return (attr as { conditionProps?: unknown })
      .conditionProps as SopConditionProps;
  }
  return undefined;
};

export const getDeviationBranchNodeId = (obj: unknown): string | undefined => {
  if (obj && typeof obj === 'object' && 'deviationBranchNodeId' in obj) {
    return (obj as { deviationBranchNodeId?: unknown })
      .deviationBranchNodeId as string;
  }
  return undefined;
};

export const getBranchNodeId = <T>(
  indivisdualPara: T,
  branchList: string[],
  targetId: string,
): string | undefined => {
  if (!Array.isArray(branchList)) return undefined;
  const index = branchList.indexOf(targetId);
  if (index === -1) return undefined;
  const key = `branchNodeId${index + 1}` as keyof T;
  return indivisdualPara && (indivisdualPara[key] as string);
};
function getbranchNodeIdDefault(obj: unknown): string | undefined {
  if (obj && typeof obj === 'object' && 'branchNodeIdDefault' in obj) {
    return (obj as { branchNodeIdDefault?: unknown })
      .branchNodeIdDefault as string;
  }
  return undefined;
}

/**
 * ループ設定時の飛び先ノード取得
 */
export const getLoopTargetNode = (
  targetId: string,
  parentId: string,
  branchList: string[],
  nodeAttrMap: NodeAttr,
  graph: Graph,
): { id: string } | null => {
  const node = graph?.getCellById(parentId);
  if (!node || !node.isNode() || !nodeAttrMap[parentId]) return null;

  const attr = nodeAttrMap[parentId];
  const sopPartsCD = (attr as { sopPartsCD?: string })?.sopPartsCD;

  switch (sopPartsCD) {
    case SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD: {
      const indivisdualPara = getIndividualPara<PartButtonBranchProps>(attr);
      const branchNodeId =
        indivisdualPara &&
        getBranchNodeId(indivisdualPara, branchList, targetId);
      return branchNodeId ? { id: branchNodeId } : null;
    }
    case SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD: {
      const indivisdualPara = getIndividualPara<PartSystemBranchProps>(attr);
      let branchIndex: number | null = null;
      if (branchList && Array.isArray(branchList)) {
        branchIndex = branchList.findIndex((branch) => branch === targetId);
      }
      const branchNodeId =
        indivisdualPara &&
        getBranchNodeId(indivisdualPara, branchList, targetId);
      const branchNodeIdDefault = getbranchNodeIdDefault(indivisdualPara);
      if (branchIndex === branchList.length - 1) {
        // 最後の分岐の場合は、デフォルトの分岐ノードIDを返す
        if (branchNodeIdDefault) {
          return { id: branchNodeIdDefault };
        }
        return null;
      }
      if (branchNodeId) {
        return { id: branchNodeId };
      }
      return null;
    }
    case SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD: {
      const indivisdualPara =
        getIndividualPara<PartInstructionConfirmProps>(attr);
      const branchIndex = branchList.findIndex((branch) => branch === targetId);
      if (branchIndex === branchList.length - 1) {
        const deviationBranchNodeId = getDeviationBranchNodeId(indivisdualPara);
        return deviationBranchNodeId ? { id: deviationBranchNodeId } : null;
      }
      return null;
    }
    case SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD: {
      const indivisdualPara = getIndividualPara<PartSopTimerProps>(attr);
      const branchIndex = branchList.findIndex((branch) => branch === targetId);
      if (branchIndex === branchList.length - 1) {
        const deviationBranchNodeId = getDeviationBranchNodeId(indivisdualPara);
        return deviationBranchNodeId ? { id: deviationBranchNodeId } : null;
      }
      return null;
    }
    case SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD: {
      const indivisdualPara = getIndividualPara<PartExternalDeviceProps>(attr);
      const branchIndex = branchList.findIndex((branch) => branch === targetId);
      if (branchIndex === branchList.length - 1) {
        const deviationBranchNodeId = getDeviationBranchNodeId(indivisdualPara);
        return deviationBranchNodeId ? { id: deviationBranchNodeId } : null;
      }
      return null;
    }
    case SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD: {
      const indivisdualPara =
        getIndividualPara<PartWeightCalibrationProps>(attr);
      const branchIndex = branchList.findIndex((branch) => branch === targetId);
      if (branchIndex === branchList.length - 1) {
        const deviationBranchNodeId = getDeviationBranchNodeId(indivisdualPara);
        return deviationBranchNodeId ? { id: deviationBranchNodeId } : null;
      }
      return null;
    }
    case SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD:
    case SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD:
    case SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD:
    case SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD: {
      const conditionProps = getConditionProps(attr);
      const branchIndex = branchList.findIndex((branch) => branch === targetId);
      if (branchIndex === branchList.length - 1) {
        const deviationBranchNodeId = getDeviationBranchNodeId(conditionProps);
        return deviationBranchNodeId ? { id: deviationBranchNodeId } : null;
      }
      return null;
    }
    default:
      return null;
  }
};

export const findParentBranchId = (
  branchList: BranchList,
  nodeId: string,
): string | null => {
  let foundBranchId: string | null = null;
  Object.keys(branchList).some((branchId) => {
    if (
      JSON.stringify(branchList[branchId]) !== '{}' &&
      branchList[branchId].includes(nodeId)
    ) {
      foundBranchId = branchId;
      return true;
    }
    return false;
  });
  return foundBranchId;
};

/**
 * ノードに接続されているスタブの ID を取得
 * @param {*} nodeId - データのId
 */
export const getCellPortsId = (nodeId: string, graph: Graph) => {
  let itemTopId: string = '';
  let itemBottomId: string = '';
  const nodeCell = graph!.getCellById(nodeId);
  if (nodeCell.isNode()) {
    const portList = nodeCell.getPorts();
    portList.forEach((item) => {
      const itemOption = item;
      if (!itemOption.id) return;
      if (itemOption.group === 'top') {
        itemTopId = itemOption.id;
      }
      if (itemOption.group === 'bottom') {
        itemBottomId = itemOption.id;
      }
    });
  }
  return {
    topId: itemTopId,
    bottomId: itemBottomId,
  };
};

/**
 * ノード接続の作成(テンプレート)
 * @param {*} nodeId - ブロック
 * @param {*} cellId - ブロック
 */
export const addBlockTargetEdge4Temp = (
  cellId: string,
  nodeId: string,
  graph: Graph,
) => {
  graph!.getEdges().forEach((anEdge) => {
    if (
      anEdge.getSourceCellId() === cellId &&
      anEdge.getTargetCellId() === nodeId
    ) {
      graph!.removeEdge(anEdge);
    }
  });
  const nodePorts = getCellPortsId(nodeId, graph);
  const cellPorts = getCellPortsId(cellId, graph);
  const edgeOption = {
    source: { cell: cellId, port: cellPorts.bottomId },
    target: { cell: nodeId, port: nodePorts.topId },
    connector: {
      name: 'jumpover',
      args: {
        type: 'arc',
        size: 5,
        radius: 20,
      },
    },
    attrs: {
      line: {
        stroke: '#A2B1C3',
        strokeWidth: 2,
        targetMarker: {
          name: 'block',
          width: 12,
          height: 8,
        },
      },
    },
    zIndex: 0,
  };
  graph!.addEdge(edgeOption);
};

/**
 * ノード接続の作成
 * @param {*} nodeId - ブロック
 * @param {*} cellId - ブロック
 * @param {*} state
 * @remarks
 * <<呼び出しメソッド>>
 * setPartNodeToFlow
 * getSOPFlowData
 * setBranchForPart
 */
export const addPathTargetEdge = (
  cellId: string,
  nodeId: string,
  graph: Graph,
) => {
  // [課題外] ADD ST ノード間複数Edge存在問題対応
  graph!.getEdges().forEach((anEdge) => {
    if (
      anEdge.getSourceCellId() === cellId &&
      anEdge.getTargetCellId() === nodeId
    ) {
      graph!.removeEdge(anEdge);
    }
  });
  // [課題外] ADD ED ノード間複数Edge存在問題対応
  const nodePorts = getCellPortsId(nodeId, graph);
  const cellPorts = getCellPortsId(cellId, graph);
  graph!.addEdge({
    source: { cell: cellId, port: cellPorts.bottomId },
    target: { cell: nodeId, port: nodePorts.topId },
    attrs: {
      line: {
        stroke: '#A2B1C3',
        strokeWidth: 2,
        targetMarker: {
          name: 'path',
          d: '',
        },
      },
    },
    zIndex: 0,
  });
};

/**
 * マージ処理用 サポート関数
 *
 */
export const findNodeCol = (matrix: Matrix, nodeId: string): number => {
  for (let r = 0; r < matrix.length; r++) {
    const c = matrix[r].indexOf(nodeId);
    if (c !== -1) return c;
  }
  return -1;
};

/**
 * 親パーツIDをキーに収束ノードIDを持つオブジェクトを作成する
 * @param confluenceNodes 収束ノード(Node型)の配列
 * @returns Record<親ノードID, 収束ノードID>
 */
export const createParentToConfluenceNodeMap = (
  confluenceNodes: SopFlowGetDataOption[] | Node[],
): Record<string, string> => {
  const map: Record<string, string> = {};

  // 型を判定
  const fromDBFlag =
    confluenceNodes.length > 0 &&
    typeof (confluenceNodes[0] as SopFlowGetDataOption).nodeId === 'string';

  // idの昇順でソート(最上位の親ノードが必ず先頭となる)
  const sortedNodes = confluenceNodes.slice().sort((a, b) => {
    const aId = fromDBFlag
      ? (a as SopFlowGetDataOption).nodeId
      : // @ts-expect-error node idがある
        (a as Node).id;
    const bId = fromDBFlag
      ? (b as SopFlowGetDataOption).nodeId
      : // @ts-expect-error node idがある
        (b as Node).id;
    if (aId < bId) return -1;
    if (aId > bId) return 1;
    return 0;
  });

  sortedNodes.forEach((confluenceNode) => {
    const parentNodeId = fromDBFlag
      ? // @ts-expect-error parentSopNodeNoある
        confluenceNode.parentSopNodeNo
      : // @ts-expect-error parentSopNodeNoある
        confluenceNode.getProp<string>('parentNodeId');
    if (parentNodeId) {
      map[parentNodeId] = fromDBFlag
        ? // @ts-expect-error nodeIdある
          confluenceNode.nodeId
        : // @ts-expect-error nodeIdある
          confluenceNode.id;
    }
  });
  return map;
};

/**
 * 全ての列（x）で親ノードから収束ノードまでのy範囲に含まれるパーツID一覧を取得する
 * @param matrix 2次元配列（[y][x]でノードIDが入っている）
 * @param parentNodeId 親ノードID
 * @param confluenceNodeId 収束ノードID
 * @returns パーツID一覧（親ノードID～収束ノードIDまで、両端含む、全x列対象）
 */
export const getPartsBetweenParentAndConfluenceFromDB = (
  matrix: (string | null)[][],
  parentNodeId: string,
  confluenceNodeId: string,
): string[] => {
  let parentY: number | null = null;
  let confluenceY: number | null = null;

  // 親ノードと収束ノードのy位置を特定（最初に見つかったものを採用）
  for (let y = 0; y < matrix.length; y++) {
    for (let x = 0; x < matrix[y].length; x++) {
      if (matrix[y][x] === parentNodeId && parentY === null) {
        parentY = y;
      }
      if (matrix[y][x] === confluenceNodeId && confluenceY === null) {
        confluenceY = y;
      }
    }
  }
  if (parentY === null || confluenceY === null) return [];

  const yStart = Math.min(parentY, confluenceY);
  const yEnd = Math.max(parentY, confluenceY);

  const parts: string[] = [];
  for (let y = yStart; y <= yEnd; y++) {
    for (let x = 0; x < matrix[y].length; x++) {
      const id = matrix[y][x];
      if (id) parts.push(id);
    }
  }
  return parts;
};

export const createBranchList = (
  allNodesMatrix: Matrix,
  graph: Graph,
  flowList: SopFlowGetDataOption[] = [],
): BranchListResult => {
  console.log('createBranchList', allNodesMatrix);
  const branchList: BranchList = {};
  const confluenceNodes = flowList.length
    ? flowList.filter(
        (node: SopFlowGetDataOption) =>
          node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
      )
    : graph!
        .getNodes()
        .filter(
          (node) =>
            node.getProp<string>('sopPartsCD') ===
            SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
        );
  const parentToConfluenceMap =
    // @ts-expect-error parentToConfluenceMapがある
    createParentToConfluenceNodeMap(confluenceNodes);
  Object.entries(parentToConfluenceMap).forEach(
    ([parentNodeId, confluenceNodeId]) => {
      // 分岐パーツIDリストを作成
      const branchNodeIds: string[] = [];
      // parentNodeIdの位置を特定
      let parentRow = -1;
      let parentCol = -1;
      for (let y = 0; y < allNodesMatrix.length; y++) {
        for (let x = 0; x < allNodesMatrix[y].length; x++) {
          if (allNodesMatrix[y][x] === parentNodeId) {
            parentRow = y;
            parentCol = x;
            break;
          }
        }
        if (parentRow !== -1) break;
      }

      // 親分岐の分岐数を取得する
      const parentNode = graph!.getCellById(parentNodeId);
      const sopPartsCd = parentNode?.getProp<string>('sopPartsCD');
      let branchNum = 0;
      if (sopPartsCd === SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD) {
        const individualPara = parentNode?.getProp(
          'individualPara',
        ) as PartButtonBranchProps;
        branchNum = individualPara.branchNumSetting || 0;
      } else if (sopPartsCd === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD) {
        const individualPara = parentNode?.getProp(
          'individualPara',
        ) as PartSystemBranchProps;
        branchNum = individualPara.branchNumSetting || 0;
      } else {
        branchNum = 2;
      }
      // 分岐パーツIDを取得
      if (parentRow !== -1 && allNodesMatrix[parentRow + 1]) {
        // parentNodeIdのひとつ下の行、かつparentCol以降を検索
        const nextRow = allNodesMatrix[parentRow + 1];
        for (let x = parentCol; x < nextRow.length; x++) {
          if (branchNodeIds.length >= branchNum) break; // 範囲外チェック
          const id = nextRow[x];
          const node = graph!.getCellById(id);
          const sopPartsCD = node?.getProp<string>('sopPartsCD');
          if (sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
            branchNodeIds.push(id);
          }
        }
      }
      const partsList = getPartsBetweenParentAndConfluenceFromDB(
        allNodesMatrix,
        parentNodeId,
        confluenceNodeId,
      );
      branchList[parentNodeId] = partsList;
    },
  );
  return { branchList, parentToConfluenceMap };
};

/**
 * 2次元配列に変換
 * @param {SopBlockPartOption[]} nodes - ノードの配列
 * @returns {Matrix} 2次元配列形式のノードマトリックス
 */
export const createNodesMatrixFromDb = (
  nodeLists: SopBlockPartOption[],
): Matrix => {
  let rowNumber = 0;
  let columnNumber = 0;
  nodeLists.forEach((node) => {
    if (node.y && rowNumber < node.y) {
      rowNumber = node.y;
    }
    if (node.x && columnNumber < node.x) {
      columnNumber = node.x;
    }
  });
  const nodesMatrix = new Array(rowNumber + 1)
    .fill(null)
    .map(() => new Array(columnNumber + 1).fill(null));
  nodeLists.forEach((node) => {
    const nodeY = node.y ? node.y : 0;
    const nodeX = node.x ? node.x : 0;
    nodesMatrix[nodeY][nodeX] = node.id;
  });
  return nodesMatrix;
};
