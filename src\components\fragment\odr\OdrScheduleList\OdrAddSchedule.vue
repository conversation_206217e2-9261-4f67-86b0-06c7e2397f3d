<template>
  <!-- 計画追加ダイアログ -->
  <DialogWindow
    :title="$t('Odr.Chr.txtPlanAddition')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="odrAddScheduleFormRef.formModel"
      :formItems="odrAddScheduleFormRef.formItems"
      @selectedItem="updateFormItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          odrAddScheduleFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 品目未選択時のエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxMaterialUnselectVisible"
    :dialogProps="messageBoxMaterialUnselectPropsRef"
    :submitCallback="() => closeDialog('messageBoxMaterialUnselectVisible')"
  />
  <!-- 製造開始予定日未入力時のエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxOrderDateNoInputVisible"
    :dialogProps="messageBoxOrderDateNoInputPropsRef"
    :submitCallback="() => closeDialog('messageBoxOrderDateNoInputVisible')"
  />
  <!-- 小日程計画追加の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPlanAdditionVisible"
    :dialogProps="messageBoxPlanAdditionPropsRef"
    :cancelCallback="() => closeDialog('messageBoxPlanAdditionVisible')"
    :submitCallback="requestApiPlanAddition"
  />
  <!-- 小日程計画追加完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPlanAdditionFinishedVisible"
    :dialogProps="messageBoxPlanAdditionFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxPlanAdditionFinished"
  />
  <!-- 処方選択ダイアログ -->
  <OdrSelectRx
    :isClicked="isClickedShowOdrSelectRxDialogRef"
    :materialCode="materialCode"
    :materialName="materialName"
    :unitName="unitName"
    :orderDates="orderDates"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    @submit="reflectPrescriptionInfo"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType, FormItem } from '@/types/CustomFormTypes';
import { PrescriptionListTableRowData } from '@/types/PrescriptionSelectDialogTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import OdrSelectRx from '@/components/include/odr/OdrSelectRx.vue';
import { useAddSchedule, useGetComboBoxDataStandard } from '@/hooks/useApi';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  getComboBoxOptionList,
  setCustomFormComboBoxOptionList,
} from '@/utils/comboBoxOptionList';
import { closeLoading, showLoading } from '@/utils/dialog';
import { rules } from '@/utils/validator';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { AddScheduleRequestData } from '@/types/HookUseApi/OdrTypes';
import {
  PLAN_PRC_NO_FIXED_CHR_LENGTH,
  getOdrAddScheduleFormItems,
  odrAddScheduleFormModel,
} from './odrAddSchedule';

/**
 * 多言語
 */
const { t } = useI18n();
// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxMaterialUnselectVisible'
  | 'messageBoxOrderDateNoInputVisible'
  | 'messageBoxPlanAdditionVisible'
  | 'messageBoxPlanAdditionFinishedVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxMaterialUnselectVisible: false,
  messageBoxOrderDateNoInputVisible: false,
  messageBoxPlanAdditionVisible: false,
  messageBoxPlanAdditionFinishedVisible: false,
};

// 子に渡す情報パラメータ
let materialCode: string = '';
let materialName: string = '';
let unitName: string = '';
let orderDates: string = '';

// カスタムフォームの描画更新トリガー
const customFormRenderingTriggerRef = ref(false);
// '処方選択' クリック
const isClickedShowOdrSelectRxDialogRef = ref<boolean>(false);

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 品目未選択時のメッセージボックス
const messageBoxMaterialUnselectPropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titleMaterialUnselect'),
  content: t('Odr.Msg.contentMaterialUnselect'),
  isSingleBtn: true,
  type: 'error',
});

// 製造開始予定日未入力時のメッセージボックス
const messageBoxOrderDateNoInputPropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titleOrderDateNoInput'),
  content: t('Odr.Msg.contentOrderDateNoInput'),
  isSingleBtn: true,
  type: 'error',
});

// 小日程計画追加の確認メッセージボックス
const messageBoxPlanAdditionPropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titlePlanAddition'),
  content: t('Odr.Msg.contentPlanAddition'),
  type: 'question',
});

// 小日程計画追加の完了メッセージボックス
const messageBoxPlanAdditionFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

const odrAddScheduleFormRef = ref<CustomFormType>({
  formItems: getOdrAddScheduleFormItems(),
  formModel: odrAddScheduleFormModel,
});

type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// NOTE:処方コードだけ計画追加ダイアログでは見せない、内部で持つデータのため個別定義
let cacheRxNo: string = '';

// 処方選択ダイアログの 実行 押下時処理
// 処方情報の反映
const reflectPrescriptionInfo = (v: PrescriptionListTableRowData) => {
  // NOTE:処方コードだけ計画追加ダイアログでは見せない、内部で持つデータ
  cacheRxNo = v.rxNo;
  // 処方名
  odrAddScheduleFormRef.value.formItems.prescriptionNm.formModelValue =
    v.rxNmJp;
  // MBR番号
  odrAddScheduleFormRef.value.formItems.masterBatchRecordNo.formModelValue =
    v.mbrNo;
  // 有効期限
  odrAddScheduleFormRef.value.formItems.prescriptionDts.formModelValue = `${v.validStYmd} - ${v.validEdYmd}`;

  // NOTE:テーブル上はカンマがついているが、入力としては不要なためカンマ除去
  const stdPrdQty = v.stdPrdQty.replace(/,/g, '');
  // 標準生産量
  odrAddScheduleFormRef.value.formItems.stdPrdQty.formModelValue = stdPrdQty;
  // 計画生産量
  // NOTE:初期値は標準生産量と同値。その後ユーザーが自由に入力変更可能になる項目。
  odrAddScheduleFormRef.value.formItems.planQty.formModelValue = stdPrdQty;
};

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  console.log('onResolve odrAddScheduleDialog');
  // 1.下記チェックを行い、チェックOKなら処理継続する。
  // １ 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    odrAddScheduleFormRef.value.customForm !== undefined &&
    (await odrAddScheduleFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return false;
  }

  // 2.以下のメッセージを表示
  // 計画追加の確認
  openDialog('messageBoxPlanAdditionVisible');
  return false;
};

// 計画追加の確認メッセージ'OK'押下時処理
// 計画追加のAPIリクエスト処理
const requestApiPlanAddition = async () => {
  console.log('requestApiPlanAddition');

  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxPlanAdditionVisible');

  showLoading();

  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する。
  const requestData: AddScheduleRequestData = {
    planPrcNo:
      odrAddScheduleFormRef.value.formItems.planPrcNo.formModelValue.toString(),
    matNo:
      odrAddScheduleFormRef.value.formItems.matNo.formModelValue.toString(),
    planQty:
      odrAddScheduleFormRef.value.formItems.planQty.formModelValue.toString(),
    odrDts:
      odrAddScheduleFormRef.value.formItems.odrDts.formModelValue.toString(),
    rxNo: cacheRxNo,
    mbrNo:
      odrAddScheduleFormRef.value.formItems.masterBatchRecordNo.formModelValue.toString(),
    skdAddExpl:
      odrAddScheduleFormRef.value.formItems.skdAddExpl.formModelValue.toString(),
  };
  const { responseRef, errorRef } = await useAddSchedule({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxPlanAdditionPropsRef.value.title,
    msgboxMsgTxt: messageBoxPlanAdditionPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return false;
  }

  if (responseRef.value) {
    // 4．小日程計画を登録する。
    // ・データベースを更新した後、以下のメッセージを表示する。
    // 小日程計画追加完了
    messageBoxPlanAdditionFinishedPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxPlanAdditionFinishedPropsRef.value.content =
      responseRef.value.data.rMsg;
  }

  closeLoading();
  openDialog('messageBoxPlanAdditionFinishedVisible');
  return false;
};

// 小日程計画追加完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxPlanAdditionFinished = () => {
  console.log('closeDialogFromMessageBoxPlanAdditionFinished');

  // 再検索用に親に実行を通知
  emit('submit', props.privilegesBtnRequestData);

  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxPlanAdditionFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

// ButtonFormのクリック時イベントの設定
const resetButtonFormOnClickHandler = () => {
  // '処方選択'ボタンイベント
  if (
    odrAddScheduleFormRef.value.formItems.buttonOdrSelectRx.formRole ===
    'button'
  ) {
    odrAddScheduleFormRef.value.formItems.buttonOdrSelectRx.onClickHandler =
      () => {
        // 品目選択状態か判定
        if (odrAddScheduleFormRef.value.formItems.matNo.formModelValue === '') {
          // 品目未選択時エラーメッセージ表示
          openDialog('messageBoxMaterialUnselectVisible');
          return;
        }
        // 製造開始予定日入力状態か判定
        if (
          odrAddScheduleFormRef.value.formItems.odrDts.formModelValue === ''
        ) {
          // 製造開始予定日未入力時エラーメッセージ表示
          openDialog('messageBoxOrderDateNoInputVisible');
          return;
        }

        // 処方選択ダイアログのパラメータセット
        materialCode =
          odrAddScheduleFormRef.value.formItems.matNo.formModelValue.toString();
        materialName =
          odrAddScheduleFormRef.value.formItems.matNm.formModelValue.toString();
        unitName =
          odrAddScheduleFormRef.value.formItems.unitNm.formModelValue.toString();
        orderDates =
          odrAddScheduleFormRef.value.formItems.odrDts.formModelValue.toString();

        // 処方選択ダイアログを表示開始
        // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
        isClickedShowOdrSelectRxDialogRef.value =
          !isClickedShowOdrSelectRxDialogRef.value;
      };
  }
};

/**
 * 指定アイテムの関連更新を行う
 */
const updateFormItems = (fieldId: string) => {
  // 品目の情報取得
  if (fieldId === 'matAttr') {
    let overrideTags: FormItem['tags'] = [];
    let overrideRules: FormItem['rules'] = [];
    let overridePropsDisabled = true;
    if (
      odrAddScheduleFormRef.value.formItems.matAttr.formRole ===
      'selectComboBox'
    ) {
      // 品目コードの取得
      const matNoValue =
        odrAddScheduleFormRef.value.formModel.matAttr.toString();

      // 品目分類の取得
      const matSdefValList = getComboBoxOptionList(
        odrAddScheduleFormRef.value.formItems.matAttr.selectOptions, // カスタムフォームのselectOptionsを指定
        matNoValue, // 対象のvalue値(JSONではitemVal)を指定
        'mat_sdef', // 取得したいoptionListのoptionCol名を指定
      );
      const matSdefValue = matSdefValList?.optionValList[0] ?? '';

      // 品名の取得
      const matNm1JpValList = getComboBoxOptionList(
        odrAddScheduleFormRef.value.formItems.matAttr.selectOptions, // 取得したいカスタムフォームのselectOptionsを指定
        matNoValue, // 対象のvalue値(JSONではitemVal)を指定
        'mat_nm1_jp', // 取得したいoptionListのoptionCol名を指定
      );
      const matNm1JpValue = matNm1JpValList?.optionValList[0] ?? '';

      // 単位名の取得
      const wgtUnitNmValList = getComboBoxOptionList(
        odrAddScheduleFormRef.value.formItems.matAttr.selectOptions, // 取得したいカスタムフォームのselectOptionsを指定
        matNoValue, // 対象のvalue値(JSONではitemVal)を指定
        'wgt_unit_nm', // 取得したいoptionListのoptionCol名を指定
      );
      const wgtUnitNmValue = wgtUnitNmValList?.optionValList[0] ?? '';

      // NOTE: C:工程品 D:中間製品 E:製品 空欄は未選択
      switch (matSdefValue) {
        case '':
        case 'C': // fall through
        case 'D': // fall through
          // 計画番号必須
          console.log('計画番号必須');
          overrideTags = [
            { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
          ];
          overrideRules = [
            rules.required('textBox'),
            rules.singleByteNumber(),
            rules.fixedChrLength(PLAN_PRC_NO_FIXED_CHR_LENGTH),
          ];
          overridePropsDisabled = false;
          break;
        case 'E':
          // 計画番号非活性
          console.log('計画番号非活性');
          overrideTags = [];
          overrideRules = [];
          overridePropsDisabled = true;
          break;
        default:
          console.log(`想定外の品目分類:${matSdefValue}`);
          break;
      }
      // 計画番号有効/無効の切り替え
      if (
        odrAddScheduleFormRef.value.formItems.planPrcNo.formRole === 'textBox'
      ) {
        // 有効無効が変化あった時だけ変更対応
        if (
          odrAddScheduleFormRef.value.formItems.planPrcNo.props!.disabled !==
          overridePropsDisabled
        ) {
          odrAddScheduleFormRef.value.formItems.planPrcNo.tags = overrideTags;
          odrAddScheduleFormRef.value.formItems.planPrcNo.rules = overrideRules;
          odrAddScheduleFormRef.value.formItems.planPrcNo.props!.disabled =
            overridePropsDisabled;

          // 有効無効に変化があった場合のみ、内容物初期化
          odrAddScheduleFormRef.value.formItems.planPrcNo.formModelValue = '';

          odrAddScheduleFormRef.value.customForm?.clearValidate('planPrcNo');
          customFormRenderingTriggerRef.value =
            !customFormRenderingTriggerRef.value;
        }
      }

      // 品目コードの設定
      if (odrAddScheduleFormRef.value.formItems.matNo.formRole === 'textBox') {
        odrAddScheduleFormRef.value.formItems.matNo.formModelValue = matNoValue;
      }
      // 品名の設定
      if (odrAddScheduleFormRef.value.formItems.matNm.formRole === 'textBox') {
        odrAddScheduleFormRef.value.formItems.matNm.formModelValue =
          matNm1JpValue;
      }
      // 単位名の設定
      if (odrAddScheduleFormRef.value.formItems.unitNm.formRole === 'suffix') {
        odrAddScheduleFormRef.value.formItems.unitNm.formModelValue =
          wgtUnitNmValue;
      }
    }
  }
};

// 品目のコンボボックス変動を検知
watch(
  () => odrAddScheduleFormRef.value.formItems.matAttr.formModelValue,
  () => {
    // 品目が変更された場合、処方情報の繋がりがなくなる。

    // 品目に紐づく処方情報のフォームだけリセットする
    // 処方名
    odrAddScheduleFormRef.value.formItems.prescriptionNm.formModelValue = '';
    // MBR番号
    odrAddScheduleFormRef.value.formItems.masterBatchRecordNo.formModelValue =
      '';
    // 有効期限
    odrAddScheduleFormRef.value.formItems.prescriptionDts.formModelValue = '';
    // 標準生産量
    odrAddScheduleFormRef.value.formItems.stdPrdQty.formModelValue = '';
    // 計画生産量
    odrAddScheduleFormRef.value.formItems.planQty.formModelValue = '';
  },
);

/**
 * 計画追加ダイアログの初期設定
 */
const odrAddScheduleInit = async () => {
  updateDialogChangeFlagRef(false);
  // FormItems初期化
  odrAddScheduleFormRef.value.formItems = getOdrAddScheduleFormItems();

  // ButtonFormのクリック時イベントの設定
  resetButtonFormOnClickHandler();

  // 標準コンボボックスAPIを挟み込む形でローディング表示
  showLoading();

  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        // 品目
        cmbId: 'matAttrOdrAdd', // formItems内のcmbIdと紐づけ
        condKey: 'm_mat',
        where: { mat_sdef: 'C,D,E' }, // 【C:工程品、D:中間製品、E:製品】
        optionCol: {
          mat_nm1_jp: '1',
          mat_sdef: '2',
          wgt_unit_nm: '3',
        },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setCustomFormComboBoxOptionList(
      odrAddScheduleFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  closeLoading();

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

// ダイアログ起動条件の監視
watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await odrAddScheduleInit();
  },
);
</script>
