import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';

const { t } = i18n.global;

// 小日程計画詳細の縦並び項目定義
const getOrderDetailInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // オーダー番号
    skdNo: {
      label: { text: t('Odr.Chr.txtScheduleNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 計画番号
    planPrcNo: {
      label: { text: t('Odr.Chr.txtPlanProcedureNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 品名
    dspNmJp: {
      label: { text: t('Odr.Chr.txtMaterialName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
    // 製造予定日
    odrDts: {
      label: { text: t('Odr.Chr.txtOrderScheduleDate') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 製造工程
    prcNmJp: {
      label: { text: t('Odr.Chr.txtOrderProcess') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 仕掛品名/工程品名
    prdDspNmJp: {
      label: { text: t('Odr.Chr.txtProcessMaterialName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 標準バッチ回数
    stdBatchTimes: {
      label: { text: t('Odr.Chr.txtStandardBatchTimes') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 標準生産量
    stdPrdQty: {
      label: { text: t('Odr.Chr.txtStandardProductionQuantity') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 単位
    unitNmJp: {
      label: { text: t('Odr.Chr.txtUnit') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
  });

export default getOrderDetailInfoShowItems;
