import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';
import CONST from '@/constants/utils';
import { gCodeFormatSetting } from '@/components/model/SopPA/SopChartSetting';

const { t } = i18n.global;

type TriggerType = 'blur' | 'change';
type RuleTriggerType = TriggerType | TriggerType[];
type RuleItemOption = {
  message: string;
  trigger: RuleTriggerType;
  required?: boolean;
  type?: string;
  pattern?: RegExp | string;
  max?: number | undefined;
  min?: number | undefined;
  difference?: number | undefined;
};
type SearchPatternOption = (option?: {
  message?: string;
  isSearch?: true;
}) => RuleItemOption;

type GCodeRulesOption = {
  gCodeFormat: SearchPatternOption;
};
const gCodeRules: GCodeRulesOption = {
  gCodeFormat: (option = {}) => {
    const { message } = option;
    return {
      message: message || `${t('Cm.Chr.txtGCodeErrorFormat')}`,
      trigger: 'blur',
      pattern: gCodeFormatSetting(),
    };
  },
};

// アイテム定義
export const gCodeDeleteFormItems: CustomFormType['formItems'] = {
  gCodeId: {
    formModelValue: '',
    label: { text: t('SOP.Chr.txtGCodeMemoTitle') },
    formRole: 'textBox',
    props: { disabled: true },
  },
  gCodeExpl: {
    formModelValue: '',
    label: { text: t('SOP.Chr.txtGCodeMemoComment') },
    formRole: 'textBox',
    props: { disabled: true },
  },
};

export const gCodeEditFormItems: CustomFormType['formItems'] = {
  gCodeId: {
    formModelValue: '',
    label: { text: t('SOP.Chr.txtGCodeMemoTitle') },
    formRole: 'textBox',
    rules: [rules.required('textBox'), gCodeRules.gCodeFormat()],
  },
  gCodeExpl: {
    formModelValue: '',
    label: { text: t('SOP.Chr.txtGCodeMemoComment') },
    formRole: 'textBox',
    rules: [
      rules.length(64),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ],
  },
};

export const gCodeAddFormItems: CustomFormType['formItems'] = {
  gCodeId: {
    formModelValue: '',
    label: { text: t('SOP.Chr.txtGCodeMemoTitle') },
    formRole: 'textBox',
    rules: [rules.required('textBox'), gCodeRules.gCodeFormat()],
  },
  gCodeExpl: {
    formModelValue: '',
    label: { text: t('SOP.Chr.txtGCodeMemoComment') },
    formRole: 'textBox',
    rules: [
      rules.length(64),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ],
  },
};

// モデル定義
export const gCodeDeleteFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(gCodeDeleteFormItems);

export const gCodeEditFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(gCodeEditFormItems);

export const gCodeAddFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(gCodeAddFormItems);
