<template>
  <!-- SOPフロー予定ダイアログ -->
  <!-- 見出し SOP予定詳細 -->
  <DialogWindow
    :title="$t('Odr.Chr.txtSOPPlanDetail')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 見出し 小日程計画詳細 -->
    <BaseHeading
      level="2"
      :text="$t('Odr.Chr.txtScheduleDetail')"
      fontSize="24px"
    />
    <!-- 小日程計画詳細の見出し+テキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="orderDetailInfoShowRef.infoShowItems"
      :isLabelVertical="orderDetailInfoShowRef.isLabelVertical"
    />

    <!-- 見出し SOPフロー一覧 -->
    <BaseHeading
      level="2"
      :text="$t('Odr.Chr.txtSOPFlowList')"
      fontSize="24px"
      class="Util_mt-16"
    />
    <!-- SOPフロー一覧テーブル -->
    <TabulatorTable :propsData="tablePropsDataSopListRef" />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { useGetMasterPrescriptionSopFlow } from '@/hooks/useApi';
import {
  GetMasterPrescriptionSopFlowRequestData,
  GetMasterPrescriptionProcessListResProcessData,
} from '@/types/HookUseApi/OdrTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import getOrderDetailInfoShowItems from './odrMasterRxSopFlowList';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey = 'fragmentDialogVisible' | 'messageBoxApiErrorVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

const orderDetailInfoShowRef = ref<InfoShowType>({
  infoShowItems: getOrderDetailInfoShowItems(),
  isLabelVertical: true,
});

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  skdNo: string; // オーダー番号
  selectedRowData: GetMasterPrescriptionProcessListResProcessData | null; // 親ページの選択行情報
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

// SOPフロー一覧用テーブル設定
const tablePropsDataSopListRef = ref<TabulatorTableIF>({
  pageName: 'ProcessList',
  dataID: 'sopFlowNo', // 主キー。ユニークになるものを設定。

  column: [
    // 主キー用。隠しカラムとして運用
    { title: '', field: 'sopFlowNo', hidden: true },
    // SOPフロー名称
    {
      title: 'Odr.Chr.txtSOPFlowName',
      field: 'sopFlowNmJp',
      width: COLUMN_WIDTHS.SOP_FLOW_NM,
    },
    // バッチ展開種別
    {
      title: 'Odr.Chr.txtBatchDeploymentType',
      field: 'sopBatchType',
      width: COLUMN_WIDTHS.ODR.SOP_BATCH_TYPE,
    },
  ],
  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
});

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
];

/**
 * SOPフロー予定ダイアログの初期設定
 */
const odrMasterRxSopFlowListInit = async () => {
  // NOTE:未選択なら処理させない。エラーメッセージはTabulatorTable側で出してくれる。
  if (props.selectedRowData === null) return;
  // eslintエラー回避目的の型ガード
  if (props.selectedRowData.prcSeq === null) return;

  showLoading();

  // SOPフロー予定取得のAPIを行う。
  const requestData: GetMasterPrescriptionSopFlowRequestData = {
    skdNo: props.skdNo,
    prcSeq: props.selectedRowData.prcSeq,
  };
  const { responseRef, errorRef } = await useGetMasterPrescriptionSopFlow({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();

    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // 小日程計画詳細レイアウト用初期値設定
    Object.entries(responseRef.value.data.rData).forEach(([key, value]) => {
      if (key in orderDetailInfoShowRef.value.infoShowItems) {
        orderDetailInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });

    // テーブル設定
    tablePropsDataSopListRef.value.tableData =
      responseRef.value.data.rData.sopFlowList;
  }

  closeLoading();

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await odrMasterRxSopFlowListInit();
  },
);
</script>
