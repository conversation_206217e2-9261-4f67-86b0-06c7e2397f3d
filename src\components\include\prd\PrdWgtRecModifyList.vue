<template>
  <!-- 製造記録確認 承認 参照 秤量記録修正履歴ダイアログ -->
  <DialogWindow
    :title="dialogTitleRef"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 見出し 修正履歴 秤量記録 -->
    <BaseHeading
      level="2"
      :text="$t('Prd.Chr.txtFixLogWeightRecord')"
      fontSize="24px"
    />
    <!-- 修正履歴 秤量記録テーブル -->
    <TabulatorTable :propsData="tablePropsDataWgtRecModifyListRef" />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { useGetConfirmWeighingRecordModifyInfo } from '@/hooks/useApi';
import {
  GetConfirmWeighingRecordModifyInfoRequestData,
  PRD_WGT_REC_MODIFY_LIST_NARROW_TYPE,
} from '@/types/HookUseApi/PrdTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey = 'fragmentDialogVisible' | 'messageBoxApiErrorVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// ダイアログのタイトル（narrowTypeで切り替え）
const dialogTitleRef = ref<string>('');

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  odrNo?: string; // 親ダイアログのodrNo
  prcSeq?: number; // 親ダイアログのprcSeq
  dspNarrowType: string; // 親ダイアログの画面区分
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

// 修正履歴 秤量記録用テーブル設定
const tablePropsDataWgtRecModifyListRef = ref<TabulatorTableIF>({
  pageName: 'PrdWgtRecModifyList',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'riWgtInstGrpNo', // NOTE:この画面は選択するものが無いため、主キー設定不要。適当に設定します。

  column: [
    // 秤量指示書番号
    {
      title: 'Prd.Chr.txtWeightInstructionsNo',
      field: 'riWgtInstGrpNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    // 秤量指示明細番号
    {
      title: 'Prd.Chr.txtWeightOrderDetailNumber',
      field: 'riWgtInstNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    // 秤量ラベル番号
    {
      title: 'Prd.Chr.txtWgtHandLblSid',
      field: 'wgtHandLblSid',
      width: COLUMN_WIDTHS.LBL_NO,
    },
    // 秤量品目コード
    {
      title: 'Prd.Chr.txtWeightMaterialCode',
      field: 'bomMatNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    // 秤量品名
    {
      title: 'Prd.Chr.txtWeightMaterialItemName',
      field: 'bomMatNmJp',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    // 製造工程
    {
      title: 'Prd.Chr.txtOrderProcess',
      field: 'prcNmJp',
      width: COLUMN_WIDTHS.PRC_NM,
    },
    // バッチ番号
    {
      title: 'Prd.Chr.txtBatchNumber',
      field: 'batchNo',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.BAT_NO,
    },
    // 投入番号
    {
      title: 'Prd.Chr.txtBillOfOrder',
      field: 'bomMatSeq',
      width: COLUMN_WIDTHS.PRD.BOM_MAT_SEQ,
    },
    // 修正回数
    {
      title: 'Prd.Chr.txtModifyCount',
      field: 'cmtTimes',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.PRD.CMT_TIMES,
    },
    // 修正前秤取量
    {
      title: 'Prd.Chr.txtBeforeWeightValue',
      field: 'bRecVal1',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 修正後秤取量
    {
      title: 'Prd.Chr.txtAfterWeightValue',
      field: 'aRecVal1',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 修正前バッチ累積秤取量
    {
      title: 'Prd.Chr.txtBeforeBatchWeightValue',
      field: 'bRecVal2',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 修正後バッチ累積秤取量
    {
      title: 'Prd.Chr.txtAfterBatchWeightValue',
      field: 'aRecVal2',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 修正前風袋重量
    {
      title: 'Prd.Chr.txtBeforeWeightTareValue',
      field: 'bRecVal3',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 修正後風袋重量
    {
      title: 'Prd.Chr.txtAfterWeightTareValue',
      field: 'aRecVal3',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 修正前記録者
    {
      title: 'Prd.Chr.txtBeforeRecUsr',
      field: 'bRecVal4',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正後記録者
    {
      title: 'Prd.Chr.txtAfterRecUsr',
      field: 'aRecVal4',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正前作業者1
    {
      title: 'Prd.Chr.txtBeforeWorkUser1',
      field: 'bRecVal5',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正後作業者1
    {
      title: 'Prd.Chr.txtAfterWorkUser1',
      field: 'aRecVal5',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正前作業者2
    {
      title: 'Prd.Chr.txtBeforeWorkUser2',
      field: 'bRecVal6',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正後作業者2
    {
      title: 'Prd.Chr.txtAfterWorkUser2',
      field: 'aRecVal6',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正前作業者3
    {
      title: 'Prd.Chr.txtBeforeWorkUser3',
      field: 'bRecVal7',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正後作業者3
    {
      title: 'Prd.Chr.txtAfterWorkUser3',
      field: 'aRecVal7',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正前作業者4
    {
      title: 'Prd.Chr.txtBeforeWorkUser4',
      field: 'bRecVal8',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正後作業者4
    {
      title: 'Prd.Chr.txtAfterWorkUser4',
      field: 'aRecVal8',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正前作業者5
    {
      title: 'Prd.Chr.txtBeforeWorkUser5',
      field: 'bRecVal9',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正後作業者5
    {
      title: 'Prd.Chr.txtAfterWorkUser5',
      field: 'aRecVal9',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正コメント
    {
      title: 'Prd.Chr.txtModifyComment',
      field: 'modExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
  ],

  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
});

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
];

/**
 * 秤量記録修正履歴ダイアログの初期設定
 */
const PrdWgtRecModifyListInit = async () => {
  const { dspNarrowType } = props;
  // 必須パラメータチェック
  if (props.odrNo === undefined) {
    console.error('odrNoが未定義です。');
    return;
  }

  const requestData: GetConfirmWeighingRecordModifyInfoRequestData = {
    odrNo: props.odrNo,
    prcSeq: props.prcSeq,
  };

  switch (dspNarrowType) {
    case PRD_WGT_REC_MODIFY_LIST_NARROW_TYPE.CONFIRM:
      // タイトル設定
      dialogTitleRef.value = t('Prd.Chr.txtConfirmPrdWgtRecModifyList');

      // 追加の必須パラメータチェック
      if (props.prcSeq === undefined) {
        console.error('prcSeqが未定義です。');
        return;
      }
      break;
    case PRD_WGT_REC_MODIFY_LIST_NARROW_TYPE.APPROVAL:
      // タイトル設定
      dialogTitleRef.value = t('Prd.Chr.txtApprovalPrdWgtRecModifyList');

      // 不要なパラメータチェック
      if (props.prcSeq !== undefined) {
        console.error('使用しないprcSeqが前画面から渡されました。');
        return;
      }
      break;
    case PRD_WGT_REC_MODIFY_LIST_NARROW_TYPE.REFERENCE:
      // タイトル設定
      dialogTitleRef.value = t('Prd.Chr.txtReferencePrdWgtRecModifyList');

      // 不要なパラメータチェック
      if (props.prcSeq !== undefined) {
        console.error('使用しないprcSeqが前画面から渡されました。');
        return;
      }
      break;
    default:
      console.error('想定外のdspNarrowTypeです。', dspNarrowType);
      break;
  }

  if (requestData === null) {
    return;
  }

  showLoading();
  const { responseRef, errorRef } = await useGetConfirmWeighingRecordModifyInfo(
    {
      ...props.privilegesBtnRequestData,
      ...requestData,
    },
  );

  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  closeLoading();
  if (responseRef.value) {
    // ダイアログ表示初期値として、レスポンス情報を格納
    tablePropsDataWgtRecModifyListRef.value.tableData =
      responseRef.value.data.rData.modLogList;
  }

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await PrdWgtRecModifyListInit();
  },
);
</script>
