import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { createFormModelByFormItems } from '@/utils/customForm';

const { t } = i18n.global;

// 指図詳細情報の縦並び項目定義
export const getOrderDetailInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 製造指図番号
    odrNo: {
      label: { text: t('Prd.Chr.txtOrderNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 品目コード
    matNo: {
      label: { text: t('Prd.Chr.txtMaterialCode') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 処方
    rxNmJp: {
      label: { text: t('Prd.Chr.txtPrescriptionName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 品名
    dspNmJp: {
      label: { text: t('Prd.Chr.txtMaterialName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 16,
    },
    // 製造番号
    lotNo: {
      label: { text: t('Prd.Chr.txtLotNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 製造工程
    prcNmJp: {
      label: { text: t('Prd.Chr.txtOrderProcess') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 16,
    },
    // 製造開始日予定
    prdStYmd: {
      label: { text: t('Prd.Chr.txtOrderStartDatePlan') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // SOPフロー名
    sopFlowName: {
      label: { text: t('Prd.Chr.txtSopFlowName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // バッチ番号
    batchNo: {
      label: { text: t('Prd.Chr.txtBatchNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 製造開始日実績
    rsltDts: {
      label: { text: t('Prd.Chr.txtOrderStartDateResults') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // フロー内異状件数
    cmtFlowDev: {
      label: { text: t('Prd.Chr.txtFlowDifferentNumber') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 確認状態
    recConfirmFlg: {
      label: { text: t('Prd.Chr.txtConfirmState') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 16,
    },
  });

// 製造記録確認_SOP作業詳細ダイアログのアイテム定義
export const getDialogFormItems: () => CustomFormType['formItems'] = () => ({
  devExpl: {
    // NOTE: タグを使用しない。
    tags: [],
    formModelValue: '',
    label: { text: t('Prd.Chr.txtDifferentConfirmComment') },
    // NOTE: rulesは実行時に設定されます
    rules: [],
    formRole: 'textComboBox',
    props: {
      clearable: true,
      placeholder: t('Cm.Chr.txtPlaceholderInputComment'),
    },
    selectOptions: [],
    cmbId: 'devExpl2',
  },
});

// 製造記録確認_SOP作業詳細ダイアログのモデル定義
export const dialogFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getDialogFormItems());
