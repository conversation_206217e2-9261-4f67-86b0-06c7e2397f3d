<template>
  <!-- 再バッチ指示登録ダイアログ -->
  <!-- 見出し 再バッチ指示登録 -->
  <DialogWindow
    :title="$t('Odr.Chr.txtReBatchOrderRegistration')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    :class="'odr-regist-rebatch-instruction'"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 見出し 製造指図情報 -->
    <BaseHeading
      level="2"
      :text="$t('Odr.Chr.txtOrderInformation')"
      fontSize="24px"
    />
    <!-- 製造指図情報の見出し+テキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="processDataInfoShowRef.infoShowItems"
      :isLabelVertical="processDataInfoShowRef.isLabelVertical"
    />

    <!-- 見出し 製造工程一覧（バッチ別） -->
    <BaseHeading
      level="2"
      :text="$t('Odr.Chr.txtOrderProcessListByBatch')"
      fontSize="24px"
      class="Util_mt-16"
    />
    <!-- 製造工程一覧テーブル -->
    <TabulatorTable
      :propsData="tablePropsDataProcessListRef"
      :routerName="props.routerName"
      @selectRow="updateSelectedRow"
    />
  </DialogWindow>
  <!-- 再実行SOPフロー選択ダイアログ -->
  <OdrSelectRerunSopFlow
    :isClicked="isClickedShowOrderSelectRerunSopFlowDialogRef"
    :odrNo="initResponseData.odrNo"
    :selectedRowData="selectedRow"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    @submit="requestApiGetOrderProcessListInRebatchInstruction"
  />
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { useGetOrderProcessListInRebatchInstruction } from '@/hooks/useApi';
import {
  GetOrderProcessListInRebatchInstructionRequestData,
  GetOrderProcessListInRebatchInstructionResData,
  GetOrderProcessListInRebatchInstructionData,
} from '@/types/HookUseApi/OdrTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import OdrSelectRerunSopFlow from './OdrSelectRerunSopFlow.vue';
import getProcessDataInfoShowItems from './odrRegistRebatchInstruction';

/**
 * 多言語
 */
const { t } = useI18n();

// 選択行情報の格納
let selectedRow: GetOrderProcessListInRebatchInstructionData | null = null;

// 再バッチ指示登録ダイアログ初期値用 再バッチ指示登録取得APIのレスポンス
let initResponseData: GetOrderProcessListInRebatchInstructionResData = {
  lotNo: '',
  odrNo: '',
  dspNmJp: '',
  odrYmd: '',
  skdAddExpl: '',
  handOverTxt: '',
  odrPrcList: [],
};

// 'SOPフロー選択' クリック
const isClickedShowOrderSelectRerunSopFlowDialogRef = ref<boolean>(false);

// ダイアログの表示切替用定義
type DialogRefKey = 'fragmentDialogVisible' | 'messageBoxApiErrorVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

const processDataInfoShowRef = ref<InfoShowType>({
  infoShowItems: getProcessDataInfoShowItems(),
  isLabelVertical: true,
});

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  odrNo: string; // 親ページのodrNo
  privilegesBtnRequestData: CommonRequestType;
  routerName: string; // TabulatorTable権限 未選択チェック用
};

const props = defineProps<Props>();

// 製造工程一覧用テーブル設定
const tablePropsDataProcessListRef = ref<TabulatorTableIF>({
  pageName: 'ProcessList',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'uniqueKey', // 主キー。ユニークになるものを設定。
  showRadio: true, // ラジオボタンとして使用。

  column: [
    // 製造工程順
    {
      title: 'Odr.Chr.txtOrderProcessSequence',
      field: 'prcSeq',
      width: COLUMN_WIDTHS.ODR.PRC_SEQ,
    },
    // バッチ番号
    {
      title: 'Odr.Chr.txtBatchNo',
      field: 'batchNo',
      width: COLUMN_WIDTHS.BAT_NO,
    },
    // 製造工程
    {
      title: 'Odr.Chr.txtOrderProcess',
      field: 'prcNmJp',
      width: COLUMN_WIDTHS.PRC_NM,
    },
    // 仕掛品名/工程品名
    {
      title: 'Odr.Chr.txtProcessMaterialName',
      field: 'dspNmJp',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    // 予定生産量
    {
      title: 'Odr.Chr.txtPlanneProductionVolume',
      field: 'prdPlanQty',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 単位
    {
      title: 'Odr.Chr.txtUnit',
      field: 'unitNmJp',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    // 製造記録確認
    {
      title: 'Odr.Chr.txtOrderProductionRecord',
      field: 'recConfilm',
      width: COLUMN_WIDTHS.ODR.REC_CONFIRM,
    },
    // 製造指図量調整種別
    {
      title: 'Odr.Chr.txtOrderQuantityAdjustmentType',
      field: 'odrAdjustType',
      width: COLUMN_WIDTHS.ODR.ODR_ADJUST_TYPE,
    },
    // SOPフロー状態
    {
      title: 'Odr.Chr.txtSOPFlowState',
      field: 'sopFlowSts',
      width: COLUMN_WIDTHS.ODR.SOP_FLOW_STS,
    },
    // レスポンスにユニークなデータが存在しないため、自前で隠しカラムでユニーク情報生成
    { title: '', field: 'uniqueKey', hidden: true },
  ],
  // NOTE:権限が親からprops経由で来るため、Init関数内でonSelectBtnsを生成する
  onSelectBtns: [],
  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
});

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
];

// 選択行情報の更新
const updateSelectedRow = (
  v: GetOrderProcessListInRebatchInstructionData | null,
) => {
  selectedRow = v;
};

// 初期処理、再検索で呼び出される
// 製造工程一覧取得APIリクエストとレスポンス情報を格納
const requestApiGetOrderProcessListInRebatchInstruction = async () => {
  if (props.odrNo === '') {
    return Promise.reject();
  }

  showLoading();

  // 製造工程一覧取得のAPIを行う。
  const requestData: GetOrderProcessListInRebatchInstructionRequestData = {
    odrNo: props.odrNo,
  };
  const { responseRef, errorRef } =
    await useGetOrderProcessListInRebatchInstruction({
      ...props.privilegesBtnRequestData,
      ...requestData,
    });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return Promise.reject(); // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return Promise.reject();
  }

  // ダイアログ表示初期値として、レスポンス情報を格納
  initResponseData = responseRef.value.data.rData;

  // 再バッチ指示登録情報レイアウト用初期値設定
  Object.entries(initResponseData).forEach(([key, value]) => {
    if (key in processDataInfoShowRef.value.infoShowItems) {
      processDataInfoShowRef.value.infoShowItems[key].infoShowModelValue =
        value?.toString() ?? '';
    }
  });

  // テーブル設定
  tablePropsDataProcessListRef.value.tableData = initResponseData.odrPrcList;

  // レスポンスに存在しないユニークキーを独自に設定して隠しカラムに仕込む対応
  tablePropsDataProcessListRef.value.tableData.forEach((value) => {
    const tableData = value;
    tableData.uniqueKey = `${value.prcSeq}-${value.batchNo}`;
  });

  closeLoading();
  return Promise.resolve();
};

/**
 * 再バッチ指示登録ダイアログの初期設定
 */
const odrRegistRebatchInstructionInit = async () => {
  // 自身の選択行情報を初期化
  selectedRow = null;

  // 再バッチ指示登録取得APIリクエストとレスポンス情報を格納
  try {
    await requestApiGetOrderProcessListInRebatchInstruction();
  } catch (error) {
    return;
  }

  // NOTE:ボタンに権限付与は親から渡されたものを設定するため、初期化はここで行う。
  tablePropsDataProcessListRef.value.onSelectBtns = [
    {
      text: 'Odr.Chr.btnSOPFlowSelect', // SOPフロー選択
      // NOTE:選択チェック用に権限付与。
      tabulatorActionId: props.privilegesBtnRequestData.btnId,
      clickHandler() {
        //  再実行SOPフロー選択ダイアログを表示開始
        // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
        isClickedShowOrderSelectRerunSopFlowDialogRef.value =
          !isClickedShowOrderSelectRerunSopFlowDialogRef.value;
      },
    },
  ];

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await odrRegistRebatchInstructionInit();
  },
);
</script>
