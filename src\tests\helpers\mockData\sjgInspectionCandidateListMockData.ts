export const mockRCode1000 = {
  rDts: '2024/08/01 12:34:56',
  rCode: 1000,
  rTitle: 'APIレスポンスタイトル',
  rMsg: '1000エラー',
};

export const mockTableData1 = [
  {
    lotSid: '**********$001',
    verifySts: 'IN',
    matNo: '1000001',
    matNm: 'アミノ酸',
    lotNo: '*********',
    shtNm: 'CTJ',
    planYmd: '2025/02/10',
    shelfLifeYmd: '2025/02/10',
    expiryYmd: '2025/02/10',
    lotoutFlg: '1',
    rsltYmd: '2025/02/10',
    lotSts: 'N',
    limsVerifyRecvMatFlg: '0',
    verifyCatStsList: [
      {
        verifyCat: '1',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '2',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '3',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '4',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '5',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '6',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '7',
        verifyCatSts: 'FN',
      },
    ],
  },
];

export const mockTableData2 = [
  {
    lotSid: '**********$001',
    verifySts: 'ST',
    matNo: '1000001',
    matNm: 'アミノ酸',
    lotNo: '*********',
    shtNm: 'CTJ',
    planYmd: '2025/02/10',
    shelfLifeYmd: '2025/02/10',
    expiryYmd: '2025/02/10',
    lotoutFlg: '1',
    rsltYmd: '2025/02/10',
    lotSts: 'N',
    limsVerifyRecvMatFlg: '0',
    verifyCatStsList: [
      {
        verifyCat: '1',
        verifyCatSts: 'IN',
      },
      {
        verifyCat: '2',
        verifyCatSts: 'ST',
      },
      {
        verifyCat: '3',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '4',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '5',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '6',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '7',
        verifyCatSts: 'IN',
      },
    ],
  },
];

export const mockTableData3 = [
  {
    lotSid: '**********$001',
    verifySts: 'FN',
    matNo: '1000001',
    matNm: 'アミノ酸',
    lotNo: '*********',
    shtNm: 'CTJ',
    planYmd: '2025/02/10',
    shelfLifeYmd: '2025/02/10',
    expiryYmd: '2025/02/10',
    lotoutFlg: '1',
    rsltYmd: '2025/02/10',
    lotSts: 'N',
    limsVerifyRecvMatFlg: '0',
    verifyCatStsList: [
      {
        verifyCat: '1',
        verifyCatSts: 'IN',
      },
      {
        verifyCat: '2',
        verifyCatSts: 'ST',
      },
      {
        verifyCat: '3',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '4',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '5',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '6',
        verifyCatSts: 'FN',
      },
      {
        verifyCat: '7',
        verifyCatSts: 'IN',
      },
    ],
  },
];
export const mockGetVerifyList = {
  rDts: '2024/08/01 12:34:56',
  rCode: 200,
  rTitle: 'APIレスポンスタイトル',
  rMsg: 'レスポンスメッセージ',
  rData: {
    verifyList: [
      {
        lotSid: '**********$001',
        verifySts: 'IN',
        matNo: '1000001',
        matNm: 'アミノ酸',
        lotNo: '*********',
        shtNm: 'CTJ',
        planYmd: '2025/02/10',
        shelfLifeYmd: '2025/02/10',
        expiryYmd: '2025/02/10',
        lotoutFlg: '1',
        rsltYmd: '2025/02/10',
        lotSts: 'N',
        limsVerifyRecvMatFlg: '0',
        verifyCatStsList: [
          {
            verifyCat: '1',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '2',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '3',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '4',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '5',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '6',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '7',
            verifyCatSts: 'FN',
          },
        ],
      },
    ],
  },
};

export const mockGetVerifyListST = {
  rDts: '2024/08/01 12:34:56',
  rCode: 200,
  rTitle: 'APIレスポンスタイトル',
  rMsg: 'レスポンスメッセージ',
  rData: {
    verifyList: [
      {
        lotSid: '**********$001',
        verifySts: 'ST',
        matNo: '1000001',
        matNm: 'アミノ酸',
        lotNo: '*********',
        shtNm: 'CTJ',
        planYmd: '2025/02/10',
        shelfLifeYmd: '2025/02/10',
        expiryYmd: '2025/02/10',
        lotoutFlg: '1',
        rsltYmd: '2025/02/10',
        lotSts: 'N',
        limsVerifyRecvMatFlg: '0',
        verifyCatStsList: [
          {
            verifyCat: '1',
            verifyCatSts: 'IN',
          },
          {
            verifyCat: '2',
            verifyCatSts: 'ST',
          },
          {
            verifyCat: '3',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '4',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '5',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '6',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '7',
            verifyCatSts: 'IN',
          },
        ],
      },
    ],
  },
};

export const mockGetVerifyListFN = {
  rDts: '2024/08/01 12:34:56',
  rCode: 200,
  rTitle: 'APIレスポンスタイトル',
  rMsg: 'レスポンスメッセージ',
  rData: {
    verifyList: [
      {
        lotSid: '**********$001',
        verifySts: 'FN',
        matNo: '1000001',
        matNm: 'アミノ酸',
        lotNo: '*********',
        shtNm: 'CTJ',
        planYmd: '2025/02/10',
        shelfLifeYmd: '2025/02/10',
        expiryYmd: '2025/02/10',
        lotoutFlg: '1',
        rsltYmd: '2025/02/10',
        lotSts: 'N',
        limsVerifyRecvMatFlg: '0',
        verifyCatStsList: [
          {
            verifyCat: '1',
            verifyCatSts: 'IN',
          },
          {
            verifyCat: '2',
            verifyCatSts: 'ST',
          },
          {
            verifyCat: '3',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '4',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '5',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '6',
            verifyCatSts: 'FN',
          },
          {
            verifyCat: '7',
            verifyCatSts: 'IN',
          },
        ],
      },
    ],
  },
};

export const mockSelectedRowData = {
  lotSid: '**********$001',
  verifySts: '未実施',
  matNo: '1000001',
  matNm: 'アミノ酸',
  lotNo: '*********',
  shtNm: 'CTJ',
  planYmd: '2025/02/10',
  shelfLifeYmd: '2025/02/10',
  expiryYmd: '2025/02/10',
  lotout: '1',
  lotoutFlg: '〇',
  rsltYmd: '2025/02/10',
  lotSts: 'N',
  limsVerifyRecvMatFlg: '0',
  verifyCatStsList: [
    { verifyCat: '1', verifyCatSts: 'IN' },
    { verifyCat: '2', verifyCatSts: 'ST' },
    { verifyCat: '3', verifyCatSts: 'FN' },
    { verifyCat: '4', verifyCatSts: 'FN' },
    { verifyCat: '5', verifyCatSts: 'FN' },
    { verifyCat: '6', verifyCatSts: 'FN' },
    { verifyCat: '7', verifyCatSts: 'IN' },
  ],
};

export const mockAddLotVerify = {
  rDts: '2024/08/01 12:34:56',
  rCode: 200,
  rTitle: 'APIレスポンスタイトル',
  rMsg: 'レスポンスメッセージ',
  rData: {},
};
