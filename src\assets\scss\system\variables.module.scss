$blue100: #0041c0; // ボタン色
$gray156: #343434; // メイン文字色
$gray285: #5e5e61; // サプ文字色
$gray655: #dadada; // 検索条件の外縁
$gray447: #949497; // キャンセル・未選択・カラムヘッダ
$blue639: #becff2; // データグリッド選択済み色
$blue727: #eaeeff; // ラジオボタン・チェックボックス選択済み
$gray720: #f0f0f0; // 背景
$blue187: #002f8c; // ボタン色（アクティブ）
$orange720: #cd820a; // ボタン色（フォーカス）
$white750: #fafafa; // ウィンドウ内の色 セカンダリボタンの色
$red330: #ff0000; // デンジャーボタン
$green300: #03af7a; // TextBoxの「ワイルドカード可」の色
$yellow496: #fff100; // 未使用
$orange416: #f6aa00; // 「※検索条件設定中」の色

$menuTextColor: #f9f9fa; // SideBarのメニューアイテムのテキストカラー
$menuIconColor: #f9f9fa; // SideBarのメニューアイテムのアイコンカラー
$menuHover: #1061ff; // SideBarのメニューアイテムのマウスオーバー時の色
$activeMenuBKColor: #4b88ff; // SideBarのアクティブメニューアイテムの背景色
$focus200: #e8f1fe; // セカンダリボタンのマウスオーバー時の色
$danger100: #fab6b6; // デンジャーボタンのマウスオーバー／Disable時の色

$tabulatorRowRed: #ff4b00; // Tabulator行色 赤
$tabulatorRowYellow: #fff100; // Tabulator行色 黄
$tabulatorRowOrange: #f6aa00; // Tabulator行色 橙
$tabulatorRowGrey: #949497; // Tabulator行色 灰
$tabulatorRowBlue: #73b5ff; // Tabulator行色 青

$pageSizeWidth: 1280px;
$pageSizeHeight: 1024px;
$sideBarWidth: 300px;
$navbarHeight: 88px; // 45px（パンくず） + 8px（padding-top） + 28px（見出し） + 7px（余白）=88px
$fontSize: 13px;
$tabulatorFontSize: 14px;
$svgSize: 24px;
$padding: 8px;
$sidebarFooterHeight: 24px;
$defaultBorderWidth: 1px;

// コンポーネントの横幅、高さ
$widthLarge: 456px;
$widthMiddle: 336px;
$widthSmall: 200px;
$height: 32px;

$sidebarTransitionTime: 1s;

$fontFamilyZHK: 'Arial', 'Microsoft YaHei UI', sans-serif;
$fontFamilyJP: 'BIZ UDPゴシック', 'Arial', sans-serif;
$fontFamilyEN: 'Meiryo', 'Arial', sans-serif;

$tooltipZIndex: 20000 !important;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  blue100: $blue100;
  gray156: $gray156;
  gray285: $gray285;
  gray655: $gray655;
  gray447: $gray447;
  blue639: $blue639;
  blue727: $blue727;
  gray720: $gray720;
  blue187: $blue187;
  orange720: $orange720;
  white750: $white750;
  red330: $red330;
  green300: $green300;
  yellow496: $yellow496;
  orange416: $orange416;
  menuHover: $menuHover;
  activeMenuBKColor: $activeMenuBKColor;
  focus200: $focus200;
  danger100: $danger100;
  pageSizeWidth: $pageSizeWidth;
  pageSizeHeight: $pageSizeHeight;
  sideBarWidth: $sideBarWidth;
  navbarHeight: $navbarHeight;
  fontSize: $fontSize;
  tabulatorFontSize: $tabulatorFontSize;
  svgSize: $svgSize;
  padding: $padding;
  sidebarFooterHeight: $sidebarFooterHeight;
  defaultBorderWidth: $defaultBorderWidth;
  widthLarge: $widthLarge;
  widthMiddle: $widthMiddle;
  widthSmall: $widthSmall;
  height: $height;
  sidebarTransitionTime: $sidebarTransitionTime;
  fontFamilyZHK: $fontFamilyZHK;
  fontFamilyJP: $fontFamilyJP;
  fontFamilyEN: $fontFamilyEN;
  tooltipZIndex: $tooltipZIndex;
}
