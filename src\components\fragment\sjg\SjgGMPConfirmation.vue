<template>
  <!-- GMP確認ダイアログ -->
  <DialogWindow
    :title="dialogTitle"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :leftButtons="dialogLeftButtons"
    :rightButtons="dialogRightButtons"
    width="1400"
  >
    <BaseHeading
      class="Util_pt-16"
      level="2"
      fontSize="24px"
      :text="$t('Sjg.Chr.txtInfoItem')"
    />
    <div>
      <InfoShow
        class="Util_mt-16"
        :infoShowItems="inspectionInfoShowRef.infoShowItems"
        :isLabelVertical="inspectionInfoShowRef.isLabelVertical"
        fontSizeLabel="12px"
        fontSizeContent="16px"
      />
    </div>
    <BaseHeading
      class="Util_mt-48"
      level="2"
      fontSize="24px"
      :text="$t('Sjg.Chr.txtGMPConfirmation')"
    />
    <div class="sjg-gmp-confirmation_content-selectinspectionitem Util_mt-16">
      <CustomForm
        :triggerRendering="customFormRenderingTriggerRef"
        :formModel="sjgGMPConfirmationRef.formModel"
        :formItems="sjgGMPConfirmationRef.formItems"
        @visible="
          (v: CustomFormType['customForm']) => {
            sjgGMPConfirmationRef.customForm = v;
          }
        "
        @change="updateFormItems"
        @changeFormModel="
          (changeFlag) => {
            updateDialogChangeFlagRef(changeFlag);
          }
        "
        width="100%"
        class="Util_mb-24"
      />
    </div>
    <TabulatorTable
      :propsData="tablePropsDialogRef"
      @selectRow="updateSelectedRow"
      :routerName="props.routerName"
    />
    <sjgGMPConfirmationAddAndEditDetails
      :selectedRowData="selectedRow"
      :isClicked="********************************"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :verifyCat="props.selectedRowData!.verifyCat"
      :verifyCatSts="props.selectedRowData!.verifyCatSts"
      :gmpDocNeedTypeNmList="gmpDocNeedTypeNmList"
      :dialogType="dialogType"
      @submit="onAddGMPConfirmationDetailsHandler"
    />
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <MessageBox
    v-if="dialogVisibleRef.infoSingleButton"
    :dialogProps="messageBoxInfoSingleButtonRef"
    :submitCallback="() => closeDialog('infoSingleButton')"
  />
  <!-- GMP確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.sjgGMPConfirmationConfirm"
    :dialogProps="sjgGMPConfirmationConfirmPropsRef"
    :cancelCallback="() => closeDialog('sjgGMPConfirmationConfirm')"
    :submitCallback="apiHandler"
  />
  <!-- GMP確認項目の切替により入力内容をクリアする -->
  <MessageBox
    v-if="dialogVisibleRef.changeGMPRsltConfirm"
    :dialogProps="messageBoxChangeGMPRsltConfirmRef"
    :cancelCallback="cancelChangeGMPRsltConfirm"
    :submitCallback="submitChangeGMPRsltConfirm"
  />
  <MessageBox
    v-if="dialogVisibleRef.messageBoxModifyGmpVerifyVisible"
    :dialogProps="messageBoxModifyGmpVerifyPropsRef"
    :cancelCallback="
      () => handleCloseMessageBox('messageBoxModifyGmpVerifyVisible')
    "
    :submitCallback="
      () => closeVerifyStartDialog(messageBoxModifyGmpVerifyPropsRef)
    "
  />
  <MessageBox
    v-if="dialogVisibleRef.sjgInfoValidCheckError"
    :dialogProps="messageBoxSjgInfoValidCheckErrorRef"
    :submitCallback="() => closeSjgInfoValidCheckError()"
  />
  <MessageBox
    v-if="dialogVisibleRef.modifyGmpVerifyStartError"
    :dialogProps="messageBoxModifyGmpVerifyStartErrorRef"
    :submitCallback="() => handleCloseMessageBox('modifyGmpVerifyStartError')"
  />
  <MessageBox
    v-if="dialogVisibleRef.maxCountAddCheck"
    :dialogProps="messageBoxMaxCountAddCheck"
    :submitCallback="() => handleCloseMessageBox('maxCountAddCheck')"
  />
  <!-- 明細削除の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxDeleteConfirm"
    :dialogProps="messageBoxDeleteConfirmProps"
    :cancelCallback="() => closeDialog('messageBoxDeleteConfirm')"
    :submitCallback="sjgGMPconfirmationDetailsDelete"
  />
</template>
<script setup lang="ts">
import { nextTick, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import CONST from '@/constants/utils';
import SCREENID from '@/constants/screenId';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import {
  ExtendCommonRequestType,
  CommonRequestType,
  ComboBoxDataStandardReturnData,
} from '@/types/HookUseApi/CommonTypes';
import {
  VerifyItemList,
  SelectInspectionItemData,
  GetGMPReq,
  CheckVerifyStatusReq,
  ModifyGMPFinishReq,
  GmpInfoListData,
  ModifyGmpVerifyStartReq,
  ModifyGMPStopReq,
  SelectGmpConfItemData,
  GMPRequestData,
  GmpConfItemListData,
  GmpDocNeedTypeNmListData,
} from '@/types/HookUseApi/SjgTypes';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import createMessageBoxForm from '@/utils/commentMessageBox';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import {
  useGetGMP,
  useModifyGMPStop,
  useCheckVerifyStatus,
  useModifyGMPFinish,
  useModifyGmpVerifyStart,
  useCheckValidVerify,
} from '@/hooks/useApi';
import InfoShow from '@/components/parts/InfoShow.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import { InfoShowType } from '@/types/InfoShowTypes';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import { getInspectionInfoShowItems } from '@/components/page/sjg/sjgSelectInspectionItem';
import sjgGMPConfirmationAddAndEditDetails from '@/components/fragment/sjg/SjgSelectInspectionItem/SjgGMPConfirmationAddAndEditDetails.vue';
import CONST_FLAGS from '@/constants/flags';
import { v4 as uuidv4 } from 'uuid';
import {
  sjgGMPConfirmationFormRadioModel,
  getSjgGMPConfirmationFormRadioItems,
  tablePropsData,
} from './sjgGMPConfirmation';

type Props = {
  selectedRowData: VerifyItemList | null;
  selectedInspectionItem: SelectInspectionItemData | null;
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
  screenId: string;
  comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;
  routerName: string; // TabulatorTable権限 未選択チェック用
};

const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);
const router = useRouter();

/**
 * 警告要否フラグ
 * - UNNECESSARY: 警告が不要であることを表します（値: 0）。
 * - NECESSITY: 警告が必要であることを表します（値: 1）：デフォルト値。
 */
const WARNING_NECESSITY_FLAG = {
  UNNECESSARY: 0,
  NECESSITY: 1,
} as const;

// 選択行データ
let selectedRow: SelectGmpConfItemData | null = null;
let gmpInfoListData: GmpInfoListData[] = [];
let gmpDocNeedTypeNmList: GmpDocNeedTypeNmListData[] = [];
let verifyGmpRsltTemp: string = '';
let gmpItemMaxCntVal: number;
let dialogType: string = '';
const customFormRenderingTriggerRef = ref(false);
type DialogRefKey =
  | 'singleButton'
  | 'modifyGmpVerifyStartError'
  | 'infoSingleButton'
  | 'sjgGMPConfirmationConfirm'
  | 'fragmentDialogVisible'
  | 'changeGMPRsltConfirm'
  | 'messageBoxModifyGmpVerifyVisible'
  | 'sjgInfoValidCheckError'
  | 'maxCountAddCheck'
  | 'messageBoxDeleteConfirm';

const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  modifyGmpVerifyStartError: false,
  infoSingleButton: false,
  sjgGMPConfirmationConfirm: false,
  fragmentDialogVisible: false,
  changeGMPRsltConfirm: false,
  messageBoxModifyGmpVerifyVisible: false,
  sjgInfoValidCheckError: false,
  maxCountAddCheck: false,
  messageBoxDeleteConfirm: false,
};

const ******************************** = ref<boolean>(false);

let dialogTitle: string = '';
const isConfirmation = () =>
  props.screenId ===
    SCREENID.SJG_GMP_CONFIRMATION_DEVIATION_HANDLING_CONFIRMATION ||
  props.screenId === SCREENID.SJG_GMP_CONFIRMATION_VALIDATION_CONFIRMATION ||
  props.screenId ===
    SCREENID.SJG_GMP_CONFIRMATION_CHANGE_MANAGEMENT_CONFIRMATION ||
  props.screenId === SCREENID.SJG_GMP_CONFIRMATION_SPECIAL_TASK_CONFIRMATION ||
  props.screenId ===
    SCREENID.SJG_GMP_CONFIRMATION_MATERIAL_REVISION_CONFIRMATION;

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const sjgGMPConfirmationRef = ref<CustomFormType>({
  formItems: getSjgGMPConfirmationFormRadioItems(''),
  formModel: sjgGMPConfirmationFormRadioModel(''),
});

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const messageBoxInfoSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const sjgGMPConfirmationConfirmPropsRef = ref<DialogProps>({
  title: t('Sjg.Msg.txtGMPConfirmationConfirmTitle'),
  content: '',
  type: 'question',
});

const getGMPReqData = ref<GetGMPReq>({
  lotSid: '',
  verifyCat: '',
});

const messageBoxChangeGMPRsltConfirmRef: DialogProps = {
  title: t('Sjg.Msg.txtChangeGMPRsltConfirmTitle'),
  content: t('Sjg.Msg.txtChangeGMPRsltConfirmInfo'),
  type: 'question',
};

const messageBoxConfirmForm = createMessageBoxForm(
  'message',
  'cmtSjgVeriOther',
);

const messageBoxModifyGmpVerifyPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isPrompt: true,
  formModel: messageBoxConfirmForm.formModel,
  formItems: messageBoxConfirmForm.formItems,
  type: 'warning',
});

// 照査データ有効チェックエラーのメッセージ
const messageBoxSjgInfoValidCheckErrorRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const messageBoxModifyGmpVerifyStartErrorRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

// 明細削除の確認メッセージボックス
const messageBoxDeleteConfirmProps: DialogProps = {
  title: t('Sjg.Msg.deleteDetailsConfirmTitle'),
  content: t('Sjg.Msg.deleteDetailsConfirmMessage'),
  type: 'question',
};

// 追加可否チェック
const messageBoxMaxCountAddCheck = ref<DialogProps>({
  title: '',
  content: '',
  type: 'error',
  isSingleBtn: true,
});

const tablePropsDialogRef = ref<TabulatorTableIF>({
  ...tablePropsData,
  onSelectBtns: [
    {
      text: 'Sjg.Chr.btnDetailDel',
      clickHandler() {
        if (selectedRow === null) {
          return;
        }
        // 明細削除の確認メッセージダイアログ表示開始
        openDialog('messageBoxDeleteConfirm');
      },
    },
    {
      text: 'Sjg.Chr.btnDetailEdit',
      clickHandler() {
        dialogType = CONST_FLAGS.SJG.GMP_DIALOG_TYPE_STATUS.EDIT;
        ********************************.value =
          !********************************.value;
      },
    },
    {
      text: 'Sjg.Chr.btnDetailAdd',
      skipClickTabulatorCommonAction: true,
      clickHandler() {
        dialogType = CONST_FLAGS.SJG.GMP_DIALOG_TYPE_STATUS.ADD;
        if (tablePropsDialogRef.value.tableData.length === gmpItemMaxCntVal) {
          messageBoxMaxCountAddCheck.value.title = t(
            'Sjg.Msg.maxGMPCountTitle',
          );
          messageBoxMaxCountAddCheck.value.content = t(
            'Sjg.Msg.maxGMPCountMessage',
            [gmpItemMaxCntVal],
          );
          openDialog('maxCountAddCheck');
        } else {
          ********************************.value =
            !********************************.value;
        }
      },
    },
  ],
});

const isGmpDataActive = (index: number): boolean =>
  gmpInfoListData.length === 0 ||
  gmpInfoListData[index]?.gmpInactiveFlg !==
    CONST_FLAGS.SJG.GMP_INACTIVE_FLAG.INACTIVE;

const handleCloseFragmentDialog = () => {
  closeDialog('fragmentDialogVisible');
  if (!isConfirmation()) emit('submit', props.privilegesBtnRequestData);
};

const handleCloseMessageBox = (closeDialogRefKey: DialogRefKey) => {
  closeDialog(closeDialogRefKey);
  if (!isConfirmation()) emit('submit', props.privilegesBtnRequestData);
};

const getGmpConfItemList: () => GmpConfItemListData[] = () =>
  tablePropsDialogRef.value.tableData.map((item) => ({
    lotSid: item.lotSid?.toString() ?? props.selectedInspectionItem!.lotSid,
    gmpSysMngNo: Number(item.gmpSysMngNo),
    gmpMngNo: item.gmpMngNo?.toString() ?? '',
    gmpTitle: item.gmpTitle?.toString() ?? '',
    gmpDes: item.gmpDes?.toString() ?? '',
    gmpDocNeedTypeVerify: item.gmpDocNeedTypeVerify?.toString() ?? '',
  }));

const checkValidVerifyHandler = async (isConfirm: boolean) => {
  let apiRequestData: ExtendCommonRequestType<{ lotSid: string }> = {
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItem!.lotSid,
  };
  if (isConfirm) {
    apiRequestData = {
      ...apiRequestData,
      msgboxTitleTxt: sjgGMPConfirmationConfirmPropsRef.value.title,
      msgboxMsgTxt: sjgGMPConfirmationConfirmPropsRef.value.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
    };
  }
  // 照査情報有効チェック
  const { errorRef } = await useCheckValidVerify(apiRequestData);
  if (errorRef.value) {
    closeLoading();
    messageBoxSjgInfoValidCheckErrorRef.value.title =
      errorRef.value.response.rTitle;
    messageBoxSjgInfoValidCheckErrorRef.value.content =
      errorRef.value.response.rMsg;
    openDialog('sjgInfoValidCheckError');
    return false;
  }
  return true;
};

const pauseGMPConfirmationForm = async () => {
  // GMP確認結果(PS:有/AS:無)
  if (
    sjgGMPConfirmationRef.value.formItems.gmpRslt.formModelValue ===
      verifyGmpRsltTemp &&
    tablePropsDialogRef.value.tableData.length === gmpInfoListData.length &&
    !tablePropsDialogRef.value.tableData.some((item, index) => {
      const gmpItem = gmpInfoListData[index];
      return (
        item.gmpMngNo !== gmpItem.gmpMngNo ||
        item.gmpTitle !== gmpItem.gmpTitle ||
        item.gmpDes !== gmpItem.gmpDes ||
        item.gmpDocNeedTypeVerify !== gmpItem.gmpDocNeedTypeVerify
      );
    })
  ) {
    messageBoxSingleButtonRef.value.title = t(
      'Sjg.Msg.txtChenkModifyGMPStopTitle',
    );
    messageBoxSingleButtonRef.value.content = t(
      'Sjg.Msg.txtChenkModifyGMPStopInfo',
    );
    openDialog('singleButton');
    return false;
  }
  if (!(await checkValidVerifyHandler(false))) {
    return false;
  }

  const gmpRsltValue =
    sjgGMPConfirmationRef.value.formModel.gmpRslt === ''
      ? CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.INITIAL
      : sjgGMPConfirmationRef.value.formModel.gmpRslt.toString();

  const gmpConfItemList = getGmpConfItemList();
  const modifyGMPStopRequestData: ExtendCommonRequestType<ModifyGMPStopReq> = {
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItem!.lotSid,
    verifyCat: props.selectedRowData!.verifyCat,
    gmpRslt: gmpRsltValue,
    gmpConfItemList,
  };

  const { responseRef, errorRef } = await useModifyGMPStop(
    modifyGMPStopRequestData,
  );

  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    openDialog('singleButton');
    return false;
  }
  if (responseRef.value) {
    handleCloseFragmentDialog();
  }
  return true;
};

const leftButtonsList: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    disabled: false,
    clickHandler() {
      handleCloseFragmentDialog();
    },
  },
  {
    text: t('Cm.Chr.btnPause'),
    type: 'secondary',
    size: 'normal',
    disabled: false,
    clickHandler() {
      pauseGMPConfirmationForm();
    },
  },
];

/**
 * GMP確認チェック
 */
const checkGMPConfirmationForm = async () => {
  let gmpRsltChangeFlag = false;
  if (
    sjgGMPConfirmationRef.value.formItems.gmpRslt.formModelValue ===
    CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.INITIAL
  ) {
    gmpRsltChangeFlag = true;
    sjgGMPConfirmationRef.value.formItems.gmpRslt.formModelValue = '';
    await nextTick();
  }
  const validate =
    sjgGMPConfirmationRef.value.customForm !== undefined &&
    (await sjgGMPConfirmationRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (validate) {
    const hasActiveItems = tablePropsDialogRef.value.tableData.some(
      (_, index) => isGmpDataActive(index),
    );
    // バリデーションが有の場合、
    // 転記分（非活性）を除いて１件以上のGMP確認結果の入力が無い場合はチェックNG
    if (
      sjgGMPConfirmationRef.value.formItems.gmpRslt.formModelValue ===
        CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.PRESENCE &&
      !hasActiveItems
    ) {
      messageBoxSingleButtonRef.value.title = t(
        'Sjg.Msg.txtGMPInputRequiredTitle',
      );
      messageBoxSingleButtonRef.value.content = t(
        'Sjg.Msg.gmpRequiedCheckMessage',
      );
      closeLoading();
      openDialog('singleButton');
      return false;
    }

    // 入力確定
    sjgGMPConfirmationConfirmPropsRef.value.content = t(
      'Sjg.Msg.txtGMPConfirmationConfirmInfo',
      [t(`Sjg.Chr.txtVerifyGmpRslt${props.selectedRowData!.verifyCat}`)],
    );
    openDialog('sjgGMPConfirmationConfirm');
  }
  if (gmpRsltChangeFlag) {
    sjgGMPConfirmationRef.value.formItems.gmpRslt.formModelValue =
      CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.INITIAL;
  }
  return true;
};

const rightButtonsList: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnInputConfirm'),
    type: 'primary',
    size: 'normal',
    clickHandler: () => {
      checkGMPConfirmationForm();
      return false;
    },
  },
];
const dialogLeftButtons = ref<DialogWindowProps['buttons']>(leftButtonsList);
const dialogRightButtons = ref<DialogWindowProps['buttons']>(rightButtonsList);

const inspectionInfoShowRef = ref<InfoShowType>({
  infoShowItems: getInspectionInfoShowItems(),
  isLabelVertical: true,
});

const resetGMPRsltData = (
  itemIsDisabled: boolean,
  isOnSelectBtnDisabled: boolean,
  isEmpty: boolean = false,
) => {
  if (itemIsDisabled) {
    tablePropsDialogRef.value.tableData = gmpInfoListData.map((item) => {
      const rectItem = {
        ...item,
        gmpInactiveFlg: CONST_FLAGS.SJG.GMP_INACTIVE_FLAG.INACTIVE,
      };
      return rectItem;
    });
  }
  if (isEmpty) {
    const nonActiveItems = tablePropsDialogRef.value.tableData.filter(
      (_, index) => !isGmpDataActive(index),
    );
    tablePropsDialogRef.value.tableData = [...nonActiveItems];
  }
  tablePropsDialogRef.value.onSelectBtns!.forEach((_, index) => {
    tablePropsDialogRef.value.onSelectBtns![index].disabled =
      isOnSelectBtnDisabled;
  });
};

const changeGMPRslt = () => {
  // GMP確認結果(PS:有/AS:無)
  const hasActiveItems = tablePropsDialogRef.value.tableData.some((_, index) =>
    isGmpDataActive(index),
  );
  if (
    sjgGMPConfirmationRef.value.formItems.gmpRslt.formModelValue ===
      CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.ABSENCE &&
    hasActiveItems
  ) {
    openDialog('changeGMPRsltConfirm');
  } else if (
    sjgGMPConfirmationRef.value.formItems.gmpRslt.formModelValue ===
    CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.ABSENCE
  ) {
    resetGMPRsltData(false, true, true);
  } else {
    resetGMPRsltData(false, false);
  }
};

const updateFormItems = () => {
  if (sjgGMPConfirmationRef.value.formItems.gmpRslt) {
    changeGMPRslt();
  }
};

const setFootBtnDisable = () => {
  // 照査完了後の確認の場合
  let dialogLeftButtonsTemp: DialogWindowProps['buttons'] = [];
  if (
    props.selectedRowData!.verifyCatSts ===
      CONST_FLAGS.SJG.GMP_VERIFY_STATUS.FINISH ||
    isConfirmation()
  ) {
    dialogLeftButtonsTemp = leftButtonsList.slice(0, 1);
    dialogRightButtons.value = [];
  } else {
    dialogLeftButtonsTemp = leftButtonsList;
    dialogRightButtons.value = rightButtonsList;
  }
  dialogLeftButtons.value = dialogLeftButtonsTemp;
};

const modifyGMPFinish = async () => {
  const gmpConfItemList = getGmpConfItemList();
  const modifyGMPFinishRequestData: ExtendCommonRequestType<ModifyGMPFinishReq> =
    {
      ...props.privilegesBtnRequestData,
      lotSid: props.selectedInspectionItem!.lotSid,
      verifyCat: props.selectedRowData!.verifyCat,
      gmpRslt:
        sjgGMPConfirmationRef.value.formItems.gmpRslt?.formModelValue.toString(),
      msgboxTitleTxt: sjgGMPConfirmationConfirmPropsRef.value.title,
      msgboxMsgTxt: sjgGMPConfirmationConfirmPropsRef.value.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      gmpConfItemList,
    };

  const { responseRef, errorRef } = await useModifyGMPFinish(
    modifyGMPFinishRequestData,
  );
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    openDialog('singleButton');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    handleCloseFragmentDialog();
  }
  return true;
};

/**
 * 明細削除
 */
const sjgGMPconfirmationDetailsDelete = () => {
  closeDialog('messageBoxDeleteConfirm');
  tablePropsDialogRef.value.tableData =
    tablePropsDialogRef.value.tableData.filter(
      (item) => item.uniqueKey !== selectedRow!.uniqueKey,
    );
  tablePropsDialogRef.value.selectRowData = '';
};

const onAddGMPConfirmationDetailsHandler = async (
  requestData: GMPRequestData,
  actionType: string,
) => {
  const sjgGMPConfirmationRequestData = {
    ...requestData,
    matNo: props.selectedInspectionItem!.matNo,
    matNm: props.selectedInspectionItem!.matNm,
    shtNm: props.selectedInspectionItem!.shtNm,
    gmpInactiveFlg: CONST_FLAGS.SJG.GMP_INACTIVE_FLAG.ACTIVE,
  };
  let newUniqueKey: string;
  if (actionType === CONST_FLAGS.SJG.GMP_DIALOG_TYPE_STATUS.ADD) {
    newUniqueKey = `${props.selectedInspectionItem!.matNo}-${uuidv4()}`;
    tablePropsDialogRef.value.tableData = [
      ...tablePropsDialogRef.value.tableData,
      {
        ...sjgGMPConfirmationRequestData,
        uniqueKey: newUniqueKey,
      },
    ];
  } else {
    newUniqueKey = selectedRow!.uniqueKey;
    tablePropsDialogRef.value.tableData =
      tablePropsDialogRef.value.tableData.map((item) =>
        item.uniqueKey === newUniqueKey
          ? {
              ...sjgGMPConfirmationRequestData,
              uniqueKey: newUniqueKey,
              gmpInactiveFlg:
                item.gmpInactiveFlg || CONST_FLAGS.SJG.GMP_INACTIVE_FLAG.ACTIVE,
            }
          : item,
      );
  }
  tablePropsDialogRef.value.selectRowData = newUniqueKey;
};

const apiHandler = async () => {
  closeDialog('sjgGMPConfirmationConfirm');
  showLoading();
  if (!(await checkValidVerifyHandler(true))) {
    return false;
  }
  const checkVerifyStatusRequestData: ExtendCommonRequestType<CheckVerifyStatusReq> =
    {
      ...props.privilegesBtnRequestData,
      lotSid: props.selectedInspectionItem!.lotSid,
      verifyCat: props.selectedRowData!.verifyCat,
      msgboxTitleTxt: sjgGMPConfirmationConfirmPropsRef.value.title,
      msgboxMsgTxt: sjgGMPConfirmationConfirmPropsRef.value.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
    };
  const { responseRef, errorRef } = await useCheckVerifyStatus(
    checkVerifyStatusRequestData,
  );
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    openDialog('singleButton');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    // 顔認証を行うための画面(W99A200)から顔認証を行う必要がある
    // 認証が成功した場合、下記の処理を行う
    try {
      // 署名ダイアログを表示
      await showSignDialog({
        commonRequestParam: {
          ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
        },
      });
    } catch (error) {
      return false;
    }
    await modifyGMPFinish();
  }
  closeLoading();
  return true;
};

const setDialogTitle = () => {
  switch (props.selectedRowData!.verifyCat) {
    case CONST_FLAGS.SJG.VERIFY_CAT_STATUS.GMP_DEVIATION_HANDLING:
      return isConfirmation()
        ? t('Sjg.Chr.txtGMPConfirmationDeviationHandlingConfirmation')
        : t('Sjg.Chr.txtGMPConfirmationDeviationHandling');
    case CONST_FLAGS.SJG.VERIFY_CAT_STATUS.GMP_VALIDATION:
      return isConfirmation()
        ? t('Sjg.Chr.txtGMPConfirmationValidationConfirmation')
        : t('Sjg.Chr.txtGMPConfirmationValidation');
    case CONST_FLAGS.SJG.VERIFY_CAT_STATUS.GMP_CHANGE_CONTROL:
      return isConfirmation()
        ? t('Sjg.Chr.txtGMPConfirmationChangeManagementConfirmation')
        : t('Sjg.Chr.txtGMPConfirmationChangeManagement');
    case CONST_FLAGS.SJG.VERIFY_CAT_STATUS.GMP_SPECIAL_OPERATION:
      return isConfirmation()
        ? t('Sjg.Chr.txtGMPConfirmationSpecialTaskConfirmation')
        : t('Sjg.Chr.txtGMPConfirmationSpecialTask');
    case CONST_FLAGS.SJG.VERIFY_CAT_STATUS.GMP_MATERIAL_REVISION:
      return isConfirmation()
        ? t('Sjg.Chr.txtGMPConfirmationMaterialRevisionConfirmation')
        : t('Sjg.Chr.txtGMPConfirmationMaterialRevision');
    default:
      return '';
  }
};

const startGmpVerification = async (
  propsSelectedRowData: VerifyItemList,
  propsSelectedInspectionItem: SelectInspectionItemData,
) => {
  const { responseRef, errorRef } = await useGetGMP({
    ...props.privilegesBtnRequestData,
    lotSid: propsSelectedInspectionItem.lotSid.toString(),
    verifyCat: propsSelectedRowData.verifyCat,
  });

  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    openDialog('singleButton');
    closeLoading();
    return;
  }

  if (responseRef.value) {
    verifyGmpRsltTemp = responseRef.value.data.rData.verifyGmpRslt;
    gmpItemMaxCntVal = responseRef.value.data.rData.gmpItemMaxCnt;
    gmpInfoListData = responseRef.value.data.rData.gmpInfoList;
    gmpDocNeedTypeNmList = responseRef.value.data.rData.gmpDocNeedTypeNmList;
    tablePropsDialogRef.value.tableData = gmpInfoListData.map((item) => {
      const rectItem = {
        ...item,
        uniqueKey: `${item.matNo}-${uuidv4()}`,
      };
      return rectItem;
    });

    if (!isConfirmation() && gmpInfoListData.length > 0) {
      // 画面共通記載のメッセージウィンドウ「インフォメーションメッセージ」を表示する。
      messageBoxInfoSingleButtonRef.value.title = t(
        'Sjg.Msg.titleInspectManufacturingRecord',
      );
      messageBoxInfoSingleButtonRef.value.content = t(
        'Sjg.Msg.contentInspectManufacturingRecord',
      );
      openDialog('infoSingleButton');
    }

    sjgGMPConfirmationRef.value.formItems.gmpRslt.formModelValue =
      verifyGmpRsltTemp;
    setFormModelValueFromApiResponse(
      sjgGMPConfirmationRef,
      getGMPReqData.value,
    );
  }

  if (isConfirmation()) {
    if (sjgGMPConfirmationRef.value.formItems.gmpRslt.formRole === 'radio') {
      sjgGMPConfirmationRef.value.formItems.gmpRslt.props!.disabled = true;
    }
  }
  resetGMPRsltData(
    isConfirmation(),
    isConfirmation() ||
      sjgGMPConfirmationRef.value.formItems.gmpRslt.formModelValue ===
        CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.ABSENCE,
  );
  openDialog('fragmentDialogVisible');
  setFootBtnDisable();
};

const modifyGmpVerifyStart = async (
  messageBoxProps: DialogProps,
  warningNecessityFlg: number = WARNING_NECESSITY_FLAG.NECESSITY,
) => {
  if (!props.selectedRowData) return;
  if (!props.selectedInspectionItem) return;
  let modifyGmpVerifyStartReq: ExtendCommonRequestType<ModifyGmpVerifyStartReq> =
    {
      ...props.privilegesBtnRequestData,
      lotSid: props.selectedInspectionItem!.lotSid,
      verifyCat: props.selectedRowData!.verifyCat,
      verifyExpl: '',
      msgboxTitleTxt: messageBoxProps.title,
      msgboxMsgTxt: messageBoxProps.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      warningNecessityFlg,
    };
  if (
    'isPrompt' in messageBoxModifyGmpVerifyPropsRef.value &&
    warningNecessityFlg === WARNING_NECESSITY_FLAG.UNNECESSARY &&
    props.comboBoxDataStandardReturnData
  ) {
    modifyGmpVerifyStartReq = {
      ...modifyGmpVerifyStartReq,
      msgboxInputCmt:
        messageBoxModifyGmpVerifyPropsRef.value.formModel.message.toString(),
      verifyExpl:
        messageBoxModifyGmpVerifyPropsRef.value.formModel.message.toString(),
    };
    setCustomFormComboBoxOptionList(
      messageBoxModifyGmpVerifyPropsRef.value.formItems,
      props.comboBoxDataStandardReturnData.rData.rList,
    );
  }
  const { responseRef, errorRef } = await useModifyGmpVerifyStart({
    ...modifyGmpVerifyStartReq,
  });
  if (errorRef.value) {
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      messageBoxModifyGmpVerifyPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxModifyGmpVerifyPropsRef.value.content =
        errorRef.value.response.rMsg;
      const resetData = createMessageBoxForm('message', 'cmtSjgVeriOther');
      if (
        'isPrompt' in messageBoxModifyGmpVerifyPropsRef.value &&
        props.comboBoxDataStandardReturnData
      ) {
        messageBoxModifyGmpVerifyPropsRef.value.formItems = resetData.formItems;
        setCustomFormComboBoxOptionList(
          messageBoxModifyGmpVerifyPropsRef.value.formItems,
          props.comboBoxDataStandardReturnData.rData.rList,
        );
      }
      openDialog('messageBoxModifyGmpVerifyVisible');
    } else {
      messageBoxModifyGmpVerifyStartErrorRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxModifyGmpVerifyStartErrorRef.value.content =
        errorRef.value.response.rMsg;
      openDialog('modifyGmpVerifyStartError');
      closeLoading();
      return;
    }
    return;
  }
  if (responseRef.value) {
    if (sjgGMPConfirmationRef.value.formItems.gmpRslt.formRole === 'radio') {
      sjgGMPConfirmationRef.value.formItems.gmpRslt.props!.disabled = false;
    }

    await startGmpVerification(
      props.selectedRowData,
      props.selectedInspectionItem,
    );
  }
};

const closeVerifyStartDialog = async (messageBoxProps: DialogProps) => {
  closeDialog('messageBoxModifyGmpVerifyVisible');
  // GMP確認開始
  await modifyGmpVerifyStart(
    messageBoxProps,
    WARNING_NECESSITY_FLAG.UNNECESSARY,
  );
  return true;
};

const updateSelectedRow = (v: SelectGmpConfItemData | null) => {
  selectedRow = v;
};

/**
 * 初期設定
 */
const sjgGMPConfirmationInit = async () => {
  if (!props.selectedRowData) return;
  if (!props.selectedInspectionItem) return;
  showLoading();
  selectedRow = null;
  updateDialogChangeFlagRef(false);
  dialogTitle = setDialogTitle();
  inspectionInfoShowRef.value.infoShowItems.expiryYmd.infoShowModelValue =
    props.selectedInspectionItem.expiryYmd.toString();
  inspectionInfoShowRef.value.infoShowItems.lotNo.infoShowModelValue =
    props.selectedInspectionItem.lotNo.toString();
  inspectionInfoShowRef.value.infoShowItems.verifyReasonNm.infoShowModelValue =
    props.selectedInspectionItem.verifyReasonNm.toString();
  inspectionInfoShowRef.value.infoShowItems.matNm.infoShowModelValue =
    props.selectedInspectionItem.matNm.toString();
  inspectionInfoShowRef.value.infoShowItems.matNo.infoShowModelValue =
    props.selectedInspectionItem.matNo.toString();
  inspectionInfoShowRef.value.infoShowItems.rsltYmd.infoShowModelValue =
    props.selectedInspectionItem.rsltYmd.toString();
  sjgGMPConfirmationRef.value.formItems = getSjgGMPConfirmationFormRadioItems(
    props.selectedRowData.verifyCat,
  );

  if (!isConfirmation()) {
    await modifyGmpVerifyStart(messageBoxModifyGmpVerifyPropsRef.value);
    closeLoading();
    return;
  }
  await startGmpVerification(
    props.selectedRowData,
    props.selectedInspectionItem,
  );
  closeLoading();
};

const cancelChangeGMPRsltConfirm = () => {
  closeDialog('changeGMPRsltConfirm');
  sjgGMPConfirmationRef.value.formItems.gmpRslt.formModelValue =
    CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.PRESENCE;
};

const submitChangeGMPRsltConfirm = () => {
  resetGMPRsltData(false, true, true);
  closeDialog('changeGMPRsltConfirm');
};

const closeSjgInfoValidCheckError = async () => {
  closeDialog('sjgInfoValidCheckError');
  router.push({
    name: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
    state: {
      routerName: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
      searchConditionData: window.history.state.conditionData,
      tableSearchData: window.history.state.tableSearchData,
    },
  });
};

watch(() => props.isClicked, sjgGMPConfirmationInit);
</script>
<style lang="scss" scoped>
$namespace: 'sjg-gmp-confirmation';

.#{$namespace} {
  background-color: $gray720;
  display: flex;
  flex-direction: column;
  flex-grow: 1;

  &_content-selectinspectionitem {
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    /** element plus */
    :deep(.el-card__body) {
      padding-bottom: 0;
      padding-inline: 16px;
      display: flex;
      flex-direction: column;
      flex-grow: 1;
    }
  }
  &_txt-gmp-case-nm {
    width: 80px;
    line-height: 2;
  }
}
.flex-container {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  padding: 16px 0;

  & + & {
    border-top: 1px solid var(--el-border-color);
  }

  :deep(.el-row) {
    display: flex;
    justify-content: flex-start;
    width: 1017px;
  }
}
.no-need-record-warning-tag {
  color: var(--el-color-danger);
  font-size: 12px;
  margin-top: 12px;
}
</style>
