<template>
  <!-- 投入記録修正ダイアログ -->
  <!-- 見出し 投入記録修正 -->
  <DialogWindow
    :title="$t('Prd.Chr.txtBillOfMaterialsRecordModify')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :class="'prd-bom-modify'"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="prdBomModifyFormRef.formModel"
      :formItems="prdBomModifyFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          prdBomModifyFormRef.customForm = v;
        }
      "
      @changeFormModel="
        (changeFlag, { changedFormModelMap }) => {
          updateDialogChangeFlagRef(changeFlag);
          updateCurrentChangedFormModelMap(changedFormModelMap);
        }
      "
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 投入記録修正確定完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPrdBomModifyFinishedVisible"
    :dialogProps="messageBoxPrdBomModifyFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxModifyPrdBomModifyFinished"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import onValidateHandler from '@/utils/validateHandler';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  useGetConfirmBomRecordInit,
  useModifyConfirmBomRecord,
  useGetComboBoxDataStandard,
} from '@/hooks/useApi';
import {
  GetConfirmBomInfoListInitResponseData,
  GetConfirmBomRecordInitRequestData,
  GetConfirmBomRecordInitResponseData,
  ModifyConfirmBomRecordRequestData,
} from '@/types/HookUseApi/PrdTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import {
  getPrdBomModifyFormItems,
  prdBomModifyFormModel,
  COMBINE_ID,
} from './prdBomModify';

// 投入記録修正初期表示APIのレスポンス
let initResponseData: GetConfirmBomRecordInitResponseData = {
  bomMatNo: '',
  dspNmJp: '',
  unitNmJp: '',
  batchNo: null,
  lotSid: '',
  lotNo: '',
  bomMatSeq: null,
  lblSid: '',
  bomPlanQty: '',
  rsltQty: '',
  disQty: '',
  disDtlQty1: '',
  disDtlQty2: '',
  disDtlQty3: '',
  updDts: '',
};

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxPrdBomModifyFinishedVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxPrdBomModifyFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 投入記録修正確定の完了メッセージボックス
const messageBoxPrdBomModifyFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

const prdBomModifyFormRef = ref<CustomFormType>({
  formItems: getPrdBomModifyFormItems(),
  formModel: prdBomModifyFormModel,
});

// 初期値から変更されたフォームモデルを保持するMap
let currentChangedFormModelMap: Map<string, string> = new Map();
const updateCurrentChangedFormModelMap = (updateValue: Map<string, string>) => {
  currentChangedFormModelMap = updateValue;
};

// 親ダイアログから渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  odrNo?: string; // 親ダイアログのodrNo
  prcSeq?: number; // 親ダイアログのprcSeq
  beforeSelectedRow: GetConfirmBomInfoListInitResponseData | null; // 製造記録確認_投入記録詳細初期表示 選択行情報
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 投入記録修正確定のAPIリクエスト処理
const requestApiModifyConfirmBomRecord = async () => {
  if (
    props.odrNo === undefined ||
    props.prcSeq === undefined ||
    props.beforeSelectedRow === null ||
    props.beforeSelectedRow.batchNo === null ||
    props.beforeSelectedRow.bomMatSeq === null ||
    props.beforeSelectedRow.rsltLnum === null
  ) {
    return;
  }

  showLoading();

  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する。
  const requestData: ModifyConfirmBomRecordRequestData = {
    odrNo: props.odrNo,
    prcSeq: props.prcSeq,
    bomMatNo: props.beforeSelectedRow.bomMatNo,
    lotSid: props.beforeSelectedRow.lotSid,
    batchNo: props.beforeSelectedRow.batchNo,
    bomMatSeq: props.beforeSelectedRow.bomMatSeq,
    rsltLnum: props.beforeSelectedRow.rsltLnum,
    lblSid:
      prdBomModifyFormRef.value.formItems.lblSid.formModelValue.toString(),
    rsltQty:
      prdBomModifyFormRef.value.formItems.rsltQty.formModelValue.toString(),
    disDtlQty1:
      prdBomModifyFormRef.value.formItems.disDtlQty1.formModelValue.toString(),
    disDtlQty2:
      prdBomModifyFormRef.value.formItems.disDtlQty2.formModelValue.toString(),
    disDtlQty3:
      prdBomModifyFormRef.value.formItems.disDtlQty3.formModelValue.toString(),
    modExpl:
      prdBomModifyFormRef.value.formItems.modExpl.formModelValue.toString(),
    updDts: initResponseData.updDts, // 更新日時
  };

  const { responseRef, errorRef } = await useModifyConfirmBomRecord({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // 4．投入記録修正確定
  // ・データベースを更新した後、以下のメッセージを表示する。
  // 投入記録修正確定完了
  messageBoxPrdBomModifyFinishedPropsRef.value.title =
    responseRef.value.data.rTitle;
  messageBoxPrdBomModifyFinishedPropsRef.value.content =
    responseRef.value.data.rMsg;

  closeLoading();

  openDialog('messageBoxPrdBomModifyFinishedVisible');
};

// 投入記録修正確定完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxModifyPrdBomModifyFinished = () => {
  // 再検索用に親に実行を通知
  emit('submit');

  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxPrdBomModifyFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // 1.下記チェックを行い、チェックOKなら処理継続する。
  // １ 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    prdBomModifyFormRef.value.customForm !== undefined &&
    (await prdBomModifyFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return false;
  }

  // 廃棄量のいずれかが同じ値でない場合、署名（パスワード認証）ダイアログ(W1Z2510)を表示し、パスワード認証を行う
  if (
    currentChangedFormModelMap.has('disDtlQty1') ||
    currentChangedFormModelMap.has('disDtlQty2') ||
    currentChangedFormModelMap.has('disDtlQty3')
  ) {
    try {
      // 署名ダイアログを表示
      await showSignDialog({
        commonRequestParam: {
          ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
        },
      });
    } catch (error) {
      return false;
    }
  }

  // 投入記録修正確定のAPIリクエスト処理
  await requestApiModifyConfirmBomRecord();

  return false;
};

/**
 * 投入記録修正ダイアログの初期設定
 */
const prdBomModifyInit = async () => {
  if (
    props.odrNo === undefined ||
    props.prcSeq === undefined ||
    props.beforeSelectedRow === null ||
    props.beforeSelectedRow.batchNo === null ||
    props.beforeSelectedRow.bomMatSeq === null ||
    props.beforeSelectedRow.rsltLnum === null
  ) {
    return;
  }

  // ダイアログ終了チェック初期化
  updateDialogChangeFlagRef(false);
  // 初期値から変更されたフォームモデル情報を初期化
  currentChangedFormModelMap.clear();

  // FormItems初期化
  prdBomModifyFormRef.value.formItems = getPrdBomModifyFormItems();

  showLoading();

  // 投入記録修正初期表示のAPIを行う。
  const requestData: GetConfirmBomRecordInitRequestData = {
    odrNo: props.odrNo,
    prcSeq: props.prcSeq,
    batchNo: props.beforeSelectedRow.batchNo,
    bomMatNo: props.beforeSelectedRow.bomMatNo,
    bomMatSeq: props.beforeSelectedRow.bomMatSeq,
    rsltLnum: props.beforeSelectedRow.rsltLnum,
    lotSid: props.beforeSelectedRow.lotSid,
    lblSid: props.beforeSelectedRow.lblSid,
  };
  const { responseRef, errorRef } = await useGetConfirmBomRecordInit({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // 投入記録修正初期表示のAPIのレスポンスを保持
  initResponseData = responseRef.value.data.rData;

  // 投入記録修正情報レイアウト用初期値設定
  setFormModelValueFromApiResponse(
    prdBomModifyFormRef,
    responseRef.value.data.rData,
  );

  // 標準コンボボックス取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        // 修正コメント入力
        cmbId: COMBINE_ID.MOD_EXPL,
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_SOP_BOM_MOD' },
      },
    ],
  });

  if (comboBoxResData) {
    setCustomFormComboBoxOptionList(
      prdBomModifyFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  closeLoading();

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await prdBomModifyInit();
  },
);
</script>
