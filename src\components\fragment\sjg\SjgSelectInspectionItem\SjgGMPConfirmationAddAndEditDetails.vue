<template>
  <!-- GMP確認項目明細追加/修正ダイアログ -->
  <DialogWindow
    v-if="dialogVisibleRef.fragmentDialogVisible"
    :title="$t('Sjg.Chr.txtGMPConfirmInput')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    @visible="updateDialogChangeFlagRef"
  >
    <CustomForm
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="sjgGMPConfirmationAddAndEditFormRef.formModel"
      :formItems="sjgGMPConfirmationAddAndEditFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          sjgGMPConfirmationAddAndEditFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import i18n from '@/constants/lang';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import { DialogProps } from '@/types/MessageBoxTypes';
import { CustomFormType } from '@/types/CustomFormTypes';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import {
  SelectGmpConfItemData,
  GMPRequestData,
  GmpDocNeedTypeNmListData,
} from '@/types/HookUseApi/SjgTypes';
import onValidateHandler from '@/utils/validateHandler';

import { closeLoading, showLoading } from '@/utils/dialog';
import CONST_FLAGS from '@/constants/flags';
import {
  getSjgGMPConfirmationAddAndEditFormItems,
  sjgGMPConfirmationAddAndEditFormModel,
} from './sjgGMPConfirmationAddAndEditDetails';

type Props = {
  selectedRowData: SelectGmpConfItemData | null;
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
  verifyCat: string;
  verifyCatSts: string;
  gmpDocNeedTypeNmList: GmpDocNeedTypeNmListData[];
  dialogType: string;
};
const props = defineProps<Props>();
const emit = defineEmits(['submit']);
const { t } = i18n.global;

const sjgGMPConfirmationAddAndEditFormRef = ref<CustomFormType>({
  formItems: getSjgGMPConfirmationAddAndEditFormItems(),
  formModel: sjgGMPConfirmationAddAndEditFormModel,
});

const sjgGMPConfirmationRequestData: GMPRequestData = {
  gmpSysMngNo: 1,
  gmpDocNeedTypeVerify: '',
  gmpDocNeedTypeVerifyNm: '',
  gmpMngNo: '',
  gmpTitle: '',
  gmpDes: '',
};

const customFormRenderingTriggerRef = ref(false);
type DialogRefKey = 'singleButton' | 'fragmentDialogVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  fragmentDialogVisible: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const checkAddAndEditForm = async () => {
  const validate =
    sjgGMPConfirmationAddAndEditFormRef.value.customForm !== undefined &&
    (await sjgGMPConfirmationAddAndEditFormRef.value.customForm.validate(
      (isValid) => {
        onValidateHandler(isValid);
      },
    ));
  if (validate) {
    sjgGMPConfirmationRequestData.gmpMngNo =
      sjgGMPConfirmationAddAndEditFormRef.value.formModel.gmpMngNo.toString();
    sjgGMPConfirmationRequestData.gmpTitle =
      sjgGMPConfirmationAddAndEditFormRef.value.formModel.gmpTitle.toString();
    sjgGMPConfirmationRequestData.gmpDes =
      sjgGMPConfirmationAddAndEditFormRef.value.formModel.gmpDes.toString();
    sjgGMPConfirmationRequestData.gmpDocNeedTypeVerify =
      sjgGMPConfirmationAddAndEditFormRef.value.formModel.gmpDocNeedTypeVerify.toString();

    const item =
      sjgGMPConfirmationAddAndEditFormRef.value.formItems.gmpDocNeedTypeVerify;
    const modelValue =
      sjgGMPConfirmationAddAndEditFormRef.value.formModel.gmpDocNeedTypeVerify?.toString();
    let selectedLabel = '';
    if (item && item.formRole === 'radio' && !Array.isArray(item)) {
      const optionsData = item.props?.optionsData;
      if (
        optionsData &&
        Array.isArray(optionsData.value) &&
        Array.isArray(optionsData.label)
      ) {
        const valueIdx = optionsData.value.indexOf(modelValue);
        if (valueIdx !== -1) {
          selectedLabel = optionsData.label[valueIdx];
        }
      }
    }
    sjgGMPConfirmationRequestData.gmpDocNeedTypeVerifyNm = selectedLabel;

    closeDialog('fragmentDialogVisible');
    emit('submit', sjgGMPConfirmationRequestData, props.dialogType);
  }
  return false;
};

const buttonsList: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    clickHandler: async () => {
      const isClose = commonRejectHandler();
      return isClose;
    },
  },
  {
    text: t('Cm.Chr.btnDecision'),
    type: 'primary',
    size: 'normal',
    clickHandler: () => {
      checkAddAndEditForm();
      return false;
    },
  },
];
const dialogButtons = ref<DialogWindowProps['buttons']>(buttonsList);
const setFootBtnDisable = () => {
  // 照査完了後の確認の場合
  let dialogButtonsTemp: DialogWindowProps['buttons'] = [];
  if (props.verifyCatSts === CONST_FLAGS.SJG.GMP_VERIFY_STATUS.FINISH) {
    dialogButtonsTemp = buttonsList.slice(0, 1);
  } else {
    dialogButtonsTemp = buttonsList;
  }
  dialogButtons.value = dialogButtonsTemp;
};

/**
 * 初期設定
 */
const sjgGMPConfirmationAddAndEditDetailsInit = async () => {
  if (
    props.dialogType === CONST_FLAGS.SJG.GMP_DIALOG_TYPE_STATUS.EDIT &&
    !props.selectedRowData
  )
    return;
  updateDialogChangeFlagRef(false);
  showLoading();
  // 連携テストダイアログの初期化必要なら書く
  sjgGMPConfirmationAddAndEditFormRef.value.formItems =
    getSjgGMPConfirmationAddAndEditFormItems(props.gmpDocNeedTypeNmList);

  setFootBtnDisable();

  if (
    props.dialogType === CONST_FLAGS.SJG.GMP_DIALOG_TYPE_STATUS.ADD &&
    props.verifyCat === CONST_FLAGS.SJG.VERIFY_CAT_STATUS.GMP_VALIDATION
  ) {
    sjgGMPConfirmationAddAndEditFormRef.value.formItems.gmpDocNeedTypeVerify.formModelValue =
      CONST_FLAGS.SJG.GMP_DOC_NEED_TYPE_STATUS.NECESSARY;
  }
  if (
    sjgGMPConfirmationAddAndEditFormRef.value.formItems.gmpDocNeedTypeVerify
      .formRole === 'radio'
  ) {
    if (props.verifyCat === CONST_FLAGS.SJG.VERIFY_CAT_STATUS.GMP_VALIDATION) {
      sjgGMPConfirmationAddAndEditFormRef.value.formItems.gmpDocNeedTypeVerify.props.disabled =
        true;
    } else {
      sjgGMPConfirmationAddAndEditFormRef.value.formItems.gmpDocNeedTypeVerify.props.disabled =
        false;
    }
  }
  if (
    props.dialogType === CONST_FLAGS.SJG.GMP_DIALOG_TYPE_STATUS.EDIT &&
    props.selectedRowData
  ) {
    sjgGMPConfirmationRequestData.gmpSysMngNo =
      props.selectedRowData.gmpSysMngNo;
    sjgGMPConfirmationRequestData.gmpDocNeedTypeVerify =
      props.selectedRowData.gmpDocNeedTypeVerify ?? '';
    sjgGMPConfirmationRequestData.gmpMngNo = props.selectedRowData.gmpMngNo;
    sjgGMPConfirmationRequestData.gmpTitle = props.selectedRowData.gmpTitle;
    sjgGMPConfirmationRequestData.gmpDes = props.selectedRowData.gmpDes;

    setFormModelValueFromApiResponse(
      sjgGMPConfirmationAddAndEditFormRef,
      sjgGMPConfirmationRequestData,
    );
  } else {
    sjgGMPConfirmationRequestData.gmpSysMngNo = 1;
  }

  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, sjgGMPConfirmationAddAndEditDetailsInit);
</script>
