import { Ref } from 'vue';
import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import { ButtonExProps } from '@/types/ButtonExTypes';
import {
  GetAogInstructionData,
  GetAogInstructionForceData,
  GetAogIoaForceRes,
  GetAogInstructionRes,
} from '@/types/HookUseApi/AogTypes';
import {
  ExtendCommonRequestType,
  AxiosErrorType,
} from '@/types/HookUseApi/CommonTypes';
import { AxiosResponse } from 'axios';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

const { t } = i18n.global;

type CallBackResult = Promise<boolean> | boolean | void;
type DialogButton = ButtonExProps & {
  clickHandler?: () => CallBackResult;
};
export type AogRequest = ExtendCommonRequestType<{ aogInstNo: string }>;
export type AxiosCallBackResult<R> = {
  responseRef: Ref<AxiosResponse<R> | undefined>;
  errorRef: Ref<AxiosErrorType>;
};
export type DialogConfig = {
  title: string;
  condList: {
    cmbId: string;
    condKey: string;
    where: { cmt_cat: string };
  }[];
  buttons?: DialogButton[];
  onResolve?: () => CallBackResult;
  onReject?: () => CallBackResult;
  formData: GetAogInstructionData | GetAogInstructionForceData | null;
};

export type DialogConfigApi = {
  useGetAogForceCompleteArrivalInspection: (
    val: AogRequest,
  ) => Promise<AxiosCallBackResult<GetAogIoaForceRes>>;
  useGetAogInstruction: (
    val: AogRequest,
  ) => Promise<AxiosCallBackResult<GetAogInstructionRes>>;
  useGetAogCancelArrivalInspectionComplete: (
    val: AogRequest,
  ) => Promise<AxiosCallBackResult<GetAogInstructionRes>>;
};

export const getAogArrivalInstructionDetailsFormItems: () => CustomFormType['formItems'] =
  () => ({
    mesUnitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    planYmd: {
      label: { text: t('Aog.Chr.txtAogDate') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    aogInstNo: {
      label: { text: t('Aog.Chr.txtAogInstNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    aogInstGrpNo: {
      label: { text: t('Aog.Chr.txtAogInstGrpNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    poDtlNo: {
      label: { text: t('Aog.Chr.txtPurchaseDetailOrderNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    matNo: {
      label: { text: t('Aog.Chr.txtItemCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    matNm: {
      label: { text: t('Aog.Chr.txtItemName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    mBpId: {
      label: { text: t('Aog.Chr.txtBusinessPartnerCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    bpNm: {
      label: { text: t('Aog.Chr.txtBusinessPartnerName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    makerLotNo: {
      label: { text: t('Aog.Chr.txtBusinessPartnerLotNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    edNo: {
      label: { text: t('Aog.Chr.txtEditionNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    mesAogQty: {
      label: { text: t('Aog.Chr.txtAogQuantity') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      suffix: { formModelProp: 'mesUnitNm' },
    },
    aogAttBinList: {
      formModelValue: [],
      formRole: 'fileUpload',
      onClickHandler() {},
      props: {
        fileList: [],
        hideTriggerBtn: true,
        size: 'small',
      },
      label: { text: t('Aog.Chr.txtAttachments') },
    },
    planExpl: {
      label: { text: t('Aog.Chr.txtAogExpl') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
  });

export const aogArrivalInstructionDetailsFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getAogArrivalInstructionDetailsFormItems());

export const tablePropsData: TabulatorTableIF = {
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'lblNo',
  height: '163px',
  column: [
    {
      title: 'Aog.Chr.txtPltNo',
      field: 'pltNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    {
      title: 'Aog.Chr.txtQty',
      field: 'ioaQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.AOG_QTY,
    },
    {
      title: 'Aog.Chr.txtMesUnit',
      field: 'unitNm',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    {
      title: 'Aog.Chr.txtPltIoaStsNm',
      field: 'pltIoaStsNm',
      width: COLUMN_WIDTHS.AOG.PLT_IOA_STS_NM,
    },
    {
      title: 'Aog.Chr.txtComment',
      field: 'pckExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    {
      title: 'Aog.Chr.txtAogYmd',
      field: 'aogYmd',
      formatter: 'date',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDD,
    },
    {
      title: 'Aog.Chr.txtIoaHtUsr',
      field: 'liftUsrNm',
      width: COLUMN_WIDTHS.USR_NM,
    },
    {
      title: 'Aog.Chr.txtZoneNm',
      field: 'mesZoneNm',
      width: COLUMN_WIDTHS.ZONE_NM,
    },
    { title: 'Aog.Chr.txtLocNm', field: 'locNm', width: COLUMN_WIDTHS.LOC_NM },
  ],
  tableData: [],
  noUseConditionSearch: true, // 絞り込み条件無し
  showConditionSearch: false, // ConditionSearch不要
};
