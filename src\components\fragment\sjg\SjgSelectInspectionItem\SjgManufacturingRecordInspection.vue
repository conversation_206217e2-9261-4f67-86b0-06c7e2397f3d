<template>
  <!-- 製造記録照査 -->
  <DialogWindow
    :title="dialogConfigTitle"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogConfig.buttons"
  >
    <!-- 照査情報 -->
    <BaseHeading
      level="2"
      fontSize="24px"
      class="Util_mb-16"
      :text="$t('Sjg.Chr.txtInspectionInfo')"
    />
    <div class="sjg-manufacturing-record-inspection_info-show-container">
      <InfoShow
        class="Util_mt-8"
        :infoShowItems="inspectManufacturingRecordInfoShowRef.infoShowItems"
        :isLabelVertical="inspectManufacturingRecordInfoShowRef.isLabelVertical"
        :fontSizeLabel="inspectManufacturingRecordInfoShowRef.fontSizeLabel"
        :fontSizeContent="inspectManufacturingRecordInfoShowRef.fontSizeContent"
      />
    </div>
    <!-- 製造記録照査項目 -->
    <BaseHeading
      level="2"
      fontSize="24px"
      class="Util_mt-48"
      :text="$t('Sjg.Chr.txtInspectionHistoryItem')"
    />
    <el-row
      v-for="(verifyItem, index) in manufacturingRecItemListRef"
      :key="index"
      class="row-with-line"
    >
      <el-col :span="12">
        <InfoShow
          :infoShowItems="getManufacturingRecInfoShowItems(verifyItem)"
          :isLabelVertical="true"
          fontSizeLabel="12px"
          fontSizeContent="16px"
        />
      </el-col>
      <el-col
        :span="12"
        class="sjg-manufacturing-record-inspection_button-ex-class"
      >
        <ButtonEx
          type="secondary"
          size="normal"
          :text="verifyItem.btnExText"
          @click="manufacturingRecItemClickHandler(index)"
        />
      </el-col>
    </el-row>
    <CustomForm
      class="Util_mt-24"
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="inspectManufacturingRecordFormRef.formModel"
      :formItems="inspectManufacturingRecordFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          inspectManufacturingRecordFormRef.customForm = v;
        }
      "
      @selectedItem="updateFormItems"
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- 製造指図記録確認ダイアログ -->
  <SjgManufacturingInstructionRecordConfirmation
    :selectedRowData="props.selectedRowData"
    :isClicked="isClickedShowManufacturingInstructionRecordConfirmationRef"
    :screenId="currentScreenId"
    :selectedInspectionItemData="props.selectedInspectionItemData"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    :saveFlag="manufacturingRecItemData?.odrRecFlg === '1'"
    @submit="onSubmitHandler"
  />
  <!-- 構成品情報確認ダイアログ -->
  <SjgComponentInformationConfirmation
    :isClicked="isClickedSjgComponentInformationConfirmationRef"
    :screenId="currentScreenId"
    :selectedRowData="props.selectedRowData"
    :selectedInspectionItemData="props.selectedInspectionItemData"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    :saveFlag="manufacturingRecItemData?.odrBomFlg === '1'"
    @submit="onSubmitHandler"
  />
  <!-- SOP記録確認ダイアログ -->
  <SjgSOPRecordConfirmation
    :selectedRowData="props.selectedRowData"
    :isClicked="isClickedShowSOPRecordConfirmationRef"
    :screenId="currentScreenId"
    :selectedInspectionItemData="props.selectedInspectionItemData"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    :saveFlag="manufacturingRecItemData?.odrSopFlg === '1'"
    @submit="onSubmitHandler"
  />
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <!-- 警告メッセージを表示 -->
  <MessageBox
    v-if="dialogVisibleRef.modifyCmtSjgVeriOtherWarning"
    :dialogProps="messageBoxModifyCmtSjgVeriOtherPropsRef"
    :cancelCallback="() => closeDialog('modifyCmtSjgVeriOtherWarning')"
    :submitCallback="closeWarningDialog"
  />
  <!-- 既存照査データ引継ぎチェック -->
  <MessageBox
    v-if="dialogVisibleRef.existingVerificationWarning"
    :dialogProps="messageBoxExistingVerificationProps"
    :cancelCallback="
      () => deleteOrderVerify(messageBoxExistingVerificationProps)
    "
    :submitCallback="
      () => closeExistingVerificationDialog(messageBoxExistingVerificationProps)
    "
  />
  <MessageBox
    v-if="dialogVisibleRef.sjgInfoValidCheckError"
    :dialogProps="messageBoxSjgInfoValidCheckErrorRef"
    :submitCallback="() => closeSjgInfoValidCheckError()"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import CONST from '@/constants/utils';
import SCREENID from '@/constants/screenId';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import ButtonEx from '@/components/parts/ButtonEx.vue';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import { CustomFormType, FormItem } from '@/types/CustomFormTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  VerifyItemList,
  CheckVerifyStatusReq,
  SelectInspectionItemData,
  GetManufacturingRecItemData,
  ModifyManufacturingRecVerifyFinReq,
  ModifyManufacturingRecVerifyStartReq,
} from '@/types/HookUseApi/SjgTypes';
import {
  CommonRequestType,
  ExtendCommonRequestType,
  ComboBoxDataStandardReturnData,
} from '@/types/HookUseApi/CommonTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import createMessageBoxForm from '@/utils/commentMessageBox';
import { rules } from '@/utils/validator';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  useCheckVerifyStatus,
  useDeleteOrderVerify,
  useGetComboBoxDataStandard,
  useGetManufacturingRecItem,
  useModifyManufacturingRecVerifyStart,
  useModifyManufacturingRecVerifyFin,
  useCheckValidVerify,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import SjgManufacturingInstructionRecordConfirmation from '@/components/fragment/sjg/SjgSelectInspectionItem/SjgManufacturingInstructionRecordConfirmation.vue';
import SjgComponentInformationConfirmation from '@/components/fragment/sjg/SjgSelectInspectionItem/SjgComponentInformationConfirmation.vue';
import SjgSOPRecordConfirmation from '@/components/fragment/sjg/SjgSelectInspectionItem/SjgSOPRecordConfirmation.vue';
import CONST_FLAGS from '@/constants/flags';
import {
  DialogConfig,
  dialogCancelButton,
  dialogExecutionButton,
  ManufacturingRecItemList,
  getManufacturingRecItemList,
  getManufacturingRecInfoShowItems,
  getInspectManufacturingRecordFormItems,
  getInspectManufacturingRecordFormModel,
  getInspectManufacturingRecordInfoShowItems,
} from './sjgManufacturingRecordInspection';

type Props = {
  isClicked: boolean;
  screenId: string;
  selectedInspectionItemData: SelectInspectionItemData | null;
  selectedRowData: VerifyItemList | null;
  privilegesBtnRequestData: CommonRequestType;
  comboBoxStandardReturnData: ComboBoxDataStandardReturnData | undefined;
};
const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

const inspectManufacturingRecordFormRef = ref<CustomFormType>({
  formItems: getInspectManufacturingRecordFormItems(props.screenId),
  formModel: getInspectManufacturingRecordFormModel(props.screenId),
});

let comboBoxResData: ComboBoxDataStandardReturnData | undefined;
let manufacturingRecItemData: GetManufacturingRecItemData | undefined;
let currentScreenId: string = '';
// 照査結果
const constantData = { odrRslt: 'odrRslt' };
/**
 *
 * 警告要否フラグ
 * - UNNECESSARY: 警告が不要であることを表します（値: 0）。
 * - NECESSITY: 警告が必要であることを表します（値: 1）：デフォルト値。
 */
const WARNING_NECESSITY_FLAG = {
  UNNECESSARY: 0,
  NECESSITY: 1,
} as const;
const dialogConfigTitle = ref<string>(t('Sjg.Chr.txtInspectionHistory'));
// カスタムフォームの描画更新トリガー
const customFormRenderingTriggerRef = ref<boolean>(false);
const isClickedShowSOPRecordConfirmationRef = ref<boolean>(false);
const isClickedSjgComponentInformationConfirmationRef = ref<boolean>(false);
const isClickedShowManufacturingInstructionRecordConfirmationRef =
  ref<boolean>(false);
const manufacturingRecItemListRef = ref<ManufacturingRecItemList[]>([]);
const dialogConfig = ref<DialogConfig>({
  buttons: [],
  onResolve: () => {},
});
const router = useRouter();

const inspectManufacturingRecordInfoShowRef = ref<InfoShowType>({
  infoShowItems: getInspectManufacturingRecordInfoShowItems(),
  isLabelVertical: true,
  fontSizeLabel: '12px',
  fontSizeContent: '16px',
});

type DialogRefKey =
  | 'singleButton'
  | 'fragmentDialogVisible'
  | 'existingVerificationWarning'
  | 'modifyCmtSjgVeriOtherWarning'
  | 'sjgInfoValidCheckError';

const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  fragmentDialogVisible: false,
  existingVerificationWarning: false,
  modifyCmtSjgVeriOtherWarning: false,
  sjgInfoValidCheckError: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxForm = createMessageBoxForm('message', 'cmtSjgVeriOther');
// 警告メッセージ
const messageBoxModifyCmtSjgVeriOtherPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});
// 既存照査データ引継ぎチェックのメッセージ
const messageBoxExistingVerificationProps: DialogProps = {
  title: t('Sjg.Msg.titleExistingVerification'),
  content: t('Sjg.Msg.contentExistingVerification'),
  type: 'warning',
};

// 照査データ有効チェックエラーのメッセージ
const messageBoxSjgInfoValidCheckErrorRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const handleCloseFragmentDialog = () => {
  closeDialog('fragmentDialogVisible');
  if (props.screenId === SCREENID.SJG_MANUFACTURING_RECORD_INSPECTION)
    emit('submit', props.privilegesBtnRequestData);
};

const closeAllDialog = async () => {
  if (props.screenId === SCREENID.SJG_MANUFACTURING_RECORD_INSPECTION) {
    if (await commonRejectHandler()) handleCloseFragmentDialog();
  } else {
    closeDialog('fragmentDialogVisible');
  }
  return true;
};

// 製造記録照査を完了する
const modifyManufacturingRecVerifyFin = async () => {
  showLoading();
  // 結果入力欄:照査結果のフィールド[odrRslt]
  const apiRequestData: ExtendCommonRequestType<ModifyManufacturingRecVerifyFinReq> =
    {
      ...props.privilegesBtnRequestData,
      lotSid: props.selectedInspectionItemData!.lotSid,
      verifyCat: props.selectedRowData!.verifyCat,
      verifyExpl:
        inspectManufacturingRecordFormRef.value.formModel.rsltVerifyExpl.toString(),
      odrRslt:
        inspectManufacturingRecordFormRef.value.formModel.odrRslt.toString(),
    };
  const { responseRef, errorRef } =
    await useModifyManufacturingRecVerifyFin(apiRequestData);
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    closeLoading();
    // ５．ダイアログを閉じる
    handleCloseFragmentDialog();
  }
  return true;
};

const checkValidVerifyHandler = async () => {
  const apiRequestData: ExtendCommonRequestType<{ lotSid: string }> = {
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
  };
  // 照査情報有効チェック
  const { errorRef } = await useCheckValidVerify(apiRequestData);
  if (errorRef.value) {
    closeLoading();
    messageBoxSjgInfoValidCheckErrorRef.value.title =
      errorRef.value.response.rTitle;
    messageBoxSjgInfoValidCheckErrorRef.value.content =
      errorRef.value.response.rMsg;
    openDialog('sjgInfoValidCheckError');
    return false;
  }
  return true;
};

// 照査完了
const checkInspectForm = async () => {
  const validate =
    inspectManufacturingRecordFormRef.value.customForm !== undefined &&
    (await inspectManufacturingRecordFormRef.value.customForm.validate(
      (isValid) => {
        onValidateHandler(isValid);
      },
    ));
  if (validate) {
    if (!(await checkValidVerifyHandler())) {
      return false;
    }
    // 照査可否チェック
    const apiRequestData: ExtendCommonRequestType<CheckVerifyStatusReq> = {
      ...props.privilegesBtnRequestData,
      lotSid: props.selectedInspectionItemData!.lotSid,
      verifyCat: props.selectedRowData!.verifyCat,
    };
    const { responseRef, errorRef } =
      await useCheckVerifyStatus(apiRequestData);

    if (errorRef.value) {
      messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
      messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
      messageBoxSingleButtonRef.value.type = 'error';
      openDialog('singleButton');
      closeLoading();
      return false;
    }
    if (responseRef.value) {
      try {
        // 顔認証を行うための画面(W99A200)から顔認証を行う必要がある。
        await showSignDialog({
          commonRequestParam: {
            ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
          },
        });
      } catch (error) {
        return false;
      }

      // 認証に成功したら処理を継続する
      // ４．製造記録照査を完了する
      await modifyManufacturingRecVerifyFin();
    }
  }
  return false;
};

/**
 * 指定アイテムの関連更新を行う
 */
const updateFormItems = (fieldId: string) => {
  let overrideTags: FormItem['tags'] = [];
  let overrideRules: FormItem['rules'] = [];
  // 照査結果の絞り込み
  if (fieldId === constantData.odrRslt) {
    if (
      inspectManufacturingRecordFormRef.value.formItems.odrRslt.formRole ===
      'selectComboBox'
    ) {
      if (
        inspectManufacturingRecordFormRef.value.formItems.odrRslt.selectOptions
          .length > 0 &&
        inspectManufacturingRecordFormRef.value.formModel.odrRslt.toString() !==
          ''
      ) {
        // 照査結果がNGの場合は必須
        if (
          inspectManufacturingRecordFormRef.value.formModel.odrRslt.toString() ===
          'FL'
        ) {
          overrideTags = [
            { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
          ];
          overrideRules = [
            rules.required('textComboBox'),
            rules.length(64, t('Cm.Chr.txtLength', [64])),
          ];
        } else {
          overrideTags = [];
          overrideRules = [rules.length(64, t('Cm.Chr.txtLength', [64]))];
        }
        inspectManufacturingRecordFormRef.value.formItems.rsltVerifyExpl.tags =
          overrideTags;
        inspectManufacturingRecordFormRef.value.formItems.rsltVerifyExpl.rules =
          overrideRules;
        customFormRenderingTriggerRef.value =
          !customFormRenderingTriggerRef.value;
      }
    }
  }
};

const checkVerifyStatusHandler = async () => {
  const { errorRef } = await useCheckVerifyStatus({
    ...props.privilegesBtnRequestData,
    verifyCat: '1',
    lotSid: props.selectedInspectionItemData!.lotSid,
  });
  if (errorRef.value) {
    closeLoading();
    messageBoxSingleButtonRef.value.type = 'error';
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    openDialog('singleButton');
    return false;
  }
  return true;
};

const manufacturingRecItemClickHandler = async (index: number) => {
  // 照査状態取得: IN：(Initial) 未実施、ST：(Start) 照査中、FN:（Finish）照査完了
  switch (index.toString()) {
    case '0':
      // 照査完了前：製造指図記録確認ダイアログ(W181510)
      // 照査完了後：製造指図記録確認(確認)ダイアログ(W181511)
      currentScreenId =
        props.selectedRowData!.verifyCatSts === 'FN' ||
        props.screenId ===
          SCREENID.SJG_MANUFACTURING_RECORD_INSPECTION_CONFIRMATION ||
        manufacturingRecItemData?.odrRecFlg === '1'
          ? SCREENID.SJG_MANUFACTURING_INSTRUCTION_RECORD_CONFIRMATION_CONFIRMATION
          : SCREENID.SJG_MANUFACTURING_INSTRUCTION_RECORD_CONFIRMATION;
      if (!(await checkValidVerifyHandler())) {
        return;
      }
      if (
        currentScreenId ===
        SCREENID.SJG_MANUFACTURING_INSTRUCTION_RECORD_CONFIRMATION
      ) {
        if (!(await checkVerifyStatusHandler())) {
          return;
        }
      }
      isClickedShowManufacturingInstructionRecordConfirmationRef.value =
        !isClickedShowManufacturingInstructionRecordConfirmationRef.value;
      break;
    case '1':
      // 照査完了前：SOP記録確認ダイアログ(W181610)
      // 照査完了後：SOP記録確認(確認)ダイアログ(W181611)
      currentScreenId =
        props.selectedRowData!.verifyCatSts === 'FN' ||
        props.screenId ===
          SCREENID.SJG_MANUFACTURING_RECORD_INSPECTION_CONFIRMATION ||
        manufacturingRecItemData?.odrSopFlg === '1'
          ? SCREENID.SJG_SOP_RECORD_CONFIRMATION_CONFIRMATION
          : SCREENID.SJG_SOP_RECORD_CONFIRMATION;
      if (!(await checkValidVerifyHandler())) {
        return;
      }
      if (currentScreenId === SCREENID.SJG_SOP_RECORD_CONFIRMATION) {
        if (!(await checkVerifyStatusHandler())) {
          return;
        }
      }
      isClickedShowSOPRecordConfirmationRef.value =
        !isClickedShowSOPRecordConfirmationRef.value;
      break;
    default:
      // 照査完了前：構成品情報確認ダイアログ(W181710)
      // 照査完了後：構成品情報確認(確認)ダイアログ(W181711)
      currentScreenId =
        props.selectedRowData!.verifyCatSts === 'FN' ||
        props.screenId ===
          SCREENID.SJG_MANUFACTURING_RECORD_INSPECTION_CONFIRMATION ||
        manufacturingRecItemData?.odrBomFlg === '1'
          ? SCREENID.SJG_COMPONENT_INFORMATION_CONFIRMATION_CONFIRMATION
          : SCREENID.SJG_COMPONENT_INFORMATION_CONFIRMATION;
      if (!(await checkValidVerifyHandler())) {
        return;
      }
      if (currentScreenId === SCREENID.SJG_COMPONENT_INFORMATION_CONFIRMATION) {
        if (!(await checkVerifyStatusHandler())) {
          return;
        }
      }
      isClickedSjgComponentInformationConfirmationRef.value =
        !isClickedSjgComponentInformationConfirmationRef.value;
      break;
  }
};

const setDialogConfigButtons = () => {
  if (manufacturingRecItemData) {
    // 製造記録照査内容の設定
    manufacturingRecItemListRef.value = getManufacturingRecItemList(
      manufacturingRecItemData,
    );
    // フッターの設定
    // 確認状態: 0：未確認，1: 確認済
    if (
      manufacturingRecItemData.odrBomFlg === '1' &&
      manufacturingRecItemData.odrRecFlg === '1' &&
      manufacturingRecItemData.odrSopFlg === '1'
    ) {
      // 全ての製造記録照査内容ボタンが、確認完了状態の場合のみ活性化する
      dialogExecutionButton!.at(0)!.disabled = false;
    } else {
      dialogExecutionButton!.at(0)!.disabled = true;
    }
    dialogCancelButton!.at(0)!.clickHandler = () => closeAllDialog();
    // 照査内容確認（製造記録照査）
    if (
      props.screenId ===
      SCREENID.SJG_MANUFACTURING_RECORD_INSPECTION_CONFIRMATION
    ) {
      dialogConfig.value.buttons = dialogCancelButton;
      dialogConfigTitle.value = t('Sjg.Chr.txtInspectionHistoryConfirmation');
    } else {
      dialogExecutionButton!.at(0)!.clickHandler = () => checkInspectForm();
      dialogConfig.value.buttons = [
        ...dialogCancelButton!,
        ...dialogExecutionButton!,
      ];
      dialogConfigTitle.value = t('Sjg.Chr.txtInspectionHistory');
    }
  }
  return true;
};

const getManufacturingRecItem = async () => {
  // 製造記録照査項目情報の取得
  showLoading();
  const { responseRef, errorRef } = await useGetManufacturingRecItem({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
  });

  if (errorRef.value) {
    closeLoading();
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    manufacturingRecItemData = undefined;
    return false;
  }
  if (responseRef.value) {
    manufacturingRecItemData = {
      ...responseRef.value.data.rData,
      odrRslt:
        responseRef.value.data.rData.odrRslt === 'IN'
          ? ''
          : responseRef.value.data.rData.odrRslt,
    };
    if (
      props.screenId ===
      SCREENID.SJG_MANUFACTURING_RECORD_INSPECTION_CONFIRMATION
    ) {
      setFormModelValueFromApiResponse(
        inspectManufacturingRecordFormRef,
        manufacturingRecItemData,
      );
    }
  }
  closeLoading();
  return true;
};

/**
 * 再検索
 */
const onSubmitHandler = async () => {
  await getManufacturingRecItem();
  setDialogConfigButtons();
};

const inspectManufacturingRecordInit = async () => {
  // 連携テストダイアログの初期化必要なら書く
  inspectManufacturingRecordFormRef.value.formItems =
    getInspectManufacturingRecordFormItems(props.screenId);
  await getManufacturingRecItem();
  if (manufacturingRecItemData) {
    setDialogConfigButtons();
    if (props.selectedInspectionItemData) {
      updateDialogChangeFlagRef(false);
      // 照査情報の初期設定
      inspectManufacturingRecordInfoShowRef.value.infoShowItems.matNo.infoShowModelValue =
        props.selectedInspectionItemData.matNo;
      inspectManufacturingRecordInfoShowRef.value.infoShowItems.lotNo.infoShowModelValue =
        props.selectedInspectionItemData.lotNo;
      inspectManufacturingRecordInfoShowRef.value.infoShowItems.matNm.infoShowModelValue =
        props.selectedInspectionItemData.matNm;
      inspectManufacturingRecordInfoShowRef.value.infoShowItems.verifyReasonNm.infoShowModelValue =
        props.selectedInspectionItemData.verifyReasonNm;
      inspectManufacturingRecordInfoShowRef.value.infoShowItems.expiryYmd.infoShowModelValue =
        props.selectedInspectionItemData.expiryYmd;
      inspectManufacturingRecordInfoShowRef.value.infoShowItems.rsltYmd.infoShowModelValue =
        props.selectedInspectionItemData.rsltYmd;

      // フッターの初期設定
      if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
        setCustomFormComboBoxOptionList(
          inspectManufacturingRecordFormRef.value.formItems,
          comboBoxResData.rData.rList,
        );
      }
    }
    // 照査完了前の場合(未実施／照査中)のみ、「インフォメーションメッセージ」を表示する
    if (
      props.selectedRowData!.verifyCatSts === 'IN' ||
      props.selectedRowData!.verifyCatSts === 'ST'
    ) {
      // 他ユーザの照査途中データを使用する場合（API名：製造記録照査開始の「4 他ユーザー照査中チェック」）、同じ「インフォメーションメッセージ」を表示する
      messageBoxSingleButtonRef.value.title = t(
        'Sjg.Msg.titleInspectManufacturingRecord',
      );
      messageBoxSingleButtonRef.value.content = t(
        'Sjg.Msg.contentInspectManufacturingRecord',
      );
      messageBoxSingleButtonRef.value.type = 'info';
      openDialog('singleButton');
    }
    openDialog('fragmentDialogVisible');
  }
  return true;
};

const closeWarningDialog = () => {
  closeDialog('modifyCmtSjgVeriOtherWarning');
  // 既存照査データ引継ぎチェック
  openDialog('existingVerificationWarning');
};

const modifyManufacturingRecVerifyStart = async (
  messageBoxProps: DialogProps,
  warningNecessityFlg: number = WARNING_NECESSITY_FLAG.NECESSITY,
) => {
  showLoading();
  // 製造記録照査開始
  let modifyManufacturingRecVerifyStartReq: ExtendCommonRequestType<ModifyManufacturingRecVerifyStartReq> =
    {
      ...props.privilegesBtnRequestData,
      lotSid: props.selectedInspectionItemData!.lotSid,
      verifyExpl: '',
      msgboxTitleTxt: messageBoxProps.title,
      msgboxMsgTxt: messageBoxProps.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      warningNecessityFlg,
    };
  if (
    'isPrompt' in messageBoxModifyCmtSjgVeriOtherPropsRef.value &&
    warningNecessityFlg === WARNING_NECESSITY_FLAG.UNNECESSARY &&
    props.comboBoxStandardReturnData
  ) {
    modifyManufacturingRecVerifyStartReq = {
      ...modifyManufacturingRecVerifyStartReq,
      msgboxInputCmt:
        messageBoxModifyCmtSjgVeriOtherPropsRef.value.formModel.message.toString(),
      verifyExpl:
        messageBoxModifyCmtSjgVeriOtherPropsRef.value.formModel.message.toString(),
    };
  }
  const { responseRef, errorRef } = await useModifyManufacturingRecVerifyStart(
    modifyManufacturingRecVerifyStartReq,
  );
  if (errorRef.value) {
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      messageBoxModifyCmtSjgVeriOtherPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxModifyCmtSjgVeriOtherPropsRef.value.content =
        errorRef.value.response.rMsg;
      // 警告コメント（他ユーザ割込）
      const resetData = createMessageBoxForm('message', 'cmtSjgVeriOther');
      if (
        'isPrompt' in messageBoxModifyCmtSjgVeriOtherPropsRef.value &&
        props.comboBoxStandardReturnData
      ) {
        messageBoxModifyCmtSjgVeriOtherPropsRef.value.formItems =
          resetData.formItems;
        setCustomFormComboBoxOptionList(
          messageBoxModifyCmtSjgVeriOtherPropsRef.value.formItems,
          props.comboBoxStandardReturnData.rData.rList,
        );
      }
      openDialog('modifyCmtSjgVeriOtherWarning');
    } else {
      messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
      messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
      messageBoxSingleButtonRef.value.type = 'error';
      openDialog('singleButton');
    }
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    closeLoading();
    await inspectManufacturingRecordInit();
  }
  return true;
};

const deleteOrderVerify = async (messageBoxProps: DialogProps) => {
  closeDialog('existingVerificationWarning');
  // ３．引継ぎしない場合、製造記録照査の取消を行う
  showLoading();
  const requestData: ExtendCommonRequestType<{ lotSid: string }> = {
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
    msgboxTitleTxt: messageBoxProps.title,
    msgboxMsgTxt: messageBoxProps.content,
    msgboxBtnTxt: t('Cm.Chr.btnCancel'),
  };
  const { responseRef, errorRef } = await useDeleteOrderVerify(requestData);
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    closeLoading();
    await modifyManufacturingRecVerifyStart(
      messageBoxProps,
      WARNING_NECESSITY_FLAG.UNNECESSARY,
    );
  }
  return true;
};

const closeExistingVerificationDialog = async (
  messageBoxProps: DialogProps,
) => {
  closeDialog('existingVerificationWarning');
  // 製造記録照査開始
  await modifyManufacturingRecVerifyStart(
    messageBoxProps,
    WARNING_NECESSITY_FLAG.UNNECESSARY,
  );
  return true;
};

const closeSjgInfoValidCheckError = async () => {
  closeDialog('sjgInfoValidCheckError');
  router.push({
    name: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
    state: {
      routerName: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
      searchConditionData: window.history.state.conditionData,
      tableSearchData: window.history.state.tableSearchData,
    },
  });
};

/**
 * 初期設定
 */
const sjgInspectManufacturingRecordInit = async () => {
  showLoading();
  // 標準コンボボックスデータ取得
  const cmbOdrRsltCdVal =
    props.selectedInspectionItemData &&
    props.selectedInspectionItemData.verifyReason ===
      CONST_FLAGS.SJG.LOT_OUT_STATUS.ONLY_LOT_OUT_ITEM
      ? 'FL'
      : 'PS,FL';
  comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtSjgVeriOdr',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'SJG_VERIFY_ODR' },
      },
      {
        cmbId: 'cmbOdrRslt',
        condKey: 'm_mp_cd',
        where: { cd_id: 'ODR_RSLT', cd_val: cmbOdrRsltCdVal },
      },
    ],
  });
  // 照査実行（製造記録照査）
  if (props.screenId === SCREENID.SJG_MANUFACTURING_RECORD_INSPECTION) {
    // 製造記録照査開始
    await modifyManufacturingRecVerifyStart(
      messageBoxModifyCmtSjgVeriOtherPropsRef.value,
    );
    return;
  }
  await inspectManufacturingRecordInit();
  closeLoading();
};

watch(() => props.isClicked, sjgInspectManufacturingRecordInit);
</script>
<style lang="scss" scoped>
.sjg-manufacturing-record-inspection_button-ex-class {
  display: flex;
  align-items: center;
}
.row-with-line {
  padding: 16px 0;
  & + & {
    border-top: 1px solid var(--el-border-color);
  }
}
</style>
