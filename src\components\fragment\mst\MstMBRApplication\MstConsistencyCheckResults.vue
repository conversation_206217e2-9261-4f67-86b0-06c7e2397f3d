<template>
  <!-- 整合性チェック結果ダイアログ -->
  <DialogWindow
    :title="$t('Mst.Chr.txtConsistencyCheckResults')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    :class="'consistency-check-results'"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    width="98vw"
  >
    <!-- MBR申請情報のテキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="mbrApplicationInfoShowRef.infoShowItems"
      :isLabelVertical="mbrApplicationInfoShowRef.isLabelVertical"
    />
    <!-- 整合性チェック結果一覧テーブル -->
    <TabulatorTable
      :propsData="tablePropsDataRef"
      :routerName="props.routerName"
    />
  </DialogWindow>
  <!-- 申請コメント入力ダイアログ -->
  <MstInputApplicationComment
    :isClicked="isClickedShowMstInputApplicationCommentDialogRef"
    :mbrNo="initResponseData.mbrNo"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    @submit="updateConsistencyCheckResultsList"
  />
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { GetConsistencyCheckResultsListReq } from '@/types/HookUseApi/MstTypes';
import { useGetConsistencyCheckResultsList } from '@/hooks/useApi';
import MstInputApplicationComment from '@/components/fragment/mst/MstMBRApplication/MstInputApplicationComment.vue';
import {
  getMBRApplicationInfoShowItems,
  tablePropsDataRef,
} from './mstConsistencyCheckResults';

/**
 * 結果の定義(OK時の文字列)
 */
const RESULTS_OK: string = 'OK';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey = 'fragmentDialogVisible' | 'messageBoxApiErrorVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

const mbrApplicationInfoShowRef = ref<InfoShowType>({
  infoShowItems: getMBRApplicationInfoShowItems(),
  isLabelVertical: true,
});

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

const isClickedShowMstInputApplicationCommentDialogRef = ref<boolean>(false);

// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
  {
    text: t('Mst.Chr.btnApplication'), // 申請
    type: 'primary',
    size: 'normal',
    clickHandler: () => {
      // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
      isClickedShowMstInputApplicationCommentDialogRef.value =
        !isClickedShowMstInputApplicationCommentDialogRef.value;

      return false;
    },
  },
];

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  mbrNo: string; // MBR番号
  privilegesBtnRequestData: CommonRequestType;
  routerName: string; // TabulatorTable権限 未選択チェック用
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 初期表示APIレスポンスデータ 必要な情報キャッシュ
const initResponseData = {
  mbrNo: '',
};

// 初回表示、再検索で呼び出される
// 整合性チェック結果一覧取得API呼び出し
const requestApiGetConsistencyCheckResultsList = async (
  requestData: GetConsistencyCheckResultsListReq,
) => {
  showLoading();

  const { responseRef, errorRef } = await useGetConsistencyCheckResultsList({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return Promise.reject(); // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // 再検索用にMBR番号をキャッシュ
    initResponseData.mbrNo = responseRef.value.data.rData.mbrNo;

    // MBR申請情報レイアウト用初期値設定
    Object.entries(responseRef.value.data.rData).forEach(([key, value]) => {
      if (key in mbrApplicationInfoShowRef.value.infoShowItems) {
        mbrApplicationInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });

    // 整合性チェック結果一覧をテーブルに反映
    tablePropsDataRef.value.tableData = responseRef.value.data.rData.masterList;

    // NGがある場合は申請ボタンを無効化、ない場合は有効化
    dialogButtons[1].disabled = false;
    responseRef.value.data.rData.masterList.forEach((master) => {
      if (master.results !== RESULTS_OK) {
        dialogButtons[1].disabled = true;
      }
    });
  }

  closeLoading();

  return Promise.resolve();
};

/**
 * マスタ申請完了後の処理
 */
const updateConsistencyCheckResultsList = async (
  requestData: CommonRequestType,
  mbrNo: string = '',
) => {
  // ダイアログを閉じる
  closeDialog('fragmentDialogVisible');
  emit('submit', requestData, mbrNo);
};

/**
 * 整合性チェック結果ダイアログの初期設定
 */
const mstConsistencyCheckResultsInit = async () => {
  // InfoShow初期化
  mbrApplicationInfoShowRef.value.infoShowItems =
    getMBRApplicationInfoShowItems();

  // 整合性チェック結果一覧取得のAPI呼び出しと反映
  try {
    const requestData: GetConsistencyCheckResultsListReq = {
      mbrNo: props.mbrNo,
    };
    await requestApiGetConsistencyCheckResultsList(requestData);
  } catch {
    return;
  }

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await mstConsistencyCheckResultsInit();
  },
);
</script>
