<template>
  <!-- 製造記録確認 秤量記録ダイアログ -->
  <DialogWindow
    :title="$t('Prd.Chr.txtConfirmWeighing')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 見出し 製造指図情報 -->
    <BaseHeading
      level="2"
      :text="$t('Prd.Chr.txtOrderInformation')"
      fontSize="24px"
    />
    <!-- 製造指図情報 テキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="detailInfoInfoShowRef.infoShowItems"
      :isLabelVertical="detailInfoInfoShowRef.isLabelVertical"
    />
    <!-- 見出し 秤量記録 -->
    <BaseHeading
      class="Util_mt-16"
      level="2"
      :text="$t('Prd.Chr.txtWeightRecord')"
      fontSize="24px"
    />
    <!-- 共通のテーブル -->
    <TabulatorTable
      :propsData="tablePropsDataRef"
      :routerName="props.routerName"
      @selectRow="updateSelectedRow"
    />
  </DialogWindow>
  <!-- 秤量記録修正ダイアログ -->
  <WgtEditWeighingRecord
    :isClicked="isClickedWgtEditWeighingRecordDialogRef"
    :wgtInstNo="getWgtInstNo()"
    :wgtCnt="getWgtCnt()"
    :seqNo="getSeqNo()"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    @submit="prdConfirmWeighingRedisplayInit"
  />
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 秤量記録確認済チェック表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxCheckInfoConfirmWeighingVisible"
    :dialogProps="messageBoxCheckInfoConfirmWeighingPropsRef"
    :submitCallback="
      () => closeDialog('messageBoxCheckInfoConfirmWeighingVisible')
    "
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { useGetConfirmWeighingInit } from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import WgtEditWeighingRecord from '@/components/include/wgt/WgtEditWeighingRecord.vue';
import {
  GetConfirmWeighingInitResponseData,
  GetConfirmWeighingInitRequestData,
  GetConfirmInfoListData,
} from '@/types/HookUseApi/PrdTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import getOrderDetailInfoShowItems from '@/components/fragment/prd/PrdConfirmList/prdConfirmInfoList';

/**
 * 多言語
 */
const { t } = useI18n();
const emit = defineEmits(['clickCancel']);

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxCheckInfoConfirmWeighingVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxCheckInfoConfirmWeighingVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// 製造指図情報
// infoShowItemsは親ダイアログの製造指図情報の項目定義を利用
const detailInfoInfoShowRef = ref<InfoShowType>({
  infoShowItems: getOrderDetailInfoShowItems(),
  isLabelVertical: true,
});

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 秤量記録確認済チェック表示のメッセージボックス
const messageBoxCheckInfoConfirmWeighingPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleCheckInfoConfirmWeighing'),
  content: t('Prd.Msg.contentCheckInfoConfirmWeighing'),
  isSingleBtn: true,
  type: 'info',
});

type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  odrNo?: string;
  prcSeq?: number;
  infoData: GetConfirmInfoListData | null;
  wgtInstConfirmFlg: boolean; // 秤量指示確認フラグ
  routerName: string;
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

// 選択行情報の格納
let selectedRowData: GetConfirmWeighingInitResponseData | null = null;

// 選択行情報の更新
const updateSelectedRow = (v: GetConfirmWeighingInitResponseData | null) => {
  // 選択行情報を保存
  selectedRowData = v;
};

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    clickHandler: () => {
      selectedRowData = null;

      // NOTE:特殊対応:子以下の階層で更新の可能性があるため、排他チェックエラーの対象とならないよう親に再更新を促す。
      // NOTE:再検索の意図で呼び出すため、本来は必要な時だけ発行したいが工数削減のため常時通知
      // ダイアログを閉じたことを親に通知する
      emit('clickCancel');

      // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandler内で行わない。
    },
  },
];

// 秤量指示明細番号の設定
const getWgtInstNo = () => {
  // 行選択されていない場合はundefined
  if (selectedRowData === null) {
    return undefined;
  }

  // 選択行情報の秤量指示明細番号を返す
  return selectedRowData.wgtInstNo;
};

// 秤量実施回数の設定
const getWgtCnt = () => {
  // 行選択されていない場合はnull
  if (selectedRowData === null) {
    return null;
  }

  // 選択行情報の秤量実施回数を返す
  return selectedRowData.wgtCnt;
};

// シーケンシャル番号の設定
const getSeqNo = () => {
  // 行選択されていない場合はnull
  if (selectedRowData === null) {
    return null;
  }

  // 選択行情報のシーケンシャル番号を返す
  return selectedRowData.seqNo;
};

// 秤量記録用テーブル設定
const tablePropsDataRef = ref<TabulatorTableIF>({
  pageName: 'PrdConfirmWeighing',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'uniqueKey', // 主キー。ユニークになるものを設定。
  showRadio: true,

  column: [
    // 秤量指示明細番号
    {
      title: 'Prd.Chr.txtWeightOrderDetailNumber',
      field: 'wgtInstNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    // 指示書状態
    {
      title: 'Prd.Chr.txtWeightInstructionStatus',
      field: 'wgtInstSts',
      width: COLUMN_WIDTHS.PRD.WGT_INST_STS,
    },
    // 秤量実施回数 隠しカラム
    { title: '', field: 'wgtCnt', hidden: true },
    // シーケンス番号 隠しカラム
    { title: '', field: 'seqNo', hidden: true },
    // 秤量品目コード
    {
      title: 'Prd.Chr.txtWeightMaterialCode',
      field: 'bomMatNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    // 秤量品名
    {
      title: 'Prd.Chr.txtWeightMaterialItemName',
      field: 'wgtDspNmJp',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    // 管理番号
    {
      title: 'Prd.Chr.txtManageNo',
      field: 'lotNo',
      width: COLUMN_WIDTHS.LOT_NO,
    },
    // バッチ番号
    {
      title: 'Prd.Chr.txtBatchNumber',
      field: 'batchNo',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.BAT_NO,
    },
    // 小分け番号
    {
      title: 'Prd.Chr.txtWeightHandNo',
      field: 'wgtHandNo',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.PRD.WGT_HAND_NO,
    },
    // 指図指示量
    {
      title: 'Prd.Chr.txtOrderInstructionValue',
      field: 'bomPlanQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 秤量指示量
    {
      title: 'Prd.Chr.txtWeightInstructionValue',
      field: 'scnInstHandVal',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // バッチ累積秤取量
    {
      title: 'Prd.Chr.txtBatchWeightValue',
      field: 'scnBatchTotalVal',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 秤取量
    {
      title: 'Prd.Chr.txtWeightValue',
      field: 'scnWgtVal',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 秤量単位
    {
      title: 'Prd.Chr.txtWeightUnit',
      field: 'unitNmJp',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    // 風袋重量
    {
      title: 'Prd.Chr.txtWeightTareValue',
      field: 'conWgt',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 風袋重量単位
    {
      title: 'Prd.Chr.txtWeightTareValueUnit',
      field: 'conUnitNmJp',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    // 秤量日時
    {
      title: 'Prd.Chr.txtWeightDate',
      field: 'wgtDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // 製造品目コード
    {
      title: 'Prd.Chr.txtOrderMaterialCD',
      field: 'matNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    // 製造品名
    {
      title: 'Prd.Chr.txtOrderMaterialName',
      field: 'dspNmJp',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    // 計量器
    {
      title: 'Prd.Chr.txtWeightDevice',
      field: 'deviceNmJp',
      width: COLUMN_WIDTHS.PRD.DEVICE_NM,
    },
    // 記録者
    {
      title: 'Prd.Chr.txtRecordUser',
      field: 'recUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 再秤量フラグ(表示用)
    {
      title: 'Prd.Chr.txtReWeightFlag',
      field: 'reWgtFlgDsp',
      width: COLUMN_WIDTHS.PRD.RE_WGT_FLG,
    },
    // 修正コメント
    {
      title: 'Prd.Chr.txtModifyComment',
      field: 'modExplDsp',
      width: COLUMN_WIDTHS.PRD.MOD_EXPL_EXIST,
    },
    // レスポンスにユニークなデータが存在しないため、自前で隠しカラムでユニーク情報生成
    { title: '', field: 'uniqueKey', hidden: true },
  ],
  tableData: [],
  // NOTE:権限が親からprops経由で来るため、Init関数内でonSelectBtnsを生成する
  onSelectBtns: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
});

// '秤量記録修正' クリック 秤量記録修正ダイアログへ遷移
const isClickedWgtEditWeighingRecordDialogRef = ref<boolean>(false);

/**
 * 製造記録確認 秤量記録ダイアログのAPI呼び出し
 */
const requestApiGetConfirmWeighingInit = async () => {
  if (
    props.odrNo === undefined ||
    props.prcSeq === undefined ||
    props.infoData === null
  ) {
    return Promise.reject();
  }

  showLoading();

  // 製造記録確認 秤量記録初期表示のAPIを行う。
  const requestData: GetConfirmWeighingInitRequestData = {
    odrNo: props.odrNo,
    prcSeq: props.prcSeq,
  };

  // 製造記録確認 秤量記録初期表示API実行
  const { responseRef, errorRef } = await useGetConfirmWeighingInit({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return Promise.reject(); // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return Promise.reject();
  }

  // テーブル表示データ
  tablePropsDataRef.value.tableData = responseRef.value.data.rData.wgtList;
  // レスポンスに存在しないユニークキーを独自に設定して隠しカラムに仕込む対応
  tablePropsDataRef.value.tableData.forEach((value) => {
    const tableData = value;
    // 秤量指示明細番号 + 秤量実施回数 + シーケンシャル番号
    tableData.uniqueKey = `${value.wgtInstNo}-${value.wgtCnt}-${value.seqNo}`;
  });

  // 製造指図情報レイアウト用初期値設定
  Object.entries(props.infoData).forEach(([key, value]) => {
    if (key in detailInfoInfoShowRef.value.infoShowItems) {
      detailInfoInfoShowRef.value.infoShowItems[key].infoShowModelValue =
        value?.toString() ?? '';
    }
  });
  detailInfoInfoShowRef.value.infoShowItems.odrNo.infoShowModelValue =
    props.odrNo;

  // NOTE:ボタンに権限付与は親から渡されたものを設定するため、初期化はここで行う。
  tablePropsDataRef.value.onSelectBtns = [
    {
      type: 'primary',
      text: 'Prd.Chr.btnWeightRecordModify', // 秤量記録修正
      // NOTE:選択チェック用に権限付与。
      tabulatorActionId: props.privilegesBtnRequestData.btnId,
      disabled: responseRef.value.data.rData.recConfirmFlg, // 記録確認フラグを設定（trueなら非活性）
      clickHandler: () => {
        // NOTE:本来は選択行情報は子でnullチェックするが、このダイアログは例外的に選択行を受け取らないため自前でチェック
        // NOTE:未選択なら処理させない。エラーメッセージはTabulatorTable側で出してくれる。
        if (selectedRowData === null) {
          return;
        }
        isClickedWgtEditWeighingRecordDialogRef.value =
          !isClickedWgtEditWeighingRecordDialogRef.value;
      },
    },
  ];

  closeLoading();

  return Promise.resolve();
};

/**
 * 製造記録確認 秤量記録ダイアログの再検索用初期設定
 */
const prdConfirmWeighingRedisplayInit = async () => {
  // 秤量記録の取得のAPI呼び出しと反映
  try {
    await requestApiGetConfirmWeighingInit();
  } catch {
    // NOTE:後続処理が無い場合、returnを書くとeslintでエラーになる。必要なら実装してください。
    // return;
  }
};

/**
 * 製造記録確認 秤量記録ダイアログの初期設定
 */
const prdConfirmWeighingInit = async () => {
  // 秤量記録の取得のAPI呼び出しと反映
  try {
    await requestApiGetConfirmWeighingInit();
  } catch {
    return;
  }

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');

  if (!props.wgtInstConfirmFlg) {
    // NOTE:特殊対応 ダイアログ起動後に秤量記録確認済チェック表示を出す
    openDialog('messageBoxCheckInfoConfirmWeighingVisible');
  }
};

// ダイアログ起動条件の監視。isClickedの真偽値が切り替わりを監視
watch(() => props.isClicked, prdConfirmWeighingInit);
</script>
