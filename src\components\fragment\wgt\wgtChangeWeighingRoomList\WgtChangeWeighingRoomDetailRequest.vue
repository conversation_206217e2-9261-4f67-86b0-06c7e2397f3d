<template>
  <!-- 秤量室変更詳細(依頼)ダイアログ -->
  <DialogWindow
    :title="$t('Wgt.Chr.txtWeightRoomChangeDetailRequest')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 秤量室変更情報 テキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="detailInfoInfoShowRef.infoShowItems"
      :isLabelVertical="detailInfoInfoShowRef.isLabelVertical"
    />

    <!-- 秤量室変更用コントロール -->
    <CustomForm
      class="Util_mt-16"
      :formModel="dialogFormRef.formModel"
      :formItems="dialogFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          dialogFormRef.customForm = v;
        }
      "
      @selectedItem="updateFormItems"
      @changeFormModel="
        (changeFlag, { changeLogModList }) => {
          updateDialogChangeFlagRef(changeFlag);
          updateCurrentChangedFormModelMap(changeLogModList);
        }
      "
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 秤量室変更依頼前の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxWeighingRoomChangeRequestVisible"
    :dialogProps="messageBoxWgtRoomChangeRequestPropsRef"
    :cancelCallback="
      () => closeDialog('messageBoxWeighingRoomChangeRequestVisible')
    "
    :submitCallback="requestApiCheckWeighingRoomChangeRequest"
  />
  <!-- 秤量室依頼前チェックのワーニング表示 -->
  <MessageBox
    v-if="
      dialogVisibleRef.messageBoxCheckWeighingRoomChangeRequestWarningVisible
    "
    :dialogProps="messageBoxCheckWeighingRoomChangeRequestWarningPropsRef"
    :cancelCallback="
      () =>
        closeDialog('messageBoxCheckWeighingRoomChangeRequestWarningVisible')
    "
    :submitCallback="
      () => {
        closeDialog('messageBoxCheckWeighingRoomChangeRequestWarningVisible');
        // チェックOKなら処理継続する。
        // 秤量室変更依頼API呼び出し
        requestApiModifyWeighingRoomChangeRequest();
      }
    "
  />
  <!-- 秤量室変更依頼の完了表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxWeighingRoomChangeRequestFinishedVisible"
    :dialogProps="messageBoxRoomChangeRequestFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxRoomChangeRequestFinished"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import CONST from '@/constants/utils';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import {
  useGetComboBoxDataStandard,
  useGetWeighingRoomChangeRequestInit,
  useCheckWeighingRoomChangeRequest,
  useModifyWeighingRoomChangeRequest,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import {
  setCustomFormComboBoxOptionList,
  setCustomFormFilterComboBoxOptionList,
} from '@/utils/comboBoxOptionList';
import {
  GetWeighingRoomChangeRequestInitResList,
  GetWeighingRoomChangeRequestInitRequestData,
  CheckWeighingRoomChangeRequestRequestData,
  CheckWeighingRoomChangeRequestRequestListData,
  ModifyWeighingRoomChangeRequestRequestData,
  ModifyWeighingRoomChangeRequestRequestListData,
  GetWeighingRoomChangeResData,
} from '@/types/HookUseApi/WgtTypes';
import {
  ComboBoxDataOptionData,
  ComboBoxDataStandardReturnData,
  CommonRequestType,
  LogModListType,
} from '@/types/HookUseApi/CommonTypes';
import {
  getDialogFormItems,
  dialogFormModel,
  getInfoShowItems,
} from './wgtChangeWeighingRoomDetailRequest';

/**
 * 多言語
 */
const { t } = useI18n();
let comboBoxOptionList: ComboBoxDataOptionData[] = [];
const constantData = {
  afterWgtDeviceNo: 'afterWgtDeviceNo',
  afterRemDeviceNo: 'afterRemDeviceNo',
  afterWgtSopsetKey: 'afterWgtSopsetKey',
} as const;

// 秤量室変更(依頼)初期表示のレスポンス リスト部分
let initResponseData: GetWeighingRoomChangeRequestInitResList = {
  wgtInstList: [],
  matNo: '',
  dspNmJp: '',
  lotNo: '',
  odrNo: '',
  prcNmJp: '',
  batchNo: 0,
  wgtSopsetNmJp: '',
  wgtRoomNmJp: '',
  deviceNmJp: '',
  remDeviceNmJp: '',
  deviceCat: '',
  remDeviceCat: '',
};

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxWeighingRoomChangeRequestVisible'
  | 'messageBoxCheckWeighingRoomChangeRequestWarningVisible'
  | 'messageBoxWeighingRoomChangeRequestFinishedVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxWeighingRoomChangeRequestVisible: false,
  messageBoxCheckWeighingRoomChangeRequestWarningVisible: false,
  messageBoxWeighingRoomChangeRequestFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const detailInfoInfoShowRef = ref<InfoShowType>({
  infoShowItems: getInfoShowItems(),
  isLabelVertical: true,
});

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 秤量室変更依頼の確認メッセージボックス
const messageBoxWgtRoomChangeRequestPropsRef = ref<DialogProps>({
  title: t('Wgt.Msg.titleWeightRoomChgRequest'),
  content: t('Wgt.Msg.contentWeightRoomChgRequest'),
  type: 'question',
});

// 変更依頼の完了メッセージボックス
const messageBoxRoomChangeRequestFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

const dialogFormRef = ref<CustomFormType>({
  formItems: getDialogFormItems(),
  formModel: dialogFormModel,
});

// 秤量室変更依頼前チェック ワーニングのメッセージボックス
const messageBoxCheckWeighingRoomChangeRequestWarningPropsRef =
  ref<DialogProps>({
    title: '', // レスポンスで上書きする
    content: '', // レスポンスで上書きする
    type: 'warning',
  });

let logModList: LogModListType = [];
const updateCurrentChangedFormModelMap = (changeLogModList: LogModListType) => {
  logModList = changeLogModList;
};

type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  selectedRow: GetWeighingRoomChangeResData | null; // 秤量室変更の選択行情報
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;
const emit = defineEmits(['submit']);

// 共通APIリクエストに渡すための、直前MessageBox表示テキストの保存情報
type MessageBoxInfo = {
  titleText: string;
  messageText: string;
  buttonText: string;
};

let messageBoxInfo: MessageBoxInfo = {
  titleText: '',
  messageText: '',
  buttonText: '',
};

// 直前MessageBox表示テキストの保存
const cacheMessageBoxInfo = (v: DialogProps) => {
  messageBoxInfo = {
    titleText: v.title,
    messageText: v.content,
    buttonText: t('Cm.Chr.btnOk'),
  };
};

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // 下記チェックを行い、チェックOKなら処理継続する。
  // 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    dialogFormRef.value.customForm !== undefined &&
    (await dialogFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return false;
  }

  // 秤量室変更依頼API用に、確認メッセージ情報を保存
  cacheMessageBoxInfo(messageBoxWgtRoomChangeRequestPropsRef.value);

  // 以下のメッセージを表示
  // 秤量室変更依頼前の確認
  openDialog('messageBoxWeighingRoomChangeRequestVisible');
  return false;
};

// 秤量室変更依頼完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxRoomChangeRequestFinished = () => {
  // 再検索用に親に実行を通知
  emit('submit', props.privilegesBtnRequestData);

  // ダイアログウィンドウを閉じる
  closeDialog('messageBoxWeighingRoomChangeRequestFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

// 指定selectComboBoxのカスタムフォームの非活性状態設定
// @param {string} formName - カスタムフォーム名
// @param {boolean} isDisabled - diabledの値。true:非活性 false:活性化
const setDisabledCustomForm = (formName: string, isDisabled: boolean) => {
  // フォームの存在チェック
  if (
    !(
      dialogFormRef.value.formModel && formName in dialogFormRef.value.formModel
    )
  ) {
    console.error('formName is not found in formModel.', formName);
    return;
  }
  // 型ガード用の判定
  if (dialogFormRef.value.formItems[formName].formRole !== 'selectComboBox') {
    console.error('formRole is not selectComboBox.', formName);
    return;
  }
  // 型ガード用の判定
  if (dialogFormRef.value.formItems[formName].props === undefined) {
    console.error('props is undefined.', formName);
    return;
  }
  dialogFormRef.value.formItems[formName].props.disabled = isDisabled;
};

// カスタムフォームafterWgtDeviceNoの非活性状態設定
// @param {boolean} isDisabled - diabledの値。true:非活性 false:活性化
const setDisabledAfterWgtDeviceNo = (isDisabled: boolean) => {
  setDisabledCustomForm(constantData.afterWgtDeviceNo, isDisabled);
};

// カスタムフォームafterRemDeviceNoの非活性状態設定
// @param {boolean} isDisabled - diabledの値。true:非活性 false:活性化
const setDisabledAfterRemDeviceNo = (isDisabled: boolean) => {
  setDisabledCustomForm(constantData.afterRemDeviceNo, isDisabled);
};

// 項目が変更された際の処理
const updateFormItems = (fieldId: string) => {
  // 変更後の秤量セット/秤量室を変更した場合
  if (fieldId === constantData.afterWgtSopsetKey) {
    // イベント：フォーカスアウト時に選択されている秤量セットに属する計量器を計量器コンボボックスに反映。
    setCustomFormFilterComboBoxOptionList(
      dialogFormRef.value.formItems,
      comboBoxOptionList,
      constantData.afterWgtDeviceNo,
      constantData.afterWgtDeviceNo,
      [fieldId],
    );

    setCustomFormFilterComboBoxOptionList(
      dialogFormRef.value.formItems,
      comboBoxOptionList,
      constantData.afterRemDeviceNo,
      constantData.afterRemDeviceNo,
      [fieldId],
    );

    // 親「変更後の秤量セット」の選択値に応じて、子「変更後の計量器」の活性状態切り替え
    const isDisabled =
      dialogFormRef.value.formItems[constantData.afterWgtSopsetKey]
        .formModelValue === '';
    setDisabledAfterWgtDeviceNo(isDisabled);
    setDisabledAfterRemDeviceNo(isDisabled);
  }
};

/**
 * 秤量室変更詳細(依頼)ダイアログの初期設定
 */
const wgtChangeWeighingRoomDetailRequestInit = async () => {
  if (props.selectedRow === null) return;
  // eslintエラー回避目的の型ガード
  if (props.selectedRow.batchNo === null) return;
  if (props.selectedRow.prcSeq === null) return;

  showLoading();
  updateDialogChangeFlagRef(false);
  logModList = [];

  // 秤量室変更詳細(依頼)初期表示のAPIを行う。
  const requestData: GetWeighingRoomChangeRequestInitRequestData = {
    odrNo: props.selectedRow.odrNo,
    wgtSopsetKey: props.selectedRow.wgtSopsetKey,
    wgtDeviceNo: props.selectedRow.wgtDeviceNo,
    remDeviceNo: props.selectedRow.remDeviceNo,
    roomReqSts: props.selectedRow.roomReqSts,
    batchNo: props.selectedRow.batchNo,
    prcSeq: props.selectedRow.prcSeq,
    reBatchFlg: props.selectedRow.reBatchFlg,
    reWgtFlg: props.selectedRow.reWgtFlg,
    grpEraseFlg: props.selectedRow.grpEraseFlg,
  };

  // 秤量室変更詳細(依頼)初期表示API実行
  const { responseRef, errorRef } = await useGetWeighingRoomChangeRequestInit({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // ダイアログ表示初期値として、レスポンス情報を格納
    initResponseData = responseRef.value.data.rData;

    // FormItems初期化
    dialogFormRef.value.formItems = getDialogFormItems();

    // 秤量室変更詳細情報レイアウト用初期値設定
    Object.entries(initResponseData).forEach(([key, value]) => {
      if (key in detailInfoInfoShowRef.value.infoShowItems) {
        detailInfoInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });
  }

  // 標準コンボボックスデータ取得
  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'afterWgtSopsetKey', // 変更後の秤量セット/秤量室
        condKey: 'm_rx_wgt_sopset',
      },
      {
        cmbId: 'afterWgtDeviceNo', // 変更後の計量器(通常秤量)
        condKey: 'm_device',
        where: { wgt_flg: '1', device_cat: initResponseData.deviceCat }, // 秤量器フラグが1のもの、前画面で取得した機器分類(通常秤量)を取得
        optionCol: { wgt_sopset_key: 'afterWgtSopsetKey' },
      },
      {
        cmbId: 'afterRemDeviceNo', // 変更後の計量器(残計量)
        condKey: 'm_device',
        where: { wgt_flg: '1', device_cat: initResponseData.remDeviceCat }, // 秤量器フラグが1のもの、前画面で取得した機器分類(残計量)を取得
        optionCol: { wgt_sopset_key: 'afterWgtSopsetKey' },
      },
      {
        cmbId: 'modExpl', // 変更コメント
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'WGT_ROOM_MOD' },
      },
    ],
  });

  // 標準コンボボックスのデータが取得できていれば画面上に反映
  if (
    comboBoxDataStandardReturnData &&
    comboBoxDataStandardReturnData.rData.rList.length > 0
  ) {
    comboBoxOptionList = comboBoxDataStandardReturnData.rData.rList;
    setCustomFormComboBoxOptionList(
      dialogFormRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
  closeLoading();
};

// 秤量室変更確認、またはワーニングチェックのメッセージ'OK'押下時処理
// 秤量室変更依頼のAPIリクエスト処理
const requestApiModifyWeighingRoomChangeRequest = async () => {
  showLoading();

  // 秤量室変更依頼APIを実行する。
  const requestData: ModifyWeighingRoomChangeRequestRequestData = {
    wgtInstList: [],
    // 変更後の秤量セット、計量器、変更コメント…コンボボックスの内容を入力
    afterWgtSopsetKey:
      dialogFormRef.value.formItems.afterWgtSopsetKey.formModelValue.toString(),
    afterWgtDeviceNo:
      dialogFormRef.value.formItems.afterWgtDeviceNo.formModelValue.toString(),
    afterRemDeviceNo:
      dialogFormRef.value.formItems.afterRemDeviceNo.formModelValue.toString(),
    modExpl: dialogFormRef.value.formItems.modExpl.formModelValue.toString(),
    logModList,
  };
  initResponseData.wgtInstList.forEach((item) => {
    const data: ModifyWeighingRoomChangeRequestRequestListData = {
      wgtInstNo: item.wgtInstNo,
      updDts: item.updDts,
    };
    requestData.wgtInstList.push(data);
  });

  const { responseRef, errorRef } = await useModifyWeighingRoomChangeRequest({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxInfo.titleText,
    msgboxMsgTxt: messageBoxInfo.messageText,
    msgboxBtnTxt: messageBoxInfo.buttonText,
    ...requestData,
  });

  if (errorRef.value) {
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // ・データベースを更新した後、以下のメッセージを表示する。
    // 秤量室変更依頼完了
    messageBoxRoomChangeRequestFinishedPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxRoomChangeRequestFinishedPropsRef.value.content =
      responseRef.value.data.rMsg;

    openDialog('messageBoxWeighingRoomChangeRequestFinishedVisible');
  }

  closeLoading();
};

// 秤量室変更依頼の確認メッセージ'OK'押下時処理
// 秤量室変更依頼前のAPIリクエスト処理
const requestApiCheckWeighingRoomChangeRequest = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxWeighingRoomChangeRequestVisible');
  showLoading();

  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 下記チェックを行い、チェックOKなら処理継続する。

  // 秤量室変更依頼前チェックのAPIを行う。
  const requestData: CheckWeighingRoomChangeRequestRequestData = {
    wgtInstList: [],
    // 変更後の秤量セット、計量器…コンボボックスの内容を入力
    afterWgtSopsetKey:
      dialogFormRef.value.formItems.afterWgtSopsetKey.formModelValue.toString(),
    afterWgtDeviceNo:
      dialogFormRef.value.formItems.afterWgtDeviceNo.formModelValue.toString(),
    afterRemDeviceNo:
      dialogFormRef.value.formItems.afterRemDeviceNo.formModelValue.toString(),
  };
  initResponseData.wgtInstList.forEach((item) => {
    const data: CheckWeighingRoomChangeRequestRequestListData = {
      wgtInstNo: item.wgtInstNo,
      updDts: item.updDts,
    };
    requestData.wgtInstList.push(data);
  });

  const { responseRef, errorRef } = await useCheckWeighingRoomChangeRequest({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxWgtRoomChangeRequestPropsRef.value.title,
    msgboxMsgTxt: messageBoxWgtRoomChangeRequestPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    ...requestData,
  });
  if (errorRef.value) {
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      // 秤量室変更依頼前チェック ワーニング用メッセージボックス起動
      messageBoxCheckWeighingRoomChangeRequestWarningPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxCheckWeighingRoomChangeRequestWarningPropsRef.value.content =
        errorRef.value.response.rMsg;
      openDialog('messageBoxCheckWeighingRoomChangeRequestWarningVisible');

      // ワーニングメッセージを保存
      cacheMessageBoxInfo(
        messageBoxCheckWeighingRoomChangeRequestWarningPropsRef.value,
      );

      closeLoading();
      return false; // ワーニングの場合、ワーニング用messageBox側で処理継続させる。
    }
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return false; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return false; // レスポンスがない場合継続処理させない
  }

  // NOTE:次に呼び出すAPIは関数内部でLoadingするため、このAPIとしてはcloseします
  closeLoading();

  // 秤量室変更依頼API呼び出し
  await requestApiModifyWeighingRoomChangeRequest();

  return true;
};

// ダイアログ起動条件の監視。isClickedの真偽値が切り替わりを監視
watch(() => props.isClicked, wgtChangeWeighingRoomDetailRequestInit);
</script>
