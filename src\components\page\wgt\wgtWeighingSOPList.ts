import CONST from '@/constants/utils';
import { rules } from '@/utils/validator';
import { ConditionData } from '@/types/ConditionSearchTypes';
import { SearchDataModel } from '@/hooks/useSearch';

// 秤量前後SOP一覧画面の権限に紐づくボタン定義
export const BUTTON_ID = {
  SEARCH: 'btnSearch', // 検索
  DETAIL: 'btnFlowDetail', // 作業詳細
} as const;

// ConditionSearchとRequestDataを紐づけるモデル定義
export const searchDataModel = {
  wgtInstGrpNo: { wgtInstGrpNo: 'inputBox' },
  sopFlowNmJp: { sopFlowName: 'inputBox' },
  stDtsFrom: { sopFlowStartDate: 'startDate' },
  stDtsTo: { sopFlowStartDate: 'endDate' },
  wgtRoomNo: { wgtRoomNo: 'selectData' },
  recConfirmFlg: { recConfirmFlgChild: 'checkBox' },
} as const satisfies SearchDataModel;

/**
 * 条件検索設定
 * @type {ConditionData[]}
 */
export const conditionData: ConditionData[] = [
  {
    label: ['Wgt.Chr.txtWeightInstructionsNo'], // 秤量指示書番号 テキストボックス
    id: 'wgtInstGrpNoTitle',
    children: [
      {
        type: 'input',
        id: 'wgtInstGrpNo',
        rules: [
          rules.length(13),
          rules.upperCaseSingleByteAlphanumeric({ isSearch: true }),
        ],
      },
    ],
  },
  {
    label: ['Wgt.Chr.txtSopFlowName'], // SOPフロー名 テキストボックス
    id: 'sopFlowNameTitle',
    children: [
      {
        type: 'input',
        id: 'sopFlowName',
        rules: [
          rules.length(32),
          rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
        ],
      },
    ],
  },
  {
    label: ['Wgt.Chr.txtSOPFlowStartDateOnly'], // SOPフロー開始日 カレンダー(年月日)
    id: 'sopFlowStartDateTitle',
    children: [
      {
        type: 'date',
        label: ['Cm.Chr.txtFrom', 'Cm.Chr.txtTo'],
        defaultValue: ['-30', '0'], // NOTE:初期値はFromが当日-30日。Toが今日。相対値で設定する。
        id: 'sopFlowStartDate',
        rules: [rules.required('date'), rules.fromToDate()],
      },
    ],
  },
  {
    label: ['Wgt.Chr.txtWeightRoom'], // 秤量室
    id: 'roomTitle',
    children: [
      {
        type: 'select',
        id: 'wgtRoomNo',
        selectOption: [],
      },
    ],
  },
  {
    label: ['Wgt.Chr.txtStatus'], // 確認状態
    id: 'recConfirmFlgTitle',
    children: [
      {
        type: 'checkBox',
        label: [
          'Wgt.Chr.txtConfirm', // 確認済
          'Wgt.Chr.txtNotConfirm', // 未確認
        ],
        value: ['1', '0'],
        defaultValue: ['0'],
        id: 'recConfirmFlgChild',
      },
    ],
  },
];
