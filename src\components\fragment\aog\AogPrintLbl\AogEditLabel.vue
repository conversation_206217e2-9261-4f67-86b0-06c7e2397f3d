<template>
  <!-- ラベル修正ダイアログ -->
  <DialogWindow
    :title="$t('Aog.Chr.txtAogEditLabel')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="onClickedResolve"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- パレット情報 -->
    <CustomForm
      :formModel="dialogInformationFormRef.formModel"
      :formItems="dialogInformationFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          dialogInformationFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>

  <!-- ラベル発行の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxAogEditLabel"
    :dialogProps="messageBoxAogEditLabelProps"
    :cancelCallback="() => closeDialog('messageBoxAogEditLabel')"
    :submitCallback="aogEditLabelCheck"
  />
</template>
<script setup lang="ts">
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { useGetComboBoxDataStandard } from '@/hooks/useApi';
import { DialogProps } from '@/types/MessageBoxTypes';
import { AogPrintLblListData } from '@/types/HookUseApi/AogTypes';
import {
  getDialogInformationFormItems,
  dialogInformationFormModel,
} from './AogEditLabel';

/**
 * 多言語
 */
const { t } = useI18n();

let palletEditData: AogPrintLblListData = {
  baleNo: null,
  baleModExpl: '',
  baleCnt: null,
  baleUnit: '',
  modFlg: null,
  unitNm: '',
};
// ダイアログの表示切替用定義
type DialogRefKey =
  | 'singleButton'
  | 'fragmentDialogVisible'
  | 'messageBoxAogEditLabel';
const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  fragmentDialogVisible: false,
  messageBoxAogEditLabel: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const dialogInformationFormRef = ref<CustomFormType>({
  formItems: getDialogInformationFormItems(),
  formModel: dialogInformationFormModel,
});

// ラベル発行の確認メッセージボックス
const messageBoxAogEditLabelProps: DialogProps = {
  title: t('Aog.Msg.titleAogEditLabel'), // レスポンスで上書きする
  content: t('Aog.Msg.contentAogEditLabelMessage'), // レスポンスで上書きする
  type: 'question',
};

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  selectedRowData: AogPrintLblListData | null;
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 実行 押下時処理
const onClickedResolve = async () => {
  // バリデート確認
  const validate =
    dialogInformationFormRef.value.customForm !== undefined &&
    (await dialogInformationFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (validate) {
    openDialog('messageBoxAogEditLabel');
  }
  return false;
};

const aogEditLabelCheck = async () => {
  if (!props.selectedRowData) return;
  palletEditData = {
    baleNo: props.selectedRowData.baleNo,
    baleCnt: Number(
      dialogInformationFormRef.value.formItems.baleCnt.formModelValue,
    ),
    baleUnit:
      dialogInformationFormRef.value.formItems.baleUnit.formModelValue.toString(),
    baleModExpl:
      dialogInformationFormRef.value.formItems.baleModExpl.formModelValue.toString(),
    modFlg: 1,
    unitNm: props.selectedRowData.unitNm,
  };
  // 親に実行結果を通知
  closeDialog('messageBoxAogEditLabel');
  closeDialog('fragmentDialogVisible');
  emit('submit', palletEditData);
};

/**
 * 処方選択ダイアログの初期設定
 */
const aogEditLabelInit = async () => {
  // FormItems初期化
  dialogInformationFormRef.value.formItems = getDialogInformationFormItems();
  if (!props.selectedRowData) return;
  updateDialogChangeFlagRef(false);
  // カスタムフォームに初期表示設定
  dialogInformationFormRef.value.formItems.unitNm.formModelValue =
    props.selectedRowData.unitNm;
  dialogInformationFormRef.value.formItems.baleUnit.formModelValue =
    props.selectedRowData.baleUnit.toString();
  dialogInformationFormRef.value.formItems.baleCnt.formModelValue =
    props.selectedRowData.baleCnt !== null
      ? props.selectedRowData.baleCnt.toString()
      : '';
  dialogInformationFormRef.value.formItems.baleModExpl.formModelValue =
    props.selectedRowData.baleModExpl;

  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'rsnCdAogMod',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'AOG_LBL_MOD' },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setCustomFormComboBoxOptionList(
      dialogInformationFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // 初期設定呼び出し
    await aogEditLabelInit();
  },
);
</script>
<style lang="scss" scoped>
$namespace: 'prescription-select-dialog';

.#{$namespace} {
  background-color: $white750;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
</style>
