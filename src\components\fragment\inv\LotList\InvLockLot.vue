<template>
  <!-- ロットロックダイアログ -->
  <DialogWindow
    :title="$t('Inv.Chr.txtLockLotIndividual')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkLockLotForm()"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="inventoryLockLotFormRef.formModel"
      :formItems="inventoryLockLotFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          inventoryLockLotFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <!-- 個装ロックの確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.inventoryLockLotConfirm"
    :dialogProps="messageBoxInventoryLockLotConfirmProps"
    :cancelCallback="() => closeDialog('inventoryLockLotConfirm')"
    :submitCallback="apiHandler"
  />
  <!-- 個装ロックのチェックメッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.inventoryLockLotInfo"
    :dialogProps="messageBoxInventoryLockLotPropsRef"
    :submitCallback="closeAllDialog"
  />
  <!-- 保管場所チェック警告メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.checkZoneConfirm"
    :dialogProps="messageCheckZoneConfirmProps"
    :cancelCallback="() => closeDialog('checkZoneConfirm')"
    :submitCallback="
      () => {
        closeDialog('checkZoneConfirm');
        invLockLotInit();
      }
    "
  />
</template>
<script setup lang="ts">
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import onValidateHandler from '@/utils/validateHandler';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  InventoryListData,
  InventoryLockLotData,
  ModifyInventoryLotLockReq,
} from '@/types/HookUseApi/InvTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import {
  useGetComboBoxDataStandard,
  useModifyInventoryLotLock,
  useGetInventoryLotLock,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import {
  CommonRequestType,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import {
  getInventoryLockLotFormItems,
  inventoryLockLotFormModel,
} from './invLockLot';

const inventoryLockLotFormRef = ref<CustomFormType>({
  formItems: getInventoryLockLotFormItems(),
  formModel: inventoryLockLotFormModel,
});

type Props = {
  selectedRowData: InventoryListData | null;
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
  isShowWarningConfirm: boolean;
};
const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

let inventoryLockLotData: InventoryLockLotData = {
  matNo: '',
  matNm: '',
  lotNo: '',
  lotUpdDts: '',
};

type DialogRefKey =
  | 'singleButton'
  | 'inventoryLockLotConfirm'
  | 'inventoryLockLotInfo'
  | 'fragmentDialogVisible'
  | 'checkZoneConfirm';
const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  inventoryLockLotConfirm: false,
  inventoryLockLotInfo: false,
  fragmentDialogVisible: false,
  checkZoneConfirm: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxInventoryLockLotConfirmProps: DialogProps = {
  title: t('Inv.Chr.txtConfirm'),
  content: t('Inv.Msg.lockLotIndividualInventoryCorrectionConfirm'),
  type: 'question',
};

const messageBoxInventoryLockLotPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageCheckZoneConfirmProps: DialogProps = {
  title: t('Inv.Msg.editRangeConfirm'),
  content: t('Inv.Msg.editInventoryList'),
  type: 'warning',
};
/**
 * ロットロック
 */
const checkLockLotForm = async () => {
  const validate =
    inventoryLockLotFormRef.value.customForm !== undefined &&
    (await inventoryLockLotFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (validate) {
    openDialog('inventoryLockLotConfirm');
  }
  return false;
};

/**
 * 確認メッセージ
 */
const apiHandler = async () => {
  closeDialog('inventoryLockLotConfirm');
  showLoading();
  const lockLotFormModel: ExtendCommonRequestType<ModifyInventoryLotLockReq> = {
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedRowData!.lotSid,
    lotLockExpl: inventoryLockLotFormRef.value.formModel.lotLockExpl.toString(),
    lotUpdDts: inventoryLockLotData.lotUpdDts,
    msgboxTitleTxt: messageBoxInventoryLockLotConfirmProps.title,
    msgboxMsgTxt: messageBoxInventoryLockLotConfirmProps.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
  };
  // ロットロックする
  const { responseRef, errorRef } =
    await useModifyInventoryLotLock(lockLotFormModel);
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    messageBoxInventoryLockLotPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxInventoryLockLotPropsRef.value.content =
      responseRef.value.data.rMsg;
    openDialog('inventoryLockLotInfo');
  }
  closeLoading();
  return true;
};

const closeAllDialog = () => {
  closeDialog('inventoryLockLotInfo');
  // W1A2910ロットロックダイアログを閉じる
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData);
};

/**
 * 初期設定
 */
const invLockLotInit = async () => {
  if (!props.selectedRowData) return;
  showLoading();
  updateDialogChangeFlagRef(false);
  inventoryLockLotFormRef.value.formItems = getInventoryLockLotFormItems();
  // ロット在庫情報取得(ロック用)
  const { responseRef, errorRef } = await useGetInventoryLotLock({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedRowData.lotSid,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    inventoryLockLotData = responseRef.value.data.rData;
    setFormModelValueFromApiResponse(
      inventoryLockLotFormRef,
      inventoryLockLotData,
    );
  }

  // 標準コンボボックスデータ取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtCatInvLotLockOn',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'LOCK_LOT_ON' },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setCustomFormComboBoxOptionList(
      inventoryLockLotFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(
  () => props.isClicked,
  () => {
    if (!props.selectedRowData) return;
    if (props.isShowWarningConfirm) {
      openDialog('checkZoneConfirm');
      return;
    }
    invLockLotInit();
  },
);
</script>
