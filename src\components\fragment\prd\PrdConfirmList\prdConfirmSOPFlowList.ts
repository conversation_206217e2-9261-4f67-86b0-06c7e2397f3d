import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';

const { t } = i18n.global;

// 指図詳細情報の縦並び項目定義
const getOrderDetailInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 製造指図番号
    odrNo: {
      label: { text: t('Prd.Chr.txtOrderNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 品目コード
    matNo: {
      label: { text: t('Prd.Chr.txtMaterialCode') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 処方
    rxNmJp: {
      label: { text: t('Prd.Chr.txtPrescriptionName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 5,
    },
    // 指図状態
    odrSts: {
      label: { text: t('Prd.Chr.txtOrderState') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 3,
    },
    // 品名
    dspNmJp: {
      label: { text: t('Prd.Chr.txtMaterialName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 16,
    },
    // 製造番号
    lotNo: {
      label: { text: t('Prd.Chr.txtLotNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 製造工程
    prcNmJp: {
      label: { text: t('Prd.Chr.txtOrderProcess') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 生産予定量
    prdPlanQty: {
      label: { text: t('Prd.Chr.txtPlanQuantity') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 製造開始日予定
    prdStYmd: {
      label: { text: t('Prd.Chr.txtOrderStartDatePlan') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 収率
    yieldVal: {
      label: { text: t('Prd.Chr.txtYieldValue') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 出来高
    rsltQty: {
      label: { text: t('Prd.Chr.txtProduction') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 製造開始日実績
    rsltDts: {
      label: { text: t('Prd.Chr.txtOrderStartDateResults') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
  });

export default getOrderDetailInfoShowItems;
