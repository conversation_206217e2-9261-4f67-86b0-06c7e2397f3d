<template>
  <!-- 製造記録承認ページ -->
  <!-- 見出し 製造記録承認 -->
  <el-card class="prd-approval-list">
    <el-row
      :class="[
        {
          'prd-approval-list_el-row-show-condition':
            tablePropsDataRef.showConditionSearch,
        },
        'prd-approval-list_el-row',
      ]"
    >
      <div class="prd-approval-list_search">
        <!-- 検索バー -->
        <ConditionSearch
          :conditionData="conditionData"
          @showCondition="showCondition"
          @getSearchData="searchClickHandler"
          @mounted="searchMountHandler"
        />
      </div>
      <div class="prd-approval-list_animated-col prd-approval-list_table">
        <BaseHeading
          level="2"
          fontSize="24px"
          :text="$t('Prd.Chr.txtOrderDocumentScan')"
        />
        <div class="prd-approval-list_search-condition Util_mt-16">
          <CustomForm
            :triggerRendering="customFormRenderingTriggerRef"
            :formModel="prdApprovalListSearchSearchFormRef.formModel"
            :formItems="prdApprovalListSearchSearchFormRef.formItems"
            width="100%"
            @visible="
              (v: CustomFormType['customForm']) => {
                prdApprovalListSearchSearchFormRef.customForm = v;
              }
            "
            @keyup.enter="keyupEnterHandler"
          />
        </div>
        <BaseHeading
          level="2"
          :text="pageTitle"
          fontSize="24px"
          class="table-title"
        />
        <!-- 共通のテーブル -->
        <TabulatorTable
          :propsData="tablePropsDataRef"
          :routerName="props.routerInfo.name"
          @selectRow="detailSelectedRow"
          @showCondition="showCondition"
        />
        <!-- 記録詳細ダイアログ -->
        <PrdApprovalInfoList
          v-if="props.privilegesBtnRequestData[BUTTON_ID.DETAIL]"
          :selectedRow="selectedRow"
          :isClicked="isClickedShowRecDetailDialogRef"
          :routerName="props.routerInfo.name"
          :privilegesBtnRequestData="
            props.privilegesBtnRequestData[BUTTON_ID.DETAIL]
          "
          @submit="prdApprovalListInit"
        />
      </div>
    </el-row>
  </el-card>
  <!-- 製造指図書スキャン入力バリデーションチェックエラーメッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.validationErr"
    :dialogProps="messageBoxValidationErrRef"
    :submitCallback="() => closeDialog('validationErr')"
  />
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { onMounted, ref } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import ConditionSearch from '@/components/model/ConditionSearch/ConditionSearch.vue';
import PrdApprovalInfoList from '@/components/fragment/prd/PrdApprovalList/PrdApprovalInfoList.vue';
import {
  useGetApprovalOrderInfoList,
  useGetComboBoxDataStandard,
  useGetUsrGrid,
} from '@/hooks/useApi';
import { DynamicModels } from '@/types/ConditionSearchTypes';
import { useSearch } from '@/hooks/useSearch';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import {
  GetApprovalOrderInfoListRequestData,
  GetApprovalOrderInfoListResponseData,
} from '@/types/HookUseApi/PrdTypes';
import { initCommonRequestFromPrivilegesType } from '@/hooks/useApi/util';
import { RouterInfoType } from '@/types/RouterTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { DialogProps } from '@/types/MessageBoxTypes';
import { setComboBoxOptionList } from '@/utils/comboBoxOptionList';
import { closeLoading, showLoading } from '@/utils/dialog';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import {
  BUTTON_ID,
  searchDataModel,
  conditionData,
  getSearchFormItems,
  searchFormModel,
} from './prdApprovalList';

/**
 * 多言語
 */
const { t } = useI18n();
const props = defineProps<RouterInfoType>();

// ページの共通リクエストデータ
const commonActionRequestData = initCommonRequestFromPrivilegesType({
  menu2Scn: props.routerInfo.name.slice(0, -1),
  menu3Act: props.routerInfo.name.at(-1),
  signType: '0',
});

let searchRequestData: GetApprovalOrderInfoListRequestData = {
  qrCode: '', // QRコード
  odrNo: '', // 製造指図番号
  odrDocVer: '', // 製造指図承認回数
  matNo: '', // 品目CD
  lotNo: '', // 製造番号
  odrStYmdFrom: '', // 製造開始日(FROM)
  odrStYmdTo: '', // 製造開始日（TO）
  odrEdYmdFrom: '', // 製造終了日(FROM)
  odrEdYmdTo: '', // 製造終了日（TO）
  recConfirmDtsFrom: '', // 記録確認日(FROM)
  recConfirmDtsTo: '', // 記録確認日（TO）
};

const customFormRenderingTriggerRef = ref(false);

const prdApprovalListSearchSearchFormRef = ref<CustomFormType>({
  formItems: getSearchFormItems(),
  formModel: searchFormModel,
});

const initialState: InitialDialogState<DialogRefKey> = {
  validationErr: false,
  messageBoxApiErrorVisible: false,
};
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

type DialogRefKey = 'validationErr' | 'messageBoxApiErrorVisible';

const messageBoxValidationErrRef = ref<DialogProps>({
  title: t('Cm.Chr.txtValidationError'),
  content: t('Cm.Msg.dialogValidationError'),
  type: 'error',
  isSingleBtn: true,
});

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// '記録詳細' クリック
const isClickedShowRecDetailDialogRef = ref<boolean>(false);

// 製造指図一覧テーブルの見出し
const pageTitle = t('Prd.Chr.txtOrderList');

// 選択行データ
let selectedRow: GetApprovalOrderInfoListResponseData | null = null;

const detailSelectedRow = (v: GetApprovalOrderInfoListResponseData | null) => {
  selectedRow = v;
};

/**
 * 記録詳細ボタン押下時のダイアログ表示
 */
const tablePropsDataRef = ref<TabulatorTableIF>({
  pageName: 'PrdApprovalList',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  title: pageTitle, // CSV出力名
  dataID: 'odrNo',
  showRadio: true,
  tableBtns: [],
  onSelectBtns: [
    {
      text: 'Prd.Chr.btnRecordDetail',
      tabulatorActionId: BUTTON_ID.DETAIL,
      type: 'primary',
      clickHandler: () => {
        // 作業詳細ダイアログを表示開始
        // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
        isClickedShowRecDetailDialogRef.value =
          !isClickedShowRecDetailDialogRef.value;
      },
    },
  ],
  column: [
    // 異状レベル
    {
      title: 'Prd.Chr.txtDeviationLevel',
      field: 'devCorrLv',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.DEV_LV,
    },
    // 製造指図番号
    {
      title: 'Prd.Chr.txtOrderNo',
      field: 'odrNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    // 品目コード
    {
      title: 'Prd.Chr.txtMaterialCode',
      field: 'matNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    // 品名
    {
      title: 'Prd.Chr.txtMaterialName',
      field: 'dspNmJp',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    // 処方
    {
      title: 'Prd.Chr.txtPrescriptionName',
      field: 'rxNmJp',
      width: COLUMN_WIDTHS.RX_NM,
    },
    // 製造番号
    {
      title: 'Prd.Chr.txtLotNo',
      field: 'lotNo',
      width: COLUMN_WIDTHS.LOT_NO,
    },
    // 生産量
    {
      title: 'Prd.Chr.txtProductionQuantity',
      field: 'rsltQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 単位
    {
      title: 'Prd.Chr.txtUnit',
      field: 'unitNmJp',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    // 製造開始予定日
    {
      title: 'Prd.Chr.txtOrderStartDate',
      field: 'prdStYmd',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDD,
    },
    // 製造終了予定日
    {
      title: 'Prd.Chr.txtOrderEndDate',
      field: 'prdEdYmd',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDD,
    },
    // 使用期限
    {
      title: 'Prd.Chr.txtExpiryDate',
      field: 'expiryDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDD,
    },
    // 指図状態
    {
      title: 'Prd.Chr.txtOrderState',
      field: 'odrSts',
      width: COLUMN_WIDTHS.PRD.ODR_STS,
    },
    // 指図コメント
    {
      title: 'Prd.Chr.txtHandOverComment',
      field: 'handOverTxt',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 異状コメント
    {
      title: 'Prd.Chr.txtDeviationComment',
      field: 'devExpl',
      width: COLUMN_WIDTHS.PRD.DEV_EXPL_EXIST,
    },
    // 作業コメント
    {
      title: 'Prd.Chr.txtWorkComment',
      field: 'msgExpl',
      width: COLUMN_WIDTHS.PRD.MSG_EXPL_EXIST,
    },
    // 修正コメント
    {
      title: 'Prd.Chr.txtModifyComment',
      field: 'modExpl',
      width: COLUMN_WIDTHS.PRD.MOD_EXPL_EXIST,
    },
    // 記録確認コメント
    {
      title: 'Prd.Chr.txtRecordConfirmComment',
      field: 'recConfirmExpl',
      width: COLUMN_WIDTHS.PRD.REC_CONFIRM_EXPL_EXIST,
    },
    // 記録確認日
    {
      title: 'Prd.Chr.txtRecordConfirmDate',
      field: 'recConfirmDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDD,
    },
    // 背景色指定隠しカラムとして運用
    {
      title: '',
      field: 'backgroundColor',
      hidden: true,
    },
  ],
  rowColor: {
    useColor: true,
    colorColumn: 'backgroundColor',
  },
  tableData: [],
  showConditionSearch: true, // ConditionSearchの表示/非表示
  usrGridInfo: {
    colSort: [],
    colHide: [],
  },
});

const {
  conditionDataRef,
  tableSearchDataRef,
  getSearchData,
  updateTableSearchDataProp,
  getApiRequestData,
} = useSearch();
// useSearch直後に初期値渡す。
conditionDataRef.value = conditionData;

/**
 * 指図を検索
 */
const requestApiGetPrdApprovalList = async (requestData: CommonRequestType) => {
  showLoading();
  const { responseRef, errorRef } = await useGetApprovalOrderInfoList({
    ...requestData,
    ...searchRequestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }

  if (responseRef.value) {
    const resData = responseRef.value.data;

    tablePropsDataRef.value.searchData = tableSearchDataRef.value;
    tablePropsDataRef.value.tableData = resData.rData.scheduleList;
  }

  closeLoading();
};

/**
 * 検索 起動直後の処理
 */
const searchMountHandler = (val: DynamicModels) => {
  const searchData = getSearchData(val, searchDataModel);
  updateTableSearchDataProp(searchData);
  searchRequestData =
    getApiRequestData<GetApprovalOrderInfoListRequestData>(searchData);
};

/**
 * 条件に応じて指図を検索
 * @param {DynamicModels} val - 条件検索パラメータ
 */
const searchClickHandler = async (val: DynamicModels) => {
  searchMountHandler(val);

  await requestApiGetPrdApprovalList({
    ...commonActionRequestData,
    btnId: BUTTON_ID.SEARCH,
  });
};

/**
 * QRスキャンのよる指図の検索
 */
const requestApiGetQrPrdApprovalList = async (
  requestData: CommonRequestType,
) => {
  searchRequestData.qrCode =
    prdApprovalListSearchSearchFormRef.value.formItems.odrNo.formModelValue.toString();
  showLoading();
  const { responseRef, errorRef } = await useGetApprovalOrderInfoList({
    ...requestData,
    ...searchRequestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }

  if (responseRef.value) {
    prdApprovalListSearchSearchFormRef.value.formItems.qrCode.formModelValue = `${t('Prd.Chr.txtQrCodeReadResult')}${prdApprovalListSearchSearchFormRef.value.formItems.odrNo.formModelValue}`;
    prdApprovalListSearchSearchFormRef.value.formItems.odrNo.formModelValue =
      '';
    const resData = responseRef.value.data;

    tablePropsDataRef.value.searchData = tableSearchDataRef.value;
    tablePropsDataRef.value.tableData = resData.rData.scheduleList;
    customFormRenderingTriggerRef.value = !customFormRenderingTriggerRef.value;
  }
  closeLoading();
};

/**
 * enterを押した指図一覧検索
 */
const keyupEnterHandler = async () => {
  if (prdApprovalListSearchSearchFormRef.value.customForm === undefined) return;
  await prdApprovalListSearchSearchFormRef.value.customForm.validate(
    async (isValid) => {
      if (
        !isValid ||
        prdApprovalListSearchSearchFormRef.value.formItems.odrNo.formModelValue
          .length === 0
      ) {
        return;
      }
      await requestApiGetQrPrdApprovalList({
        ...commonActionRequestData,
        btnId: '',
      });
    },
  );
};

/**
 * 製造指図書QRコードの入力欄をアクティブの状態にする
 */
const focusOnQrCodeInput = () => {
  prdApprovalListSearchSearchFormRef.value.customForm?.$el
    .querySelector('input')
    .focus();
};

/**
 * 指図一覧画面の初期設定
 */
const prdApprovalListInit = async () => {
  showLoading();

  // 標準コンボボックス取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.pageCommonRequest,
    condList: [
      {
        // 品目
        cmbId: 'matNo',
        condKey: 'm_mat',
        where: { mat_sdef: 'C,D,E' }, // 【C:工程品、D:中間製品、E:製品】
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setComboBoxOptionList(conditionDataRef.value, comboBoxResData.rData.rList);
  }

  const { responseRef, errorRef } = await useGetApprovalOrderInfoList({
    ...props.pageCommonRequest, // 初回設定時はprops経由で共通リクエストパラメータを設定
    ...searchRequestData,
  });
  if (errorRef.value) {
    closeLoading();

    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }

  if (responseRef.value) {
    const resData = responseRef.value.data;
    tablePropsDataRef.value.searchData = tableSearchDataRef.value;
    tablePropsDataRef.value.tableData = resData.rData.scheduleList;
  }
  focusOnQrCodeInput();

  const usrGridResData = await useGetUsrGrid(props.pageCommonRequest);
  if (usrGridResData) {
    tablePropsDataRef.value.usrGridInfo!.colSort = usrGridResData.colSort;
    tablePropsDataRef.value.usrGridInfo!.colHide = usrGridResData.colHide;
  }

  closeLoading();
};

/**
 * 条件検索サイドバー折りたたみ
 */
const showCondition = () => {
  tablePropsDataRef.value.showConditionSearch =
    !tablePropsDataRef.value.showConditionSearch;
};

onMounted(prdApprovalListInit);
</script>
<style lang="scss" scoped>
$namespace: 'prd-approval-list';

.#{$namespace} {
  background-color: $white750;
  display: flex;
  flex-direction: column;
  flex-grow: 1;

  /** element plus */
  :deep(.el-card__body) {
    padding-top: 32px;
    padding-bottom: 0;
    padding-inline: 16px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  &_el-row {
    flex-grow: 1;
    flex-wrap: nowrap;

    &-show-condition {
      .#{$namespace}_search {
        max-width: 275px;
        opacity: 1;
        transition: max-width 0.28s ease;
        width: 100%;
      }

      .#{$namespace}_table {
        max-width: calc(100% - 290px);
        padding-left: 15px;
      }
    }
  }
  &_search-condition {
    margin-bottom: 10px;
    display: flex;
    flex-wrap: nowrap;
    position: relative;
    box-sizing: border-box;
    :deep(.custom-form_suffix) {
      font-size: 14px;
      margin-left: 16px;
      position: unset;
    }
  }
  &_search {
    transition: max-width 0.28s ease-in-out;
    max-width: 0;
    opacity: 0;
    flex-shrink: 0;
  }
  &_table {
    max-width: 100%;
  }

  &_animated-col {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  .table-title {
    flex-shrink: 0;
    margin-top: 15px;
  }
}
</style>
