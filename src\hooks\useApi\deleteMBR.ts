import END_POINT from '@/constants/endPoint';
import { DeleteMBRReq, DeleteMBRRes } from '@/types/HookUseApi/MstTypes';
import {
  ExtendCommonRequestType,
  ExtendCommonRequestWithMainApiFlagType,
} from '@/types/HookUseApi/CommonTypes';
import { useApi } from './util';

const useDeleteMBR = (data: ExtendCommonRequestType<DeleteMBRReq>) =>
  useApi<ExtendCommonRequestWithMainApiFlagType<DeleteMBRReq>, DeleteMBRRes>(
    END_POINT.DEL_MST_MBR,
    'post',
    { ...data, mainApiFlg: 0 },
  );

export default useDeleteMBR;
