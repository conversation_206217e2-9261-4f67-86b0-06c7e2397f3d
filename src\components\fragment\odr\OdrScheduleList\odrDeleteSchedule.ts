import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';

const { t } = i18n.global;

// 計画削除ダイアログのアイテム定義
export const getOdrDeleteScheduleFormItems: () => CustomFormType['formItems'] =
  () => ({
    // NOTE: GetScheduleDeleteInitResDataの命名と合わせている
    unitNmJp: {
      formModelValue: '',
      formRole: 'suffix',
    },
    planPrcNo: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPlanProcedureNo') }, // 計画番号
      formRole: 'textBox',
      props: { disabled: true },
    },
    matNo: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtMaterialCode') }, // 品目コード
      formRole: 'textBox',
      props: { disabled: true },
    },
    dspNmJp: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtMaterialName') }, // 品名
      formRole: 'textBox',
      props: { disabled: true },
    },
    rxNmJp: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPrescriptionName') }, // 処方名
      formRole: 'textBox',
      props: { disabled: true },
    },
    mbrNo: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtMasterBatchRecordNo') }, // MBR番号
      formRole: 'textBox',
      props: { disabled: true },
    },
    validYmd: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPrescriptionDeadlineDate') }, // 処方有効期限
      formRole: 'textBox',
      props: { disabled: true },
    },
    stdPrdQty: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtStandardProductionQuantity') }, // 標準生産量
      suffix: { formModelProp: 'unitNmJp' },
      formRole: 'textBox',
      props: { disabled: true },
    },
    planQty: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtOrderQuantity') }, // 計画生産量
      suffix: { formModelProp: 'unitNmJp' },
      formRole: 'textBox',
      props: { disabled: true },
    },
    odrDts: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtOrderDate') }, // 製造開始予定日
      formRole: 'date',
      props: { modelValue: '', type: 'date', disabled: true },
    },
    skdAddExpl: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtPlanComment') }, // 計画コメント
      formRole: 'textBox',
      props: { disabled: true },
    },
  });

// 計画削除ダイアログのモデル定義
export const odrDeleteScheduleFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getOdrDeleteScheduleFormItems());
