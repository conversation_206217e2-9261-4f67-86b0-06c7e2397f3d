<template>
  <!-- 製造記録確認SOP作業詳細ダイアログ -->
  <DialogWindow
    :title="$t('Prd.Chr.txtConfirmSopFlowInfoList')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 見出し SOPフロー情報 -->
    <BaseHeading
      level="2"
      :text="$t('Prd.Chr.txtSopFlowInformation')"
      fontSize="24px"
    />
    <!-- 製造指図情報の見出し+テキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="orderDetailInfoShowRef.infoShowItems"
      :isLabelVertical="orderDetailInfoShowRef.isLabelVertical"
    />
    <!-- 見出し 作業実施記録 -->
    <BaseHeading
      class="Util_mt-16"
      level="2"
      :text="$t('Prd.Chr.txtWorkOperationRecord')"
      fontSize="24px"
    />
    <!-- 共通のテーブル -->
    <TabulatorTable
      :propsData="tablePropsDataRef"
      :routerName="props.routerName"
      @selectRows="updateSelectedRows"
      @displayedAllRows="displayedAllRowsRef"
      @clickBtnColumn="updateEmitSelectedRow"
    />
    <div class="confrim-sop-flow-info-list_bottom-element">
      <!-- 異状確認補足コメント(2行) -->
      <div class="confrim-sop-flow-info-list_cautionary-item">
        <div class="confrim-sop-flow-info-list_cautionary">
          <span>{{ $t('Prd.Chr.txtRecordGuide') }}</span>
        </div>
        <div class="confrim-sop-flow-info-list_cautionary-space"></div>
        <div class="confrim-sop-flow-info-list_cautionary">
          <span>{{ $t('Prd.Chr.contentDifferentConfirmComent') }}</span>
        </div>
      </div>
      <div>
        <CustomForm
          :formModel="dialogFormRef.formModel"
          :formItems="dialogFormRef.formItems"
          @visible="
            (v: CustomFormType['customForm']) => {
              dialogFormRef.customForm = v;
            }
          "
          @changeFormModel="updateCustomFormChangeFlag"
        />
      </div>
      <!-- SOP記録検印ボタン -->
      <div class="confrim-sop-flow-info-list_bottom-button">
        <ButtonEx
          type="primary"
          size="normal"
          :text="t('Prd.Chr.btnSOPRecordStamp')"
          :disabled="disabledPrdRecordSealButtonRef"
          @click="clickSopRecordStamp"
        />
      </div>
    </div>
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- SOP記録検印押下後の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxConfirmationMessage"
    :dialogProps="messageBoxConfirmationPropsRef"
    :cancelCallback="() => closeDialog('messageBoxConfirmationMessage')"
    :submitCallback="() => openDialog('messageBoxUpdateWarningMessage')"
  />
  <!-- SOP記録検印押下後の警告メッセージ（コメントあり） -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxUpdateWarningMessage"
    :dialogProps="messageBoxUpdateWarningPropsRef"
    :cancelCallback="() => closeDialog('messageBoxUpdateWarningMessage')"
    :submitCallback="() => modifyConfSopFlowInfo()"
  />
  <!-- SOP記録検印完了メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxUpdateResultMessage"
    :dialogProps="messageBoxUpdateResultPropsRef"
    :submitCallback="() => closeUpdateResultMessageDialog()"
  />
  <!-- 製造記録修正_SOP実施記録ダイアログ -->
  <PrdConfirmSOPModify
    :odrNo="props.odrNo"
    :isClicked="isClickedEditSOPRecordDialogRef"
    :selectedRowData="emitSelectedRow"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    @submit="handleSOPFlowInfoListSubmit"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import onValidateHandler from '@/utils/validateHandler';
import CONST from '@/constants/utils';
import { toNumberOrNull } from '@/utils/index';
import { rules } from '@/utils/validator';
import {
  GetConfirmSOPFlowInfoListRequestListData,
  GetConfirmSOPFlowListData,
  GetConfirmSOPFlowInfoListData,
  GetConfirmInfoListData,
  ModifyConfSopFlowInfoRequestData,
} from '@/types/HookUseApi/PrdTypes';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import {
  TabulatorTableIF,
  CustomOptionsData,
} from '@/components/model/common/TabulatorTable/TabulatorTable';
import {
  useGetSopFlowDetailListInit,
  useGetComboBoxDataStandard,
  useModifyConfirmSopFlowInfo,
} from '@/hooks/useApi';
import { DialogProps } from '@/types/MessageBoxTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import InfoShow from '@/components/parts/InfoShow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import createMessageBoxForm from '@/utils/commentMessageBox';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import BaseHeading from '@/components/base/BaseHeading.vue';
import ButtonEx from '@/components/parts/ButtonEx.vue';
import PrdConfirmSOPModify from '@/components/fragment/prd/PrdConfirmList/PrdConfirmSOPModify.vue';
import {
  CommonRequestType,
  ComboBoxDataStandardReturnData,
} from '@/types/HookUseApi/CommonTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import {
  getOrderDetailInfoShowItems,
  getDialogFormItems,
  dialogFormModel,
} from './prdConfirmSOPFlowInfoList';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxUpdateWarningMessage'
  | 'messageBoxConfirmationMessage'
  | 'messageBoxUpdateResultMessage'
  | 'messageBoxApiErrorVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxUpdateWarningMessage: false,
  messageBoxConfirmationMessage: false,
  messageBoxUpdateResultMessage: false,
  messageBoxApiErrorVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

type Props = {
  odrNo?: string; // 遷移元から引き継ぐ製造指図番号
  infoData: GetConfirmInfoListData | null; // 遷移元から引き継ぐ画面表示項目
  prcNo?: string; // 遷移元から引き継ぐ製造工程番号
  prcSeq?: number; // 遷移元から引き継ぐ製造工程順
  selectedSopRecData: GetConfirmSOPFlowListData | null;
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  routerName: string; // TabulatorTable権限
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

const emit = defineEmits(['submit']);

// 活性チェックボックス数
let enableCheckboxCount = 0;
// スクロールバー最下部検知
const isDisplayedAllRowsRef = ref<boolean>(false);

// 指図記録 検印ボタン活性・非活性切り替え
const disabledPrdRecordSealButtonRef = ref<boolean>(true);

const orderDetailInfoShowRef = ref<InfoShowType>({
  infoShowItems: getOrderDetailInfoShowItems(),
  isLabelVertical: true,
});

// 記録検印ボタン非活性フラグ
let recConfirmButtonFlag = false;
// 更新日時
let useUpdDts = '';

// カスタムフォーム変更状態 ダイアログ終了チェック用
let customFormChangeFlag: boolean = false;
// 選択行変更状態 ダイアログ終了チェック用
let selectedRowChangeFlag: boolean = false;
// 管理している状態フラグ全てを見てupdateDialogChangeFlagRefを更新する
const checkDialogChangeFlag = () => {
  updateDialogChangeFlagRef(customFormChangeFlag || selectedRowChangeFlag);
};
// カスタムフォーム状態更新
const updateCustomFormChangeFlag = (flag: boolean = false) => {
  customFormChangeFlag = flag;
  checkDialogChangeFlag();
};
// 選択行変更状態更新
const updateSelectedRowChangeFlag = (flag: boolean = false) => {
  selectedRowChangeFlag = flag;
  checkDialogChangeFlag();
};

// TabulatorTableの行情報定義
type SOPDetailTableRowData = GetConfirmSOPFlowInfoListData & {
  uniqueKey: string; // テーブル用主キー
};
// 選択行情報(複数)の格納
let selectedRows: SOPDetailTableRowData[] = [];

/**
 * 指図記録 検印ボタン活性・非活性切り替え
 */
const switchDisabledPrdRecordSealButton = () => {
  if (
    isDisplayedAllRowsRef.value &&
    // NOTE:20250616時点、全チェックボタンの機能を利用していないが、利用した場合は無効化されたチェックボックスもカウントされる挙動となる。
    //      活性数と選択数の一致で見ていると検印ボタンが活性化出来なくなる可能性がある。
    //      エンバグを防ぐため修正しないが、全チェックボタンを利用する場合は確実に修正する必要がある。
    enableCheckboxCount === selectedRows.length &&
    recConfirmButtonFlag === false
  ) {
    disabledPrdRecordSealButtonRef.value = false;
  } else {
    disabledPrdRecordSealButtonRef.value = true;
  }
};

// チェックボックス選択時処理
const updateSelectedRows = (v: SOPDetailTableRowData[]) => {
  selectedRows = v;
  // 活性化非活性化の切り替え処理
  switchDisabledPrdRecordSealButton();

  if (v.length > 0) {
    updateSelectedRowChangeFlag(true);
  } else {
    updateSelectedRowChangeFlag(false);
  }
};

// スクロールバー最下部検知時処理
const displayedAllRowsRef = () => {
  isDisplayedAllRowsRef.value = true;
  switchDisabledPrdRecordSealButton();
};

const dialogFormRef = ref<CustomFormType>({
  formItems: getDialogFormItems(),
  formModel: dialogFormModel,
});

let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;

const messageBoxForm = createMessageBoxForm('message', 'cmtWarning');
// SOP記録検印押下後の警告ダイアログ（コメントあり）
const messageBoxUpdateWarningPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleSopFlowRecordConfirm'),
  content: t('Prd.Msg.contentSopFlowRecordConfirm'),
  isPrompt: true,
  isSkipValidation: true, // 任意入力とする
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});
// SOP記録検印押下後の確認メッセージ
const messageBoxConfirmationPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleSopFlowRecordConfirm'),
  content: t('Prd.Msg.contentProductSopRecordStamp'),
  type: 'info',
});
// SOP記録検印完了メッセージ
const messageBoxUpdateResultPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});
// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 入力付き確認メッセージボックスの入力を初期化
const clearInputMessageBoxForm = () => {
  if ('isPrompt' in messageBoxUpdateWarningPropsRef.value) {
    messageBoxUpdateWarningPropsRef.value.formItems.message.formModelValue = '';
  }
};

// '記録修正' クリック
const isClickedEditSOPRecordDialogRef = ref<boolean>(false);
/**
 * 記録修正ボタン押下時
 */
const clickBtnColumn = () => {
  isClickedEditSOPRecordDialogRef.value =
    !isClickedEditSOPRecordDialogRef.value;
};
// 押下されたカラムボタンの行情報の格納
let emitSelectedRow: GetConfirmSOPFlowInfoListData | null = null;
/**
 * 記録修正ボタン押下時処理
 */
const updateEmitSelectedRow = (v: GetConfirmSOPFlowInfoListData | null) => {
  emitSelectedRow = v;
  // 活性化非活性化の切り替え処理
  clickBtnColumn();
};

const tablePropsDataRef = ref<TabulatorTableIF>({
  pageName: 'PrdConfirmSOPFlowInfoList',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'uniqueKey',
  tableBtns: [],
  showCheckbox: {
    show: true,
    condition: 'checkboxFlg',
    conditionValue: 0,
    allAllowed: false,
  },
  btnColumn: {
    frozen: true, // 凍結列
    columnWidth: 70, // ボタン列幅
    condition: 'recModBtnFlg',
    conditionValue: 0,
    btnProps: {
      text: t('Prd.Chr.txtRecordModify'),
      type: 'secondary',
      size: 'tabulator',
    },
  },
  selectRowsData: [], // 選択行情報

  column: [
    // SOPフローNo 隠しカラム
    { title: '', field: 'sopFlowNo', hidden: true },
    // SOPフロー実行ログ番号 隠しカラム
    { title: '', field: 'sopFlowLnum', hidden: true },
    // SOPノード実行ログ番号 隠しカラム
    { title: '', field: 'sopNodeLnum', hidden: true },
    // 実行数
    {
      title: 'Prd.Chr.txtSopNodeTimes',
      field: 'sopNodeTimes',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.SOP_NODE_TIMES,
    },
    // 異状レベル
    {
      title: 'Prd.Chr.txtDeviationLevel',
      field: 'devCorrLv',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.DEV_LV,
    },
    // 作業指示内容
    {
      title: 'Prd.Chr.txtWorkInstructionDetail',
      field: 'cmtMain',
      width: COLUMN_WIDTHS.PRD.CMT_MAIN,
    },
    // 指示値
    {
      title: 'Prd.Chr.txtInstructionValue',
      field: 'instVal',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.INST_VAL,
    },
    // 修正回数
    {
      title: 'Prd.Chr.txtModifyCount',
      field: 'recModTimes',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.PRD.CMT_TIMES,
    },
    // 単位
    {
      title: 'Prd.Chr.txtUnit',
      field: 'instUnitText',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    // 記録値1
    {
      title: 'Prd.Chr.txtRecordValue1',
      field: 'recVal1',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 記録値2
    {
      title: 'Prd.Chr.txtRecordValue2',
      field: 'recVal2',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 記録値3
    {
      title: 'Prd.Chr.txtRecordValue3',
      field: 'recVal3',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 記録値4
    {
      title: 'Prd.Chr.txtRecordValue4',
      field: 'recVal4',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 記録値5
    {
      title: 'Prd.Chr.txtRecordValue5',
      field: 'recVal5',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 異状下限
    {
      title: 'Prd.Chr.txtDeviationLowerLimit',
      field: 'thValLlmt',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.PRD.VAL_LMT,
    },
    // 異状上限
    {
      title: 'Prd.Chr.txtDeviationUpperLimit',
      field: 'thValUlmt',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.PRD.VAL_LMT,
    },
    // 参考値1
    {
      title: 'Prd.Chr.txtReferenceValue1',
      field: 'refVal1',
      width: COLUMN_WIDTHS.PRD.REF_VAL,
    },
    // 参考値2
    {
      title: 'Prd.Chr.txtReferenceValue2',
      field: 'refVal2',
      width: COLUMN_WIDTHS.PRD.REF_VAL,
    },
    // 参考値3
    {
      title: 'Prd.Chr.txtReferenceValue3',
      field: 'refVal3',
      width: COLUMN_WIDTHS.PRD.REF_VAL,
    },
    // 参考値4
    {
      title: 'Prd.Chr.txtReferenceValue4',
      field: 'refVal4',
      width: COLUMN_WIDTHS.PRD.REF_VAL,
    },
    // 参考値5
    {
      title: 'Prd.Chr.txtReferenceValue5',
      field: 'refVal5',
      width: COLUMN_WIDTHS.PRD.REF_VAL,
    },
    // SOPヘルプ表示日時
    {
      title: 'Prd.Chr.txtSopHelpDts',
      field: 'helpDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // 記録日時
    {
      title: 'Prd.Chr.txtRecordDate',
      field: 'recDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // 記録者
    {
      title: 'Prd.Chr.txtRecordUser',
      field: 'recUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // D記録
    {
      title: 'Prd.Chr.txtDRecord',
      field: 'dcheckVal',
      width: COLUMN_WIDTHS.PRD.D_CHK_VAL,
    },
    // D記録日時
    {
      title: 'Prd.Chr.txtDRecordDate',
      field: 'dcheckDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // D記録者
    {
      title: 'Prd.Chr.txtDRecordUser',
      field: 'dcheckUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 複数作業者
    {
      title: 'Prd.Chr.txtMultipleWorkUser',
      field: 'workUsr',
      width: COLUMN_WIDTHS.PRD.WORK_USR_EXIST,
    },
    // 異状コメント
    {
      title: 'Prd.Chr.txtDeviationComment',
      field: 'devExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 作業コメント
    {
      title: 'Prd.Chr.txtWorkComment',
      field: 'msgExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 修正コメント
    {
      title: 'Prd.Chr.txtModifyComment',
      field: 'modExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    {
      title: '',
      field: 'operations',
      formatter: 'btn',
    },
    // 背景色指定隠しカラム
    {
      title: '',
      field: 'backgroundColor',
      hidden: true,
    },
    // チェックボックス活性・非活性フラグの隠しカラム
    {
      title: '',
      field: 'checkboxFlg',
      hidden: true,
    },
    // 記録修正ボタン活性・非活性フラグの隠しカラム
    {
      title: '',
      field: 'recModBtnFlg',
      hidden: true,
    },
  ],
  rowColor: {
    useColor: true,
    colorColumn: 'backgroundColor',
  },
  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true,
  tableId: 'detail-table',
  hideCheckboxTitleFormatter: true,
  textWrapColumns: ['cmtMain'],
  rowHeight: 60,
});

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    clickHandler: () => {
      tablePropsDataRef.value.selectRowsData = [];
      return commonRejectHandler();
    },
  },
];

/**
 * 作業指示コメント結合
 * @param {CustomOptionsData} res - テーブル表示データ
 */
const joinCmtMain = (res: CustomOptionsData) => {
  const comments = [res.cmtMain1, res.cmtMain2, res.cmtMain3].filter(Boolean);
  res.cmtMain = comments.join('<br>');
};
/**
 * レスポンスデータチェック
 * @param {CustomOptionsData[]} tableData - テーブル表示データ
 */
const checkResData = (tableData: CustomOptionsData[]) => {
  // 活性コンボボックス数初期化
  enableCheckboxCount = 0;
  tableData.forEach((res) => {
    res.recModBtnFlg = toNumberOrNull(res.recModBtnFlg) ?? 1;
    res.checkboxFlg = toNumberOrNull(res.checkboxFlg) ?? 1;
    if (res.checkboxFlg === 0) {
      enableCheckboxCount += 1;
    }
    // 作業指示コメント結合
    joinCmtMain(res);
  });
};
/**
 * 画面状態の初期化
 */
const resetStatus = () => {
  // 活性チェックボックス数
  enableCheckboxCount = 0;
  // スクロールバー最下部検知
  isDisplayedAllRowsRef.value = false;
  // 指図記録 検印ボタン活性・非活性切り替え
  disabledPrdRecordSealButtonRef.value = true;
  // 選択行情報(複数)
  selectedRows = [];
  updateDialogChangeFlagRef(false);
  customFormChangeFlag = false;
  selectedRowChangeFlag = false;
};

/**
 * SOP作業詳細の初期設定
 */
const prdConfirmSOPFlowInfoListInit = async () => {
  // NOTE:未選択なら処理させない。エラーメッセージはTabulatorTable側で出してくれる。
  if (props.selectedSopRecData === null) return;
  resetStatus();
  // ローディング表示
  showLoading();

  // SOP作業詳細初期表示APIを呼び出し
  const apiRequestData = <GetConfirmSOPFlowInfoListRequestListData>{
    sopFlowNo: props.selectedSopRecData.sopFlowNo,
    sopFlowLnum: props.selectedSopRecData.sopFlowLnum,
  };
  const { responseRef, errorRef } = await useGetSopFlowDetailListInit({
    ...props.privilegesBtnRequestData,
    ...apiRequestData,
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }
  if (responseRef.value) {
    const resData = responseRef.value.data;

    if (resData.rData.devCorrLvFlg === false) {
      // 必須外す設定
      dialogFormRef.value.formItems.devExpl.rules = [
        rules.length(64, t('Cm.Chr.txtLength', [64])),
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      ];
    } else {
      // 必須付与設定
      dialogFormRef.value.formItems.devExpl.rules = [
        rules.required('textComboBox'), // 必須
        rules.length(64, t('Cm.Chr.txtLength', [64])),
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      ];
    }

    // 再検索時にバリデーション変更の可能性があるため状態初期化
    if (dialogFormRef.value.customForm) {
      dialogFormRef.value.customForm.clearValidate('devExpl');
    }

    const tableData: SOPDetailTableRowData[] = [];
    // 行情報にユニークキーを追加して格納
    resData.rData.sopFlowList.forEach((res) => {
      const tableRowData: SOPDetailTableRowData = {
        ...res,
        uniqueKey: `${res.sopFlowNo}-${res.sopFlowLnum}-${res.sopNodeLnum}`,
      };
      tableData.push(tableRowData);
    });
    // ダイアログ表示初期値として、レスポンス情報を格納
    tablePropsDataRef.value.tableData = tableData;
    checkResData(tablePropsDataRef.value.tableData);

    // SOPフロー情報レイアウト用初期値設定(レスポンスから取得)
    Object.entries(responseRef.value.data.rData).forEach(([key, value]) => {
      if (key in orderDetailInfoShowRef.value.infoShowItems) {
        orderDetailInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });

    recConfirmButtonFlag = resData.rData.recConfirmBtnFlg;
    useUpdDts = resData.rData.updDts;
  }

  if (props.infoData) {
    // SOPフロー情報レイアウト用初期値設定(前画面InfoShowから取得)
    Object.entries(props.infoData).forEach(([key, value]) => {
      if (key in orderDetailInfoShowRef.value.infoShowItems) {
        orderDetailInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });
    if (props.odrNo) {
      orderDetailInfoShowRef.value.infoShowItems.odrNo.infoShowModelValue =
        props.odrNo;
    }
    if (props.selectedSopRecData) {
      orderDetailInfoShowRef.value.infoShowItems.batchNo.infoShowModelValue =
        props.selectedSopRecData.batchNo?.toString() ?? '';
      orderDetailInfoShowRef.value.infoShowItems.sopFlowName.infoShowModelValue =
        props.selectedSopRecData.sopFlowNmJp;
      // NOTE:確認状態は二つ前の画面でも同名変数がある。一つ前画面の変数で上書きする。
      orderDetailInfoShowRef.value.infoShowItems.recConfirmFlg.infoShowModelValue =
        props.selectedSopRecData.recConfirmFlgDsp;
    }
  }

  // 標準コンボボックスデータ取得
  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'devExpl2', // 異状確認コメント
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_SOP_DEV_CONF' },
      },
      {
        cmbId: 'cmtWarning',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_SOP_FLOW_CONF' },
      },
    ],
  });

  // 標準コンボボックスのデータが取得できていれば画面上に反映
  if (
    comboBoxDataStandardReturnData &&
    comboBoxDataStandardReturnData.rData.rList.length > 0
  ) {
    setCustomFormComboBoxOptionList(
      dialogFormRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }

  if (
    'isPrompt' in messageBoxUpdateWarningPropsRef.value &&
    comboBoxDataStandardReturnData
  ) {
    const resetData = createMessageBoxForm('message', 'cmtWarning');
    messageBoxUpdateWarningPropsRef.value.formItems = resetData.formItems;

    // NOTE:共通コンボボックス情報を元にコメント付き警告メッセージを設定
    setCustomFormComboBoxOptionList(
      messageBoxUpdateWarningPropsRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }
  closeLoading();
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

/**
 * 再検索用の初期化処理
 */
const handleSOPFlowInfoListSubmit = () => {
  tablePropsDataRef.value.selectRowsData = [];
  // 選択状態を復元
  if (selectedRows.length > 0) {
    tablePropsDataRef.value.selectRowsData = selectedRows.map(
      (row) => row.uniqueKey,
    );
  }
  prdConfirmSOPFlowInfoListInit();
};

/**
 * SOP記録 検印ボタン押下時処理
 */
const clickSopRecordStamp = async () => {
  // 下記チェックを行い、チェックOKなら処理継続する。
  // 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  // NOTE: 異状確認レベルフラグが1の場合、カスタムフォームのバリデーションを行う
  const validate =
    dialogFormRef.value.customForm !== undefined &&
    (await dialogFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return;
  }

  // NOTE:「確認→コメント」の順序で開く
  // 次のダイアログを表示
  clearInputMessageBoxForm();
  openDialog('messageBoxConfirmationMessage');
};

/**
 * SOP記録検印処理
 */
const modifyConfSopFlowInfo = async () => {
  closeDialog('messageBoxConfirmationMessage');
  closeDialog('messageBoxUpdateWarningMessage');

  if (
    !props.odrNo ||
    !props.prcNo ||
    !props.prcSeq ||
    props.selectedSopRecData === null
  ) {
    return;
  }
  if (!('isPrompt' in messageBoxUpdateWarningPropsRef.value)) return;

  const requestData: ModifyConfSopFlowInfoRequestData = {
    odrNo: props.odrNo,
    prcNo: props.prcNo,
    prcSeq: props.prcSeq,
    sopFlowNo: props.selectedSopRecData.sopFlowNo,
    sopFlowLnum: props.selectedSopRecData.sopFlowLnum,
    devExpl: dialogFormRef.value.formItems.devExpl.formModelValue.toString(),
    sopExpl:
      messageBoxUpdateWarningPropsRef.value.formItems.message.formModelValue.toString(),
    updDts: useUpdDts,
  };

  // NOTE:直前メッセージをキャッシュせず直書きしている。仕様変更等でメッセージ順序を入れ替えた際に意図しないものを送る可能性あり。要注意。
  const { responseRef, errorRef } = await useModifyConfirmSopFlowInfo({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxUpdateWarningPropsRef.value.title,
    msgboxMsgTxt: messageBoxUpdateWarningPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    msgboxInputCmt:
      messageBoxUpdateWarningPropsRef.value.formItems.message.formModelValue.toString(),
    ...requestData,
  });
  if (errorRef.value) {
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    // ・データベースを更新した後、以下のメッセージを表示する。
    // SOP記録検印完了
    messageBoxUpdateResultPropsRef.value.title = responseRef.value.data.rTitle;
    messageBoxUpdateResultPropsRef.value.content = responseRef.value.data.rMsg;
  }

  closeLoading();
  openDialog('messageBoxUpdateResultMessage');
};

/**
 * SOP記録検印完了メッセージクローズ処理
 */
const closeUpdateResultMessageDialog = () => {
  emit('submit');
  closeDialog('messageBoxUpdateResultMessage');
  closeDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // NOTE:ダイアログ起動時限定処理をここに記載。再検索用初期化処理が関数分かれていないため。
    // FormItems初期化
    dialogFormRef.value.formItems = getDialogFormItems();

    // 初期設定呼び出し
    prdConfirmSOPFlowInfoListInit();
  },
);
</script>
<style lang="scss" scoped>
$namespace: 'confrim-sop-flow-info-list';
.#{$namespace} {
  // 警告文
  &_cautionary {
    font-size: 14px;
    color: $red330;
  }
  // 警告文 行間調整用
  &_cautionary-space {
    padding-top: 2px;
  }
  // 画面下部要素 ボタン(記録検印)
  &_bottom-button {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
