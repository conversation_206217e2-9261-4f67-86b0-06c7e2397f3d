import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

const { t } = i18n.global;

export const ColorMapping: Record<string, string> = {
  RED: 'red',
  ORANGE: 'orange',
  YELLOW: 'yellow',
} as const;

export const getProcessDataInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 品目コード
    matNo: {
      label: { text: t('Sjg.Chr.txtMatNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 3,
    },
    // 品名
    matNm: {
      label: { text: t('Sjg.Chr.txtMatNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 9,
    },
    // 製造番号
    lotNo: {
      label: { text: t('Sjg.Chr.txtLotNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 3,
    },
    // ロットアウト品
    verifyReasonNm: {
      label: { text: t('Sjg.Chr.txtVerityReason') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 3,
    },
    // 使用期限
    expiryYmd: {
      label: { text: t('Sjg.Chr.txtExpiryYmd') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 3,
    },
    // 出来高日
    rsltYmd: {
      label: { text: t('Sjg.Chr.txtRsltYmd') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 3,
    },
  });

export const tablePropsDataVerifyResultList: TabulatorTableIF = {
  pageName: 'VerifyResultList',
  pageSize: 20,
  height: '474',
  pagination: false, // ページネーションの表示/非表示
  multiLevelGrouping: false, // Multi Level Grouping データグリッドのグルーピング (Grouping Data)
  dataID: 'uniqueKey',
  hideCheckboxTitleFormatter: true,
  showCheckbox: {
    show: true,
    condition: '',
    conditionValue: 0,
    allAllowed: false,
  },
  selectRowsData: [],
  showRadio: false,
  rowHeight: 35,
  btnColumn: {
    frozen: true, // 凍結列
    columnWidth: 130, // ボタン列幅
    condition: 'detailsBtn',
    conditionValue: 0,
    btnProps: {
      text: t('Cm.Chr.btnDetail'),
      size: 'tabulator',
      type: 'secondary',
    },
  },
  searchData: [],
  column: [
    {
      title: 'Sjg.Chr.txtOdrNo',
      field: 'odrNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    {
      title: 'Sjg.Chr.txtMatNo',
      field: 'matNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    { title: 'Sjg.Chr.txtMatNm', field: 'matNm', width: COLUMN_WIDTHS.MAT_NM },
    {
      title: 'Sjg.Chr.txtSopFlowNm',
      field: 'sopFlowNm',
      width: COLUMN_WIDTHS.SOP_FLOW_NM,
    },
    {
      title: 'Sjg.Chr.txtBatchNo',
      field: 'batchNo',
      width: COLUMN_WIDTHS.BAT_NO,
    },
    {
      title: 'Sjg.Chr.txtSopNodeTimes',
      field: 'sopNodeTimes',
      width: COLUMN_WIDTHS.SOP_NODE_TIMES,
    },
    {
      title: 'Sjg.Chr.txtDeviationLevel',
      field: 'devCorrLvNm',
      width: COLUMN_WIDTHS.DEV_LV,
    },
    {
      title: 'Sjg.Chr.txtCmtMain',
      field: 'cmtMain',
      width: COLUMN_WIDTHS.SJG.CMT_MAIN,
    },
    {
      title: 'Sjg.Chr.txtInstVal',
      field: 'instVal',
      width: COLUMN_WIDTHS.INST_VAL,
    },
    {
      title: 'Sjg.Chr.txtRecVal',
      field: 'recVal',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtEdDts',
      field: 'edDts',
      formatter: 'date',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    {
      title: 'Sjg.Chr.txtRecUsr',
      field: 'recUsrNm',
      width: COLUMN_WIDTHS.USR_NM,
    },
    {
      title: 'Sjg.Chr.txtDcheckVal',
      field: 'dcheckVal',
      width: COLUMN_WIDTHS.SJG.D_CHK_VAL,
    },
    {
      title: 'Sjg.Chr.txtDcheckEdDts',
      field: 'dcheckEdDts',
      formatter: 'date',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    {
      title: 'Sjg.Chr.txtDcheckUsr',
      field: 'dcheckUsrNm',
      width: COLUMN_WIDTHS.USR_NM,
    },
    {
      title: 'Sjg.Chr.txtMultipleUsrFlg',
      field: 'multipleUsrFlgNm',
      width: COLUMN_WIDTHS.USR_NM,
    },
    {
      title: 'Sjg.Chr.txtDevExplFlg',
      field: 'devExplFlgNm',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    {
      title: 'Sjg.Chr.txtLogExplFlg',
      field: 'logExplFlgNm',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    {
      title: 'Sjg.Chr.txtModExplFlg',
      field: 'modExplFlgNm',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    {
      title: 'Sjg.Chr.txtRecConfFlg',
      field: 'recConfFlgNm',
      width: COLUMN_WIDTHS.SJG.REC_CONF_FLG,
    },
    { title: 'Sjg.Chr.txtAttBinNo', field: 'attBinNo', hidden: true },
    {
      title: 'Sjg.Chr.txtAttBinNo',
      field: 'attBinFlgNm',
      width: COLUMN_WIDTHS.SJG.FLG_NM,
    },
    // 背景色指定隠しカラムとして運用
    {
      title: '',
      field: 'backgroundColor',
      hidden: true,
    },
    {
      title: '',
      field: 'detailsBtn',
      formatter: 'btn',
      width: 130,
      hozAlign: 'center',
    },
    { title: '', field: 'uniqueKey', hidden: true },
  ],
  rowColor: {
    useColor: true,
    colorColumn: 'backgroundColor',
  },
  tableData: [],
  showConditionSearch: false, // ConditionSearchの表示/非表示
  hideSearchHeader: true, // サーチヘッダー
  hideColumnEditBtn: true, // カラム編集ボタン
  hideCsvExportBtn: true, // CSV出力ボタン
  textWrapColumns: ['cmtMain'],
};
