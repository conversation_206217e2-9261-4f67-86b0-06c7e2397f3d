<template>
  <!-- 構成品情報確認 -->
  <DialogWindow
    :title="dislogTitle"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    :leftButtons="dialogLeftButtons"
    :rightButtons="dialogRightButtons"
    :width="CONST.OVERRIDE_DIALOG_WIDTH.WIDTH_1600"
  >
    <BaseHeading
      level="2"
      fontSize="24px"
      :text="$t('Sjg.Chr.txtInspectionMatInfo')"
      class="Util_mb-16"
    />
    <div class="Sjg-component-information-confirmation_content-inspectionInfo">
      <InfoShow
        :infoShowItems="inspectionInfoShowRef.infoShowItems"
        :isLabelVertical="inspectionInfoShowRef.isLabelVertical"
        :fontSizeLabel="inspectionInfoShowRef.fontSizeLabel"
        :fontSizeContent="inspectionInfoShowRef.fontSizeContent"
      />
    </div>
    <div class="Util_mt-48">
      <BaseHeading
        level="2"
        fontSize="24px"
        :text="$t('Sjg.Chr.txtOrderBomLotListInfo')"
      />
      <TabulatorTable
        :propsData="tablePropsDialogRef"
        @clickBtnColumn="updateEmitSelectedRow"
        @selectRows="updateSelectedRows"
      />
    </div>
  </DialogWindow>
  <SjgComponentInformationAttachments
    :isClicked="isClickedSjgComponentInformationAttachmentsRef"
    :selectedRow="selectedRow"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
  />
  <!-- 異常 -->
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <MessageBox
    v-if="dialogVisibleRef.sjgInfoValidCheckError"
    :dialogProps="messageBoxSjgInfoValidCheckErrorRef"
    :submitCallback="() => closeSjgInfoValidCheckError()"
  />
  <MessageBox
    v-if="dialogVisibleRef.attachExistenceCheckRef"
    :dialogProps="messageBoxAttachExistenceCheckRef"
    :submitCallback="() => closeDialog('attachExistenceCheckRef')"
  />
  <MessageBox
    v-if="dialogVisibleRef.modifyBomVerifyFinishRef"
    :dialogProps="messageBoxModifyBomVerifyFinishRef"
    :cancelCallback="() => closeDialog('modifyBomVerifyFinishRef')"
    :submitCallback="
      () => {
        closeDialog('modifyBomVerifyFinishRef');
        modifyBomVerifyFinish(
          messageBoxModifyBomVerifyFinishRef,
          CONST_FLAGS.WARNING_NECESSITY_FLAG.UNNECESSARY,
        );
      }
    "
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { ref, watch } from 'vue';
import {
  VerifyItemList,
  SjgGetModListData,
  SelectInspectionItemData,
  ModifyBomVerifyFinishReq,
  SjgGetModBomList,
} from '@/types/HookUseApi/SjgTypes';
import SCREENID from '@/constants/screenId';
import {
  CommonRequestType,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import { DialogProps } from '@/types/MessageBoxTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import {
  useSjgGetBomList,
  useModifyBomVerifyFinish,
  useCheckValidVerify,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import { InfoShowType } from '@/types/InfoShowTypes';
import { getInspectionInfoShowItems } from '@/components/page/sjg/sjgSelectInspectionItem';
import SjgComponentInformationAttachments from '@/components/fragment/sjg/SjgSelectInspectionItem/SjgComponentInformationAttachments.vue';
import CONST_FLAGS from '@/constants/flags';
import CONST from '@/constants/utils';
import { tablePropsData } from './sjgComponentInformationConfirmation';

type Props = {
  isClicked: boolean;
  selectedRowData: VerifyItemList | null;
  selectedInspectionItemData: SelectInspectionItemData | null;
  privilegesBtnRequestData: CommonRequestType;
  screenId: string;
  saveFlag: boolean;
};

type SjgGetModListDataWithUniqueKey = SjgGetModListData & {
  uniqueKey: string;
};

const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);
const router = useRouter();

type DialogRefKey =
  | 'singleButton'
  | 'infoConfirm'
  | 'fragmentDialogVisible'
  | 'sjgInfoValidCheckError'
  | 'attachExistenceCheckRef'
  | 'modifyBomVerifyFinishRef';
const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  infoConfirm: false,
  fragmentDialogVisible: false,
  sjgInfoValidCheckError: false,
  attachExistenceCheckRef: false,
  modifyBomVerifyFinishRef: false,
};
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
const tablePropsDialogRef = ref<TabulatorTableIF>({
  ...tablePropsData,
});
const isClickedSjgComponentInformationAttachmentsRef = ref<boolean>(false);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const messageBoxAttachExistenceCheckRef = ref<DialogProps>({
  title: t('Sjg.Msg.txtAttachmentExistenceCheckError'),
  content: t('Sjg.Msg.txtAttachmentDoesNotExist'),
  isSingleBtn: true,
  type: 'info',
});

// 照査データ有効チェックエラーのメッセージ
const messageBoxSjgInfoValidCheckErrorRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const messageBoxModifyBomVerifyFinishRef = ref<DialogProps>({
  title: '',
  content: '',
  type: 'warning',
});

let sjgGetModBomList: SjgGetModBomList[] = [];

let checkTargetsList: string[] = [];

let dislogTitle: string = '';

// 選択行データ
let selectedRow: SjgGetModListData | null = null;
const checkValidVerifyHandler = async () => {
  const apiRequestData: ExtendCommonRequestType<{ lotSid: string }> = {
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
  };
  // 照査情報有効チェック
  const { errorRef } = await useCheckValidVerify(apiRequestData);
  if (errorRef.value) {
    closeLoading();
    messageBoxSjgInfoValidCheckErrorRef.value.title =
      errorRef.value.response.rTitle;
    messageBoxSjgInfoValidCheckErrorRef.value.content =
      errorRef.value.response.rMsg;
    openDialog('sjgInfoValidCheckError');
    return false;
  }
  return true;
};

const modifyBomVerifyFinish = async (
  messageBoxProps: DialogProps | null = null,
  warningNecessityFlg: number = CONST_FLAGS.WARNING_NECESSITY_FLAG.NECESSITY,
) => {
  const modifyGmpVerifyStartReq: ExtendCommonRequestType<ModifyBomVerifyFinishReq> =
    {
      ...props.privilegesBtnRequestData,
      lotSid: props.selectedInspectionItemData!.lotSid.toString(),
      warningNecessityFlg,
      msgboxTitleTxt: messageBoxProps?.title ?? '',
      msgboxMsgTxt: messageBoxProps?.content ?? '',
      msgboxBtnTxt: messageBoxProps ? t('Cm.Chr.btnOk') : '',
      bomList: sjgGetModBomList,
    };
  const { responseRef, errorRef } = await useModifyBomVerifyFinish(
    modifyGmpVerifyStartReq,
  );
  if (errorRef.value) {
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      messageBoxModifyBomVerifyFinishRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxModifyBomVerifyFinishRef.value.content =
        errorRef.value.response.rMsg;
      openDialog('modifyBomVerifyFinishRef');
    } else {
      messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
      messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
      openDialog('singleButton');
    }
    return;
  }
  if (responseRef.value) {
    closeDialog('fragmentDialogVisible');
    emit('submit');
  }
};

const saveSjgComponentInformationConfirmationForm = async () => {
  // 「チェック対象」全てにチェックがついていない場合はチェックNG
  if (
    tablePropsDialogRef.value.selectRowsData?.length !== checkTargetsList.length
  ) {
    openDialog('singleButton');
    messageBoxSingleButtonRef.value.title = t('Sjg.Msg.txtStorableCheckError');
    messageBoxSingleButtonRef.value.content = t(
      'Sjg.Msg.txtItemsHaveNotBeenChecked',
    );
    return;
  }
  showLoading();
  if (!(await checkValidVerifyHandler())) {
    return;
  }
  await modifyBomVerifyFinish();
  closeLoading();
};

const rightButtonsList: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnTemporary'),
    type: 'primary',
    size: 'normal',
    clickHandler: () => {
      saveSjgComponentInformationConfirmationForm();
      return false;
    },
  },
];
const dialogLeftButtons = ref<DialogWindowProps['buttons']>([
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    clickHandler() {
      if (props.screenId === SCREENID.SJG_COMPONENT_INFORMATION_CONFIRMATION)
        emit('submit', props.privilegesBtnRequestData);
      closeDialog('fragmentDialogVisible');
    },
  },
]);
const dialogRightButtons = ref<DialogWindowProps['buttons']>(rightButtonsList);

const inspectionInfoShowRef = ref<InfoShowType>({
  infoShowItems: getInspectionInfoShowItems(),
  isLabelVertical: true,
  fontSizeLabel: '12px',
  fontSizeContent: '16px',
});

const updateEmitSelectedRow = async (v: SjgGetModListData) => {
  selectedRow = v;
  isClickedSjgComponentInformationAttachmentsRef.value =
    !isClickedSjgComponentInformationAttachmentsRef.value;
};

const updateSelectedRows = (v: SjgGetModListDataWithUniqueKey[]) => {
  // 選択複数行情報を保存
  if (props.screenId === SCREENID.SJG_COMPONENT_INFORMATION_CONFIRMATION) {
    tablePropsDialogRef.value.selectRowsData = v.map((item) => item.uniqueKey);
  }
};

const closeSjgInfoValidCheckError = async () => {
  closeDialog('sjgInfoValidCheckError');
  router.push({
    name: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
    state: {
      routerName: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
      searchConditionData: window.history.state.conditionData,
      tableSearchData: window.history.state.tableSearchData,
    },
  });
};

/**
 * 初期設定
 */
const sjgComponentInformationConfirmationInit = async () => {
  if (!props.selectedRowData) return;
  showLoading();
  tablePropsDialogRef.value.tableData = [];
  tablePropsDialogRef.value.selectRowsData = [];
  checkTargetsList = [];
  selectedRow = null;
  sjgGetModBomList = [];

  if (
    props.screenId ===
    SCREENID.SJG_COMPONENT_INFORMATION_CONFIRMATION_CONFIRMATION
  ) {
    dislogTitle = t('Sjg.Chr.txtComponentInformationConfirmationConfirmation');
  } else {
    dislogTitle = t('Sjg.Chr.txtComponentInformationConfirmation');
  }

  inspectionInfoShowRef.value.infoShowItems.expiryYmd.infoShowModelValue =
    props.selectedInspectionItemData!.expiryYmd.toString();
  inspectionInfoShowRef.value.infoShowItems.lotNo.infoShowModelValue =
    props.selectedInspectionItemData!.lotNo.toString();
  inspectionInfoShowRef.value.infoShowItems.verifyReasonNm.infoShowModelValue =
    props.selectedInspectionItemData!.verifyReasonNm.toString();
  inspectionInfoShowRef.value.infoShowItems.matNm.infoShowModelValue =
    props.selectedInspectionItemData!.matNm.toString();
  inspectionInfoShowRef.value.infoShowItems.matNo.infoShowModelValue =
    props.selectedInspectionItemData!.matNo.toString();
  inspectionInfoShowRef.value.infoShowItems.rsltYmd.infoShowModelValue =
    props.selectedInspectionItemData!.rsltYmd.toString();

  const { responseRef, errorRef } = await useSjgGetBomList({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid.toString(),
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    openDialog('singleButton');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    tablePropsDialogRef.value.tableData =
      responseRef.value.data.rData.bomList.map((item: SjgGetModListData, i) => {
        const cells = [
          item.verifyBomFlg === CONST_FLAGS.SJG.VERIFY_BOM_FLG.CHECKED &&
            'verifyBomFlgNm',
          item.logLotStsCheck ===
            CONST_FLAGS.SJG.LOG_LOT_STS_CHECK_STATUS.INCLUDING_FAIL &&
            'logLotStsCheckNm',
          item.logExpiryYmdCheck ===
            CONST_FLAGS.SJG.LOG_EXPIRY_DTS_CHECK_STATUS.OUT_RANGE &&
            'logExpiryYmdCheckNm',
          item.logShelfLifeYmdCheck ===
            CONST_FLAGS.SJG.LOG_EXPIRY_DTS_CHECK_STATUS.OUT_RANGE &&
            'logShelfLifeYmdCheckNm',
          item.logWgtYmdCheck ===
            CONST_FLAGS.SJG.LOG_EXPIRY_DTS_CHECK_STATUS.OUT_RANGE &&
            'logWgtYmdCheckNm',
          item.logConsDeadlineCheck ===
            CONST_FLAGS.SJG.LOG_EXPIRY_DTS_CHECK_STATUS.OUT_RANGE &&
            'logConsDeadlineCheckNm',
          item.lotLotSts === CONST_FLAGS.SJG.LOG_LOT_STS_STATUS.FAIL &&
            'lotLotStsNm',
        ]
          .filter(Boolean)
          .join(',');

        sjgGetModBomList.push({
          bomLotSid: item.bomLotSid?.toString() ?? '',
          odrNo: item.odrNo?.toString() ?? '',
          bomMatNo: item.bomMatNo?.toString() ?? '',
          bomLotNo: item.bomLotNo?.toString() ?? '',
          logLotStsCheck: item.logLotStsCheck?.toString() ?? '',
          lotLotSts: item.lotLotSts?.toString() ?? '',
          logExpiryYmdCheck: item.logExpiryYmdCheck?.toString() ?? '',
          lotExpiryYmd: item.lotExpiryYmd?.toString() ?? '',
          logShelfLifeYmdCheck: item.logShelfLifeYmdCheck?.toString() ?? '',
          lotShelfLifeYmd: item.lotShelfLifeYmd?.toString() ?? '',
          logWgtYmdCheck: item.logWgtYmdCheck?.toString() ?? '',
          logConsDeadlineCheck: item.logConsDeadlineCheck?.toString() ?? '',
        });

        const rectItem = {
          ...item,
          disabledBtnFlg: 1,
          cells,
          verifyBomFlgNo: Number(CONST_FLAGS.SJG.VERIFY_BOM_FLG.NOT_CHECKED),
          uniqueKey: `${item.odrNo}-${i}`,
          attExistFlgNm: '',
        };
        if (
          props.screenId === SCREENID.SJG_COMPONENT_INFORMATION_CONFIRMATION
        ) {
          rectItem.verifyBomFlgNo = Number(rectItem.verifyBomFlg);
        }
        if (item.verifyBomFlg === CONST_FLAGS.SJG.VERIFY_BOM_FLG.CHECKED) {
          checkTargetsList.push(rectItem.uniqueKey);
        }
        return rectItem;
      });
  }
  if (
    props.screenId ===
    SCREENID.SJG_COMPONENT_INFORMATION_CONFIRMATION_CONFIRMATION
  ) {
    dialogRightButtons.value = [];
    if (
      props.selectedRowData?.verifyCatSts ===
        CONST_FLAGS.SJG.GMP_VERIFY_STATUS.FINISH ||
      props.saveFlag
    ) {
      tablePropsDialogRef.value.selectRowsData = checkTargetsList;
    }
  } else {
    dialogRightButtons.value = rightButtonsList;
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, sjgComponentInformationConfirmationInit);
</script>
<style lang="scss" scoped>
$namespace: 'Sjg-component-information-confirmation';

.#{$namespace} {
  background-color: $gray720;
  display: flex;
  flex-direction: column;
  flex-grow: 1;

  &_content-inspectionInfo {
    height: 108px;
    overflow-y: auto;
  }

  &_content-selectinspectionitem {
    height: 400px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    /** element plus */
    :deep(.el-card__body) {
      padding-bottom: 0;
      padding-inline: 16px;
      display: flex;
      flex-direction: column;
      flex-grow: 1;
    }
  }
  &_txt-gmp-case-nm {
    width: 80px;
    line-height: 2;
  }
}
.flex-container {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}
</style>
