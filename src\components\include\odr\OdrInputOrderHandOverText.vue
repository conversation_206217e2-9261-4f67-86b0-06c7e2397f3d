<template>
  <!-- 指図コメント入力ダイアログ -->
  <!-- 見出し 指図コメント入力 -->
  <DialogWindow
    :title="$t('Odr.Chr.txtInstructionCommentEntry')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :class="'odr-input-order-hand-over-text'"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="orderInputOrderHandOverTextFormRef.formModel"
      :formItems="orderInputOrderHandOverTextFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          orderInputOrderHandOverTextFormRef.customForm = v;
        }
      "
      @changeFormModel="
        (changeFlag, { changeLogModList }) => {
          updateDialogChangeFlagRef(changeFlag);
          updateCurrentChangedFormModelMap(changeLogModList);
        }
      "
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 製造指図コメント更新の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxOrderInputOrderHandOverTextVisible"
    :dialogProps="messageBoxOrderInputOrderHandOverTextPropsRef"
    :cancelCallback="
      () => closeDialog('messageBoxOrderInputOrderHandOverTextVisible')
    "
    :submitCallback="requestApiModifyOrderHandOverText"
  />
  <!-- 製造指図コメント更新完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxOrderInputOrderHandOverTextFinishedVisible"
    :dialogProps="messageBoxOrderInputOrderHandOverTextFinishedPropsRef"
    :submitCallback="
      closeDialogFromMessageBoxModifyOrderInputOrderHandOverTextFinished
    "
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import onValidateHandler from '@/utils/validateHandler';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  useGetOrderHandOverText,
  useModifyOrderHandOverText,
} from '@/hooks/useApi';
import {
  GetOrderHandOverTextRequestData,
  ModifyOrderHandOverTextRequestData,
} from '@/types/HookUseApi/OdrTypes';
import {
  CommonRequestType,
  LogModListType,
} from '@/types/HookUseApi/CommonTypes';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  getOrderInputOrderHandOverTextFormItems,
  orderInputOrderHandOverTextFormModel,
} from './odrInputOrderHandOverText';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxOrderInputOrderHandOverTextVisible'
  | 'messageBoxOrderInputOrderHandOverTextFinishedVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxOrderInputOrderHandOverTextVisible: false,
  messageBoxOrderInputOrderHandOverTextFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 製造指図コメント更新の確認メッセージボックス
const messageBoxOrderInputOrderHandOverTextPropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titleOrderInputOrderHandOverText'),
  content: t('Odr.Msg.contentOrderInputOrderHandOverText'),
  type: 'question',
});

// 製造指図コメント更新の完了メッセージボックス
const messageBoxOrderInputOrderHandOverTextFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

const orderInputOrderHandOverTextFormRef = ref<CustomFormType>({
  formItems: getOrderInputOrderHandOverTextFormItems(),
  formModel: orderInputOrderHandOverTextFormModel,
});

let logModList: LogModListType = [];
// 変更履歴更新時処理
const updateCurrentChangedFormModelMap = (changeLogModList: LogModListType) => {
  logModList = changeLogModList;
};

// 親ダイアログから渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  odrNo: string; // 親ダイアログのodrNo
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 指図コメント更新用パラメータ
let updDts: string = '';

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // 1.下記チェックを行い、チェックOKなら処理継続する。
  // １ 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    orderInputOrderHandOverTextFormRef.value.customForm !== undefined &&
    (await orderInputOrderHandOverTextFormRef.value.customForm.validate(
      (isValid) => {
        onValidateHandler(isValid);
      },
    ));

  if (!validate) {
    return false;
  }

  // 2.以下のメッセージを表示
  // 製造指図コメント更新の確認
  openDialog('messageBoxOrderInputOrderHandOverTextVisible');
  return false;
};

// 製造指図コメント更新の確認メッセージ'OK'押下時処理
// 製造指図コメント更新のAPIリクエスト処理
const requestApiModifyOrderHandOverText = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxOrderInputOrderHandOverTextVisible');

  showLoading();

  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する。
  const requestData: ModifyOrderHandOverTextRequestData = {
    odrNo:
      orderInputOrderHandOverTextFormRef.value.formItems.odrNo.formModelValue.toString(),
    handOverTxt:
      orderInputOrderHandOverTextFormRef.value.formItems.handOverTxt.formModelValue.toString(),
    odrUpdDts: updDts,
    logModList,
  };
  const { responseRef, errorRef } = await useModifyOrderHandOverText({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxOrderInputOrderHandOverTextPropsRef.value.title,
    msgboxMsgTxt: messageBoxOrderInputOrderHandOverTextPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // 4．製造指図コメント更新
  // ・データベースを更新した後、以下のメッセージを表示する。
  // 製造指図コメント更新完了
  messageBoxOrderInputOrderHandOverTextFinishedPropsRef.value.title =
    responseRef.value.data.rTitle;
  messageBoxOrderInputOrderHandOverTextFinishedPropsRef.value.content =
    responseRef.value.data.rMsg;

  closeLoading();

  openDialog('messageBoxOrderInputOrderHandOverTextFinishedVisible');
};

// 製造指図コメント更新完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxModifyOrderInputOrderHandOverTextFinished =
  () => {
    // 再検索用に親に実行を通知
    emit('submit');

    // 5.ダイアログウィンドウを閉じる
    closeDialog('messageBoxOrderInputOrderHandOverTextFinishedVisible');
    closeDialog('fragmentDialogVisible');
  };

/**
 * 指図コメント入力ダイアログの初期設定
 */
const odrInputOrderHandOverTextInit = async () => {
  if (props.odrNo === '') {
    return;
  }

  // ダイアログ終了チェック初期化
  updateDialogChangeFlagRef(false);
  logModList = [];

  showLoading();

  // 指図コメント入力初期表示のAPIを行う。
  const requestData: GetOrderHandOverTextRequestData = {
    odrNo: props.odrNo,
  };
  const { responseRef, errorRef } = await useGetOrderHandOverText({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // 指図コメント入力情報レイアウト用初期値設定
  setFormModelValueFromApiResponse(
    orderInputOrderHandOverTextFormRef,
    responseRef.value.data.rData,
  );

  // 指図コメント更新用パラメータを指図コメント入力初期表示API取得の更新日付から保持
  updDts = responseRef.value.data.rData.updDts.toString();

  closeLoading();

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await odrInputOrderHandOverTextInit();
  },
);
</script>
