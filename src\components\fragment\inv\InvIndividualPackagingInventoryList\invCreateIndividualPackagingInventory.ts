import CONST from '@/constants/utils';
import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

export const getIndividualInventoryCreateFormItems: () => CustomFormType['formItems'] =
  () => ({
    unitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    matNo: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtItemCode_Name') },
      formModelValue: '',
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: 'matNo',
    },
    bpId: {
      label: { text: t('Inv.Chr.txtBusinessPartnerName') },
      formModelValue: '',
      formRole: 'selectComboBox',
      props: { disabled: true, optionsData: [] },
      selectOptions: [],
      cmbId: '',
    },
    lotNo: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtManageNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { clearable: true },
      rules: [
        rules.required('textBox'),
        rules.length(15, t('Cm.Chr.txtLength', [15])),
        rules.upperCaseSingleByteAlphanumericNumberCharacters(),
      ],
    },
    totalInvQty: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtTotalInvQty') },
      formModelValue: '',
      formRole: 'textBox',
      props: { clearable: true },
      suffix: { formModelProp: 'unitNm' },
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.positiveRealNumericOnly(),
      ],
    },
    invQty: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtInvQty') },
      formModelValue: '',
      formRole: 'textBox',
      props: { clearable: true },
      suffix: { formModelProp: 'unitNm' },
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.positiveRealNumericOnly(),
      ],
    },
    invPackageQty: {
      label: { text: t('Inv.Chr.txtInvPackageQty') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    zoneNo: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtZone') },
      formModelValue: '',
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: 'zoneNo',
    },
    locNo: {
      label: { text: t('Inv.Chr.txtLocation') },
      formModelValue: '',
      rules: [],
      formRole: 'selectComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: 'locNo',
    },
    makerLotNo: {
      label: { text: t('Inv.Chr.txtBusinessPartnerLotNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { clearable: true },
      rules: [
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
        rules.length(16, t('Cm.Chr.txtLength', [16])),
      ],
    },
    calcShelfDts: {
      label: { text: t('Inv.Chr.txtEffectiveCalcDate') },
      formModelValue: '',
      formRole: 'date',
      props: { modelValue: '', type: 'date' },
      rules: [rules.required('date'), rules.futureDate()],
    },
    calcStDts: {
      label: { text: t('Inv.Chr.txtExpirationCalcDate') },
      formModelValue: '',
      formRole: 'date',
      props: { modelValue: '', type: 'date' },
      rules: [rules.required('date'), rules.futureDate()],
    },
    edNo: {
      label: { text: t('Inv.Chr.txtVersionNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { clearable: true },
      rules: [
        rules.upperCaseSingleByteAlphanumeric(),
        rules.length(10, t('Cm.Chr.txtLength', [10])),
      ],
    },
    accrualDts: {
      label: { text: t('Inv.Chr.txtAccrualDate') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    afmRefNo: {
      formModelValue: '',
      label: { text: t('Inv.Chr.txtAfmRefNo') },
      formRole: 'textBox',
      rules: [
        rules.length(16, t('Cm.Chr.txtLength', [16])),
        rules.upperCaseSingleByteAlphanumeric(),
      ],
    },
    icRsnCd: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtReasonCode') },
      formModelValue: '',
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: 'rsnCd',
    },
    afmExpl: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtComment') },
      formModelValue: '',
      rules: [
        rules.required('textComboBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
      ],
      formRole: 'textComboBox',
      props: { clearable: true },
      selectOptions: [],
      cmbId: 'cmtInvGen',
    },
  });

export const individualInventoryCreateFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getIndividualInventoryCreateFormItems());
