<template>
  <!-- MBR申請確認ダイアログ -->
  <DialogWindow
    :title="$t('Mst.Chr.txtMBRApplicationConfirmation')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :leftButtons="dialogLeftButtons"
    :rightButtons="dialogRightButtons"
    :class="'mbr-application-confirmation'"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- MBR申請情報のテキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="mbrApplicationInfoShowRef.infoShowItems"
      :isLabelVertical="mbrApplicationInfoShowRef.isLabelVertical"
    />
    <CustomForm
      class="Util_mt-16"
      :formModel="mbrApplicationConfirmationFormRef.formModel"
      :formItems="mbrApplicationConfirmationFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          mbrApplicationConfirmationFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- MBR承認の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApproveConfirmVisible"
    :dialogProps="messageBoxApproveConfirmPropsRef"
    :cancelCallback="() => closeDialog('messageBoxApproveConfirmVisible')"
    :submitCallback="requestApiApproveMBR"
  />
  <!-- MBR承認完了の表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApproveCompleteVisible"
    :dialogProps="messageBoxApproveCompletePropsRef"
    :submitCallback="requestApiApproveMBRFinished"
  />
  <!-- MBR否認の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxDenyConfirmVisible"
    :dialogProps="messageBoxDenyConfirmPropsRef"
    :cancelCallback="() => closeDialog('messageBoxDenyConfirmVisible')"
    :submitCallback="requestApiDenyMBR"
  />
  <!-- MBR否認完了の表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxDenyCompleteVisible"
    :dialogProps="messageBoxDenyCompletePropsRef"
    :submitCallback="requestApiDenyMBRFinished"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { GetApplicationInformationReq } from '@/types/HookUseApi/MstTypes';
import {
  useGetApplicationInformation,
  useModifyMBRApprove,
  useModifyMBRDeny,
} from '@/hooks/useApi';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import {
  getMBRApplicationInfoShowItems,
  getMBRApplicationConfirmationFormItems,
  mbrApplicationConfirmationFormModel,
} from './mstMBRApplicationConfirmation';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxApproveConfirmVisible'
  | 'messageBoxApproveCompleteVisible'
  | 'messageBoxDenyConfirmVisible'
  | 'messageBoxDenyCompleteVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxApproveConfirmVisible: false,
  messageBoxApproveCompleteVisible: false,
  messageBoxDenyConfirmVisible: false,
  messageBoxDenyCompleteVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const mbrApplicationInfoShowRef = ref<InfoShowType>({
  infoShowItems: getMBRApplicationInfoShowItems(),
  isLabelVertical: true,
});

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// MBR承認の確認メッセージボックス
const messageBoxApproveConfirmPropsRef = ref<DialogProps>({
  title: t('Mst.Msg.titleApprovalConfirm'),
  content: t('Mst.Msg.contentApprovalConfirm'),
  type: 'question',
});

// MBR承認完了のメッセージボックス
const messageBoxApproveCompletePropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

// MBR否認の確認メッセージボックス
const messageBoxDenyConfirmPropsRef = ref<DialogProps>({
  title: t('Mst.Msg.titleDenialConfirm'),
  content: t('Mst.Msg.contentDenialConfirm'),
  type: 'question',
});

// MBR否認完了のメッセージボックス
const messageBoxDenyCompletePropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const mbrApplicationConfirmationFormRef = ref<CustomFormType>({
  formItems: getMBRApplicationConfirmationFormItems(),
  formModel: mbrApplicationConfirmationFormModel,
});

// ダイアログ用ボタン設定
const dialogLeftButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    clickHandler: async () => {
      const isClose = commonRejectHandler();
      return isClose;
    },
  },
];

// ダイアログ用ボタン設定
const dialogRightButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Mst.Chr.btnDenial'), // 否認
    type: 'dangerSecond',
    size: 'normal',
    class: 'button-denial',
    clickHandler: async () => {
      // 入力チェック
      const validate =
        mbrApplicationConfirmationFormRef.value.customForm !== undefined &&
        (await mbrApplicationConfirmationFormRef.value.customForm.validate(
          (isValid) => {
            onValidateHandler(isValid);
          },
        ));

      if (validate) {
        // MBR否認の確認メッセージ表示
        openDialog('messageBoxDenyConfirmVisible');
      }

      return false;
    },
  },
  {
    text: t('Mst.Chr.btnApproval'), // 承認
    type: 'primary',
    size: 'normal',
    clickHandler: async () => {
      // 入力チェック
      const validate =
        mbrApplicationConfirmationFormRef.value.customForm !== undefined &&
        (await mbrApplicationConfirmationFormRef.value.customForm.validate(
          (isValid) => {
            onValidateHandler(isValid);
          },
        ));

      if (validate) {
        // MBR承認の確認メッセージ表示
        openDialog('messageBoxApproveConfirmVisible');
      }

      return false;
    },
  },
];

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  mbrNo: string; // MBR番号
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

/**
 * MBR承認処理
 */
const requestApiApproveMBR = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxApproveConfirmVisible');

  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    return;
  }

  showLoading();

  const approveMBRData = {
    ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
    msgboxTitleTxt: messageBoxDenyConfirmPropsRef.value.title,
    msgboxMsgTxt: messageBoxDenyConfirmPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    mbrNo: props.mbrNo,
    approvalComment:
      mbrApplicationConfirmationFormRef.value.formModel.approvalDenialComment.toString(),
  };

  const { responseRef, errorRef } = await useModifyMBRApprove(approveMBRData);

  closeLoading();

  if (errorRef.value) {
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // MBR承認完了
    messageBoxApproveCompletePropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxApproveCompletePropsRef.value.content =
      responseRef.value.data.rMsg;
    openDialog('messageBoxApproveCompleteVisible');
  }
};

/**
 * MBR承認完了後の処理
 */
const requestApiApproveMBRFinished = async () => {
  // メッセージを閉じる
  closeDialog('messageBoxApproveCompleteVisible');

  // ダイアログを閉じる
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData, props.mbrNo);
};

/**
 * MBR否認処理
 */
const requestApiDenyMBR = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxDenyConfirmVisible');

  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    return;
  }

  showLoading();

  const denyMBRData = {
    ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
    msgboxTitleTxt: messageBoxDenyConfirmPropsRef.value.title,
    msgboxMsgTxt: messageBoxDenyConfirmPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    mbrNo: props.mbrNo,
    denialComment:
      mbrApplicationConfirmationFormRef.value.formModel.approvalDenialComment.toString(),
  };

  const { responseRef, errorRef } = await useModifyMBRDeny(denyMBRData);

  closeLoading();

  if (errorRef.value) {
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // MBR否認完了
    messageBoxDenyCompletePropsRef.value.title = responseRef.value.data.rTitle;
    messageBoxDenyCompletePropsRef.value.content = responseRef.value.data.rMsg;
    openDialog('messageBoxDenyCompleteVisible');
  }
};

/**
 * MBR否認完了後の処理
 */
const requestApiDenyMBRFinished = async () => {
  // メッセージを閉じる
  closeDialog('messageBoxDenyCompleteVisible');

  // ダイアログを閉じる
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData, props.mbrNo);
};

// 初回表示、再検索で呼び出される
// 申請情報取得API呼び出し
const requestApiGetApplicationInformation = async (
  requestData: GetApplicationInformationReq,
) => {
  showLoading();

  // InfoShow初期化
  mbrApplicationInfoShowRef.value.infoShowItems =
    getMBRApplicationInfoShowItems();

  // 申請情報を取得
  const { responseRef, errorRef } = await useGetApplicationInformation({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });

  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return Promise.reject(); // エラーの場合継続処理させない
  }

  // ダイアログ終了チェック初期化
  updateDialogChangeFlagRef(false);

  // FormItems初期化
  mbrApplicationConfirmationFormRef.value.formItems =
    getMBRApplicationConfirmationFormItems();

  if (responseRef.value) {
    // 申請情報を表示
    Object.entries(responseRef.value.data.rData).forEach(([key, value]) => {
      if (key in mbrApplicationInfoShowRef.value.infoShowItems) {
        mbrApplicationInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });

    // 承認/否認コメント
    mbrApplicationConfirmationFormRef.value.formItems.approvalDenialComment.formModelValue =
      responseRef.value?.data.rData.approvalDenialComment ?? '';
  }

  // 申請中の場合はテキストエリアを有効化、それ以外は無効化
  Object.entries(mbrApplicationConfirmationFormRef.value.formItems).forEach(
    ([key, v]) => {
      const formItem = v;
      if (key === 'approvalDenialComment') {
        if ('props' in formItem) {
          formItem.props = {
            type: 'textarea',
            disabled: !(responseRef.value?.data.rData.mbrSts === 'R'),
          };
        }
      }
    },
  );

  // 申請中の場合は承認、否認ボタンを有効化、それ以外は無効化
  dialogRightButtons[0].disabled = !(
    responseRef.value?.data.rData.mbrSts === 'R'
  );
  dialogRightButtons[1].disabled = !(
    responseRef.value?.data.rData.mbrSts === 'R'
  );

  closeLoading();

  return Promise.resolve();
};

/**
 * MBR申請確認ダイアログの初期設定
 */
const mstMBRApplicationConfirmationInit = async () => {
  // 申請情報取得のAPI呼び出しと反映
  try {
    const requestData: GetApplicationInformationReq = {
      mbrNo: props.mbrNo,
    };
    await requestApiGetApplicationInformation(requestData);
  } catch {
    return;
  }

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await mstMBRApplicationConfirmationInit();
  },
);
</script>
