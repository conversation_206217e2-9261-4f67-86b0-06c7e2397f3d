import { ref } from 'vue';
import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import UTILS from '@/constants/utils';

const { t } = i18n.global;

// MBR申請情報の縦並び項目定義
export const getMBRApplicationInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // MBR番号
    mbrNo: {
      label: { text: t('Mst.Chr.txtMBRNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
    // 品目コード
    matNo: {
      label: { text: t('Mst.Chr.txtMatNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 品名
    matNm: {
      label: { text: t('Mst.Chr.txtMatNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 18,
    },
    // 処方コード
    rxNo: {
      label: { text: t('Mst.Chr.txtPrescriptionCode') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 処方名
    rxNm: {
      label: { text: t('Mst.Chr.txtPrescriptionName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 18,
    },
  });

// 整合性チェック結果一覧用テーブル設定
export const tablePropsDataRef = ref<TabulatorTableIF>({
  pageName: 'ConsistencyCheckResultsList',
  dataID: 'tableName', // 主キー。ユニークになるものを設定。
  frozenColumn: 'results', // 凍結列
  autoWidth: 'detailedContents', // 自動幅の列
  height: '408px',
  column: [
    {
      title: 'Mst.Chr.txtMasterName',
      field: 'masterName',
      width: 15 * UTILS.ZENKAKU_CHAR_WIDTH,
    },
    { title: 'Mst.Chr.txtResults', field: 'results' },
    {
      title: 'Mst.Chr.txtDetailedContents',
      field: 'detailedContents',
    },
    { title: '', field: 'tableName', hidden: true },
  ],
  tableData: [],
  // NOTE:権限が親からprops経由で来るため、Init関数内でonSelectBtnsを生成する
  onSelectBtns: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
});
