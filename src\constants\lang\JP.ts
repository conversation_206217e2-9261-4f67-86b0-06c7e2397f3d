// *****************************************
// 概要：MP-Connect言語設定
// 備考：
// 作成：2025/07/28 15:21:01
// *****************************************
const lang = {
  Cm: {
    // 共通
    Chr: {
      // キャラクタ
      btnButton: 'ボタン',
      btnCancel: 'キャンセル',
      btnInputCancel: '入力キャンセル',
      btnClose: '閉じる',
      btnExcel: 'XLSX',
      btnExecution: '実行',
      btnLoginAgain: '再度ログイン',
      btnOk: 'OK',
      btnSetUpFinish: '設定完了',
      btnSave: '保存せず戻る',
      btnColumnEdit: 'カラム編集',
      btnCsvExport: 'CSV出力',
      btnTemporary: '保存',
      btnChange: '変更',
      btnDetail: '詳細',
      btnDelate: '削除',
      btnAddFile: 'ファイル添付',
      btnAddImage: '画像添付',
      btnAddPdf: 'PDF添付',
      btnBackToLogin: 'ログインに戻る',
      btnPause: '中断',
      btnInputConfirm: '入力確定',
      txtChinese: '中国語',
      txtEnglish: '英語',
      txtHomeTitle: 'ようこそ {0}さん',
      txtJapanese: '日本語',
      txtTagOfRequired: '必須',
      txtValidationError: '入力項目エラー',
      txtRequiredInput: '必須入力',
      txtInputError: '入力エラー',
      txtConfirm: '確認',
      txtRequiredSelect: '必須選択',
      txtUpperCaseSingleByteAlphanumericNumberCharacters:
        '半角英数(大文字)記号のみ入力可能',
      txtSingleByteAlphanumericNumberCharacters: '半角英数記号のみ入力可能',
      txtUpperCaseSingleByteAlphanumeric: '半角英数(大文字)のみ入力可能',
      txtSingleByteNumeric: '半角数字のみ入力可能',
      txtLength: '{0}文字以内のみ入力可能',
      txtFixedChrLength: '{0}文字とすること',
      txtProhibitedCharacters: '「{0}」以外のみ入力可能',
      txtNumericOnly: '数値のみ入力可能',
      txtIntOnly: '整数のみ入力可能',
      txtNaturalNumericOnly: '自然数(0<)のみ入力可能',
      txtPositiveRealNumericOnly: '正の実数（0.0<）のみ入力可能',
      txtNonNegativeRealNumericOnly: '非負の実数（0.0≦）のみ入力可能',
      txtDecimalPlaces: '小数部{0}桁まで入力可能',
      txtIntPlaces: '整数部{0}桁まで入力可能',
      txtIntAndDecimalPlaces: '整数部{0}桁、小数部{1}桁まで入力可能',
      txtRangeOfNumeric: '{0}～{1}のみ入力可能',
      txtIsOneRequiredSelect: 'いずれか入力必須',
      txtInconsistency: '上下限に入力された値に誤りがあります',
      txtGCodeErrorFormat: 'G-コードフォーマットが正しくありません',
      txtIPAddrErrorFormat: 'IPアドレスフォーマットが正しくありません',
      txtDifferentDataNoTableNo:
        '取得データ数とテーブルデータ数が異なっています。',
      txtCSVErrorImport: 'CSVインポートエラー',
      txtCSVFileSelect: 'csvファイルを選択してください。',
      txtCSVFileEmpty: '指定のcsvファイルが空です。',
      txtCSVDataNumError: 'csvデータ数が取得データ数を上回っています。',
      txtCSVFomatError: 'csvデータのフォーマットが正しくありません。',
      txtFromToDate: '開始日≦終了日のみ有効',
      txtRangeOfDate: '開始日の{0}日以内のみ有効',
      txtBeforeYesterday: '昨日以前のみ有効',
      txtAfterToday: '本日以降に入力可',
      txtExistFilePath: '存在するファイルパスのみ有効',
      txtNoneBlank: '半角スペースまたは全角スペースのみの入力はできません',
      txtPdf: 'PDF',
      txtImage: '画像',
      txtUnselectedData: 'データレコード未選択',
      txtUnselectedSingleData: '1件レコード選択',
      txtSelectedMaxData: '最大選択件数チェックエラー',
      txtOperations: '操作',
      txtErrorPageInfo:
        'ログイン情報が取得できませんでした。\n長時間操作されておらずタイムアウトが発生した可能性があります。\nログイン画面に戻り、再度ログインを実施してください。',
      txtErrorPageExceptionInfo:
        'サーバーで例外処理が発生しました。\n管理者に連絡してください。',
      txtErrorPageServerMsg: 'サーバメッセージ：',
      txtErrorPageTimeoutInfo:
        'サーバーから応答がありませんでした。\n管理者に連絡してください。',
      txtInputComment: 'コメント入力',
      txtPlaceholderInputComment: 'コメントを入力もしくは選択',
      txtNotSelected: '未選択',
      txtUserId: 'ユーザーID',
      txtUserPassword: 'パスワード',
      txtUnFinished: '未',
      txtFinished: '済',
      txtOK: 'OK',
      txtNG: 'NG',
      txtHave: '有',
      txtNotHave: '無',
      txtNecessary: '必要',
      txtUnNecessary: '不要',
      btnDecision: '確定',
      txtTitleIdPassSign: '署名(パスワード認証)',
      txtPass: '〇',
      txtFail: '×',
      txtTitleFaceAuthSign: '署名(顔認証)',
      txtCamera: 'カメラ',
      txtFEVersion: 'FE Ver.',
      txtBEVersion: 'BE Ver.',
      txtTitleDeviceError: 'デバイス起動エラー',
      txtNoCamera: 'デバイスが正しく起動しませんでした',
      txtFrom: 'FROM',
      txtTo: 'TO',
      txtTitleSignAuthComplete: '署名認証',
      txtMsgSignAuthComplete: '認証が完了しました。',
      txtLicenseInfo: 'ライセンス情報',
      txtOpenSourceLicenseInfo: 'オープンソースライセンス情報',
      Menu: {
        //
        txtHome: 'ホーム',
        txtSog: '出荷',
        txtSogPlanList: '出荷予定作成',
        txtSogConfirmedShipmentRecordList: '出荷実績確定一覧',
        txtInventoryList: '在庫一覧',
        txtInventoryControl: '在庫管理',
        txtInventory: '在庫',
        txtInventoryReturnInstructionCreate: '返納指示作成',
        txtStorageAndRetrieval: '保管出納',
        txtInvInventorySnapshotProcessingList: '在庫スナップショット実績',
        txtIndividualInventoryList: '個装在庫一覧',
        txtIndividualPackagingInventoryListFormulation: '個装在庫一覧(製剤)',
        txtIndividualPackagingInventoryListPackaging: '個装在庫一覧(包装)',
        txtLotList: 'ロット在庫一覧',
        txtLotListFormulation: 'ロット在庫一覧(製剤)',
        txtLotListPackaging: 'ロット在庫一覧(包装)',
        txtInvSearchIndividualPackagingInventory: '個装在庫検索',
        txtInvSearchIndividualPackagingInventoryFormulation:
          '個装在庫検索(製剤)',
        txtInvSearchIndividualPackagingInventoryPackaging: '個装在庫検索(包装)',
        txtInvInventoryMismatchList: '在庫アンマッチ一覧',
        txtInvSnapshotScheduleInst: '在庫スナップショット計画',
        txtInvLoadReturnInstruction: '荷戻し確認',
        txtInvLoadReturnTargetLabel: '返納対象QR読取',
        txtInventoryRegistReturn: '返品登録',
        txtInventoryRegistReturnTitle: '返品在庫一覧',
        txtAog: '入荷',
        txtAogPlanList: '入荷予定一覧',
        txtAogConfirmReceiptRecord: '受入実績確定',
        txtAogArrivalInstructionList: '入荷指示一覧',
        txtAogRsltCarList: '入荷時検品記録一覧',
        txtAogRsltCarCheck: '入荷時検品記録確認',
        txtAogInstructionCreate: '入荷指示作成',
        txtAogLabelIssuancePlanList: '貼付確認・原材料ラベル発行',
        txtAogArrivalRecordList: '入荷実績一覧',
        txtAogArrivalRecordDetails: '入荷実績詳細一覧',
        txtSjgGMPConfirmationTitle3: 'GMP確認(逸脱処理)',
        txtSjgGMPConfirmationTitle4: 'GMP確認(バリデーション)',
        txtSjgGMPConfirmationTitle5: 'GMP確認(変更管理)',
        txtSjgGMPConfirmationTitle6: 'GMP確認(特別作業)',
        txtSjgGMPConfirmationTitle7: 'GMP確認(資材改版)',
        txtTransfer: '出庫',
        txtTrfShippableInstructionListFormulation: '指図参照出庫依頼(製剤)',
        txtTrfShippableInstructionListPackaging: '指図参照出庫依頼(包装)',
        txtTrfCreateShipmentRequestbyInstructionReferenceFormulation:
          '指図参照出庫依頼作成(製剤)',
        txtTrfCreateShipmentRequestbyInstructionReferencePackaging:
          '指図参照出庫依頼作成(包装)',
        txtTrfShipmentInstructionList: '出庫指示一覧',
        txtTrfConfirmedShipmentInstructionList: '出庫実績一覧',
        txtTrfIndividualShipmentRequest: '個別出庫依頼',
        txtTrfIndividualShipmentRequestFormulation: '個別出庫依頼(製剤)',
        txtTrfIndividualShipmentRequestPackaging: '個別出庫依頼(包装)',
        txtTrfLoadShipmentReport: '荷受確認',
        txtTrfPrintReceiptConfirmationLabel: '荷受確認ラベル発行',
        txtTrfShipmentRequestList: '出庫依頼一覧',
        txtTrfShipmentRequestListFormulation: '出庫依頼一覧(製剤)',
        txtTrfShipmentRequestListPackaging: '出庫依頼一覧(包装)',
        txtSop: 'SOP管理',
        txtInvestigation: '調査依頼',
        txtSopPA: 'SOPマスタ管理',
        txtRxSop: '製造指図SOP',
        txtPrcSop: '工程作業SOP',
        txtWgtSop: '秤量前後SOP',
        txtSopEdit: 'SOPフロー編集',
        txtRestockScheduled: '入荷予定',
        txtOrder: '計画・指図',
        txtSchedule: '小日程計画',
        txtScheduleList: '小日程計画一覧',
        txtOrderRegistration: '製造指図登録',
        txtWeighing: '秤量',
        txtWeighingInstructionCreate: '秤量指示作成',
        txtWeighingInstructionList: '秤量指示一覧',
        txtWeighingRoomChange: '秤量室変更',
        txtReWeighingInstructionCreate: '再秤量指示作成',
        txtWeighingRecord: '秤量記録確認',
        txtWeighingSopList: '秤量前後SOP一覧',
        txtPrd: '製造',
        txtProcessRecord: '製造記録',
        txtPrdConfirmList: '製造記録確認',
        txtPrdApprovalList: '製造記録承認',
        txtPrdRefList: '製造記録参照',
        txtPrdPrcFlowList: '工程作業記録',
        txtLogin: 'ログイン',
        txtOrderStateList: '製造指図状況',
        txtOrderApproval: '製造指図承認',
        txtSogCreateProductShipmentInstruction: '製品出庫指示作成',
        txtSogRegistShipmentInstruction: '予定出荷指示登録',
        txtSogProductShipmentInstructionList: '製品出庫指示一覧',
        txtMaster: 'マスタ保守',
        txtMBRCreate: 'MBR作成',
        txtSelectMBRComparisonMaster: 'MBR比較マスタ選択',
        txtMBRApplication: 'MBR申請',
        txtApproveMBR: 'MBR承認',
        txtMasterList: 'マスタ承認',
        txtOrderList: '製造指図一覧',
        txtInvReturnInstructionList: '返納指示一覧',
        txtTst: '試験',
        txtTestRequestCandidateCreate: '試験依頼作成',
        txtTstTestRequestCandidateList: '試験候補一覧',
        txtTstTestRequestStatusList: '試験状況一覧',
        txtTstQualityControlInventoryList: '品質管理対象在庫一覧',
        txtTstChangeQualityStatusExpiration: '品質状態・期限変更',
        txtShipmentDecision: '出荷判定',
        txtSjgInspectionCandidateList: '照査',
        txtSjgSelectInspectionItem: '照査項目選択',
        txtLabelTrace: 'ラベルトレース',
        txtReprintLabel: 'ラベル再発行',
        txtLotTrace: 'ロットトレース',
        txtSogConfirmShipmentRecord: '出荷実績確定',
        txtSogShipmentInstructionList: '出荷指示一覧',
        txtSogRegistRepackagingShipmentInstruction: '組替出荷指示登録',
        txtSogStatusReportInstruction: '出荷状況報告',
        txtSjgShipmentDecisionResultList: '判定結果一覧',
        txtSjgShipmentDecisionCandidateList: '出荷判定候補一覧',
        txtEifERPFileIntegrationHistory: 'ERPファイル連携履歴',
        txtEifLIMSFileIntegrationHistory: 'LIMSファイル連携履歴',
        txtEifWarehouseSystemFileIntegrationHistory:
          '倉庫システムファイル連携履歴',
        txtSignatureLogList: '署名ログ一覧',
        txtOperationLogList: '操作ログ一覧',
        txtSysSOPFlowExclusiveRelease: 'SOPフロー排他解除',
        txtSysDeviceExclusiveRelease: 'デバイス排他解除',
        txtSOPMasterCreationExclusiveRelease: 'SOPマスタ作成排他解除',
      },
    },
    Msg: {
      // メッセージ
      charactersNumber: '文字数{0},{1}',
      dateRange: '検索期間の範囲を確認してください。',
      dialogValidationError: '各入力項目下のエラー内容に従い、修正してください',
      halfAngle: '半角英数字を入力してください。',
      loginTimeout: 'タイムアウトしました、もう一度ログインしてください。',
      placeholderInput: '入力してください',
      pleaseInput: '入力してください',
      pleaseSelect: '選択してください',
      selectData: '日付を選択してください。',
      updateErr: '更新に失敗しました',
      updateSucceed: '更新は成功しました',
      deleteUploadFile: '{0}ファイルの削除',
      confirmDeleteUploadFile: 'サーバー上にあるファイルを削除しますか？',
      fileUploadError: 'ファイルアップロードエラー',
      uploadFileTypeError: '{0}ファイル以外はアップロードできません',
      uploadFileSizeError: '{0}MB以上のファイルはアップロードできません。',
      unselectedData: '１件データを選択してください',
      unselectedCheckBoxData: '１件以上のデータを選択してください',
      unselectedSingleData: '１件のデータを選択してください',
      selectedMaxData: '最大{0}件までしか選択できません。',
      dialogOnCancel:
        '入力内容に変更がありますが、キャンセルしてもよろしいですか？',
      titleFileDownloadConfirm: 'ダウンロード完了',
      contentFileDownloadConfirm: 'ダウンロード処理を完了しました。',
    },
  },
  Login: {
    // Login
    Chr: {
      // キャラクタ
      btnLogin: 'ログイン',
      btnLogining: 'ログイン中',
      txtLoginAccount: 'Login Account',
      txtAccountSave: 'アカウント保存',
      txtTitle: 'MP-Connect',
      txtPasswordChange: 'パスワード変更',
      txtUserName: 'ユーザー名',
      txtNewPassword: '新しいパスワード',
      txtChkPassword: '新しいパスワード(確認)',
      txtPasswordExpired:
        'パスワードの有効期限が過ぎています。\n新しいバスワードを設定してください。',
      txtNeedToChangePassword:
        'パスワードの変更が必要です。\n新しいパスワードを設定してください。 ',
    },
    Msg: {
      // メッセージ
      account: 'アカウント',
      loginError: 'ログイン失敗',
      loginIncorrect: 'ユーザーIDまたはパスワードが間違っています。',
      noLoginPrivilege: '権限がありません。',
      notEmptyUserIdAndPassword: 'ユーザー名とパスワードを入力してください',
      password: 'パスワード',
      passwordExpired:
        'パスワードの有効期限が切れました。パスワードを変更してください。',
      passwordNotNull: 'パスワードを入力してください',
      userLock: 'アカウントがロックされています。管理者へ連絡してください。',
      userNotNull: 'ユーザー名を入力してください',
      passwordMismatch:
        '新しいパスワードと新しいパスワード（確認）が異なります。',
      passwordModifyFinish: 'パスワード変更完了',
      passwordHasBeenChanged: 'パスワードの変更が完了しました。',
      returnToLogin: 'ログイン画面に戻りますか？',
      changePasswordRequest: 'パスワード変更依頼',
      needToChangePassword:
        '初期パスワードから変更されていません。パスワードを変更してください。',
      reLogin: '再ログイン',
      redirectedToLoginPage: 'ログイン画面に遷移しますか？',
    },
  },
  Navbar: {
    // Navbar
    Chr: {
      // キャラクタ
      txtLogOut: 'ログアウト',
    },
    Msg: {
      // メッセージ
      passwordError: 'パスワードが間違っています',
      toLogOut: 'ログアウトしますか。',
      titleToLogOut: 'Info',
    },
  },
  Pagination: {
    // Pagination
    Chr: {
      // キャラクタ
      txtAll: '全て',
      txtFirst: '最初',
      txtFirstTitle: '最初のページ',
      txtLast: '最終',
      txtLastTitle: '最終のページ',
      txtNext: '次',
      txtNextTitle: '次のページ',
      txtOf: '総計',
      txtPages: 'ページ',
      txtPageTitle: 'ページ表示',
      txtPrev: '前',
      txtPrevTitle: '前のページ',
      txtRows: '行',
      txtShowing: '表示',
    },
    Msg: {
      // メッセージ
    },
  },
  Cc: {
    // Cc
    Chr: {
      // キャラクタ
      btnSearch: '検索',
      returnDefault: 'デフォルトの復元',
      textConditionTitle: '絞り込み',
      txtConditionInputPlaceholder: '入力してください。',
      txtInputPredictTime: '入荷予定日',
      txtItemCode: '品目コード',
      txtDonotSearchDate: '日付検索しない',
      txtSearchDate: '日付検索をする',
      txtItemCodeSearch: '品目コードで検索',
      txtItemName: '品目名称',
      txtItemSearch: '品目で検索',
      txtSearchItem: '品目検索',
      txtselectData: '選択してください',
      txtAogDate: '入荷日',
    },
    Msg: {
      // メッセージ
    },
  },
  Tt: {
    // Tt
    Chr: {
      // キャラクタ
      txtApiResponseErr: '',
      txtNoDataSet: 'データなし',
      txtSearchNumber: '検索件数：{0} 件',
      txtSearchPeriodFrom: '検索期間(FROM)',
      txtSearchPeriodTo: '検索期間(TO)',
      txtSearchTime: '検索時間 {0}',
      txtSettingConditions: '検索条件設定中',
    },
    Msg: {
      // メッセージ
      selectData: 'データを選択してください。',
    },
  },
  Odr: {
    // 計画指図
    Chr: {
      // キャラクタ
      txtFilter: '絞り込み',
      txtOrderStartDate: '製造開始日',
      txtOrderStartDateFrom: '製造開始日(FROM)',
      txtOrderStartDateTo: '製造開始日(TO)',
      txtScheduleStatus: '小日程計画状態',
      txtScheduleStatusPlan: '未確定',
      txtScheduleStatusApproval: '確定',
      txtPlanProcedureNo: '計画番号',
      txtOrderNo: '製造指図番号',
      txtMaterial: '品目',
      txtLotNo: '製造番号',
      txtOrderStatus: '製造指図状態',
      txtOrderStatusCreateInit: '指図作成',
      txtOrderStatusCreateConfirm: '指図登録（承認待ち）',
      txtOrderStatusCreateAprove: '指図承認',
      txtOrderStatusCreateReplace: '指図差替',
      txtOrderStatusDiscontinueInit: '指図中止登録',
      txtOrderStatusDiscontinueAprove: '指図中止承認',
      txtOrderStatusRecordConfirm:
        '製造記録確認（全工程の確認完了後、承認待ち）',
      txtOrderStatusRecordAprove: '製造記録承認',
      txtOrderCategory: '指図分類',
      txtBulkMaterial: '中間製品品目',
      btnSearch: '検索',
      txtScheduleList: '小日程計画一覧',
      txtApprovedScheduleList: '確定小日程計画',
      txtScheduleNo: 'オーダー番号',
      txtMaterialName: '品名',
      txtBulkMaterialName: '中間製品名',
      txtOrderQuantity: '計画生産量',
      txtUnit: '単位',
      txtOrderDate: '製造開始予定日',
      txtLotReservationStatus: 'ロット予約状態',
      txtMaterialCode: '品目コード',
      txtBulkMaterialCode: '中間製品品目コード',
      txtDisplayLotNo: '製造番号',
      txtPrescriptionName: '処方名',
      txtManualFlag: '計画作成区分',
      txtPlanComment: '計画コメント',
      txtPrescriptionDeadlineStatus: '処方期限状態',
      btnPlanConfirm: '計画確定',
      btnPlanConfirmedCancellation: '計画確定取消',
      btnPlanChange: '計画変更',
      txtPlanChange: '計画変更',
      btnLotReservation: 'ロット予約',
      btnCancellation: '解除',
      btnPlanDelete: '計画削除',
      txtPlanDelete: '計画削除',
      txtPlanAddition: '計画追加',
      btnPlanImport: '計画取込',
      btnPlanAddition: '計画追加',
      btnOrderRegistration: '指図登録',
      btnPrescriptionChange: '処方変更',
      txtPrescriptionChange: '処方変更',
      txtDenyCommentSet: '否認コメント有無',
      txtAppendixCommentSet: '特別作業指示有無',
      txtEditionNoDeadlineStatus: '版番号期限状態',
      txtEditionNoChange: '版番号変更',
      txtPlanCommentSet: '計画コメント有無',
      btnOrderPlanDetail: '指図予定詳細',
      txtOrderRegistrationList: '製造指図登録',
      txtPrescriptionSelect: '処方選択',
      txtSelectedMaterial: '選択品目',
      txtPrescriptionList: '処方一覧',
      txtPrescriptionCode: '処方コード',
      txtDeadlineStartDate: '有効期限開始日',
      txtDeadlineEndDate: '有効期限終了日',
      btnPrescriptionSelect: '処方選択',
      txtMasterBatchRecordNo: 'MBR番号',
      txtPrescriptionDeadlineDate: '処方有効期限',
      txtLotDeadlineDate: '有効期限',
      txtStandardProductionQuantity: '標準生産量',
      txtLotReservation: 'ロット予約',
      txtScheduleInfo: '小日程計画情報',
      txtPrescriptionBillOfMaterialsList: '投入品一覧',
      txtPrescriptionBillOfMaterialsType: '投入品区分',
      txtPrescriptionBillOfMaterialsMaterialSequence: '投入番号',
      txtBillOfMaterialsCode: '構成品目コード',
      txtBillOfMaterialsName: '構成品名',
      txtStandardBillOfMaterialsQuantity: '標準仕込量',
      txtDesignLotQuantity: 'ロット予約数量',
      txtRawLotReservation: '原材料ロット予約',
      txtSelectedPrescriptionBillOfMaterials: '選択投入品',
      txtLotReservationList: 'ロット予約対象一覧',
      txtManageNo: '管理番号',
      txtAogDate: '入荷日',
      txtInventoryQuantity: '在庫量',
      txtDesignableLotQuantity: 'ロット予約可能数量',
      txtBulkLotReservation: '中間製品ロット予約',
      txtSelectedBulk: '選択中間製品',
      txtProductionDate: '製造日時',
      txtFileUploadGuide:
        '実行ボタンを選択することでファイルをアップロードします',
      btnFileSelect: 'ファイル選択',
      txtOrderRegistration: '指図登録',
      txtExpiryDate: '使用期限日',
      txtPrescriptionChangeComment: '処方変更コメント',
      txtHandOverComment: '指図コメント',
      txtAppendix: '特別作業指示',
      txtOrderPlanDetail: '指図予定詳細',
      txtOrderInformation: '製造指図情報',
      txtOrderProcessList: '製造工程一覧',
      txtOrderProcessSequence: '製造工程順',
      txtOrderProcess: '製造工程',
      txtBatchNo: 'バッチ番号',
      txtProcessMaterialName: '仕掛品名/工程品名',
      txtStandardBatchTimes: '標準バッチ回数',
      txtDenyComment: '否認コメント',
      btnBillOfMaterialsPlanDetail: '構成品詳細',
      btnSOPPlanDetail: 'SOPフロー',
      txtBillOfMaterialsPlanDetail: '構成品予定詳細',
      txtSOPPlanDetail: 'SOP予定詳細',
      txtEditionManagementType: '版管理種別',
      txtHasEdtion: '版指定',
      txtBatchWeight: 'バッチ指示重量',
      txtEditionNo: '版番号',
      btnDeleteEditionChange: '版変更解除',
      btnEditionChange: '版変更',
      txtEditionChange: '版変更',
      txtCurrentEditionNo: '現版番号',
      txtEditionChangeDate: '新版番号切替日',
      txtSelectEditionNo: '指定版番号',
      txtOrderScheduleDate: '製造予定日',
      txtSOPFlowName: 'SOPフロー名称',
      txtBatchDeploymentType: 'バッチ展開種別',
      txtSOPWorkDetail: 'SOP作業詳細',
      txtOrderState: '指図状態',
      txtOrderStopFlag: '指図中止フラグ',
      txtOrderSceduleNumber: '指図予定数',
      txtOrderScheduleEndDate: '製造終了予定日',
      txtExpiryData: '使用期限',
      txtOrderComment: '指図コメント有無',
      btnOrderDetail: '指図詳細',
      txtOrderDetail: '指図詳細',
      txtBatchCount: 'バッチ回数',
      txtPlanneProductionVolume: '予定生産量',
      txtScheduleProductionStartDate: '生産開始予定日',
      txtScheduleProductionEndDate: '生産終了予定日',
      btnSOPDetail: 'SOP詳細',
      txtSOPDetail: 'SOP詳細',
      btnComponentDetail: '構成品詳細',
      btnOrderInstruction: '製造指図書',
      btnInstructionCommentEntry: '指図コメント入力',
      btnWeightOrderDetail: '秤量指示明細',
      txtPlanneStockQuantity: '予定仕込量',
      txtSOPFlowState: 'SOPフロー状態',
      txtSOPFlowStartDate: 'SOPフロー開始日時',
      txtSOPFlowEndDate: 'SOPフロー終了日時',
      txtSOPFlowSchedule: 'SOPフロー予定',
      txtOrderStateList: '指図状態一覧',
      txtSOPFlow: 'SOPフロー',
      txtSOPFlowList: 'SOPフロー一覧',
      btnScheduledDate: '製造番号発番',
      txtOrderScheduleQuantity: '製造予定数',
      txtUseExpiryDate: '使用期限起算日',
      txtEffectiveDate: '有効期限日',
      txtEffectiveCalcDate: '有効期限起算日',
      txtLotNoIssuanceType: '製造番号発番区分',
      txtExpiryDateType: '使用期限区分',
      txtExpiryDateCalculationType: '使用期限起算日区分',
      txtShelfLifeType: '有効期限区分',
      txtShelfLifeCalculationType: '有効期限起算日区分',
      txtLotNoAndIssuanceType: '製造番号(製造番号発番区分:{0})',
      txtExpiryDateAndType: '使用期限日(使用期限区分:{0})',
      txtUseExpiryDateAndCalculationType:
        '使用期限起算日(使用期限起算日区分:{0})',
      txtEffectiveDateAndType: '有効期限日(有効期限区分:{0})',
      txtEffectiveCalcDateAndCalculationType:
        '有効期限起算日(有効期限起算日区分:{0})',
      txtWeightOrderDetailNumber: '秤量指示明細番号',
      txtWeightOrderDetailState: '秤量指示明細状態',
      txtWeightSet: '秤量セット',
      txtWeightSetKey: '秤量セットキー',
      txtWeightRoom: '秤量室',
      txtWeightMethod: '秤量方法',
      txtWeightMaterialCode: '秤量品目コード',
      txtWeightMaterialname: '秤量品名',
      txtWeightMaterialInputOrder: '秤量品投入番号',
      txtOrderList: '製造指図一覧',
      txtOrderListAfterRegistration: '製造指図一覧（指図登録後指図承認前）',
      txtOrderListApproved: '製造指図一覧（指図承認済）',
      txtOrderApproval: '製造指図承認',
      btnOrderDisapproval: '指図否認',
      txtPrescriptionEffectiveDate: '処方有効期限',
      btnReBatchOrder: '再バッチ指示',
      btnOrderCancel: '指図取消',
      btnOrderStop: '指図中止',
      txtReBatchOrderRegistration: '再バッチ指示登録',
      txtOrderPlanProductionQuantity: '生産予定数量',
      txtOrderProductionRecord: '製造記録確認',
      txtOrderQuantityAdjustmentType: '製造指図量調整種別',
      txtReRunSOPFlowSelect: '再実行SOPフロー選択',
      txtBatchExpansion: 'バッチ展開',
      txtOdrQty: '製造指図量',
      btnAppendix: '特別作業指示',
      btnReBatchOrderRegistration: '再バッチ指示登録',
      txtComponentDetail: '構成品詳細',
      txtWeightOrderDetail: '秤量指示明細',
      txtInstructionCommentEntry: '指図コメント入力',
      btnSOPFlowSelect: 'SOPフロー選択',
      txtOrderDisapproval: '指図否認',
      txtOrderStop: '指図中止',
      txtOrderCancel: '指図取消',
      txtPlanImport: '計画取込',
      btnCSVImport: 'CSVインポート',
      txtOdrSkdCsvMesId: 'MESID',
      txtOdrSkdCsvMaterialOrderNo: '品目オーダNo',
      txtOdrSkdCsvMesMaterialCode: 'MES品目コード',
      txtOdrSkdCsvOrderLocationCode: '製造場所コード',
      txtOdrSkdCsvStorageLocationCode: '格納場所コード',
      txtOdrSkdCsvUnitCode: '単位コード',
      txtOdrSkdCsvOrderSceduleNumber: '指図予定数',
      txtOdrSkdCsvStartScheduleDate: '着手予定日',
      txtOdrSkdCsvAvailableScheduleDate: '使用可能予定日',
      txtOdrSkdCsvArrgtNo1: '手配No１（納入月）',
      txtOdrSkdCsvArrgtNo2: '手配No２（連番（製剤単位））',
      txtOdrSkdCsvArrgtNo3: '手配No３（製剤ロットNo）',
      txtOdrSkdCsvArrgtNo4: '手配No４（製剤を取り分ける優先順）',
      txtOdrSkdCsvRemarks: '備考',
      btnSOPFlow: 'SOPフロー',
      txtComponentList: '構成品一覧',
      txtOrderProcessListByBatch: '製造工程一覧（バッチ別）',
      txtScheduleDetail: '小日程計画詳細',
      txtOrderInstructionValue: '指図指示量',
      txtWeightTiterKey: '補正計算',
    },
    Msg: {
      // メッセージ
      titlePlanConfirm: '小日程計画確定',
      contentPlanConfirm: '選択した小日程計画の状態を確定にしますか？',
      titlePlanConfirmedCancellation: '小日程計画確定取消',
      contentPlanConfirmedCancellation:
        '選択した小日程計画の状態を未確定にしますか？',
      titleMaterialUnselect: '品目未選択',
      contentMaterialUnselect: '品目を選択してください。',
      titleOrderDateNoInput: '製造開始予定日未入力',
      contentOrderDateNoInput: '製造開始予定日を入力してください。',
      titlePlanAddition: '小日程計画追加',
      contentPlanAddition: '小日程計画を追加しますか？',
      titlePlanUpdate: '小日程計画更新',
      contentPlanUpdate: '小日程計画を更新しますか？',
      titlePlanDelete: '小日程計画削除',
      contentPlanDelete: '小日程計画を削除しますか？',
      titleLotCancellation: 'ロット予約解除',
      contentLotCancellation: '選択した構成品のロット予約を全て解除しますか？',
      titleLotReservation: 'ロット予約',
      contentLotReservation: '選択したロットを予約しますか？',
      titleLotNoIssuance: '製造番号発番エラー',
      contentLotNoIssuance: '製造番号発番を実施してください。',
      titleRegistOrder: '製造指図登録',
      contentRegistOrder: '製造指図を登録しますか？',
      titlePrescriptionEdit: '小日程計画処方変更',
      contentPrescriptionEdit: '小日程計画の処方を変更しますか？',
      titleEditionChangeDelete: '版変更解除',
      contentEditionChangeDelete: '選択した構成品の版変更を解除しますか？',
      titleEditionChangeRegistration: '版変更登録',
      contentEditionChangeRegistration:
        '入力した版番号で版変更を登録しますか？\n※実行するにはコメント入力が必要です。',
      titleOrderInputOrderHandOverText: '製造指図コメント更新',
      contentOrderInputOrderHandOverText: '製造指図コメントを更新しますか？',
      titleOrderAppendix: '特別作業指示登録',
      contentOrderAppendix:
        '選択した製造指図に特別作業指示を登録しますか？\n※実行するにはコメント入力が必要です。',
      contentOrderAppendixUploadedFileSelected:
        'アップロード済のファイルはアップロードできません',
      titleModifyOrderApproval: '製造指図承認',
      contentModifyOrderApproval: '選択した製造指図を承認しますか？',
      titleModifyOrderDenial: '製造指図否認',
      contentModifyOrderDenial:
        '製造指図を否認しますか？\n※実行するにはコメント入力が必要です。',
      titleAddOrderRebatchInstruction: '再バッチ指示登録',
      contentAddOrderRebatchInstruction:
        '選択したSOPフローで再バッチ指示登録をしますか?\n※実行するにはコメント入力が必要です。',
      titleModifyOrderDiscontinue: '製造指図中止',
      contentModifyOrderDiscontinue:
        '製造指図を中止しますか？\n※実行するにはコメント入力が必要です。',
      titleModifyOrderErase: '製造指図取消',
      contentModifyOrderErase:
        '製造指図を取消しますか？\n※実行するにはコメント入力が必要です。',
      titleOrderInputScheduleFile: '計画取込',
      contentOrderInputScheduleFile:
        '選択したCSVファイルから計画取込をしますか。',
      titleNotInputSkdYmd: '着手予定日未入力エラー',
      contentNotInputSkdYmd: '{0}行目に着手予定日が入力されていません。',
      titleFormatErrorSkdYmd: '着手予定日フォーマットエラー',
      contentFormatErrorSkdYmd:
        '{0}行目の着手予定日が8桁数値文字列で入力されていません。',
      titleNotInputMesMatNo: 'MES品目コード未入力エラー',
      contentNotInputMesMatNo: '{0}行目にMES品目コードが入力されていません。',
      titleCsvNotImported: 'CSV未選択エラー',
      contentCsvNotImported: 'CSVがインポートされていません。',
      titleOrderInstructionErrorUnapproved: '製造指図未承認',
      contentOrderInstructionErrorUnapproved:
        '選択中の製造指図が未承認のため製造指図書はダウンロードできません。',
      titleOrderInstructionConfirm: '製造指図書ダウンロード',
      contentOrderInstructionConfirm: '製造指図書をダウンロードしますか？',
      titleOrderInstructionFinished: '製造指図書ダウンロード完了',
      contentOrderInstructionFinished:
        '製造指図書のダウンロードが完了しました。',
    },
  },
  Aog: {
    // 入荷管理
    Chr: {
      // キャラクタ
      txtAogPlanList: '入荷予定一覧',
      txtMatNoNm: '品目コード/品名',
      txtAogNumber: '入荷予定番号',
      txtAogDate: '入荷予定日',
      txtAogDateFrom: '入荷予定日(FROM)',
      txtAogDateTo: '入荷予定日(TO)',
      txtAogYmdFrom: '入荷日(FROM)',
      txtAogYmdTo: '入荷日(TO)',
      txtPurchaseDetailOrderNumber: '発注明細番号',
      txtItemCode: '品目コード',
      txtItemName: '品名',
      txtErpAogAmount: 'ERP入荷予定数量',
      txtErpUnit: 'ERP単位',
      txtMesAogAmount: 'MES入荷予定数量',
      txtMesUnit: '単位',
      txtBusinessPartnerCode: 'メーカーコード',
      txtBusinessPartnerName: 'メーカー名',
      txtBusinessPartner: 'メーカー',
      txtBusinessPartnerLotNumber: 'メーカーロット番号',
      txtEditionNumber: '版番号',
      txtLotNumber: '管理予定番号',
      txtPurchaseOrderAvailableToPromiseNumber: '納期回答番号',
      txtPurchaseDetailOrderExpl: '発注明細備考',
      txtPurchaseOrderAvailableToPromiseExpl: '納期回答備考',
      txtAttachments: '添付資料',
      txtAogExpl: '入荷予定コメント',
      txtAogInstructionExpl1: '作業指示コメント1',
      txtAogInstructionExpl2: '作業指示コメント2',
      txtAogInstructionExpl3: '作業指示コメント3',
      txtAogInstructionStatus: '入荷指示作成状態',
      txtUploadFiles: 'PDF添付',
      txtAogQuantity: '入荷予定数量',
      txtAogInstructionUnissue: '入荷指示未発行',
      txtAogPlanDelete: '入荷予定削除',
      txtBeforeAogPlan: '入荷指示前',
      btnAogPlanDelete: '入荷予定削除',
      btnAogPlanCopy: 'コピー追加',
      btnAogPlanUpdate: '入荷予定修正',
      btnAogDetail: '入荷予定詳細',
      btnAogInstructionCreate: '入荷指示作成',
      txtAogPlanDetail: '入荷予定を新規追加します。',
      btnColumnEdit: 'カラム編集',
      txtAogInstNoList: '入荷検品済み入荷指示明細一覧',
      txtAogInstNo: '入荷指示明細番号',
      txtAogInstGrpNo: '入荷指示番号',
      txtAogInstGrpPrtDts: '入荷指示作成日',
      txtAogYmd: '入荷日',
      txtLotNo: '管理番号',
      txtExpiryDtsGrp: '使用期限算出区分',
      txtExpiryDts: '使用期限入力日',
      txtShelfDtsGrp: '有効期限算出区分',
      txtShelfDts: '有効期限入力日',
      txtAogPlanAttExistNm: '添付資料',
      txtAogPltCnt: '必要パレット数',
      txtIoaQty: '検品済入荷数量',
      txtRsltPckExpl: '入荷検品コメント',
      txtPickPlt: '検品済パレット数',
      txtLiftPltCnt: '貼付確認済パレット数',
      txtStoringPltCnt: '入庫済パレット数',
      txtCarText1: '確認内容1',
      txtCarRslt1: '確認結果1',
      txtCarText2: '確認内容2',
      txtCarRslt2: '確認結果2',
      txtCarText3: '確認内容3',
      txtCarRslt3: '確認結果3',
      txtCarText4: '確認内容4',
      txtCarRslt4: '確認結果4',
      txtCarText5: '確認内容5',
      txtCarRslt5: '確認結果5',
      txtCarText6: '確認内容6',
      txtCarRslt6: '確認結果6',
      txtCarText7: '確認内容7',
      txtCarRslt7: '確認結果7',
      txtCarText8: '確認内容8',
      txtCarRslt8: '確認結果8',
      txtCarText9: '確認内容9',
      txtCarRslt9: '確認結果9',
      txtCarText10: '確認内容10',
      txtCarRslt10: '確認結果10',
      txtCarText11: '確認内容11',
      txtCarRslt11: '確認結果11',
      txtCarText12: '確認内容12',
      txtCarRslt12: '確認結果12',
      txtCarText13: '確認内容13',
      txtCarRslt13: '確認結果13',
      txtCarText14: '確認内容14',
      txtCarRslt14: '確認結果14',
      txtCarText15: '確認内容15',
      txtCarRslt15: '確認結果15',
      txtCarText16: '確認内容16',
      txtCarRslt16: '確認結果16',
      txtCarText17: '確認内容17',
      txtCarRslt17: '確認結果17',
      txtCarText18: '確認内容18',
      txtCarRslt18: '確認結果18',
      txtCarText19: '確認内容19',
      txtCarRslt19: '確認結果19',
      txtCarText20: '確認内容20',
      txtCarRslt20: '確認結果20',
      txtCarExpl: '入荷時検品記録コメント',
      txtGradeAndSupplierListCheck: '成績書・供給者リストの確認',
      txtOuterBag: '外袋確認',
      txtLotback: 'ロットバックの確認',
      txtDeliveryNoteCheck: '納品書確認',
      txtTestReportCheck: '試験成績書の確認',
      txtComment: 'コメント',
      txtAogCarSts: '入荷時検品記録',
      txtPalletCnt: 'パレット数',
      btnAogCarInput: '検品記録入力',
      txtAogPlanDeleteFinish: '入荷予定削除完了',
      txtAogDetails: '入荷予定詳細',
      txtAogInstructionCreate: '入荷指示書作成',
      txtAogInstructionCreateFinish: '入荷指示作成完了',
      txtAogDatePastDataCheck: '入荷予定日の過去日チェックエラー',
      btnAogPlanAdd: '入荷予定作成',
      txtAogCopyArrivalPlan: '入荷予定コピー追加',
      txtAogPlanAddConfirm: '入荷予定作成',
      txtAogEditArrivalPlan: '入荷予定修正',
      txtInspectionRecordItems: '検品記録項目',
      txtAogCarStsInput: '入荷時検品記録入力',
      btnCheckCancel: '確認取消',
      btnAogCarStsCheck: '検品記録確認',
      btnAogArrivalInstructionDetails: '指示明細詳細',
      btnAogInstructionGroupPrint: '入荷指示印刷',
      btnAogCancelArrivalInstruction: '入荷指示取消',
      btnAogIaoFree: '検品継続解除',
      btnAogForceCompleteArrivalInspection: '検品強制完了',
      btnAogCancelArrivalInspectionComplete: '検品完了取消',
      txtAogInstSts: '入荷ステータス',
      txtAogPickSts: '入荷検品作業状況',
      txtIoaHtUsr: '入荷検品作業者',
      txtAogPpq: '正袋重量',
      txtAogBaleUnit: '標準入れ目',
      txtAogVar: '入り数割れ数',
      txtPltFullQty: '倉庫パレット満載数',
      txtPltFullBalePtnNm: '倉庫パレット積載パターン',
      txtMesZoneNm: 'デフォルト入庫ゾーン',
      txtLocGrp: '入庫可能ロケーショングループ',
      txtAogArrivalInstructionDetails: '入荷指示明細',
      txtAogForceArrivalInspection: '入荷検品強制完了',
      txtAogCancelArrivalInspectionComplete: '入荷検品完了取消',
      txtAogRecord: '入荷実績',
      txtPltList: 'パレット一覧',
      txtPltNo: 'パレット番号',
      txtQty: '数量',
      txtPltIoaStsNm: '入荷検品ステータス',
      txtZoneNm: '入庫先ゾーン名',
      txtLocNm: '入庫先ロケーション名',
      txtAogRecordAmountCheck: '入荷実績量チェック',
      txtAogInstStsCd: '入荷指示明細ステータス',
      txtAogInstStsNm: '入荷指示明細ステータス名',
      txtExpiryDspTxt: '使用期限算出区分',
      txtExpiryDspData: '使用期限入力日',
      txtShelfLifeDspTxt: '有効期限算出区分',
      txtShelfLifeData: '有効期限入力日',
      txtAttBinNm: '添付資料有無',
      txtAogCarStsNm: '入荷時検品記録',
      txtRsltQty: '受入実績報告入荷数量',
      txtRsltExpl: '受入実績報告コメント',
      txtAogConfirmReceiptRecord: '入荷指示明細一覧(受入実績確定)',
      txtAogEditReceiptRecord: '受入実績修正',
      txtAogReceiptRecordDetails: '受入実績詳細',
      txtAogEditContentInput: '修正内容入力',
      txtAogQty: '入荷数量',
      txtEffectiveCalcDate: '有効期限起算日',
      txtExpirationCalcDate: '使用期限起算日',
      txtEffectiveDateAuthorizedProducts: '有効期限起算日（メーカー製造日）',
      txtExpirationDateAuthorizedProducts: '使用期限起算日（メーカー製造日）',
      txtStoringComplete: '入庫完了',
      btnAogMod: '受入実績修正',
      btnAogRslt: '受入実績確定',
      txtDifference: '相違有',
      txtNoDifference: '相違無',
      txtAogInstListAogCarStsBefore: '入荷指示明細一覧(入荷検品記録前)',
      txtAogInstListAogCarStsAfter: '入荷指示明細一覧(入荷時検品記録済)',
      txtAogLblCnt: 'ラベル数',
      txtAogPltModExpl: '積載情報修正コメント',
      btnMod: '積載情報修正',
      btnModLabel: '積載数修正',
      btnPrintLabel: 'ラベル発行',
      txtAogPltMod: 'パレット積載情報修正',
      txtAogPlt: 'パレット情報',
      txtAogEditLabel: 'パレット積載数修正',
      txtAogPltInfo: 'パレット積載情報',
      txtBaleUnit: '個装重量',
      txtBaleCnt: '個装数',
      txtAogPltModInto: '修正内容入力',
      txtAogPltIoaQty: 'パレット積載数量',
      txtConfirmed: '確認済',
    },
    Msg: {
      // メッセージ
      deleteMessageConfirm: '入荷予定を削除します。よろしいですか？',
      aogInstructionCreateConfirm: '入荷指示書を作成します。よろしいですか？',
      editAogArrivalPlanCorrectionConfirm:
        '入荷予定を修正します。よろしいですか？',
      copyAogArrivalPlanCorrectionConfirm:
        '入荷予定を作成します。よろしいですか？',
      aogForceCompleteArrivalInspection:
        'ハンディの入荷検品の作業を強制的に完了にします。よろしいですか？',
      aogCancelCompleteArrivalInspection:
        'ハンディで入荷検品が継続可能な状態に戻します。よろしいですか？',
      aogCompleteCheckError: '入庫完了チェックエラー',
      titleAogRsltCarInputConfirm: '入荷時検品記録入力',
      contentAogRsltCarRegistrationConfirm:
        '入荷時検品記録を登録します。よろしいですか？',
      titleAogRsltCarCheckCancelConfirm: '入荷時検品記録確認取消',
      contentAogRsltCarCheckEraseConfirm:
        '入荷時検品記録の確認済を取消します。よろしいですか？',
      titleAogRsltCarCheckConfirm: '入荷時検品記録確認',
      contentAogRsltCarCheckConfirm:
        '入荷時検品記録を確認済します。よろしいですか？',
      aogCompleteCannotCancel: '入庫が完了しているため完了取消できません。',
      titleAogConfirmReceiptRecordConfirm: '受入実績確定',
      contentAogConfirmReceiptRecordConfirm:
        '受入実績を確定します。よろしいですか？',
      contentAogConfirmReceiptRecordWarningConfirm:
        '品質状態・期限変更機能で使用期限または有効期限の入力が必要な入荷指示明細が選択されています。\n受入実績を確定します。よろしいですか？',
      aogEditReceiptRecordConfirm: '受入実績を修正します。よろしいですか？',
      checkAogRecordWaring:
        '入荷実績量と入荷予定量に差異があります。コメントを入力してください。\n入荷予定量：{0}{1}\n入荷実績量：{2}{3}',
      titleAogInstructionGroupPrintConfirm: '入荷指示書ダウンロード',
      contentAogInstructionGroupPrintConfirm:
        '入荷指示書をダウンロードします。よろしいですか？',
      titleAogInstructionGroupCancelConfirm: '入荷指示取消',
      contentAogInstructionGroupCancelConfirm:
        '入荷指示を取消します。よろしいですか？',
      titleAogIaoFreeConfirm: '検品継続解除',
      contentAogIaoFreeConfirm:
        '検品継続を解除し、検品が再開できる状態にします。\nよろしいですか？',
      titleAogPrint: 'ラベル発行',
      contentAogPrintCheck:
        '貼付確認と原材料のラベルを発行します。よろしいですか？',
      titleAogEditLabel: 'パレット積載数修正',
      contentAogEditLabelMessage: '修正内容を反映します。よろしいですか？',
      titleAogIoaQtyError: 'パレット満載量超過チェックエラー',
      contentAogIoaQtyErrorMessage: 'パレット満載量を超過しています。',
      titleAogPalletModCheck: 'パレット積載情報修正',
      contentAogPalletModCheckMessage:
        'パレット積載情報を修正します。よろしいですか？',
      titleAogArrRecord: '入荷実績量チェック',
      titleAogPlanDeleteError: '入荷指示作成済チェックエラー',
      contentAogPlanDeleteErrorMessage:
        '入荷指示が作成されているため削除できません。',
    },
  },
  Sjg: {
    // 出荷判定
    Chr: {
      // キャラクタ
      txtVerifySts: '照査状態',
      txtMatNoNm: '品目コード/品名',
      txtMatNo: '品目コード',
      txtMatNm: '品名',
      txtLotNo: '製造番号',
      txtShtNm: '略号',
      txtPlanYmd: '出荷予定日',
      txtShelfLifeYmd: '有効期限',
      txtExpiryYmd: '使用期限',
      txtLotoutFlg: 'ロットアウト品',
      txtManufacturingControlInspection: '製造管理照査',
      txtConfirmQualityControlInspection: '品質管理照査確認',
      txtGMPConfirmation: 'GMP確認',
      txtRsltYmd: '出来高日',
      txtManageNo: '製造番号',
      txtLotout: 'ロットアウト',
      txtVerifyCompFlg: '照査完了有無',
      txtInspectionInfo: '照査情報',
      txtInspectionMatInfo: '照査品目情報',
      txtSelectInspectionItem: '照査項目選択',
      txtInspectionInfoPoint: '照査未完了の構成品が含まれています。',
      txtVerifyCat: '照査項目',
      txtVerifyCatSts: '照査状態',
      txtVerifyUsrNm: '照査者',
      txtVerifyDts: '照査日時',
      txtVerifyStsInitial: '未実施',
      txtVerifyStsStart: '照査中',
      txtVerifyStsFinish: '照査完了',
      txtVerifyUnfinished: '照査未完了',
      txtVerifyFinish: '照査完了済',
      txtOnlylotout: 'ロットアウト品のみ',
      txtExceptlotout: 'ロットアウト品除く',
      txtInfoItem: '品目情報',
      txtInspectionHistoryItem: '製造記録照査項目',
      txtInspectionHistory: '製造記録照査',
      txtInspectionHistoryConfirmation: '製造記録照査(確認)',
      txtConfirmQualityRecordInspection: '品質記録照査確認',
      txtConfirmQualityRecordInspectionConfirmation: '品質記録照査確認(確認)',
      txtGMPConfirmationDeviationHandling: 'GMP確認(逸脱処理)',
      txtGMPConfirmationDeviationHandlingConfirmation:
        'GMP確認(逸脱処理)(確認)',
      txtGMPConfirmationValidation: 'GMP確認(バリデーション)',
      txtGMPConfirmationValidationConfirmation: 'GMP確認(バリデーション)(確認)',
      txtGMPConfirmationChangeManagement: 'GMP確認(変更管理)',
      txtGMPConfirmationChangeManagementConfirmation: 'GMP確認(変更管理)(確認)',
      txtGMPConfirmationSpecialTask: 'GMP確認(特別作業)',
      txtGMPConfirmationSpecialTaskConfirmation: 'GMP確認(特別作業)(確認)',
      txtGMPConfirmationMaterialRevision: 'GMP確認(資材改版)',
      txtGMPConfirmationMaterialRevisionConfirmation: 'GMP確認(資材改版)(確認)',
      txtConfirmInspectionCompletion: '照査完了確認',
      txtVerifyGmpRslt3: '逸脱処理',
      txtVerifyGmpRslt4: 'バリデーション',
      txtVerifyGmpRslt5: '変更管理',
      txtVerifyGmpRslt6: '特別作業',
      txtVerifyGmpRslt7: '資材改版',
      txtGmpInfoList: 'GMP確認内容一覧',
      txtMngNo: '管理番号',
      txtGmpTitle: '件名',
      txtGmpCaseNm: '件目',
      txtGmpDes: '説明',
      txtGmpInactiveFlg: 'GMP確認内容非活性フラグ',
      btnVerify: '照査',
      btnConfirmInspectionContent: '照査内容確認',
      btnInspectionOperation: '照査実行',
      btnInspectionResults: '照査結果',
      btnInspectionCancel: '照査取消',
      txtSjgInspectionResultsDetails: '照査結果詳細',
      txtGMPConfirmationResults: 'GMP確認結果',
      txtGMPInformationResults: 'GMP確認結果詳細',
      txtVerifyOdrRslt: '照査結果',
      txtVerifyUserName: '照査・確認者',
      txtVerifyYmd: '照査・確認日',
      txtVerifyYmdhs: '照査・確認日時',
      txtVerifyExpl: '照査・確認コメント',
      txtVerifyGmpRslt: '結果有無',
      txtVerifyGmpDocNeedType: '出荷可否決定書記載',
      txtGMPMngNo: 'GMP管理番号',
      txtGMPTitle: '件名',
      txtGMPDes: 'コメント',
      txtVerifyCat1: '製造記録照査',
      txtVerifyCat2: '品質記録照査確認',
      txtVerifyCat3: 'GMP逸脱処理',
      txtVerifyCat4: 'GMPバリデーション',
      txtVerifyCat5: 'GMP変更管理',
      txtVerifyCat6: 'GMP特別作業',
      txtVerifyCat7: 'GMP資材改版',
      txtVerifyCat8: '照査完了確認',
      verifyOdrRsltIN: '未判定',
      verifyOdrRsltNV: '照査未実施',
      releaseJudgeRouteN: '照査・出荷判定なし',
      releaseJudgeRouteV: '照査のみ',
      releaseJudgeRouteM: '照査＋出荷判定（工場出荷のみ）',
      releaseJudgeRouteQ: '照査＋出荷判定（工場出荷・市場出荷）',
      txtInspectionCompletion: '照査完了',
      txtConfirmCompletion: '確認完了',
      txtQualityRecordInspectionConfirmation: '品質記録照査確認',
      txtQualityRecordInspectionInfo: '品質記録照査情報',
      txtVerifyYmdSimple: '照査日',
      txtVerifyExplComment: 'コメント',
      txtCarRslt: '確認結果',
      txtPass: '適',
      txtFail: '不適',
      txtSjgManufacturingInstructionRecordConfirmation: '製造指図記録確認',
      txtSjgManufacturingInstructionRecordConfirmationConfirmation:
        '製造指図記録確認(確認)',
      txtSjgManufacturingInstructionRecord: '製造指図記録',
      txtOdrNo: '製造指図番号',
      txtBomModList: '投入修正履歴',
      txtPrdModList: '出来高修正履歴',
      txtSopModList: 'SOP修正履歴',
      txtDevModList: '異状履歴',
      txtDevList: '異状有無',
      txtOrderRecordApprovalDates: '製造記録承認日時',
      txtOrderRecordApprovalUser: '製造記録承認者',
      txtOrderRecordApprovalExpl: '製造記録承認コメント',
      txtManufacturingInstructionRecordDetails: '製造指図記録詳細',
      txtCmtTimes: '修正回数',
      txtMatNoDetail: '品目CD',
      txtDisplayNameJp: '投入品名',
      txtUnitNmJp: '単位',
      txtBeforeRecordValue: '修正前記録値',
      txtAfterRecordValue: '修正後記録値',
      txtBRecVal2: '修正前廃棄量',
      txtBRecVal3: '修正前廃棄量内訳1',
      txtBRecVal4: '修正前廃棄量内訳2',
      txtBRecVal5: '修正前廃棄量内訳3',
      txtARecVal2: '修正後廃棄量',
      txtARecVal3: '修正後廃棄量内訳1',
      txtARecVal4: '修正後廃棄量内訳2',
      txtARecVal5: '修正後廃棄量内訳3',
      txtRecordModifyDate: '記録修正日時',
      txtBeforeRecordValue1: '修正前記録値1',
      txtAfterRecordValue1: '修正後記録値1',
      txtBeforeRecordValue2: '修正前記録値2',
      txtAfterRecordValue2: '修正後記録値2',
      txtBeforeRecordValue3: '修正前記録値3',
      txtAfterRecordValue3: '修正後記録値3',
      txtBeforeRecordValue4: '修正前記録値4',
      txtAfterRecordValue4: '修正後記録値4',
      txtBeforeRecordValue5: '修正前記録値5',
      txtAfterRecordValue5: '修正後記録値5',
      txtModifyUser: '修正者',
      txtComment: '修正コメント',
      txtSopFlowNm: 'SOPフロー名称',
      txtRxNmJp: '処方',
      txtSopFlowNmDetail: 'SOPフロー名',
      txtInstValDetail: '指示値',
      txtRecVal1: '記録値',
      txtThValType: '判定種別',
      txtThValLlmt: '異状値下限',
      txtThValUlmt: '異状値上限',
      txtEdDtsDetail: '異状発生日時',
      txtModUsr: '確認者',
      txtConfirmDate: '確認日時',
      txtDevExpl: '異状コメント',
      thValTyp1: '絶対値',
      thValTyp2: '相対値',
      thValTyp3: '相対値(%)',
      txtDspNmJp: '出来高品名',
      txtWorkInstructionDetail: '作業指示内容',
      txtDeviationLevel: '異状レベル',
      txtSjgConfirmInspectionCompletion: '照査完了確認',
      txtReWeightDownload: '秤量記録書DL',
      txtOrderDownload: '製造記録書DL',
      txtShipmentDecisionResultsList: '出荷判定結果一覧',
      txtProductName: '製品名',
      txtDecisionResults: '判定結果',
      txtShipmentDecisionComment: '出荷判定コメント',
      txtDecisionReportDl: '決定書DL',
      txtDecider: '判定者',
      txtDecisionYmd: '判定日',
      txtUnit: '単位',
      txtQuantity: '数量',
      txtShipmentYes: '出荷可',
      txtShipmentNo: '出荷否',
      txtUsrNm: '照査完了確認者',
      txtSjgConfirmInspectionResults: '出荷判定結果入力',
      txtSjgInspectionResults: '照査結果',
      txtlotProductNo: '製品番号',
      txtSjg: '出荷判定',
      btnSjgConfirm: '出荷判定確定',
      txtVerifyCompleteYmd: '照査完了日',
      txtSjgInformation: '出荷判定品目情報',
      txtmanufactureQualityManageResult: '製造管理および品質管理の結果',
      txtDeviationHaveOrNotHandleContent: '逸脱の有無と措置の内容',
      txtChangeManageHaveOrNot: '変更管理の有無と措置の内容',
      txtPlantShipmentDecisionResult: '工場出荷可否判定結果',
      txtConfirmSjgCorrectlyImplemented: '適切に出荷判定が実施された確認',
      txtManufacturingShipmentDecisionResult: '製造所からの出荷判定結果',
      txtDeviationMarketDeliveryProcedure: '市場出荷手順からの逸脱の有無',
      txtInstructionsQualityDirectorDeviation: '逸脱に関する品質責任者の指示',
      txtInformationMaterialsProductQuality:
        '原材料および製品の品質等に関する情報',
      txtTestReport: '試験成績書',
      txtMarketShipmentDecisionResult: '市場出荷可否判定結果',
      txtOK: 'OK',
      txtNG: 'NG',
      txtOrderRecord: '製造指図記録',
      txtSopRecord: 'SOP記録',
      txtOrderBomInfo: '構成品情報',
      txtOrderBomLotListInfo: '構成品一覧（ロット単位）',
      txtVerifyNotConfirm: '未確認',
      txtVerifyConfirmed: '確認済',
      txtConfirmItem: '確認項目',
      txtConfirmSts: '確認状態',
      btnOrderRecord: '製造指図記録',
      btnSopRecord: 'SOP記録',
      btnOrderBomInfo: '構成品情報',
      btnInspectionCompletion: '照査完了',
      txtCannot: '不可',
      txtHave: '有',
      txtNotHave: '無',
      txtCan: '可',
      txtHavePass: '有(適)',
      txtNotHaveFail: '有(不適)',
      txtHaveKana: 'あり',
      txtNotHaveKana: 'なし',
      txtSjgSOPRecordConfirmation: 'SOP記録確認',
      txtSjgSOPRecordConfirmationConfirmation: 'SOP記録確認(確認)',
      txtSjgSOPRecord: 'SOP記録',
      txtSjgSOPRecordConfirmationSupplement:
        '異状登録（レベル１～３）されたSOPノード記録、SOPマスタにて確認必須チェックがONに設定したSOPノード記録、コメントが存在するSOPノード記録が含まれているSOPノードを表示します。',
      txtBatchNo: 'バッチNo',
      txtSopNodeTimes: '実行数',
      txtCmtMain: '作業指示内容',
      txtInstVal: '指示内容',
      txtRecVal: '記録',
      txtEdDts: '記録日時',
      txtRecUsr: '記録者',
      txtDcheckVal: 'D記録',
      txtDcheckEdDts: 'D記録日時',
      txtDcheckUsr: 'D記録者',
      txtMultipleUsrFlg: '複数作業者',
      txtDevExplFlg: '異状コメント',
      txtLogExplFlg: '作業コメント',
      txtModExplFlg: '修正コメント',
      txtRecConfFlg: '確認必須',
      txtAttBinNo: '添付資料',
      txtSjgSOPOperationDetails: 'SOP作業詳細',
      txtInventoryQuantity: '在庫数量',
      btnAttExistFlg: '添付資料',
      txtAttBin: '添付資料',
      txtComponentInformationConfirmation: '構成品情報確認',
      txtComponentInformationConfirmationConfirmation: '構成品情報確認(確認)',
      txtOdrCat: '指図分類',
      txtBomMatNo: '投入品品目コード',
      txtBomMatNm: '投入品品名',
      txtBomLotNo: '管理/製造番号',
      txtBomRsltDts: '投入実績日',
      txtLogLotSts: '投入時品質状態',
      txtLotSts: '現在の品質状態',
      txtShelfLifeDts: '有効期限',
      txtExpiryDts: '使用期限',
      txtPrdMatNo: '出来高品_品目コード',
      txtPrdMatNm: '出来高品_品名',
      txtPrdLotNo: '出来高品_管理/製造番号',
      txtPrdRsltDts: '出来高実績日',
      txtPrdShtNm: '出来高品_略号',
      txtAttExistFlg: '添付資料有無フラグ',
      txtVerifyBomFlg: '構成品チェック対象',
      txtSjgComponentInformationAttachments: '構成品情報確認(品目詳細)',
      txtBomLotNoNm: '投入品製造番号',
      btnReturn: '一覧に戻る',
      txtVerityReason: '照査理由',
      txtGmpDocNeedTypeVerify: '出荷可否決定書記載要否',
      btnDetailAdd: '明細追加',
      btnDetailEdit: '明細修正',
      btnDetailDel: '明細削除',
      txtGMPConfirmInput: 'GMP確認入力',
      txtAogAttExistFlgNm: '入荷時添付資料',
      txtLogLotStsCheckNm: '投入時品質状態チェック',
      txtLogExpiryDtsCheckNm: '使用期限チェック（投入時）',
      txtLotExpiryDts: '使用期限（現在値）',
      txtLogShelfLifeDtsCheckNm: '有効期限チェック（投入時）',
      txtLogWgtShelfLifeDtsCheckNm: '秤量有効期限チェック（投入時）',
      txtLogConsDeadlineCheckNm: '投入有効期限チェック（投入時）',
      txtLblSid: '個装番号',
      txtLogLotStsNm: '品質状態（投入時）',
      txtlotLotStsNm: '品質状態（現在）',
      txtRsltDts: '投入日時',
      txtLogShelfLifeDts: '有効期限（投入時）',
      txtLotShelfLifeDts: '有効期限（現在）',
      txtLogExpiryDts: '使用期限（投入時）',
      txtLotExpiryNowDts: '使用期限（現在）',
      txtLogConsDeadlineCheckStart: '投入有効期限（開始・投入時）',
      txtLogConsDeadlineCheckEnd: '投入有効期限（終了・投入時）',
      txtLblConsDeadlineCheckStart: '投入有効期限（開始・現在）',
      txtLblConsDeadlineCheckEnd: '投入有効期限（終了・現在）',
      txtLogWgtDts: '秤量後有効期限（投入時）',
      txtLblWgtDts: '秤量後有効期限（現在）',
      txtBomOdrNo: '指図番号',
      txtLblList: '個装一覧',
      txtManufacturingAndQualityRecordResult: '製造記録照査・品質記録照査結果',
      txtGMPInformationResult: 'GMP照査結果',
      txtRsltQty: '出来高数量',
      txtVerifyGmpDocNeedTypeVerify: '照査時：出荷可否決定書記載要否',
      txtGmpDocNeedTypeChange: '記載要否切替',
      txtWhetherMaterialRevised: '資材改版の有無の記載',
      txtReleaseRsltM: '工場出荷判定',
      txtReleaseRsltQ: '市場出荷判定',
      txtRecordYes: '記載する',
      txtRecordNo: '記載しない',
      txtAogAttNotExist: '入荷時の添付資料が存在しません。',
      txtExpiryTxt: '使用期限(表記用)',
      txtOdrDevFlg: '異状レベル3',
    },
    Msg: {
      // メッセージ
      titleInspectionErase: '照査取消',
      contentInspectionErase:
        '照査取消を行った場合、照査情報は全て取り消され、\n照査未実施の状態に戻ります。照査を取消しますか？',
      titleInspectionEraseFinish: '照査取消完了',
      contentInspectionEraseFinish: '照査を取り消しました。',
      txtGMPDocNeedTypeTitle: '出荷可否決定書記載必須チェックエラー',
      txtGMPDocNeedTypeInfo: 'が有の場合、出荷可否決定書記載は選択必須です。',
      txtGMPInputRequiredTitle: '入力必須チェックエラー',
      txtGMPInputRequiredInfo:
        'GMP確認結果を入力する場合、管理番号、件名、コメントの3点をすべて入力してください。',
      txtGMPNumberOfEntriesInfo:
        'が有の場合、GMP確認結果は転記データを含めて1件以上入力する必要があります。',
      txtGMPMngNoTitle: '管理番号必須チェックエラー',
      txtGMPMngNoInfo: 'が有の場合、管理番号は入力必須です。',
      txtGMPTitleTitle: '件名必須チェックエラー',
      txtGMPTitleInfo: 'が有の場合、件名は入力必須です。',
      txtGMPDesTitle: 'コメント必須チェックエラー',
      txtGMPDesInfo: 'が有の場合、コメントは入力必須です。',
      txtGMPConfirmationConfirmTitle: '入力確定',
      txtGMPConfirmationConfirmInfo:
        'GMP確認({0})の入力を確定します。よろしいですか？',
      txtChangeGMPRsltConfirmTitle: '入力内容初期化確認',
      txtChangeGMPRsltConfirmInfo:
        'GMP確認項目を無に変更した場合、入力内容がクリアされます。\n操作を確定しますか？',
      txtChenkModifyGMPStopTitle: ' 保存内容存在チェックエラー',
      txtChenkModifyGMPStopInfo: '保存すべき入力が行われておりません。',
      titleConfirmCompletion: '照査完了確認',
      contentConfirmCompletion: '照査完了確認を行います。よろしいですか？',
      titleRequiredComment: '照査NG時コメント必須チェックエラー',
      contentRequiredComment: '照査結果がNGの場合、コメント必須です。',
      titleRequiredCommentRecord: 'コメント必須チェックエラー',
      contentRequiredCommentRecord:
        '照査結果がNGか未実施の場合、コメントは入力必須です。',
      titleExistingVerification: '既存照査データ引継ぎ確認',
      contentExistingVerification:
        '既存の照査データを引き継ぎますか？\n引き継がない場合は、既存照査データは破棄されます。',
      titleInspectManufacturingRecord: '一時保存・引き継ぎデータ読み込み完了',
      contentInspectManufacturingRecord:
        '一時保存していたデータ、もしくは前工程から引き継いだ内容を読み込みました。',
      titleNoteCheckRequiredDelivery:
        '照査OK出荷不可時コメント必須チェックエラー',
      titleRecordConfirmationCheckBox: '保存可能チェックエラー',
      contentRecordConfirmationCheckBox:
        '確認が終わっていない項目が存在します。',
      contentNoteCheckRequiredDelivery:
        '照査結果がOKの場合で、工場または市場出荷可否判定結果に不可を選択した場合、コメント必須です。',
      contentSjgConfirm: '出荷判定の結果を確定します。よろしいですか？',
      titleErrorRequiredCheckShippableRemarks:
        '出荷可異状レベル3時コメント必須チェックエラー',
      contentErrorRequiredCheckShippableRemarks:
        '出荷可を選択しており、SOPに異状レベル3以上のものがあるため、コメント必須です。',
      titleUnableDeliveGoodsCheckResultError:
        '照査結果NG時出荷不可チェックエラー',
      contentUnableDeliveGoodsCheckResultError:
        '照査結果がNGのため、出荷可にすることはできません。',
      txtAttachmentExistenceCheckError: '添付資料存在チェック結果',
      txtAttachmentDoesNotExist: '添付資料が存在しません。',
      txtStorableCheckError: '保存可能チェックエラー',
      txtItemsHaveNotBeenChecked:
        'チェック対象の品目でチェックが完了していない項目があります。',
      txtSjgInformationValidCheck: '照査データ有効チェックエラー',
      decisionReportDlTitle: '出荷可否決定書ダウンロード',
      decisionReportDlConfirm:
        '出荷可否決定書をダウンロードします。よろしいですか？',
      gmpRequiedCheckMessage:
        '出荷可否決定書へ記載が必要な場合、GMP確認結果は転記データを除いて１件以上入力する必要があります。',
      shipmentDecisionResultCheckError: '出荷可否判定結果チェックエラー',
      shipmentDecisionResultCheckErrorMessage:
        '工場出荷可否判定結果が「否」の場合、市場出荷可否判定結果を「可」に出来ません。',
      returnToListConfirmMessage: '一覧画面に戻りますか？',
      noNeedRecordItem: '出荷可否決定書記載が不要で登録されている項目です。',
      maxGMPCountTitle: '最大件数入力済',
      maxGMPCountMessage: 'GMP確認項目は最大{0}件までしか記入できません。',
      deleteDetailsConfirmTitle: '削除確認',
      deleteDetailsConfirmMessage:
        '選択したGMP確認項目を削除します。よろしいですか？',
      mfgMgrQcRsltCheckError:
        '製造・品質記録照査結果NG時出荷不可チェックエラー',
      mfgMgrQcRsltCheckErrorMessage:
        '「製造管理および品質管理の結果」が「不適」で入力されているため、市場出荷判定を出荷可にすることはできません。',
      mfgPlantRelExecConfCheckError: '出荷判定適切チェックエラー',
      mfgPlantRelExecConfCheckErrorMessage:
        '「適切に出荷判定が実施された確認」が「不適」で入力されているため、市場出荷判定を出荷可にすることはできません。',
      mfgPlantRelRsltCheckError: '製造所からの出荷判定結果チェックエラー',
      mfgPlantRelRsltCheckErrorMessage:
        '「製造所からの出荷判定結果」が不適のため、市場出荷判定を出荷可にすることはできません。',
      gmpDocNeedTypeVerifyChg: '出荷可否決定書記載変更',
      gmpDocNeedTypeVerifyChgMessage:
        '出荷可否決定書への記載設定を変更しました。',
    },
  },
  Trf: {
    // 出庫管理
    Chr: {
      // キャラクタ
      txtTrfLoadShipmentReportTitle: '出庫報告書スキャン',
      txtTrfPrintReceiptConfirmationLabel: '個装ラベルスキャン',
      txtSelectedInstructionList: '選択済 製造指図一覧',
      txtIndivisualQRText: 'QRコード読取結果: ',
      txtComponentList: '構成品一覧',
      txtShipmentRequestCreate: '出庫依頼作成',
      txtTrfShipmentInstructionListTitle: '出庫指示一覧(実績確定前)',
      txtTrfConfirmShipmentInstructionListTitle: '出庫指示一覧(実績確定済)',
      txtChangeShipmentRequestQuantity: '依頼量変更',
      txtOdrNo: '指図番号',
      txtOdrSts: '指図状態',
      txtTrfPlanRequested: '依頼済',
      txtMatNoNm: '品目コード/品名',
      txtMatNo: '品目コード',
      txtMatNm: '品名',
      txtLotNo: '製造番号',
      txtRxNm: '処方名',
      txtOdrCatNm: '製造指図分類',
      txtOdrQty: '製造指図量',
      txtUnitNm: '単位',
      txtOdrDt: '製造予定日',
      txtOdrDtFrom: '製造予定日(FROM)',
      txtOdrDtTo: '製造予定日(TO)',
      txtSelectDstZoneNoAndSrcZoneGrpNo:
        '出庫元ゾーングループ・出庫先ゾーン選択',
      txtSelectDstZoneNoAndSrcZoneGrpNoFormulation:
        '出庫元ゾーングループ・出庫先ゾーン選択（製剤）',
      txtSelectDstZoneNoAndSrcZoneGrpNoPackaging:
        '出庫元ゾーングループ・出庫先ゾーン選択（包装）',
      txtDstZoneNoAndSrcZoneGrpNo: '出庫元ゾーングループ・出庫先ゾーン',
      txtSrcZoneGrpNo: '出庫元ゾーングループ',
      txtDstZoneNo: '出庫先ゾーン',
      txtSrcZoneGrpNoNm: '出庫元ゾーングループ名',
      txtDstZoneNoNm: '出庫先ゾーン名',
      txtTransferStatusCreateAprove: '指図承認',
      txtManageNo: '管理番号/製造番号',
      txtManageLotNo: '管理番号/製品番号',
      txtManagementNo: '管理番号',
      txtEdNo: '版番号',
      txtOdrMatQty: '予定所要量',
      txtTrfQty: '依頼量',
      txtZoneWhNm: '場所',
      txtWareHouseInvQty: '倉庫在庫量',
      txtAvailableWhInvQty: '引当可能倉庫在庫量',
      txtInvQty: '現場在庫量',
      txtComment: 'コメント',
      txtInventoryQuantity: '在庫量',
      txtChangeTransferShipmentRequestQuantity: '出庫依頼量変更',
      txtZoneEachInvQty: 'ゾーン毎在庫量',
      txtZoneEachInvQtyExplain: 'ゾーン毎の在庫量を表示します。',
      txtShipmentRequestCreateExplain: '以下の内容で出庫依頼を作成します。',
      txtShipmentRequestEditExplain: '以下の内容で出庫依頼を修正します。',
      txtShipmentRequestEraseExplain: '以下の出庫依頼を取り消します。',
      txtCreateShipmentInstructionExplain: '以下の内容で出庫指示を作成します。',
      txtZoneNm: 'ゾーン',
      txtZoneInvQty: 'ゾーン内在庫',
      txtDsrDts: '出庫希望日時',
      txtDetailExpl: '明細コメント',
      txtTrfCreateConfirm: '出庫依頼作成確認',
      txtTrfEditConfirm: '出庫依頼修正確認',
      txtShipmentRequestNo: '出庫依頼番号',
      txtTrfPlanDtlList: '出庫依頼予定明細一覧',
      txtUnselectedSrcZoneGrpNo: '出庫元ゾーングループを選択してください',
      txtUnselectedDstZoneNo: '出庫先ゾーンを選択してください',
      txtAddShipmentRequest: '出庫依頼明細追加',
      txtEditShipmentRequest: '出庫依頼明細修正',
      txtSrcOrDstZoneCheck: '出庫元、出庫先選択チェックエラー',
      txtDuplicationDetailsCheck: '明細重複チェックエラー',
      txtTrfPlanDetailExpl: '出庫依頼明細コメント',
      txtShipmentRequestListCheck: '明細存在チェックエラー',
      txtTrfInstGrpNo: '出庫指示番号',
      txtDsrYmd: '出庫希望日',
      txtInstUsr: '出庫指示者',
      txtPrtDts: '出庫指示日',
      txtPlanUsr: '出庫依頼者',
      txtInstStsNm: '明細ステータス',
      txtDstZoneNm: '出庫先ゾーン',
      txtFnpckHtExpl: 'HT出庫ピッキング完了コメント',
      txtTrfInstNo: '出庫指示明細番号',
      txtInstructionDetails: '出庫指示詳細',
      txtInstructionConfirm: '出庫実績確定',
      txtInstructionDetailsList: '出庫指示明細一覧',
      txtDeleteTrfShipmentInstructionConfirm: '出庫指示取消確認',
      txtTrfShipmentInstructionPrintConfirm: '出庫指示書ダウンロード',
      txtTrfShipmentReportPrintConfirm: '出庫報告書ダウンロード',
      txtTrfShipmentInstructionRestartConfirm:
        'ピッキング中のステータス解除確認',
      txtTrfConfirmShipmentInstructionRecordConfirm: '出庫実績確定実施確認',
      txtInstQty: '指示量',
      txtTrfShipType: '出庫可否',
      txtTrfProprietyNm: '出庫可否',
      txtTrfShipEnable: '出庫可',
      txtTrfShipDisable: '出庫不可',
      txtTrfPlanTypeNm: '指図参照',
      txtTrfInstType: '依頼種別',
      txtTrfPlanType: '依頼種別',
      txtTrfPlanGrpNo: '出庫依頼番号',
      txtPlanYmd: '出庫依頼日',
      txtPlanYmdFrom: '出庫依頼日(FROM)',
      txtPlanYmdTo: '出庫依頼日(TO)',
      txtDsrYmdFrom: '出庫希望日(FROM)',
      txtDsrYmdTo: '出庫希望日(TO)',
      txtTrfPlanGrpTypeNm: '依頼種別',
      txtPlanGrpYmd: '出庫依頼日',
      txtSrcZoneNm: '出庫元ゾーン',
      txtUsrNm: '出庫依頼者',
      txtPlanGrpExpl: 'コメント',
      txtTrfShipmentInstPlanList: '出庫指示予定一覧',
      txtShipmentRequestDetail: '出庫依頼詳細',
      txtShipmentRequestErase: '出庫依頼取消',
      txtCreateShipmentInstruction: '出庫指示作成',
      txtSrcZoneGrpNm: '出庫元ゾーングループ',
      txtConfirmRecordComment: '出庫実績確定コメント',
      txtConfirmRecordDialogTitle: '以下の内容で出庫実績を確定します。',
      btnShipmentRequestDetail: '出庫依頼詳細',
      btnShipmentRequestModify: '出庫依頼修正',
      btnShipmentRequestErase: '出庫依頼取消',
      btnCreateShipmentInstruction: '出庫指示作成',
      btnDelDetail: '明細削除',
      btnModDetail: '明細修正',
      btnAddDetail: '明細追加',
      btnSetting: '設定',
      btnChangeShipmentRequestQuantity: '依頼量変更',
      btnShipmentRequestCreate: '出庫依頼作成',
      txtRecvSts: 'ステータス',
      txtPlanQty: '依頼量',
      txtRsltQty: '出庫量',
      txtRecvQty: '荷受量',
      txtPckExpl: 'ピッキング完了コメント',
      txtRecvCnt: '荷受済個装数（明細）',
      txtRsltCnt: '明細総個装数',
      txtInstRepNoRef: '出庫報告書番号',
      txtRsltYmd: '出庫日',
      txtRecvTtlCnt: '荷受済個装数（全体）',
      txtRsltTtlCnt: '総出庫個装数',
      txtReceiptConfirmation: '荷受確認',
      btnPause: '一時中断',
      btnForce: '強制完了',
      txtStopReceipt: '荷受の一時中断',
      txtReceiptForceComplete: '荷受の強制完了',
      btnUnlockPicking: 'ピッキング中解除',
      btnInstructionPrint: '指示書印刷',
      btnReportPrint: '報告書印刷',
      btnInstructionDetails: '出庫指示詳細',
      btnInstructionCancel: '出庫指示取消',
      btnInstructionConfirm: '出庫実績確定',
      txtRecvStsInit: '荷受前',
      txtRecvStsStart: '荷受中',
      txtRecvStsEnd: '荷受済',
      txtRecvStsForce: '強制終了',
      txtEdManageNo: '版管理',
    },
    Msg: {
      // メッセージ
      destroyConfirm: '入力内容を破棄して戻りますか？',
      radioCheckError: '単一選択チェックエラー',
      selectOnlyOneComponent:
        '依頼量の変更を行う場合は、構成品を１つだけ選択してください。',
      titleShipmentRequestCreate: '出庫依頼作成',
      contentShipmentRequestCreate: '出庫依頼を作成しますか？',
      titleShipmentRequestChange: '出庫依頼変更',
      contentShipmentRequestChange: '出庫依頼を変更しますか？',
      titleEmergencyOutboundContact: '緊急出庫連絡',
      contentEmergencyOutboundContact:
        '緊急出庫となるため、倉庫担当へ連絡してください。',
      srcOrDstZoneCheckError:
        '出庫元ゾーングループ、出庫先ゾーンを選択してください。',
      duplicationDetailsCheckError:
        '品目コード、管理番号/製造番号、版番号が他の明細と重複する為、追加できません。',
      duplicationDetailsModCheckError:
        '品目コード、管理番号/製造番号、版番号が他の明細と重複する為、修正できません。',
      titleShipmentRequestDelete: '明細削除確認',
      contentShipmentRequestDelete:
        '出庫依頼明細から削除します。よろしいですか？',
      shipmentRequestListCheckError: '出庫依頼を行う品目がありません。',
      contentQrCodeRead: '出庫報告書のQRコードを読み取ってください',
      packagerintReceiptConfirmationQrCodeRead:
        '個装ラベルのQRコードを読み取ってください',
      stopReceipt:
        '荷受を一時中断します。\n出庫報告書を読み込むことで、再開が可能です。一時中断しますか？',
      receiptForceComplete: '荷受作業を強制的に完了させます。よろしいですか？',
      titleShipmentRequestErase: '出庫依頼取消確認',
      contentShipmentRequestErase: '出庫依頼を取り消します。よろしいですか？',
      titleShipmentInstructionCreate: '出庫指示作成確認',
      contentShipmentInstructionCreate:
        '出庫指示を作成します。よろしいですか？',
      deleteTrfShipmentInstructionConfirm:
        'ピッキング実施前の出庫指示明細を取り消します。よろしいですか？',
      trfShipmentInstructionPrintConfirm:
        '出庫指示書をダウンロードします。よろしいですか？',
      trfShipmentReportPrintConfirm:
        '出庫報告書をダウンロードします。よろしいですか？',
      trfShipmentInstructionRestartConfirm:
        'ピッキング中のステータスを解除しますか？',
      trfConfirmShipmentInstructionRecordConfirm: '出庫実績を確定しますか？',
      titleMoveOtherPageWarning: '他の画面に移動する警告',
      moveOtherPageWarning:
        '登録せずに他の画面に移動しています。宜しいですか。',
    },
  },
  Sog: {
    // 出荷管理
    Chr: {
      // キャラクタ
      txtCreateProductShipmentInstructionTitle:
        '出荷予定一覧（製品出庫指示前）',
      txtSogPlanYmd: '出荷予定日',
      txtSogPlanYmdFrom: '出荷予定日（FROM）',
      txtSogPlanYmdTo: '出荷予定日（TO）',
      txtSogPlanNo: '出荷予定番号',
      txtSogPlanStsNm: '出荷予定状態',
      txtBpTrfNm: '出荷先',
      txtMatNo: '品目コード',
      txtMatNm: '品名',
      txtMatNoNm: '品目コード/品名',
      txtLotNo: '製造番号',
      txtSogQty: '出荷予定量',
      txtMesUnit: '単位',
      txtReleaseRsltNm: '出荷判定',
      txtLotStsNm: '品質状態',
      txtLotLockNm: 'ロットロック',
      txtExpiryDts: '使用期限日',
      txtSogExpiryDt: '出荷可能日数',
      txtPltCnt: 'パレット数',
      txtBaleCnt: '段ボール数',
      btnCreateProductShipmentInstruction: '出庫指示作成',
      txtShipment: '出荷',
      txtConfirmProductShipmentRecord: '製品出庫実績確定',
      txtProductShipmentInstructionNo: '製品出庫指示番号',
      txtProductShipmentInstructionGroupNo: '製品出庫指示書番号',
      txtShipmentRecipientName: '出荷先名',
      txtDetailsNo: '明細番号',
      txtShipmentInstructionState: '出荷指示状態',
      txtShipmentRecordQuantity: '出庫実績量',
      txtCommentShipmentInstruction: 'コメント（出庫指示）',
      txtCommentPicking: 'コメント（ピッキング）',
      txtProductShipmentInstructionGroupList:
        '製品出庫指示書一覧（ピッキング済）',
      txtProductShipmentInstructionTitle: '製品出庫指示一覧（出荷指示前）',
      txtSogInstGrpNo: '製品出庫指示書番号',
      txtSogInstNo: '明細番号',
      txtSogInstStsNm: '出荷指示状態',
      txtRsltPckQty: '出庫実績量',
      txtLockFlgNm: 'ロットロック',
      txtApprExpl: 'コメント（出庫指示）',
      txtFnpckHtExpl: 'コメント（ピッキング）',
      btnPrintSlip: '納品予定PDF',
      btnPrintInst: '出庫指示PDF',
      btnDiscon: '出庫指示書取消',
      btnErase: 'ピッキング中解除',
      txtSogPlanList: '出荷予定リスト',
      txtBpTrfId: '出荷先コード',
      txtUnitNm: '単位',
      txtSrcZoneGrpNo: '出庫元ゾーングループ',
      txtSrcZoneGrpNm: '出庫元ゾーングループ',
      txtSogPlanUpdDts: '出荷予定更新日時',
      txtSogAddShipmentPlan: '出荷予定追加',
      txtSogEditShipmentPlan: '出荷予定修正',
      txtBtnAdd: '予定追加',
      txtBtnImport: '予定CSV取込',
      txtBtnCancel: '予定削除',
      txtBtnMod: '予定修正',
      txtBtnCancelConfirm: '予定確定取消',
      txtBtnConfirm: '予定確定',
      txtSogYmd: '工場出荷日',
      txtSogYmdFrom: '工場出荷日(FROM)',
      txtSogYmdTo: '工場出荷日(TO)',
      txtSogRsltFixList: '出荷実績確定リスト',
      txtSogSlipGrpNo: '出荷指示書番号',
      txtTruckId: 'トラックID',
      txtPlanQty: '出荷予定量',
      txtRsltQty: '出荷実績量',
      txtSogTrfInstExpl: 'コメント（出庫指示）',
      txtSogFnpckHtExpl: 'コメント（ピッキング）',
      txtSogInstExpl: 'コメント（出荷指示）',
      txtSogForceExpl: 'コメント（強制出荷）',
      txtSogrsltfixmodExpl: 'コメント（実績修正）',
      txtSogConfirmShipmentRecord: '出荷指示一覧（出荷実績確定前）',
      txtRsltModQty: '修正後　出荷実績量',
      txtComment: 'コメント',
      txtCancelSogShipmentInstructionConfirm: '出荷指示を取り消します。',
      txtSogShipmentTodayUnloaded: '出荷予定日本日の未出荷明細有り',
      txtSogEditShipmentRecord: '出荷実績修正',
      txtSogShipmentRecordConfirm: '出荷実績確定',
      txtSogEditShipmentRecordConfirm: '出荷実績修正',
      txtSogForceShipment: '強制出荷',
      txtSogForceDialogConfirm: '強制出荷します。よろしいですか？',
      txtSogForceDialogInfo: '更新後、出荷実績確定処理してください。',
      txtSogDts: '出荷時間',
      txtSogForceShipmentConfirm: '強制出荷',
      txtSogForceShipmentComplete: '正常終了',
      txtLabelScan: '製品出庫指示書読み取り',
      txtSogInstStsAlread: '出荷指示済',
      txtSogInstStsConfirm: '製品出庫実績確定済',
      txtRegistShipmentInstructionQRText: 'QRコード読取結果: ',
      txtSogConfirmShipmentInstructionRegistration: '出荷指示確定',
      btnSogRegistShipmentInstruction: '出荷指示登録',
      btnEditShipmentRecord: '実績修正',
      btnForceShipment: '強制出荷',
      btnConfirmShipmentRecord: '実績確定',
      txtShipmentReportCreate: '出荷報告書作成',
      txtShipmentRecordReportPDFCreate: '出荷実績報告',
      txtShipmentRecordReportPDF: '出荷実績表PDF',
      txtShipmentReportPDF: '出荷報告書PDF',
      txtSelectYmd: '年月日指定',
      txtSelectYmdFrom: '年月日指定（FROM）',
      txtSelectYmdTo: '年月日指定（TO）',
      txtShipmentDecisionYmd: '出荷判定日',
      txtYmdSelectItem: '日時指定項目',
      txtShipmentRecordList: '出荷実績一覧',
      txtShipmentDecider: '出荷判定者',
      txtQualityOrNotDecisionYmd: '品質合否判定日',
      txtSogShipmentInstructionList: '出荷指示一覧（出荷指示済）',
      txtSogShipmentInstructionCancelConfirm: '出荷指示取消',
      txtSogShipmentInstructionReceiptPrintConfirm: '受領書PDFダウンロード',
      txtSogShipmentInstructionSlipPrintConfirm: '納品書PDFダウンロード',
      txtSogShipmentInstructionTruckPrintConfirm:
        'トラック明細報告書PDFダウンロード',
      txtInstQty: '出荷指示量',
      txtTrfRsltQty: '出庫実績量',
      txtSogRegistRepackagingShipmentInstruction:
        '製品出庫指示一覧(製品出庫実績確定済)',
      txtSogEditProductShipmentRecord: '出荷情報変更',
      txtSogEditProductShipmentRecordConfirm: '出荷情報変更',
      txtSogInstructionCancel: '出荷指示中止',
      btnDlReceipt: '受領書PDF',
      btnDlDelivery: '納品書PDF',
      btnDlTruck: 'ﾄﾗｯｸ明細報告書',
      btnCancel: '出荷指示取消',
      btnRegistCancel: '出荷中止',
      btnRegistMod: '出荷情報変更',
      btnRegistCreate: '出荷指示確定',
    },
    Msg: {
      // メッセージ
      createProductShipmentInstructionConfirmTitle: '製品出庫指示書作成',
      createProductShipmentInstructionConfirm:
        '製品出庫指示書を作成します。よろしいですか？',
      contentConfirmProductShipmentRecord:
        '製品出庫実績を確定します。よろしいですか？',
      printSlipConfirmTitle: '納品予定書PDFダウンロード',
      printSlipConfirm: '納品予定書をダウンロードします。よろしいですか？',
      printInstConfirmTitle: '出庫指示書PDFダウンロード',
      printInstConfirm: '出庫指示書をダウンロードします。よろしいですか？',
      disconConfirmTitle: '製品出庫指示書取消',
      disconConfirm: '製品出庫指示書を取消します。よろしいですか？',
      eraseConfirmTitle: 'ピッキング中解除',
      eraseConfirm: 'ピッキング中のステータスを解除しますか？',
      txtPreExecutionCheck: '実行前確認',
      txtAddShippingConfirm: '出荷予定を追加します。よろしいですか？',
      txtEditShippingConfirm: '出荷予定を修正します。よろしいですか？',
      txtExecutionCheckEndTitle: '処理完了',
      txtSogPlanEditCheckEndInfo: '出荷予定を修正しました。',
      txtSogPlanDeleteTitle: '出荷予定の削除',
      txtSogPlanDeleteInfo: ' 選択されている出荷予定を削除しますか？',
      txtSogPlanDeleteCheckEndTitle: '出荷予定の削除',
      txtSogPlanDeleteCheckEndInfo: '出荷予定を削除しました。',
      txtSogPlanConfirmCancelTitle: '出荷予定確定取消',
      txtSogPlanConfirmCancelInfo: '選択されている出荷予定を取消しますか？',
      txtSogPlanConfirmTitle: '出荷予定確定',
      txtSogPlanConfirmInfo: '選択されている出荷予定を確定しますか？',
      txtSogPlanCompletedTitle: '出荷予定修正完了',
      txtSogPlanCompletedInfo:
        '出荷予定を修正しました。\n出荷予定番号：PSD2410290001',
      sogShipmentRecordConfirm: '出荷実績を確定します。よろしいですか？',
      sogForceShipmentConfirm: '強制出荷します。よろしいですか？',
      sogShipmentTodayUnloaded:
        '本日出荷予定ですが「出荷済」になっていない製品があります',
      titleConfirmShipmentInstructionRegistration: '出荷指示作成',
      contentConfirmShipmentInstructionRegistration:
        '出荷指示します。よろしいですか？',
      titleSogRegistShipmentInstructionCheckError:
        '出荷指示明細ステータスチェックエラー',
      contentSogRegistShipmentInstructionCheckError:
        '「製品出庫実績確定済」が１件以上なければ、出荷指示登録できません。',
      titleSogRegistRepackagingShipmentInstructionCheckError:
        '出荷予定日相違チェックエラー',
      contentSogRegistRepackagingShipmentInstructionCheckError:
        '明細の出荷予定日は全て同じである必要があります。',
      titleReceiptOutputConfirm: '帳票出力確認',
      contentReceiptOutputConfirm:
        '絞り込み条件に従い、出荷実績表をダウンロードします。よろしいですか？',
      contentReportOutputConfirm:
        '絞り込み条件に従い、出荷報告書をダウンロードします。よろしいですか？',
      titleNoSearchCheckError: '未検索チェックエラー',
      contentShipmentRecordSearch: '出荷実績を検索してください',
      sogEditShipmentRecordConfirm: '出荷実績を修正します。よろしいですか？',
      cancelSogShipmentInstructionConfirm:
        '出荷指示を取り消します。\n取り消した出荷指示は、出荷指示前の状態に戻ります。\n取り消しますか？',
      sogShipmentInstructionReceiptPrintConfirm:
        '受領書をダウンロードします。よろしいですか？',
      sogShipmentInstructionSlipPrintConfirm:
        '納品書をダウンロードします。よろしいですか？',
      sogShipmentInstructionTruckPrintConfirm:
        'トラック明細報告書をダウンロードします。よろしいですか？',
      sogEditProductShipmentRecordConfirm:
        '出荷情報を変更します。よろしいですか？',
      sogInstructionCancel:
        '出荷指示を中止します。\n中止した出荷指示は、出荷予定も削除します。取り消しますか？',
    },
  },
  Tst: {
    // 試験管理
    Chr: {
      // キャラクタ
      txtTstTestRequestCandidateList: '試験候補一覧(試験依頼前)',
      txtManageNo: '管理番号/製造番号',
      txtItem: '品目',
      txtItemCode: '品目コード',
      txtMatNm: '品名',
      txtShelfLifeDate: '有効期限',
      txtShelfLifeDateFrom: '有効期限(FROM)',
      txtShelfLifeDateTo: '有効期限(TO)',
      txtExpiryDate: '使用期限',
      txtOdrDate: '製造予定日',
      txtMakerLotNo: 'メーカーロット',
      txtShelfLifeExpiredLotDisp: '有効期限切れロット表示',
      txtExpiredOdrDts: '直近の製造予定日有',
      txtAllDisp: '全表示',
      txtEditionNo: '版番号',
      txtBusinessPartnerCode: 'メーカーコード',
      txtBusinessPartnerName: 'メーカー名',
      txtBusinessPartnerLotNumber: 'メーカーロット番号',
      txtTstReqExpl: '依頼コメント',
      txtCreateTestRequestExplain: '以下の試験依頼を作成しますか？',
      txtTestRequestCandidateCreate: '試験依頼作成',
      txtTestRequestList: '試験依頼一覧',
      txtLimsIfId: '試験依頼番号',
      txtQltCat: '試験区分',
      txtMatAttr: 'ユーザー品目分類',
      txtQltResSts: '試験依頼状態',
      txtResJudgeRslt: '検査結果',
      txtResJudgeYmd: '判定日',
      txtResJudgeYmdFrom: '判定日(FROM)',
      txtResJudgeYmdTo: '判定日(TO)',
      txtReqYmd: '依頼日',
      txtReqYmdFrom: '依頼日(FROM)',
      txtReqYmdTo: '依頼日(TO)',
      txtResJudgeRep: '判定者',
      txtResJudgeExpl: 'LIMS特記事項',
      txtTstTestReqExpl: '試験依頼コメント',
      txtTstTestRequestDetailsTitle: '試験依頼情報詳細',
      txtTstTestRequestDetails: '試験依頼詳細',
      txtTstCancelTest: '試験中止',
      txtQltItemNm: '試験項目',
      txtQltItemVal: '試験結果',
      txtTstCancelTestInfo: '以下の試験依頼を中止しますか？',
      txtTstDisconExpl: '中止コメント',
      txtCancelTestConfirm: '試験中止処理確認',
      btnTestCancel: '試験中止',
      btnTestDetails: '依頼詳細',
      btnTestRequestCandidateCreate: '試験依頼作成',
      txtLotNo: '管理番号',
      txtMatNo: '品目コード',
      txtShelfLifeDts: '有効期限',
      txtShelfLifeDtsFrom: '有効期限(FROM)',
      txtShelfLifeDtsTo: '有効期限(TO)',
      txtInvList: '在庫一覧',
      txtLotSid: 'システム用ロットID',
      txtEdNo: '版番号',
      txtMBpId: 'メーカーコード',
      txtMakerNm: 'メーカー名',
      txtMakerLotNoNm: 'メーカーロット番号',
      txtExpiryDts: '使用期限',
      txtInvQty: '在庫量',
      txtUnitNm: '単位',
      txtLotUpdDts: 'ロット更新日時',
      txtLotSts: '品質状態',
      resJudgeRsltNm0: '不適',
      resJudgeRsltNm1: '適',
      resJudgeRsltNm3: '中止',
      txtQltCatNm1: '受入',
      txtQltCatNm2: '中間製品',
      txtQltCatNm3: '製品',
      txtQltCatNm4: '経年/リテスト',
      txtQltCatNm9: '中止',
      txtTstChangeQualityStatusBtn: '品質状態変更',
      txtChangeComment: '変更コメント',
      txtChangeQualityStatusPresentation:
        '以下のデータの品質状態・期限を変更しますか？',
      txtSamplingRecordList: 'サンプリング実績一覧',
      txtInventoryCorrection: '在庫修正',
      txtTestRequestYmd: '試験依頼日',
      txtIndividuaPackagingSamplingNumber: 'サンプリング個装数',
      txtSamplingInventoryCorrection: 'サンプリング在庫修正',
      txtLabelNo: 'ラベル番号',
      txtFixBefore: '修正前',
      txtFixAfter: '修正後',
      txtSamplingQuantity: 'サンプリング量',
    },
    Msg: {
      // メッセージ
      titleTestRequestCandidateCreate: '試験依頼作成処理確認',
      contentTestRequestCandidateCreate: '試験依頼作成処理を行いますか？',
      cancelTestConfirm: '試験中止処理を行いますか？',
      txtAmountError: '変更項目チェックエラー',
      txtIndividualInventoryAmountConfirm: '変更されている項目がありません。',
      txtChecksumExpiry: '期限日大小チェックエラー',
      txtExpiryMustBeInTheFuture:
        '使用期限日は有効期限日以降の日付を指定してください。',
      txtChangeQualityStatusExpirationConfirmationTitle:
        '品質状態・期限変更処理確認',
      txtChangeQualityStatusExpirationConfirmationInfo:
        '品質状態・期限変更処理を行いますか？',
      txtChangeQualityStatusExpirationEndTitle: '品質状態・期限変更処理終了',
      txtChangeQualityStatusExpirationEndInfo:
        '品質状態・期限変更処理を実行しました。',
      titlePerformSamplingInventoryAdjustmentAccording:
        '以下の内容でサンプリング在庫修正処理を実行します。',
      contentReplaceLabeObjectWithReleasedLabel:
        '発行されたラベルで対象個装のラベル貼替えをして下さい。',
      titleCorrectTheSamplingInventory:
        'サンプリング在庫修正処理を行いますか？',
      contentSamplingQuantityExceedsInventoryQuantity:
        'サンプリング数量が在庫数量を超えています。',
      titleSampleInventoryAdjustmentProcessingConfirmation:
        'サンプリング在庫修正処理確認',
      titleSamplingQuantityCheckError: 'サンプリング数量チェックエラー',
    },
  },
  Inv: {
    // 在庫
    Chr: {
      // キャラクタ
      btnAccept: '受入れ',
      btnAllInventorySearch: '検索',
      btnReceived: '受入れ済',
      txtAogInstructionsNo: '入荷指示書No',
      txtColumnName: 'カラム名',
      txtItem: '品目',
      txtItemCode: '品目コード',
      txtStatus: 'ステータス',
      btnAbortReturnInstruction: '返納指示中止',
      btnCreateReturnInstruction: '返納指示作成',
      btnUnlock: 'ロック解除',
      btnLockIndividual: '個装ロック',
      btnLockLot: 'ロットロック',
      btnUnlockIndividual: '個装ロック解除',
      btnUnlockLot: 'ロットロック解除',
      btnMoveIndividualInventory: '個装在庫移動',
      btnModifyIndividualInventory: '個装在庫修正',
      btnGenerateIndividualInventory: '個装在庫生成',
      btnAddIndividualInventory: '個装在庫追加',
      btnMoveLot: 'ロット移動',
      btnReturn: '返品',
      txtIndividualInventoryCorrection: '個装在庫修正',
      txtAggregationMethod: '在庫集計方法',
      txtItemCode_Name: '品目コード/品名',
      txtAmount: '数量',
      txtComment: 'コメント',
      txtContainerId: '容器ID',
      txtCreateReturnInstruction: '返納指示作成',
      txtEffectiveDate: '有効期限日',
      txtExpirationDate: '使用期限日',
      txtGeneralPurposeClassification: '汎用区分',
      txtIndividualAmount: '個装数',
      txtIndividualNo: '個装番号',
      txtIndividualUnit: '個装単位',
      txtInventoryList: '在庫一覧',
      txtInventoryListLotUnit: '在庫一覧（ロット単位）',
      txtInventoryListIndivisualUnit: '在庫一覧（個装単位）',
      txtInventoryReturnedItemScan: '返納物スキャン',
      txtReturnPlanList: '返納予定一覧',
      txtInvLoadReturnInstructionTitle: '返納指示書スキャン',
      txtInvLoadReturnInstruction: '返納指示書QRコード',
      txtInvLoadReturnTargetLabelTitle: '返納対象QR読取',
      txtInvLoadReturnTargetLabel: '返納物QRコード',
      txtInvLoadReturnTargetLabelTableTitle: 'ロット一覧（返納対象）',
      txtRtnInstGrpNoFile: '返納指示書番号',
      txtRecvTtlCnt: '荷受済個装数（全体）',
      txtRtnTtlPkgCnt: '総返納個装数',
      txtMatNm: '品名',
      txtRtndQty: '返納済み数量',
      txtRtnQty: '返納数量',
      txtRtnSrcZneNm: '返納元',
      txtRtnDstZneNm: '返納先',
      txtRecvCnt: '荷受済個装数',
      txtTtlDtlPkgCnt: '明細総個装数',
      btnForce: '強制完了',
      txtReturnForceComplete: '返納強制完了',
      txtItemClassification: '品目区分',
      txtInventoryInfo: '在庫情報',
      txtLabelScan: 'ラベルスキャン',
      txtIndivisualQRCode: '個装QRコード',
      txtItemName: '品名',
      txtItemNameClassification: '品名区分',
      txtItemUnit: '品目単位',
      txtLocation: 'ロケーション',
      txtMoveIndividualInventory: '個装ロック状態',
      txtLotLocked: 'ロットロック状態',
      txtLockLotIndividual: 'ロットロック',
      txtUnlockLotIndividual: 'ロットロック解除',
      txtLotUnit: 'ロット単位',
      txtMakerLot: 'メーカーロット',
      txtRsltDts: '実績日時',
      txtRsltDtsFrom: '実績日時(FROM)',
      txtRsltDtsTo: '実績日時(TO)',
      txtTrRsltType: '出納種別',
      txtMatNo: '品目コード/品名',
      txtLotNo: '管理番号/製造番号',
      txtLotNoSimple: '管理番号',
      txtManageNo: '管理番号/製造番号',
      txtManageNoSelect: '管理番号選択',
      txtMesUnit: '単位',
      txtNoqualityStatus: '品質状態',
      txtPaletteNo: 'パレットNo',
      txtPaletteUnit: 'パレット単位',
      txtReasonCode: '事由コード',
      txtReturnedItemQrCode: '返納物QRコード',
      txtReturnFrom: '返納元',
      txtReturnItemList: '返納物一覧',
      txtReturnQuantity: '返納数量',
      txtReturnTo: '返納先',
      txtStopCreateReturnInstruction: '返納指示作成中止',
      txtVersionNumber: '版番号',
      txtZone: 'ゾーン',
      txtZoneGroup: 'ゾーングループ',
      txtItemGroup: '品目グループ',
      txtPackageIndividualInventory: '個装在庫移動',
      txtTranferZoneNo: '移動先ゾーン',
      txtTranferLotNo: '移動先ロケーション',
      txtEditRangeConfirm: '範囲編集確認',
      txtExistCheckError: '存在チェックエラー',
      txtLockError: 'ロットロックエラー',
      txtLockDuplicateError: '個装ロックエラー',
      txtLotLockStsNm: 'ロック',
      txtLblLockStsNm: '個装ロック解除エラー',
      txtLockIndividual: '個装ロック',
      txtUnlockIndividual: '個装ロック解除',
      txtAddIndividualInventory: '個装在庫追加',
      txtConfirm: '確認',
      txtMoveMessageFinish: '在庫情報を移動しました',
      txtAmountError: '数量エラー',
      txtIcRsnCdError: '廃棄エラー',
      txtIncreaseError: '増加エラー',
      txtDecreaseError: '減少エラー',
      txtAogVarFlgNumberConfirm: '入り数割れ品数量確認',
      txtDetailsNo: '明細No',
      txtActualYmd: '入荷実績日',
      txtReceiveGoods: '入荷品受け入れ',
      txtRestockDate: '入荷日',
      txtCreateIndividualInventory: '個装在庫生成',
      txtBusinessPartnerName: 'メーカー名',
      txtTotalInvQty: '全体量',
      txtInvQty: '１梱の数量',
      txtInvPackageQty: '梱数',
      txtBusinessPartnerLotNumber: 'メーカーロット番号',
      txtEffectiveCalcDate: '有効期限起算日：',
      txtExpirationCalcDate: '使用期限起算日：',
      txtAccrualDate: '計上日',
      txtAfmRefNo: '関連番号',
      txtLblCat: 'ラベル種類',
      txtManageNoSearch: '管理番号/製造番号検索',
      txtIndivisualQRText: 'QRコード読取結果: ',
      txtEffectiveDateAuthorizedProducts: '有効期限日（品質管理者指定品）',
      txtExpirationDateAuthorizedProducts: '使用期限日（品質管理者指定品）',
      txtManageNoList: '管理番号/製造番号一覧',
      txtMatSdef: 'システム品目分類',
      txtMatAttr: 'ユーザー品目分類',
      txtLiftDts: '入荷日',
      txtAogInstGrpPrtDtsFrom: '入荷指示作成日(FROM)',
      txtAogInstGrpPrtDtsTo: '入荷指示作成日(TO)',
      txtAogInstGrpPrtDts: '入荷指示作成日',
      txtLiftDtsFrom: '入荷日(From)',
      txtLiftDtsTo: '入荷日(To)',
      txtAogInstGrpNo: '入荷指示番号',
      txtPoDtlNo: '発注明細番号',
      txtStorZoneNo: '入荷場所',
      txtReturnConfirm: '返品確認',
      txtReturnComplete: '返品完了',
      txtMesIfNo: '出納管理番号',
      txtMesIfDtl: '出納明細番号',
      txtOdrNo: '製造指図番号',
      matNo: '品目コード',
      matNm: '品名',
      txtZoneNm: '発生ゾーン',
      txtLocNm: '発生ロケーション',
      txtPltNo: '発生パレット番号',
      txtConNo: '発生容器ID',
      txtInInvQty: '入庫量',
      txtOutInvQty: '出庫量',
      txtAlblInvQty: '発生ラベル在庫量（更新後）',
      txtBlblInvQty: '発生ラベル在庫量（更新前）',
      txtAlotInvQty: '発生ロット在庫量（更新後）',
      txtBlotInvQty: '発生ロット在庫量（更新前）',
      txtAmatInvQty: '発生品目在庫量（更新後）',
      txtBmatInvQty: '発生品目在庫量（更新前）',
      txtConsAlblInvQty: '消費ラベル在庫量（更新後）',
      txtConsBlblInvQty: '消費ラベル在庫量（更新前）',
      txtConsAlotInvQty: '消費ロット在庫量（更新後）',
      txtConsBlotInvQty: '消費ロット在庫量（更新前）',
      txtConsAmatInvQty: '消費品目在庫量（更新後）',
      txtConsBmatInvQty: '消費品目在庫量（更新前）',
      txtUnitNm: '単位',
      txtConsZoneNm: '消費ゾーン',
      txtConsLocNm: '消費ロケーション',
      txtConsPltNo: '消費パレット番号',
      txtConsConNo: '消費容器ID',
      txtLblNo: '個装番号',
      txtRsnCd: '事由コード',
      txtExpl: 'コメント',
      txtWarningExpl: '警告コメント',
      txtAfmUsr: '対応者',
      txtTagOfRequired: '必須',
      txtRtnInstGrpNo: '返納指示番号',
      txtRtnInstGrpDts: '返納指示発行日',
      txtPrtDts: '印刷日時',
      txtPrtUsrNm: '印刷者',
      txtInvReturnInstructionDetails: '返納指示詳細',
      txtInvReturnInstructionDetailsList: '返納指示詳細一覧',
      txtDataSourceNm: 'データ取得元',
      txtPltInfo: 'パレット情報',
      txtShelfLifeDts: '有効期限',
      txtExpiryDts: '使用期限',
      btnRtnInstPrint: '指示書印刷',
      btnRtnInstDetail: '指示詳細',
      txtSnapshotYmd: 'スナップショット取得日',
      txtSnapshotYmdFrom: 'スナップショット取得日(FROM)',
      txtSnapshotYmdTo: 'スナップショット取得日(TO)',
      txtSnapshotList: 'スナップショット実績一覧',
      txtSnapshotId: 'スナップショットID',
      txtSnapshotNm: 'スナップショット計画名',
      txtSnapshotDts: 'スナップショット日時',
      txtSnapshotSts: 'スナップショット実施状態',
      txtLotStsNm: '在庫ステータス',
      txtNotTested: '未試験',
      txtPass: '適',
      txtFail: '不適',
      txtTrRsltType21: '入荷',
      txtTrRsltType11: '出来高',
      txtTrRsltType13: '投入',
      txtTrRsltTypeJA: '工数',
      txtTrRsltType31: '移送',
      txtTrRsltType33: '廃棄・他勘定',
      txtMesMatNo: 'MES品目コード',
      txtMesMatNoNm: 'MES品目名',
      txtLotSid: 'システム用ロットID',
      txtLblSid: 'システム用ラベルID',
      txtZoneNo: 'MES保管場所',
      txtZoneNoNm: 'MES保管場所名',
      txtLocNo: 'MESロケーション',
      txtLocNoNm: 'MESロケーション名',
      txtSspConNo: '容器番号',
      txtSspPltNo: 'パレット番号',
      txtSspInvQty: '在庫数量',
      txtVirInvFlg: '仮在庫フラグ',
      txtVirInvNm: '在庫種別',
      txtTrfInstQty: '出庫指示数量',
      txtCrtDts: '登録日時',
      txtCrtUsrId: '登録ユーザーID',
      txtCrtUsrNm: '登録ユーザー名',
      txtUpdDts: '更新日時',
      txtUpdUsrId: '更新ユーザーID',
      txtUpdUsrNm: '更新ユーザー名',
    },
    Msg: {
      // メッセージ
      completeCreateReturnInstruction: '返納指示作成完了',
      createReturnInstruction:
        '返納物一覧の内容で返納指示を作成します。よろしいですか？',
      titleDuplicationReturnCheck: '返納物重複チェックエラー',
      registReturnConfirm:
        '選択された在庫の返品処理を行います。よろしいですか？',
      registReturnComplete: '返品処理を行いました。\n件数：{0} 件',
      contentDuplicationReturnCheck: '返納物として既に追加されています。',
      printedReturnInstruction: '返納指示を作成し、返納指示書を印刷しました。',
      stopCreateReturnInstruction:
        '返納指示作成を中止し、返納物一覧の内容を空にします。よろしいですか？',
      txtReturnForceComplete:
        '未完了の返納在庫がありますが、作業を強制的に完了します。\nよろしいですか？',
      moveIndivisualError: 'ロット内のすべての在庫に対して編集を行います。',
      noInventory: '在庫が見つかりません。',
      lockStateCannotEdit: 'ロットロック状態のため変更できません。',
      lockStateCannotEditDuplicate: '個装ロック状態のため変更できません。',
      UnlockStateCannotEdit:
        '選択した在庫は個装ロック解除状態のため変更できません。',
      moveMessageConfirm: '在庫情報を移動します。よろしいですか？',
      individualInventoryCorrectionConfirm:
        '在庫情報を修正します。よろしいですか？',
      individualInventoryAmountConfirm: '数量が変更されていません。',
      individualInventoryIcRsnCdConfirm:
        '廃棄を行う場合、数量は０に指定してください。',
      individualInventoryIncreaseConfirm:
        '出納登録モードが在庫増加のため、在庫数を増やしてください。',
      individualInventoryDecreaseConfirm:
        '出納登録モードが在庫減少のため、在庫数を減らしてください。\n０を入力する場合は在庫削除用の事由コードを選択してください。',
      moveIndividualInventoryCorrectionConfirm:
        '在庫情報を移動します。よろしいですか？',
      cannotSaveInSpecificLocation:
        '指定したゾーンに本品目を保管することは許可されていません。',
      lockIndividualInventoryCorrectionConfirm:
        '個装ロックを行います。よろしいですか？',
      finishLock: '個装ロックを行いました。',
      unlockIndividualInventoryCorrectionConfirm:
        '個装ロックを解除します。よろしいですか？',
      finishUnlock: '個装ロック解除を行いました。',
      createIndividualInventoryCorrectionConfirm:
        '在庫情報を生成します。よろしいですか？',
      addIndividualInventoryCorrectionConfirm:
        '在庫情報を追加します。よろしいですか？',
      excessiveQuantity:
        '標準入れ目を超える数量が指定されていますが、よろしいですか。',
      editRangeConfirm: '編集範囲確認',
      editInventoryList: '一覧に表示中のロット内の在庫に対して編集を行います。',
      lockLotIndividualInventoryCorrectionConfirm:
        'ロットロックを行います。よろしいですか？',
      finishLockLot: 'ロットロック完了',
      unlockLotIndividualInventoryCorrectionConfirm:
        'ロットロックを解除します。よろしいですか？',
      finishUnlockLot: 'ロットロック解除完了',
      inventoryMoveLotCorrectionConfirm:
        'ロット移動を行います。よろしいですか？',
      titleReturnInstPrintConfirm: '返納指示書ダウンロード',
      contentReturnInstPrintConfirm:
        '返納指示書をダウンロードします。よろしいですか？',
      contentReturnInstPrintInfoConfirm: 'ダウンロードを完了しました。',
    },
  },
  Wgt: {
    // 秤量
    Chr: {
      // キャラクタ
      txtWeightInstructionsCreate: '秤量指示作成',
      txtFilter: '絞り込み',
      txtOrderStartScheduledDate: '指図開始予定日',
      txtOrderStartScheduledDateFrom: '指図開始予定日(FROM)',
      txtOrderStartScheduledDateTo: '指図開始予定日(TO)',
      txtMaterialCodeName: '製造品目コード',
      txtLotNo: '製造番号',
      txtReWeight: '再秤量',
      txtReWeightExcept: '再秤量を除く',
      txtReWeightOnly: '再秤量のみ',
      btnSearch: '検索',
      txtCollectiveWeightGroupList: '秤量まとめグループ一覧(秤量指示書作成前)',
      txtMaterialCode: '製造品目コード',
      txtMaterialName: '製造品名',
      txtBatchNo: 'バッチ番号',
      txtOrderNo: '製造指図番号',
      txtOrderProcess: '製造工程',
      txtInstractionCount: '明細数',
      txtWeightSetKey: '秤量セットキー',
      txtWeightSetName: '秤量セット名',
      txtWeightSet: '秤量セット',
      txtWeightRoomNo: '秤量室No',
      txtWeightRoom: '秤量室',
      txtBeforeWeightSOPFlowNo: '秤量前SOPフローNo',
      txtBeforeWeightSOPFlowName: '秤量前SOPフロー名',
      txtAfterWeightSOPFlowNo: '秤量後SOPフローNo',
      txtAfterWeightSOPFlowName: '秤量後SOPフロー名',
      txtWeightInstructionFormat: '秤量指示書フォーマット',
      txtCollectiveMaterialGroup: '品目まとめグループ',
      txtGroupAutoFlag: '自動生成フラグ',
      txtCollectiveOrderFlag: '別指図まとめ可能フラグ',
      txtReWeightFlag: '再秤量フラグ',
      txtReBatchFlag: '再バッチ指示フラグ',
      txtEraseFlag: '取消フラグ',
      txtBeforeEraseWeightInstructionNo: '取消元秤量指示書番号',
      txtChangeRequest: '秤量室変更依頼済',
      txtChangeRequestStatus: '変更依頼状態',
      txtDataExists: '有',
      txtDataNotExists: '-',
      btnInstructionsEntry: '指示書登録',
      txtWeightInstructionsEntry: '秤量指示書登録',
      txtWeightInstructionsDetailConfirm: '秤量指示書詳細(確定)',
      txtWeightInstructionsDetailErase: '秤量指示書詳細(取消)',
      txtWeightInstructionsDetailPrint: '秤量指示書詳細(印刷)',
      txtWeightInstructionDetailNo: '秤量指示明細番号',
      txtWeightInstructionsNo: '秤量指示書番号',
      txtWeightTiterKey: '補正計算',
      txtWeightSequence: '秤量順',
      txtWeightRoomChangeTimes: '秤量室変更回数',
      txtWeightPlanDate: '秤量予定日',
      txtWeightInstructionsComment: '秤量指示書コメント',
      txtAppendix: '特別作業指示',
      btnReference: '参照',
      txtEraseComment: '取消コメント',
      txtWeightInstructionsDetailList: '秤量指示明細一覧',
      txtWeightInstructionsListTitle: '秤量指示一覧',
      txtWeightPlanDateFrom: '秤量予定日(FROM)',
      txtWeightPlanDateTo: '秤量予定日(TO)',
      txtWeightInstructionStatus: '秤量指示書状態',
      txtApprovDateTime: '確定日時',
      txtApprovUser: '確定者',
      txtWeightInstructionStatusCreate: '指示書作成',
      txtWeightInstructionStatusApproval: '指示書確定',
      txtWeightInstructionStatusWork: '指示書処理中',
      txtWeightInstructionStatusEnd: '指示書処理済',
      btnPrint: '指示書印刷',
      btnErase: '指示書取消',
      btnConfirm: '指示書確定',
      txtWeightRoomChange: '秤量室変更',
      txtWeightDevice: '計量器',
      txtWeightDeviceSearch: '計量器(秤量/残計量用)',
      txtNormalWeightDevice: '計量器(通常秤量)',
      txtRemainingDevice: '計量器(残計量)',
      txtWeightDeviceNo: '計量器番号',
      txtWeightDeviceName: '計量器名',
      txtWeightRoomChangeApprove: '秤量室変更有無',
      txtReWeightInitApproval: '依頼前/承認済',
      txtReWeightInit: '依頼前',
      txtReWeightRequest: '依頼済',
      txtReWeightApproval: '承認済',
      btnRequestErase: '依頼取消',
      btnRequest: '依頼',
      btnApproval: '承認',
      txtWeightRoomChangeDetailRequest: '秤量室変更詳細(依頼)',
      txtWeightRoomChangeDetailApproval: '秤量室変更詳細(承認)',
      txtWeightSetBeforeChange: '変更前の秤量セット',
      txtWeightRoomBeforeChange: '変更前の秤量室',
      txtWeightDeviceBeforeChange: '変更前の計量器(通常秤量)',
      txtRemainingDeviceBeforeChange: '変更前の計量器(残計量)',
      txtWeightSetRoomAfterChange: '変更後の秤量セット/秤量室',
      txtWeightDeviceAfterChange: '変更後の計量器(通常秤量)',
      txtRemainingDeviceAfterChange: '変更後の計量器(残計量)',
      txtChangeComment: '変更コメント',
      txtReWeightInstructionCreate: '再秤量指示作成',
      txtWeightInstructionDetailStatus: '秤量指示明細状態',
      txtWeightMethod: '秤量方法',
      txtWeightMaterialCode: '秤量品目コード',
      txtWeightMaterialName: '秤量品名',
      txtWeightMaterialSequence: '秤量品投入番号',
      txtPrcRecConfirmFlag: '製造記録確認状態',
      txtLotoutFlag: '指図中止フラグ',
      btnRegWrapping: '小分け登録',
      btnRegDetail: '明細登録',
      txtReWeightInstructionEntryIndividual: '再秤量指示登録(小分け)',
      txtReWeightInstructionEntryDetail: '再秤量指示登録(明細)',
      SubdivisionList: '小分け一覧',
      LabelNo: 'ラベル番号',
      txtLabelDisuseFlag: 'ラベル投入停止フラグ',
      txtCreateComment: '作成コメント',
      txtReWeightInstructionComment: '再秤量指示書コメント',
      txtWeightRecordConfirm: '秤量記録確認',
      txtWeightRecordConfirmList: '秤量記録一覧',
      txtManageNo: '管理番号',
      txtWeightHandNo: '秤量ラベル発行回数',
      txtOrderInstructionValue: '指図指示量(成分値)',
      txtWeightInstructionValue: '秤量指示量(補正値)',
      txtWeightValue: '秤取量',
      txtWeightLabelValueIngr: 'ラベル秤取量(成分値)',
      txtWeightLabelValueAdjt: 'ラベル秤取量(補正値)',
      txtBatchWeightValue: 'バッチ累積秤取量',
      txtWeightLabelValueIngrTotal: 'ラベル累積秤取量(成分値)',
      txtWeightLabelValueAdjtTotal: 'ラベル累積秤取量(補正値)',
      txtRecRawDataT: '生データ(秤取量):風袋',
      txtRecRawDataM: '生データ(秤取量):原料',
      txtUnit: '単位',
      txtWgtUnit: '計量単位',
      txtWeightUnit: '秤量単位',
      txtWeightDate: '秤量日時',
      txtWeightTareNo: '風袋番号(風袋指定)',
      txtWeightTareName: '風袋名(風袋指定)',
      txtConNo: '容器番号',
      txtConName: '容器名',
      txtWeightTareValue: '風袋重量',
      txtWeightTareValueUnit: '風袋重量単位',
      txtRecordUser: '記録者',
      txtLastRecordModifyUser: '修正記録者(最終)',
      txtWorkUserCount: '作業者人数',
      txtWeightComment: '秤量コメント',
      txtModifyComment: '修正コメント',
      txtTermNo: '端末No',
      txtTermName: '端末名',
      txtBomMatSeq: '投入番号',
      txtWeighitTiterText: '補正計算式(テキスト)',
      txtTiterFormulaSubSt: '補正計算式(代入後)',
      txtTiterFormulaCalc: '補正計算式(結果)',
      txtTareNo: '風袋マスタ時の容器コード',
      txtTareName: '風袋マスタ時の容器名',
      txtCheckSopRecord: '※SOP記録を確認してください',
      btnCheck: '記録確認',
      btnWeightRecordDetail: '秤量記録詳細',
      btnWeightWorkerList: '作業者確認',
      btnWeightRecordEdit: '秤量記録修正',
      btnSopRecord: 'SOP記録',
      txtWeightRecordDetail: '秤量記録詳細',
      txtWeightWorkerList: '作業者確認',
      txtWorkUser: '作業者',
      txtWorkUserList: '作業者一覧',
      txtCommentList: 'コメント一覧',
      txtComment: 'コメント',
      txtWeightRecordEdit: '秤量記録修正',
      txtWorkUser1: '作業者1',
      txtWorkUser2: '作業者2',
      txtWorkUser3: '作業者3',
      txtWorkUser4: '作業者4',
      txtWorkUser5: '作業者5',
      txtAfterWeightInstructionValue: '修正後秤量指示量',
      txtAfterWeightValue: '修正後秤取量',
      txtAfterBatchWeightValue: '修正後バッチ累積秤取量',
      txtAfterWeightDate: '修正後秤量日時',
      txtAfterWeightTareValue: '修正後風袋重量',
      txtAfterRecordUser: '修正後記録者ID',
      txtAfterWorkUser1Id: '修正後作業者1ID',
      txtAfterWorkUser2Id: '修正後作業者2ID',
      txtAfterWorkUser3Id: '修正後作業者3ID',
      txtAfterWorkUser4Id: '修正後作業者4ID',
      txtAfterWorkUser5Id: '修正後作業者5ID',
      txtModifyCommentInpt: '修正コメント入力',
      txtWeightSopRecord: '秤量前後SOP記録',
      txtWeightSopFlowList: 'SOPフロー一覧',
      txtStatus: '確認状態',
      txtFlowStatus: 'フロー状態',
      txtDeviationLevel: '異状レベル',
      txtSopFlowName: 'SOPフロー名',
      txtOrderStartDate: '製造開始日時',
      txtSOPFlowStartDate: 'SOPフロー開始日時',
      txtSOPFlowStartDateOnly: 'SOPフロー開始日',
      txtOrderEndDate: '製造終了日時',
      txtSOPFlowEndDate: 'SOPフロー終了日時',
      txtOrderRecordConfirmDate: '製造記録確認日時',
      txtSOPFlowRecordStampDate: 'SOPフロー記録検印日時',
      txtDeviationComment: '異状コメント',
      txtWorkComment: '作業コメント',
      txtDeviationCheckComment: '異状確認コメント',
      txtRecordCheckComment: '記録確認コメント',
      btnFlowDetail: '作業詳細',
      txtWeightSopDetail: '秤量前後SOP作業詳細',
      txtWeightSopFlowInfo: 'SOPフロー情報',
      txtWorkOperationRecord: '作業実施記録',
      txtFlowDifferentNumber: 'フロー内異状件数',
      txtSopNodeTimes: '実行数',
      txtWorkInstructionDetail: '作業指示内容',
      txtInstructionValue: '指示値',
      txtModifyCount: '修正回数',
      txtRecordValue1: '記録値1',
      txtRecordValue2: '記録値2',
      txtRecordValue3: '記録値3',
      txtRecordValue4: '記録値4',
      txtRecordValue5: '記録値5',
      txtDeviationLowerLimit: '異状値下限',
      txtDeviationUpperLimit: '異状値上限',
      txtReferenceValue1: '参考値1',
      txtReferenceValue2: '参考値2',
      txtReferenceValue3: '参考値3',
      txtReferenceValue4: '参考値4',
      txtReferenceValue5: '参考値5',
      txtSopHelpDts: 'SOPヘルプ表示日時',
      txtRecordDate: '記録日時',
      txtDRecord: 'D記録',
      txtDRecordDate: 'D記録日時',
      txtDRecordUser: 'D記録者',
      txtMultipleWorkUser: '複数作業者',
      btnRecordEdit: '記録修正',
      btnSopRecordEditHistory: '修正履歴',
      btnSopRecordDeviationHistory: '異状履歴',
      txtRecordGuide: '※SOPフロー実施記録を最後まで確認してください',
      contentDifferentConfirmComent:
        '異状コメントを確認し、異状確認コメントを入力してください',
      btnSopRecordStamp: 'SOP記録検印',
      txtWeightSopComment: '秤量前後SOPコメント履歴一覧',
      txtType: '種別',
      txtSopRecordEditHistory: '秤量前後SOP修正履歴',
      txtBeforeRecordValue1: '修正前記録値1',
      txtAfterRecordValue1: '修正後記録値1',
      txtBeforeRecordValue2: '修正前記録値2',
      txtAfterRecordValue2: '修正後記録値2',
      txtBeforeRecordValue3: '修正前記録値3',
      txtAfterRecordValue3: '修正後記録値3',
      txtBeforeRecordValue4: '修正前記録値4',
      txtAfterRecordValue4: '修正後記録値4',
      txtBeforeRecordValue5: '修正前記録値5',
      txtAfterRecordValue5: '修正後記録値5',
      txtRecordModifyDate: '記録修正日時',
      txtModifyUser: '修正者',
      txtSopRecordDeviationHistory: '秤量前後SOP異状履歴',
      txtRecordValue: '記録値',
      txtJudgementType: '判定種別',
      txtDeviationDate: '異状発生日時',
      txtConfirmUser: '確認者',
      txtConfirmDate: '確認日時',
      txtSopRecordModify: '秤量前後SOP記録修正',
      txtWeightInstructionQRScan: '秤量指示書スキャン',
      txtWeightInstructionQRScanMessage:
        '秤量指示書のQRコードを読み取ってください',
      txtConfirm: '確認済',
      txtNotConfirm: '未確認',
    },
    Msg: {
      // メッセージ
      titleWeightInstructionsEntry: '秤量指示書登録',
      contentWeightInstructionsEntry: '秤量指示書を登録しますか？',
      titleWeightInstructionsEntryAppendix: '秤量指示書登録(特別作業指示あり)',
      contentWeightInstructionsEntryAppendix:
        '秤量指示書を登録しますか？\n※特別作業指示を登録するにはコメント入力が必要です。',
      titleWeightInstructionsConfirm: '秤量指示書確定',
      contentWeightInstructionsConfirm: '秤量指示書を確定しますか？',
      titleWeightInstructionsConfirmAppendix:
        '秤量指示書確定(特別作業指示あり)',
      contentWeightInstructionsConfirmAppendix:
        '秤量指示書を確定しますか？\n※特別作業指示を登録するにはコメント入力が必要です。',
      titleWeightInstructionsErase: '秤量指示書取消',
      contentWeightInstructionsErase: '秤量指示書を取消しますか？',
      titleWeightInstructionsPrint: '秤量指示書印刷',
      contentWeightInstructionsPrint: '秤量指示書を印刷しますか？',
      titleWeightRoomChgRequest: '秤量室変更依頼',
      contentWeightRoomChgRequest: '秤量室の変更を依頼しますか？',
      titleWeightRoomChgRequestErase: '秤量室変更依頼取消',
      contentWeightRoomChgRequestErase: '秤量室の変更依頼を取消しますか？',
      titleWeightRoomChgApproval: '秤量室変更承認',
      contentWeightRoomChgApproval: '秤量室の変更を承認しますか？',
      titleReWeightInstructionDetailCreate: '再秤量指示明細作成',
      contentReWeightInstructionDetailCreateWrapping:
        '以下の小分けに対して再秤量指示明細を作成しますか？\n再秤量対象の小分けは投入不可となります。\nラベル番号:{0}',
      contentReWeightInstructionDetailCreateDetail:
        '以下の秤量指示明細に対して再秤量指示明細を作成しますか？\n再秤量対象の小分けは投入不可となります。\n秤量指示明細番号:{0}',
      titleWeightRecordConfirm: '秤量記録確認',
      contentWeightRecordConfirm: '秤量記録の確認を完了しますか？',
      titleNotEnteredDataError: '修正項目未入力エラー',
      contentNotEnteredDataError: '最低1項目の修正内容を入力して下さい。',
      titleWeightRecordEdit: '秤量記録修正',
      contentWeightRecordEdit: '秤量記録の修正を確定しますか？',
      titleWeightSopRecordConfirm: 'SOPフロー記録確認',
      contentWeightSopRecordConfirm: 'SOPフロー記録の確認を実行しますか？',
      contentWeightSopRecordStamp:
        'SOP記録検印後は記録修正ができなくなります。',
      titleWeightSOPRecordEdit: '秤量前後SOP記録修正',
      contentWeightSOPRecordEdit: '秤量前後SOP記録を修正しますか？',
    },
  },
  Prd: {
    // 製造
    Chr: {
      // キャラクタ
      txtFilter: '絞り込み',
      txtProcessRecord: '製造記録',
      txtProcessSopFlowList: '工程任意SOP一覧',
      txtSopFlowNo: 'SOPフローNo',
      txtSopFlowName: 'SOPフロー名',
      txtSopFlowLnum: 'SOPフロー実行ログ番号',
      txtSopNodeNo: 'SOPノードNo',
      txtSopNodeLnum: 'SOPノード実行ログ番号',
      txtExecutionDate: '実施日',
      txtExecutionDateFrom: '実施日（FROM）',
      txtExecutionDateTo: '実施日（TO）',
      txtDeviationLevel: '異状レベル',
      txtProcess: '工程',
      btnSearch: '検索',
      txtExecutionCount: '実施回数',
      btnFlowDetail: 'フロー詳細',
      txtSopFlowDetail: 'SOPフロー詳細',
      txtSopFlowInformation: 'SOPフロー情報',
      txtSopFlowRecord: 'SOPフロー実施記録',
      txtOrderProcess: '製造工程',
      txtOrderProcessSequence: '製造工程順',
      txtCheck: 'チェック',
      txtSopNodeTimes: '実行数',
      txtWorkInstructionDetail: '作業指示内容',
      txtInstructionDetail: '指示内容',
      txtRecord: '記録',
      txtSopHelpDts: 'SOPヘルプ表示日時',
      txtRecordDate: '記録日時',
      txtRecordUser: '記録者',
      txtDRecord: 'D記録',
      txtDRecordDate: 'D記録日時',
      txtDRecordUser: 'D記録者',
      txtMultipleWorkUser: '複数作業者',
      txtDeviationComment: '異状コメント',
      txtWorkComment: '作業コメント',
      txtModifyComment: '修正コメント',
      btnRecordModify: '記録修正',
      btnModifyHistory: '修正履歴',
      btnCommentConfirm: 'コメント確認',
      btnDeviationHistory: '異状履歴',
      btnRecordApproval: '記録承認',
      txtRecordApproval: '記録承認',
      txtRecordConfirm: '記録確認',
      txtRecordGuide: '※SOPフロー実施記録を最後まで確認してください',
      contentDifferentConfirmComent:
        '異状コメントを確認し、異状確認コメントを入力してください',
      txtRecordModify: '記録修正',
      txtWorkDetail: '作業内容',
      txtBeforeRecordValue: '修正前記録値',
      txtAfterRecordValue: '修正後記録値',
      txtModifyCommentInput: '修正コメント入力',
      btnModifyConfirm: '修正確定',
      txtModifyHistoryList: '修正履歴一覧',
      txtModifyCount: '修正回数',
      txtRecordModifyDate: '記録修正日時',
      txtModifyUser: '修正者',
      txtComment: 'コメント',
      btnSopRecord: 'SOP記録',
      btnBillOfMaterialsInformation: '投入情報',
      btnProductionInformation: '出来高情報',
      txtProductionInformation: '出来高情報',
      txtProductionInformationSubDivision: '出来高情報（小分け）',
      txtSopRecordCount: 'SOP記録件数',
      txtBillOfMaterialsRecordCount: '投入記録件数',
      txtProductionRecordCount: '出来高記録件数',
      txtDeviationHistoryList: '異状履歴一覧',
      txtInstructionValue: '指示値',
      txtUnit: '単位',
      txtRecordValue: '記録値',
      txtJudgementType: '判定種別',
      txtBoundLowerLimit: '限界値下限',
      txtStandardLowerLimit: '規格値下限',
      txtStandardUpperLimit: '規格値上限',
      txtBoundUpperLimit: '限界値上限',
      txtDeviationDate: '異状発生日時',
      txtConfirmUser: '確認者',
      txtConfirmDate: '確認日時',
      txtDeviationCount: '異状件数',
      txtCommentHistoryList: 'コメント履歴一覧',
      txtType: '種別',
      txtOrderRecordConfirm: '製造記録確認',
      txtRecordConfirmList: '記録確認対象一覧',
      txtOrderQrCode: '製造指図書QRコード',
      txtOrderNo: '製造指図番号',
      txtMaterialCodeName: '品名コード/品名',
      txtPrescriptionCodeName: '処方コード/処方名',
      txtOrderStartDateFrom: '製造開始日（FROM）',
      txtOrderStartDateTo: '製造開始日（TO）',
      txtOrderEndDateFrom: '製造終了日（FROM）',
      txtOrderEndDateTo: '製造終了日（TO）',
      txtOrderStatus: '製造指図状態',
      txtOrderStatusInProduction: '製造指図状態（生産中）',
      txtOrderStatusWaitingForConfirmation: '製造指図状態（記録確認待ち）',
      txtMaterialTitle: '品目名称',
      txtPrescriptionName: '処方',
      txtOrderStartDate: '製造開始予定日',
      txtSOPFlowStartDate: 'SOPフロー開始日時',
      txtOrderEndDate: '製造終了予定日',
      txtSOPFlowEndDate: 'SOPフロー終了日時',
      txtExpiryDate: '使用期限',
      txtStatus: '状態',
      txtHandOverComment: '指図コメント',
      txtRecordApprovalComment: '記録承認コメント',
      btnRecordDetail: '記録詳細',
      txtOrderRecordDetail: '製造記録詳細',
      txtOrderInformation: '製造指図情報',
      txtProductionMaterialCode: '親品目コード',
      txtProductionMaterialName: '親品目',
      txtPlanQuantity: '生産予定量',
      txtProduction: '出来高',
      txtOrderStartDatePlan: '製造開始日　予定',
      txtOrderStartDateResults: '製造開始日　実績',
      txtYieldValue: '収率',
      btnWorkloadRegistration: '作業工数登録',
      btnProductionModify: '出来高修正',
      txtBatchNo: 'バッチ番号',
      txtOrderRecordConfirmDate: '製造記録確認日',
      txtSOPFlowRecordStampDate: 'SOPフロー記録検印日時',
      btnSopFlowList: 'SOPフロー一覧',
      btnOrderRecordConfirm: '指図記録確認',
      btnWorkloadConfirm: '作業工数確認',
      btnFlowList: 'フロー一覧',
      txtSopFlowList: 'SOPフロー一覧',
      txtBillOfMaterialsInformation: '投入情報',
      txtMaterialCode: '品目コード',
      txtBillOfMaterialsName: '投入品名',
      txtBillOfMaterialsQuantity: '投入実績量',
      btnBillOfMaterialsRecordModify: '投入記録修正',
      txtBillOfMaterialsRecordModify: '投入記録修正',
      txtBeforeDiscardQuantity: '修正前廃棄量',
      txtAfterDiscardQuantity: '修正後廃棄量',
      txtDiscardQuantity: '廃棄量',
      txtWeightRecord: '秤量記録',
      txtWeightInstructionsCreate: '秤量指示書',
      txtWeightInstructionStatus: '指示書状態',
      txtWorkTime: '作業時刻',
      txtLotNo: '製造番号',
      txtWeightMaterialCode: '秤量品目コード',
      txtWeightMaterialItemName: '秤量品名',
      txtManageNo: '管理番号',
      txtOrderInstructionValue: '指図指示量',
      txtWeightInstructionValue: '秤量指示量(補正値)',
      txtBatch: 'バッチ',
      txtWeightHand: '小分け',
      txtWeightTare: '風袋',
      txtWeightValue: '秤取量',
      txtBatchCount: 'バッチ総数',
      txtReWeight: '再秤量',
      txtReWeightFlag: '再秤量フラグ',
      txtDeviceId: '機器ID',
      txtWeightUser: '秤量者・補助者',
      btnWeightRecordModify: '秤量記録修正',
      txtProductionModify: '出来高修正',
      txtBeforeProduction: '修正前出来高',
      txtAfterProduction: '修正後出来高',
      txtWorkloadRegistration: '作業工数登録',
      txtWorkerCount: '作業人数',
      txtWorkingTimePre: '作業時間(前作業)',
      txtWorkingTimeSwitch: '作業時間(切替作業)',
      txtWorkingTimeActual: '作業時間(実作業)',
      txtWorkingTimeCleaning: '作業時間(人清掃)',
      btnConfirm: '確定',
      txtSopFlowRecordCount: 'SOPフロー実施記録件数',
      btnRecordConfirm: '記録確認',
      txtDeviationHistoryConfirm: '異状履歴確認',
      txtDeviationLowerLimit: '異状値下限',
      txtDeviationUpperLimit: '異状値上限',
      btnCheck: '確認',
      txtDeviationCommentInput:
        '異状確認コメント入力：異状レベル２以上の異状がある場合入力必須',
      txtRecordDateFrom: '記録日（FROM）',
      txtRecordDateTo: '記録日（TO）',
      txtDeviationCheckComment: '製造記録確認時の異状確認コメント',
      txtProcessSopRecord: '工程任意SOP記録',
      txtGranulation: '造粒',
      txtBlending: '混合',
      txtTabletting: '打錠',
      txtPackaging: '包装',
      txtOrderRecordReference: '製造記録参照',
      txtOrderDocumentScan: '製造指図書スキャン',
      txtOrderRecordListOptionalSOPList: '製造指図記録一覧/工程任意SOP一覧',
      txtOrderDocumentNo: '製造指図書番号',
      btnClear: 'クリア',
      btnRecordRePrint: '記録書再出力',
      btnApprovalCancel: '承認取り消し',
      btnConfirmCancel: '確認取り消し',
      txtFormOutput: '帳票出力',
      txtSelectGuide: '出力する製造記録帳票を選んでください',
      txtSopRecord: 'SOP実施記録',
      txtProductionRecord: '出来高記録',
      txtBillOfMaterialsRecord: '投入記録',
      btnOutput: '出力',
      txtOrderRecordApproval: '製造記録承認',
      txtOrderList: '製造指図一覧',
      btnOrderRecordAprove: '製造記録承認',
      txtRecord1: '記録値ー1',
      txtRecord2: '記録値ー2',
      txtRecord3: '記録値ー3',
      txtRecord4: '記録値ー4',
      txtRecord5: '記録値ー5',
      txtAfterRecordValue1: '修正後記録値ー1',
      txtAfterRecordValue2: '修正後記録値ー2',
      txtAfterRecordValue3: '修正後記録値ー3',
      txtAfterRecordValue4: '修正後記録値ー4',
      txtAfterRecordValue5: '修正後記録値ー5',
      txtWeightHandNo: '秤量ラベル発行回数',
      txtBatchWeightValue: 'バッチ累計秤取量',
      txtWeightDate: '秤量日時',
      txtWeightTareValue: '風袋重量',
      txtWeightTareValueUnit: '風袋重量単位',
      txtHistorySelect: '修正履歴選択',
      txtHistorySelectGuide: '修正履歴を選択してください',
      btnSopModifyRecord: 'SOP修正記録',
      btnBillOfMaterialsModify: '投入修正',
      txtRecordConfirmDateFrom: '記録確認日（FROM）',
      txtRecordConfirmDateTo: '記録確認日（TO）',
      txtQuantityResults: '生産数実績',
      txtRecordConfirmDate: '記録確認日',
      txtOrderRecordConfirmUser: '製造記録確認者',
      txtOrderRecordApprovalDate: '製造記録承認日',
      txtOrderRecordApprovalUser: '製造記録承認者',
      txtOrderProcessRecordConfirmSubjectList: '製造工程別 記録確認対象一覧',
      txtOrderProgress: '指図状態（実行中）',
      txtOrderComplete: '指図状態（実行完了）',
      txtOrderConfirm: '指図状態（確認済）',
      txtMaterialCD: '品目コード',
      txtVolumeName: '出来高品名',
      txtProductionQuantity: '生産量',
      txtOrderState: '指図状態',
      txtRecordConfirmComment: '記録確認コメント',
      btnWeightRecordConfirmFix: '秤量記録確認・修正',
      btnSOPRecordConfirmFix: 'SOP記録確認・修正',
      btnBillOfMaterialsResultRecordConfirmFix: '投入実績確認・修正',
      btnWorkCostRecordConfirmFix: '作業工数確認・修正',
      btnSOPRecordFixHistory: 'SOP記録修正履歴',
      btnBillOfMaterialsResultFixHistory: '投入実績修正履歴',
      btnVolumeFixHistory: '出来高修正履歴',
      btnSOPRecordDifferentHistory: 'SOP記録異状履歴',
      btnOrderRecordStamp: '指図記録検印',
      txtConfirmState: '確認状態',
      txtFlowState: 'フロー状態',
      txtDifferentConfirmComment: '異状確認コメント',
      txtOrderQuantity: '指図数量',
      txtFirstBillOfMaterialsActualQuantity: '当初投入実績量',
      txtFirstDisposalActualQuantity: '当初廃棄実績量',
      txtFirstDisposalActualQuantityItem1: '当初廃棄実績量内訳1',
      txtFirstDisposalActualQuantityItem2: '当初廃棄実績量内訳2',
      txtFirstDisposalActualQuantityItem3: '当初廃棄実績量内訳3',
      txtDisposalActualQuantity: '廃棄実績量',
      txtDisposalActualQuantityItem1: '廃棄実績量内訳1',
      txtDisposalActualQuantityItem2: '廃棄実績量内訳2',
      txtDisposalActualQuantityItem3: '廃棄実績量内訳3',
      txtBeforeDiscardQuantityItem1: '修正前廃棄量内訳1',
      txtAfterDiscardQuantityItem1: '修正後廃棄量内訳1',
      txtBeforeDiscardQuantityItem2: '修正前廃棄量内訳2',
      txtAfterDiscardQuantityItem2: '修正後廃棄量内訳2',
      txtBeforeDiscardQuantityItem3: '修正前廃棄量内訳3',
      txtAfterDiscardQuantityItem3: '修正後廃棄量内訳3',
      txtDiscardQuantityItem1: '廃棄量内訳1',
      txtDiscardQuantityItem2: '廃棄量内訳2',
      txtDiscardQuantityItem3: '廃棄量内訳3',
      txtMaterial: '品目',
      txtWeightInstructionsNo: '秤量指示書番号',
      txtWeightOrderDetailNumber: '秤量指示明細番号',
      txtWgtHandLblSid: '秤量ラベル番号',
      txtWeightOrderState: '秤量指示状態',
      txtWeightMaterialCD: '秤量品目CD',
      txtWeightMaterialName: '秤量品名',
      txtBatchNumber: 'バッチ番号',
      txtOrderMaterialCD: '製造品目コード',
      txtOrderMaterialName: '製造品名',
      txtFixBeforeYield: '修正前収率 [%]',
      txtFixAfterYield: '修正後収率 [%]',
      txtFixYield: '収率 [%]',
      txtPeopleCleanTime: 'H01 人・清掃 時間',
      txtFixPeopleCleanTime: '修正 H01 人・清掃 時間',
      txtPeopleOperationTime: 'H02 人・稼働 時間',
      txtFixPeopleOperationTime: '修正 H02 人・稼働 時間',
      txtPeopleSwitchTime: 'H03 人・切替準備 時間',
      txtFixPeopleSwitchTime: '修正 H03 人・切替準備 時間',
      txtMachineOperationTime: 'M01 機械・稼働 時間',
      txtFixMachineOperationTime: '修正 M01 機械・稼働 時間',
      txtOrderRegister: '指図登録',
      txtOrderDisapproval: '指図否認',
      txtOrderCancel: '指図取消',
      txtOrderApproval: '指図承認',
      txtOrderStop: '指図中止',
      txtSuspendResume: '中断再開',
      txtStamp: '検印',
      txtRecordDisapproval: '記録否認',
      txtWorkCost: '作業工数',
      txtRecordApprovalCancel: '記録承認取消',
      txtApprovalCancel: '承認取消',
      txtCancel: '中断',
      txtSkip: 'スキップ',
      txtCategory: '分類',
      txtEvent: 'イベント',
      txtWorkOperationRecord: '作業実施記録',
      txtFlowDifferentNumber: 'フロー内異状件数',
      txtCheckBox: 'チェックボックス',
      txtRecordValue1: '記録値1',
      txtRecordValue2: '記録値2',
      txtRecordValue3: '記録値3',
      txtRecordValue4: '記録値4',
      txtRecordValue5: '記録値5',
      txtReferenceValue1: '参考値１',
      txtReferenceValue2: '参考値２',
      txtReferenceValue3: '参考値３',
      txtReferenceValue4: '参考値４',
      txtReferenceValue5: '参考値５',
      btnSOPRecordStamp: 'SOP記録 検印',
      txtFixLogSOPWorkRecord: '修正履歴 SOP作業記録',
      txtFixBeforeRecordValue1: '修正前記録値１',
      txtFixAfterRecordValue1: '修正後記録値１',
      txtFixBeforeRecordValue2: '修正前記録値２',
      txtFixAfterRecordValue2: '修正後記録値２',
      txtFixBeforeRecordValue3: '修正前記録値３',
      txtFixAfterRecordValue3: '修正後記録値３',
      txtFixBeforeRecordValue4: '修正前記録値４',
      txtFixAfterRecordValue4: '修正後記録値４',
      txtFixBeforeRecordValue5: '修正前記録値５',
      txtFixAfterRecordValue5: '修正後記録値５',
      txtBeforeWeightValue: '修正前秤取量',
      txtAfterWeightValue: '修正後秤取量',
      txtBeforeBatchWeightValue: '修正前バッチ累積秤取量',
      txtAfterBatchWeightValue: '修正後バッチ累積秤取量',
      txtBeforeWeightTareValue: '修正前風袋重量',
      txtAfterWeightTareValue: '修正後風袋重量',
      txtBeforeRecUsr: '修正前記録者',
      txtAfterRecUsr: '修正後記録者',
      txtBeforeWorkUser1: '修正前作業者1',
      txtAfterWorkUser1: '修正後作業者1',
      txtBeforeWorkUser2: '修正前作業者2',
      txtAfterWorkUser2: '修正後作業者2',
      txtBeforeWorkUser3: '修正前作業者3',
      txtAfterWorkUser3: '修正後作業者3',
      txtBeforeWorkUser4: '修正前作業者4',
      txtAfterWorkUser4: '修正後作業者4',
      txtBeforeWorkUser5: '修正前作業者5',
      txtAfterWorkUser5: '修正後作業者5',
      txtFixLogBillOfMaterialsRecord: '修正履歴 投入記録',
      txtFixLogVolumeRecord: '修正履歴 出来高記録',
      txtFixLogWeightRecord: '修正履歴 秤量記録',
      txtQrCodeReadResult: 'QRコード読取結果: ',
      txtOrderRecordApproveCount: '製造記録承認回数',
      btnWeightRecordConfirm: '秤量記録 確認',
      btnSOPRecordFixLog: 'SOP記録修正 履歴',
      btnBillOfMaterialsInfomationConfirm: '投入情報 確認',
      btnWorkCostConfirmFix: '作業工数 確認',
      btnSOPRecordConfirm: 'SOP記録 確認',
      btnOrderRecordApprove: '指図記録 承認',
      btnOrderRecordDisapprove: '指図記録 否認',
      txtDifferentConfirmSubjectSOPFlowList: '異状確認対象SOPフロー一覧',
      txtDifferentConfirm: '異状確認',
      txtOrderRecordList: '製造指図記録一覧',
      txtOrderStateUnverified: '指図状態（未照査）',
      txtOrderStateVerifying: '指図状態（照査中）',
      txtOrderStateVerified: '指図状態（照査済）',
      txtApprovalDsp: '承認状態',
      txtApproveCount: '承認回数',
      txtRecordApproveDay: '記録承認日',
      txtOrderDetail: '指図詳細',
      txtRecordRePrint: '記録書再出力',
      btnPrcFlowDetail: '作業詳細',
      btnSOPFixLog: 'SOP修正履歴',
      txtImplementationRecordDay: '実施記録日',
      btnProcessSOPApprove: '工程SOP 承認',
      txtProcessSOPApprove: '工程SOP 承認',
      btnRecordApprovalCancel: '記録承認取消',
      txtMaterialName: '品名',
      txtProductBatchNo: '生産バッチ番号',
      txtBillOfOrder: '投入番号',
      txtBillOfLabel: '投入ラベル',
      txtProductionLabel: '出来高ラベル',
      txtResultLogNumber: '実績ログ番号',
      btnProductionRecordDetail: '出来高記録詳細',
      btnBillOfRecordDetail: '投入記録詳細',
      btnFix: '修正',
      btnFixHistory: '修正履歴',
      btnRecordConfirmFix: '確認・修正',
      txtActualInput: '投入実績',
      txtSopRec: 'SOP記録',
      txtSopRecordConfirmComment: 'SOP記録確認コメント',
      txtWeightDevice: '計量器',
      txtWgtUnit: '計量単位',
      txtWeightUnit: '秤量単位',
      txtReBatchFlag: '再バッチ指示フラグ',
      txtWeightLabelValueIngr: 'ラベル秤取量(成分値)',
      txtWeightLabelValueAdjt: 'ラベル秤取量(補正値)',
      txtWeightLabelValueIngrTotal: 'ラベル累積秤取量(成分値)',
      txtWeightLabelValueAdjtTotal: 'ラベル累積秤取量(補正値)',
      txtRecRawDataT: '生データ(秤取量):風袋',
      txtRecRawDataM: '生データ(秤取量):原料',
      txtWeightTareNo: '風袋番号(風袋指定)',
      txtWeightTareName: '風袋名(風袋指定)',
      txtConNo: '容器番号',
      txtConName: '容器名',
      txtLastRecordModifyUser: '修正記録者(最終)',
      txtWorkUserCount: '作業者人数',
      txtWeightComment: '秤量コメント',
      txtTermNo: '端末No',
      txtTermName: '端末名',
      txtWeighitTiterText: '補正計算式(テキスト)',
      txtTiterFormulaSubSt: '補正計算式(代入後)',
      txtTiterFormulaCalc: '補正計算式(結果)',
      txtTareNo: '風袋マスタ時の容器コード',
      txtTareName: '風袋マスタ時の容器名',
      txtWeightDeviceNo: '計量器番号',
      txtWeightDeviceName: '計量器名',
      txtWeightMaterialSequence: '秤量品投入番号',
      txtWeightMethod: '秤量方法',
      btnSopRecordStamp: 'SOP記録検印',
      txtConfirmList: '製造記録確認 記録詳細',
      txtApprovalList: '製造記録承認 記録詳細',
      txtReferenceList: '製造記録参照 記録詳細',
      txtConfirmSopFlowList: '製造記録確認 SOP記録',
      txtApprovalSopFlowList: '製造記録承認 SOP記録',
      txtReferenceSopFlowList: '製造記録参照 SOP記録',
      txtConfirmSopFlowInfoList: '製造記録確認 SOP作業詳細',
      txtApprovalSopFlowInfoList: '製造記録承認 SOP作業詳細',
      txtReferenceSopFlowInfoList: '製造記録参照 SOP作業詳細',
      txtProcessSopFlowInfoList: '工程作業記録 SOP作業詳細',
      txtConfirmWeighing: '製造記録確認 秤量記録',
      txtApprovalWeighing: '製造記録承認 秤量記録',
      txtReferenceWeighing: '製造記録参照 秤量記録',
      txtConfirmPrdWgtRecModifyList: '製造記録確認 秤量記録修正履歴',
      txtApprovalPrdWgtRecModifyList: '製造記録承認 秤量記録修正履歴',
      txtReferencePrdWgtRecModifyList: '製造記録参照 秤量記録修正履歴',
      txtConfirmDeviantList: '製造記録確認 異状履歴',
      txtApprovalDeviantList: '製造記録承認 異状確認履歴',
      txtReferenceDeviantList: '製造記録参照 異状確認履歴',
      txtConfirmPrdBomList: '製造記録確認 投入実績',
      txtApprovalPrdBomList: '製造記録承認 投入実績',
      txtReferencePrdBomList: '製造記録参照 投入実績',
      txtConfirmPrdConfirmBomInfoList: '製造記録確認 投入記録詳細',
      txtApprovalPrdConfirmBomInfoList: '製造記録承認 投入記録詳細',
      txtReferencePrdConfirmBomInfoList: '製造記録参照 投入記録詳細',
      txtConfirmPrdProductList: '製造記録確認 出来高記録',
      txtApprovalPrdProductList: '製造記録承認 出来高記録',
      txtReferencePrdProductList: '製造記録参照 出来高記録',
      txtConfirmPrdConfirmProductInfoList: '製造記録確認 出来高記録詳細',
      txtApprovalPrdConfirmProductInfoList: '製造記録承認 出来高記録詳細',
      txtReferencePrdConfirmProductInfoList: '製造記録参照 出来高記録詳細',
      btnDifferentConfirm: '異状確認',
      txtBeforeBillOfMaterialsQuantity: '修正前投入量',
      txtAfterBillOfMaterialsQuantity: '修正後投入量',
      txtConfirmed: '確認済',
      txtApprovalPrdApprovalSOPDeviantList: '製造記録承認 異状確認',
      txtReferencePrdApprovalSOPDeviantList: '製造記録参照 異状確認',
      txtProductionRecordEdit: '製造記録修正 SOP実施記録',
      txtConfirmSopModList: '製造記録確認 SOP修正履歴',
      txtApprovalSopModList: '製造記録承認 SOP修正履歴',
      txtReferenceSopModList: '製造記録参照 SOP修正履歴',
      txtProcessSopModList: '工程作業記録 SOP修正履歴',
      txtConfirmBomModList: '製造記録確認 投入修正履歴',
      txtApprovalBomModList: '製造記録承認 投入修正履歴',
      txtReferenceBomModList: '製造記録参照 投入修正履歴',
      txtConfirmPrdModList: '製造記録確認 出来高修正履歴',
      txtApprovalPrdModList: '製造記録承認 出来高修正履歴',
      txtReferencePrdModList: '製造記録参照 出来高修正履歴',
      txtApprovalWorkloadConfirm: '製造記録承認 作業工数確認',
      txtReferenceWorkloadConfirm: '製造記録参照 作業工数確認',
    },
    Msg: {
      // メッセージ
      titleOrderRecordConfirm: '指図記録確認',
      contentOrderRecordConfirm: '指図記録の確認を実行しますか？',
      titleOrderRecordConfirmComplete: '指図記録確認終了',
      contentOrderRecordConfirmComplete: '指図記録を確認しました。',
      titleBillOfMaterialsModifyComplete: '投入記録修正終了',
      contentBillOfMaterialsModifyComplete:
        '修正内容は在庫量に反映しないため、別途在庫編集で修正してください。',
      titleProductionModifyComplete: '出来高記録修正終了',
      contentProductionModifyComplete:
        '出来高を修正しても在庫や収率の記録は変更されないため、別途在庫消費や収率の記録を修正してください。',
      titleSopFlowRecordConfirm: 'SOPフロー記録確認',
      contentSopFlowRecordConfirm: 'SOPフロー記録の確認を実行しますか？',
      titleSopFlowRecordConfirmComplete: 'SOPフロー記録確認終了',
      contentSopFlowRecordConfirmComplete: 'SOPフロー記録を確認しました。',
      titleOrderRecordApproval: '指図記録承認',
      contentOrderRecordApproval: '指図記録の承認を実行しますか？',
      titleSopFlowRecordConfirmCancel: '指図記録確認取り消し',
      contentSopFlowRecordConfirmCancel:
        '指図記録の確認取り消しを実行しますか？\n※実行するにはコメント入力が必須です。',
      selectOutput: '出力する帳票を選んでください。',
      titleProcessSopFlowRecordConfirm: '工程任意SOPフロー記録確認',
      contentProcessSopFlowRecordConfirm:
        '工程任意SOPフロー記録の確認を実行しますか？',
      titleWorkCostRegisterComplete: '作業工数登録終了',
      contentWorkCostRegisterComplete: '作業工数を登録しました。',
      contentSopStampRecordFixConfirm:
        '工程SOP 承認後は記録修正ができなくなります。',
      contentProductSopRecordStamp:
        'SOP記録検印後は記録修正ができなくなります。',
      titleOrderRecordApprovalComplete: '指図記録承認終了',
      contentOrderRecordApprovalComplete: '指図記録を承認しました。',
      titleOrderRecordApprovalCancel: '指図記録承認取消',
      contentOrderRecordApprovalCancel:
        '指図記録の承認取消を実行しますか？\n※実行するにはコメント入力が必須です。',
      titleRecordApprovalCancel: '記録承認取消',
      contentRecordApprovalCancel:
        '工程作業記録の承認取消を実行しますか？\n※実行するにはコメント入力が必須です。',
      titleNotSelectError: '未選択エラー',
      contentNotSelectError: '出力する帳票を選んでください',
      contentSopFlowRecordNoConfirm:
        '全てのSOPフローの状態が確認済ではありません。',
      titleOrderRecordConfirmError: '指図記録確認エラー',
      contentOrderRecordConfirmed: '指図記録が確認済です。',
      contentQrCodeRead: '製造指図書のQRコードを読み取ってください',
      contentDifferentConfirmComent:
        '異状コメントを確認し、異状確認コメントを入力してください',
      titleProductionRecordEdit: '製造記録修正',
      contentProductionRecordEdit: '製造記録修正を実施しますか？',
      titleProductInstructionErrorUnapproved: '製造記録未承認',
      contentProductInstructionErrorUnapproved:
        '選択中の製造記録が未承認のため製造指図書はダウンロードできません。',
      titleProductInstructionConfirm: '製造記録書ダウンロード',
      contentProductInstructionConfirm: '製造記録書をダウンロードしますか？',
      titleProductInstructionFinished: '製造記録書ダウンロード完了',
      contentProductInstructionFinished:
        '製造記録書のダウンロードが完了しました。',
      titleProcessInstructionErrorUnapproved: '工程作業記録未承認',
      contentProcessInstructionErrorUnapproved:
        '選択中の工程作業記録が未承認のため工程作業記録書はダウンロードできません。',
      titleProcessInstructionConfirm: '工程作業記録書ダウンロード',
      contentProcessInstructionConfirm:
        '工程作業記録書をダウンロードしますか？',
      titleProcessInstructionFinished: '工程作業記録書ダウンロード完了',
      contentProcessInstructionFinished:
        '工程作業記録書のダウンロードが完了しました。',
      titleCheckInfoConfirmWeighing: '秤量記録確認済チェック',
      contentCheckInfoConfirmWeighing:
        '指図の工程に紐づく秤量の記録確認が全て完了していないため、秤量記録は確認済にできません。',
    },
  },
  Mgr: {
    // Mgr
    Chr: {
      // キャラクタ
      txtDictionaryManagement: '辞書管理',
      txtGroupManagement: 'グループ管理',
      txtLogManagement: 'ログ管理',
      txtOperationHistory: '操作履歴',
      txtOperationHistoryDetails: '操作履歴詳細',
      txtPermissionManagement: '権限管理',
      txtRegistrationHistory: '登録履歴',
      txtRegistrationHistoryDetails: '登録履歴詳細',
      txtSystemLog: 'システムログ',
      txtSystemLogDetails: 'システムログ詳細',
      txtSystemManagement: '便利機能',
      txtUserManagement: 'ユーザー管理',
    },
    Msg: {
      // メッセージ
    },
  },
  Sys: {
    // システム
    Chr: {
      // キャラクタ
      txtInfoList: '情報一覧',
      txtLabelNo: '個装番号',
      txtTraceDir: 'トレース方向',
      txtLabelCatBefReplace: '貼替前 ラベル分類',
      txtLabelNoBefReplace: '貼替前 個装番号',
      txtQuantityBefReplace: '貼替前 数量',
      txtQtyUnitBefReplace: '貼替前 単位',
      txtLabelCatAftReplace: '貼替後 ラベル分類',
      txtLabelNoAftReplace: '貼替後 個装番号',
      txtQuantityAftReplace: '貼替後 数量',
      txtQtyUnitAftReplace: '貼替後 単位',
      txtReplaceDateTime: '貼替 実績日時',
      txtLotNo: 'ロットNo',
      txtItemCode: '品目コード',
      txtItemName: '品名',
      txtMesIfNo: '出納管理番号',
      btnTraceBackBefReplace: '貼替前 遡及',
      btnTraceFwdBefReplace: '貼替前 追跡',
      btnTraceBackAftReplace: '貼替後 遡及',
      btnTraceFwdAftReplace: '貼替後 追跡',
      btnTraceFwdLot: 'ロット 追跡',
      txtReprintLabel: 'ラベル再発行',
      txtLabelCategory: 'ラベル分類',
      txtPrintDate: '印刷日時',
      txtPrintDateFrom: '印刷日時(FROM)',
      txtPrintDateTo: '印刷日時(TO)',
      txtPrintUser: '印刷担当者',
      txtPrintNum: '印刷回数',
      txtLblQtyCur: '印刷情報 数量',
      txtLblQtyUnit: '単位',
      txtpltNo: 'パレット番号',
      txtReprintLabelList: 'ラベル情報一覧',
      btnPrint: '印刷',
      txtProcessName: '工程名',
      txtOdrNo: '指図番号',
      txtBillOfMaterialsWgtResultDate: '投入品 秤量 実績日時',
      txtBillOfMaterialsMakerLotNo: '投入品 メーカーロットNo.',
      txtBillOfMaterialsMakerName: '投入品 メーカー名',
      txtBillOfMaterialsLotNo: '投入品 ロットNo',
      txtBillOfMaterialsItemClassification: '投入品 品目区分',
      txtBillOfMaterialsItemCode: '投入品 品目コード',
      txtBillOfMaterialsItemName: '投入品 品名',
      txtBillOfMaterialsResultDate: '投入品 実績日時',
      txtBillOfMaterialsInvestedQualityStatus: '投入品 投入時 品質状態',
      txtBillOfMaterialsInvestedTestComment: '投入品 投入時 試験コメント',
      txtBillOfMaterialsInvestedEffectiveDate: '投入品 投入時 有効期限',
      txtBillOfMaterialsInvestedExpiryDate: '投入品 投入時 使用期限',
      txtBillOfMaterialsCurrentQualityStatus: '投入品 現在 品質状態',
      txtBillOfMaterialsCurrentTestComment: '投入品 現在 試験コメント',
      txtBillOfMaterialsCurrentEffectiveDate: '投入品 現在 有効期限',
      txtBillOfMaterialsCurrentExpiryDate: '投入品 現在 使用期限',
      txtVolumeLotNo: '出来高品 ロットNo',
      txtVolumeItemCode: '出来高品 品目コード',
      txtVolumeItemName: '出来高品 品名',
      txtVolumeResultDate: '出来高品 実績日時',
      txtVolumeCurrentQualityStatus: '出来高品 現在 品質状態',
      txtVolumeCurrentTestComment: '出来高品 現在 試験コメント',
      btnPdfPrint: 'PDF出力',
      btnQualityComment: '品質コメント',
      btnBillOfMaterialsTraceBack: '投入 遡及',
      btnBillOfMaterialsTraceForward: '投入 追跡',
      btnProductionTraceBack: '出来高 遡及',
      btnProductionTraceForward: '出来高 追跡',
    },
    Msg: {
      // メッセージ
      titleLabelReprintConfirm: 'ラベル再発行',
      contentLabelReprintConfirm:
        '選択したラベルを再発行します。よろしいですか？',
      titleLabelReprintComplete: 'ラベル再発行完了',
      contentLabelReprintComplete: 'ラベルの再発行が完了しました。',
    },
  },
  SOP: {
    // sop
    Chr: {
      // キャラクタ
      txtSopTitle: 'SOP 管理',
      txtSopName: 'SOP 名',
      txtSopID: 'ID',
      txtSopFlowNo: 'SOPフローNo',
      txtSopFlowNm: 'SOPフロー名称',
      txtMatNo: '品目CD',
      txtMatNm: '品名',
      txtRxNo: '処方CD',
      txtRxNm: '処方',
      txtPrcSeq: '製造順',
      txtPrcNm: '製造工程',
      txtSopBatchType: 'バッチ展開区分',
      txtWgtRoomNo: '秤量室',
      txtWgtRoomNm: '秤量室名',
      txtWgtSopCat: '秤量実行SOP分類',
      txtWgtSopCatSelect: '秤量実行分類',
      txtDspSeq: '表示順',
      txtHelp: 'ヘルプ',
      txtDetail: '詳細',
      txtCommon: '共通',
      txtSopGrid: 'グリッド',
      txtSopPicture: 'Miniｍap',
      txtSopAdaptCanvas: 'Canvas',
      txtSopActualSize: '実際サイズ',
      txtSopUndo: '撤销',
      txtSopRedo: 'リセット',
      txtSopZoomIn: '拡大',
      txtSopZoomOut: '縮小',
      txtSopDelete: '削除',
      txtSopAdapt: '自适应',
      txtSopCopy: '複製',
      txtSopPaste: '粘贴',
      txtSopUPDownCenter: '上下整列',
      txtSopLeftRightCenter: '左右整列',
      txtSopPreview: '预览',
      txtSopMenu: 'メニュー',
      txtSopTempName: '名称',
      txtSopCategory: 'カテゴリー',
      txtSopType: 'タイプ',
      txtSopTempRegistration: 'テンプレート登録',
      txtSopNoTempSelected: 'テンプレート未選択',
      txtSopTempDelete: 'テンプレート削除',
      txtSopBlockDetail: 'ブロック詳細',
      txtSopBlock: 'ブロック',
      txtSopNoBlockSelected: 'ブロック未選択',
      txtSopTemplate: 'テンプレート',
      txtSopTempSelect: 'テンプレート選択',
      txtSopTempSelectTitle: 'SOP テンプレート選択',
      txtSopDocSummary: 'ドキュメントサマリー',
      txtSopLayout: 'レイアウト',
      txtSopColor: 'カラー',
      txtSopMargin: 'マージン',
      txtSopBleed: '裁ち落とし',
      txtSopSchool: 'スクール',
      txtSopPageWidth: 'ページ幅',
      txtSopPageHeight: 'ページ高さ',
      txtSopSave: '保存',
      txtSopTemporarySave: '一時保存',
      txtSopSaveTitle: 'SOPノード登録',
      txtNodeID: 'ノードID',
      txtCommonSetting: '共通設定',
      txtPartSetting: '個別設定',
      txtConditionSetting: '条件設定',
      txtDisplaySetting: '表示設定',
      txtOtherSetting: 'その他の設定',
      txtSelectError: 'ノード選択チェックエラー',
      txtMissRange: 'ノード選択範囲開始終了チェックエラー',
      txtNoCategory: '未分類',
      txtSopSetting: 'SOP設定',
      txtSopSettingTitle: 'SOPフロー 全体設定',
      txtStartDate: 'SOPフロー使用 開始日',
      txtEndDate: 'SOPフロー使用 終了日',
      txtConfirmSetting: '確認/承認の有無',
      txtApproveGroup: 'フローの強制終了時 承認者グループ',
      txtApproveGroupYes: 'あり',
      txtApproveGroupNo: 'なし',
      txtSkipGroup: 'SOPスキップ時 承認者グループ',
      txtHelpSetting: 'SOPフローヘルプのファイルパス（PDFのみ、フルパス）設定',
      txtExpressionInput: '式入力',
      txtConnectedError: 'フロー連結エラー',
      txtSOPSettingNoValueError: '必須項目未入力',
      txtSettingStatusError: 'ノード詳細設定未確定エラー',
      txtGCodeMemoTitle: 'G-コードメモ',
      txtGCodeMemoList: 'G-コードメモ一覧',
      txtGCodeMemoComment: 'Gコードメモコメント',
      txtGCodeMemoDelete: 'G-コードメモ削除',
      txtGCodeMemoEdit: 'G-コードメモ編集',
      txtGCodeMemoAdd: 'G-コードメモ追加',
      btnGCodeMemoDelete: 'メモ削除',
      btnGCodeMemoEdit: 'メモ編集',
      btnGCodeMemoAdd: 'メモ追加',
      btnGCodeMemoApply: '反映',
      txtRangeSystem: 'システム全体',
      txtRangeBulkMatNo: '中間製品品目コード',
      txtRangeMatNo: '品目コード',
      txtRangeLotNo: '製造番号',
      txtRangeBatchUnit: 'バッチ単位',
      txtRangePrcSeq: '製造工程',
      txtRangeTerminal: '端末',
      txtRangeInstruction: '指図',
      txtEditError: 'ノード編集エラー',
      txtBranchSettingError: '分岐設定エラー',
      SOPRxSetting: {
        //
        txtTitleSearch: '指図SOPフロー検索',
        txtTitleAdd: 'SOPフロー新規作成',
        txtTitleCopyAdd: 'SOPフローコピー新規作成',
        txtMatNoNm: '品目CD/品名',
        txtRxNoNm: '処方CD/処方',
        txtPrcSeq: '工程順',
        txtMatNo: '品目CD',
        txtMatNm: '品名',
        txtRxNo: '処方CD',
        txtRxNm: '処方',
        txtPrcNm: '製造工程',
        txtSopFlowNo: 'SOPフローNo',
        txtSopFlowNmJp: 'SOPフロー名',
        txtFromMatNo: '【コピー元】品目CD/品名',
        txtFromRxNo: '【コピー元】処方CD/処方',
        txtFromPrcSeq: '【コピー元】工程順',
        txtFromSopFlowNo: '【コピー元】SOPフローNo/SOPフロー名',
      },
      SOPRxListSetting: {
        //
        btnAdd: '新規',
        btnCopyAdd: 'コピー新規',
        btnMod: '編集',
        btnDel: '削除',
        btnChange: '処方条件変更',
        txtMatInfo: '処方情報',
        txtRxSOPList: '指図SOP一覧',
        txtMatNo: '品目CD',
        txtMatNm: '品名',
        txtRxNo: '処方CD',
        txtRxNm: '処方',
        txtPrcSeq: '工程順',
        txtPrcNm: '製造工程',
        txtSopFlowNo: 'SOPフローNo',
        txtSopFlowNmJp: 'SOPフロー名',
        txtSopFlowVer: 'バージョン',
        txtMbrNo: 'MBR番号',
        txtMstRelPermFlg: 'リリース許可',
        txtCrtUsrId: '作成者ID',
        txtCrtUsrNm: '作成者',
        txtCrtDts: '作成日',
        txtUpdUsrId: '最終更新者ID',
        txtUpdUsrNm: '最終更新者',
        txtUpdDts: '最終更新日',
        txtSopBatchType: 'バッチ展開区分',
        txtHelpBinPath: 'フローヘルプ登録',
        txtDspSeq: '表示順',
      },
      SOPPrcSetting: {
        //
        txtTitleSearch: '工程任意SOPフロー検索',
        txtTitleAdd: 'SOPフロー新規作成',
        txtTitleCopyAdd: 'SOPフローコピー新規作成',
        txtPrcNo: '工程No',
        txtPrcNm: '工程',
        txtSopFlowNo: 'SOPフローNo',
        txtSopFlowNmJp: 'SOPフロー名',
        txtFromPrcNo: '【コピー元】工程',
        txtFromSopFlowNo: '【コピー元】SOPフローNo/SOPフロー名',
      },
      SOPWgtSetting: {
        //
        txtTitleSearch: '秤量前後SOPフロー検索',
        txtTitleAdd: 'SOPフロー新規作成',
        txtTitleCopyAdd: 'SOPフローコピー新規作成',
        txtWgtCat: '秤量SOP分類',
        txtWgtRoomLabel: '秤量室No/秤量室名',
        txtFromWgtRoomLabel: '【コピー元】秤量室No/秤量室名',
        txtFromFlowNm: '【コピー元】SOPフローNo/SOPフロー名',
        txtSopFlowNo: 'SOPフローNo',
        txtSopFlowNmJp: 'SOPフロー名',
      },
      SOPWgtListSetting: {
        //
        btnAdd: '新規',
        btnCopyAdd: 'コピー新規',
        btnMod: '編集',
        btnDel: '削除',
        btnChange: '秤量条件変更',
        txtWgtInfo: '秤量室情報',
        txtWgtSOPList: '秤量前後SOP一覧',
        txtWgtRoomNo: '秤量室No',
        txtWgtRoom: '秤量室',
        txtSopFlowNo: 'SOPフローNo',
        txtSopFlowNmJp: 'SOPフロー名',
        txtSopFlowVer: 'バージョン',
        txtMstRelPermFlg: 'リリース許可',
        txtCrtUsrId: '作成者ID',
        txtCrtUsrNm: '作成者',
        txtCrtDts: '作成日',
        txtUpdUsrId: '最終更新者ID',
        txtUpdUsrNm: '最終更新者',
        txtUpdDts: '最終更新日',
        txtWgtOperationType: '秤量実行分類',
        txtDspSeq: '表示順',
      },
      SOPPrcListSetting: {
        //
        btnAdd: '新規',
        btnCopyAdd: 'コピー新規',
        btnMod: '編集',
        btnDel: '削除',
        btnChange: '工程条件変更',
        txtPrcInfo: '工程情報',
        txtPrcSOPList: '工程任意SOP一覧',
        txtPrcNm: '製造工程',
        txtSopFlowNo: 'SOPフローNo',
        txtSopFlowNmJp: 'SOPフロー名',
        txtSopFlowVer: 'バージョン',
        txtMstRelPermFlg: 'リリース許可',
        txtCrtUsrId: '作成者ID',
        txtCrtUsrNm: '作成者',
        txtCrtDts: '作成日',
        txtUpdUsrId: '最終更新者ID',
        txtUpdUsrNm: '最終更新者',
        txtUpdDts: '最終更新日',
        txtRecApprovFlg: '記録承認設定',
        txtStYMD: 'SOPフロー使用開始日',
        txtEdYMD: 'SOPフロー使用有効期限',
        txtDspSeq: '表示順',
      },
      SOPInfoSetting: {
        //
        txtCommandSet: '指示内容設定',
        txtEmphasisWords: '強調ワード',
        txtSupplementaryComments: '補足コメント',
        txtManufacturingRecordSet: '製造記録記載設定',
        txtProductionRecord: '製造記録ヘの表示',
        txtDisp: '表示する',
        txtNotDisp: '表示しない',
        txtRepeatOperation: '繰り返し実行',
        txtFirstOnlyOperation: '初回のみ実行',
        txtPluralOperation: '複数回実行',
        txtRepeatOperationRecord: '繰り返し実行時の記録方法',
        txtLatestOnly: '最新のみ表示',
        txtAll: 'すべて表示',
        txtConfirmationCheckRecord: '記録確認時に確認必須',
        txtArbitrary: '任意',
        txtRequirement: '必須',
        txtDoubleCheck: 'ダブルチェック',
        txtDoubleCheckYes: 'する',
        txtDoubleCheckNo: 'しない',
        txtCheckerPermissionGroup: 'チェック者の権限グループ',
        txtNoNeedDoubleCheck: 'ダブルチェック必要なし',
        txtSpecificRole: '特定ロール',
        txtWorkerList: '作業者一覧',
        txtNonloggedIn: 'ログイン者以外',
        txtDeviationSettings: '逸脱設定',
        txtDeviationLevelSetting: '逸脱レベル設定',
        txtAnomalyLevelSetting: '異状レベル設定',
        txtJudgeValueShowFlg: '実行画面表示',
        txtHelpShowFlg: '作業ヘルプの強制表示指示',
        txtInterruptedCheck: 'SOP中断可否',
        txtInterruptedYes: '中断可能',
        txtInterruptedNo: '中断不可',
        txtskippableFlg: 'SOPスキップ可否',
        txtskippableFlgYes: 'スキップ可能',
        txtskippableFlgNo: 'スキップ不可',
        placeholderPleaseSelect: '選択項目',
        txtBlockName: 'ブロック名称',
        txtBlockBackground: 'ブロック背景色',
        txtNodeName: 'ノード名称',
        txtFirstOnly: '初回のみ',
        txtJudgeValueShowFlgYes: '表示',
        txtJudgeValueShowFlgNo: '非表示',
        txtHelpShowFlgExist: 'あり',
        txtHelpShowFlgWithout: 'なし',
        txtDevCorrLv: '異状発生時、記録確認作業の異状レベル',
        txtDevCorrLv0: '0',
        txtDevCorrLv1: '1',
        txtDevCorrLv2: '2',
        txtDevCorrLv3: '3',
        txtConfShowFlg: '確認ボタン押下時ダイアログ確認ウィンドウ表示',
        txtConfShowFlgYes: '表示',
        txtConfShowFlgNo: '表示せず次へ',
        txtConfirmProductionRecord: '製造記録確認時の確認必須',
        txtConfirmProductionRecordMust: '確認必須',
        txtConfirmProductionRecordArbitrary: '任意',
        txtHelpFileType: 'ヘルプタイプ',
        txtHelpFileTypeNo: 'なし',
        txtHelpFileTypeImage: '画像',
        txtHelpFileTypeMovie: '動画',
        txtHelpFileTypePDF: 'PDF',
        txtHelpFilePath: 'ヘルプファイルのファイルパス（フルパス）設定',
        txtHelpText: 'ヘルプテキスト',
        txtHelpClear: 'クリア',
        txtDeviationInputFlg: '異状時 権限対応の必要有無',
        txtDeviationInputFlgExist: 'あり',
        txtDeviationInputFlgWithout: 'なし',
        txtDeviationPrivGrpCd: '異状時 対応者権限グループ',
      },
      SOPDetailSetting: {
        //
        txtConfirmBtnType: '確認行為のボタン種別',
        txtConfirmRecordOption: '確認記録種別',
        txtConfirmationWord: '確認/未確認',
        txtConfirmationLetter: 'OK/NG',
        txtBranchSetting: '分岐設定',
        txtBranchSettingNull: '未選択',
        txtBranchSettingOK: 'OKや確認など、正側選択',
        txtBranchSettingNG: 'NGや未確認など、否側選択',
        txtBranchDestinationType: '分岐先種別',
        txtSelfNodeRepetition: '自ノード繰り返し',
        txtNoneBranch: 'なし',
        txtDestinationPartName: '移動先ノード名',
        txtDeviantResponse: '逸脱対応',
        txtOK: 'する',
        txtNO: 'しない',
        txtIndicatedValueSetting: '指示値設定',
        txtInstructionFixedValue: '固定値',
        txtLabel: 'ラベル',
        txtManualInput: '手入力 ',
        txtNodeID: 'ノードID',
        txtTable: 'テーブル',
        txtSeal: 'シール',
        txtArithmeticInput: '演算入力',
        txtTotalVolume: '出来高合計',
        txtTotalActualInput: '投入実績合計',
        txtTotalNodeValues: 'ノード値合計',
        txtSettingValue: '設定値',
        txtRangeSelection: '範囲選択',
        txtStartNodeNo: '開始ノードNo',
        txtEndNodeNo: '終了ノードNo',
        txtDecimalPlaces: '小数点以下桁数',
        txtOutputSelect: '出力先選択',
        txtExist: 'あり',
        txtWithout: 'なし',
        txtOutput: '出力先',
        txtVolume: '出来高',
        txtGCode: 'G-コード',
        txt232C: '２３２C',
        txtSCADA: 'SCADA',
        txtVolumeType: '出来高種別',
        txtVolumeNumber: '出来高数量',
        txtVolumePalette: '出来高パレット',
        txtVolumeContainer: '出来高容器',
        txtVolumeLocationSet: '出来高設定先ゾーン',
        txtVolumeLocationCode: '出来高ゾーンコード',
        txtInventoryUpdate: '在庫更新',
        txtlocationCodeSet: '設定先シールコード',
        txtexpirationDateSet: '使用期限有無',
        txtSelectDate: '日付を入力する',
        txtInputValueJudgment: '入力値判定',
        txtJudgmentMethod: '判定方法',
        txtWithinRange: '範囲内 ≦≧',
        txtWithinGreater: '範囲内 ≦',
        txtRangeValueType: '範囲値種類',
        txtAbsoluteValueInput: '絶対値入力',
        txtInstructionRelativeValue: '指図に対する相対値（値）',
        txtInstructionRelativePerCent: '指図に対する相対値（％）',
        txtJudgmentValue: '判定値',
        txtNoAbnormalities: '異状なし',
        txtUpperLimit: '管理上限',
        txtDeviationUpperLimit: '警告上限',
        txtDeviationLowerLimit: '警告下限',
        txtLowerLimit: '管理下限',
        txtShowJudgementValue: '判定値の表示',
        txtCommDeviceSet: '外部機器設定',
        txtInstructionValueUnit: '指示値単位',
        txtInputAcquisitionMethod: '入力取得方法',
        txtSingleTag: '単一タグ',
        txtMultipleTags: '複数タグ',
        txtDesignationTag: '飛び版',
        txtTagIDInput: 'タグID入力',
        txtSingleTagFrom: 'From',
        txtSingleTagTo: 'To',
        txtSeparatedTagCode: 'タグ番号（，区切り）',
        txtHaveSeparator: '区切りあり',
        txtNoSeparator: '区切り無し',
        txtSeparatorWord: '区切り文字',
        txtSingleAcquisition: '単一取得',
        txtContinuousAcquisition: '連続取得',
        txtDesignationAcquisition: '飛び取得',
        txtSeparatorPositionCode: '区切り位置番号',
        txtSeparatorPositionCodeFrom: '区切り位置番号 from',
        txtSeparatorPositionCodeTo: '区切り位置番号 to',
        txtPositionCodeSeparated: '区切り位置番号（,区切り）',
        txtWordRetrievalFrom: '文字列取得開始 From',
        txtWordRetrievalTo: '文字列取得開始 To',
        txtSamplingInput: 'サンプリング入力',
        txtSamplingMethod: 'サンプリング方法',
        txtSamplingTime: 'サンプリングタイム',
        txtLatestTime: '最新',
        txtMaximum: '最大数',
        txtScreenDisplayWay: '実行画面表示方法',
        txtPlotXToY: 'ｘ－ｙ Plot',
        txtNoPlot: '順不同',
        txtPlotX: 'X',
        txtPlotY: 'Y',
        txtAcquiredData: '取得データ',
        txtAcquiredCount: '取得回数',
        txtItemDisplay: '項目表示',
        txtDeviceName: '装置名称',
        txtInputOrOutputType: '入出力タイプ',
        txtConnectedDeviceRead: '接続機器から入力',
        txtConnectedDeviceWrite: '接続機器へ出力',
        txtUpperLowerLimitCheck: '上下限チェック',
        txtLimitCheckType: 'チェック種別',
        txtConsumptionSequence: '投入番号',
        txtProcessingMode: '処理モード',
        txtAcceptance: '受入(在庫消費無し)',
        txtAcceptanceInventoryConsumption: '受入(在庫消費有り)',
        txtActualConfirmation: '実績確定(残戻し無し)',
        txtActualConfirmationSurplus: '実績確定(残戻し有り)',
        txtAcceptanceActualConfirmation: '受入＋実績確定',
        txtReturnInventoryZoneCdSelection: '残戻しゾーン選択',
        txtItemCode: '品目(コード)',
        txtItemInput: '入力',
        txtPairedSOPSet: '対になるSOPノード指定',
        txtQuantityIndicated: '指示量（総数）',
        txtHave: '有',
        txtNotHave: '無',
        txtInventoryConsumption: '在庫消費(有り/無し)',
        txtDestinationZone: '受入れ先(実績確定時は消費元)ゾーン',
        txtSelectFromMaster: 'マスタから選択',
        txtAllConsumableItems: '全数消費品目(有効／無効)',
        txtActive: '有効',
        txtInactive: '無効',
        txtInputMode: '投入モード(バーコード／在庫選択)',
        txtBarcode: 'バーコード',
        txtInventorySelection: '在庫選択',
        txtAmountAccepted: '受け入れ量の上下限チェック',
        txtInputCheckSetting: '投入チェック　有無セッテイ',
        txtBarcodeReading: 'バーコード読み込み',
        txtDateSetMethod: '日時入力方式',
        txtDeviceAndVesselSetting: '装置、容器設定',
        txtContainers: '容器リスト',
        txtInstructionSelection: '指示選択',
        txtExpirationUpdate: '有効期限更新',
        txtCleaningStatusUpdate: '清掃状態更新',
        txtWorkConfirmationTime: '作業確定時の時間',
        txtSOPNodeDisplayedTime: 'SOPノードが表示されたときの時間',
        txtButtonPressedTime: 'ボタン押下時の時間',
        txtScreenDisplay: '画面表示',
        txtConsumptionItemName: '消費対象品目',
        txtLabelSetting: 'ラベル設定',
        txtSamplingLabelSetting: 'サンプリングラベル設定',
        txtLabelType: 'ラベル種別',
        txtVolumeLabel: '出来高ラベル',
        txtProductReceiptSlip: '製品入庫伝票',
        txtSampling: 'サンプリング',
        txtCardboardLabel: '段ボールラベル',
        txtSOPDisplay: 'SOP表示有無',
        txtOutputMethod: '出力方法',
        txtPerVolumeRecord: '出来高記録毎',
        txtVolumeRecordTotal: '出来高記録合計',
        txtByProductPerVolumeRecord: '副産物出来高記録毎',
        txtByProductVolumeRecordTotal: '副産物出来高記録合計',
        txtProductNameCode: '品名コード',
        txtFixed: '固定',
        txtNonFixed: '非固定',
        txtFixedNumOfSheet: '固定枚数',
        txtAdoptedNumSetType: '採取量入力方法',
        txtAdoptedNum: '採取量',
        txtUnit: '単位',
        txtTitle: 'タイトル',
        txtInputSource: '入力元',
        txtPcsOrLabel: '個／ラベル',
        txtCheckerPermissionGroup: 'チェック者の権限グループ',
        txtReviewerGroupSetting: '確認者グループ設定',
        txtReviewerGroupCode: '確認者グループコード',
        txtProcessingOriginalData: '元データの処理',
        txtDeleteAfterRegistration: '登録後削除',
        txtSaveAfterRegistration: '登録後も保持',
        txtDataFileDefine: 'データファイルの定義',
        txtCameraActivation: 'カメラ起動',
        txtFilePath: 'ファイルパス',
        txtFilePathSet: 'ファイルパス設定',
        txtWorkerSelection: '作業者選択',
        txtSystemSetting: 'システムで設定',
        txtFolderName: 'フォルダ名',
        txtFileName: 'ファイル名',
        txtFileNameSetting: 'ファイル名設定',
        txtDirectInput: '直接入力',
        txtNodeName: 'ノード名',
        txtGlobalSealReference: 'グローバルシール参照',
        txtBranchingMethod: '分岐方法',
        txtMenuSelection: 'メニュー選択',
        txtBranchNumSetting: '分岐数設定',
        txtMenuItemName: 'メニュー項目名称',
        txtConditional: '条件式',
        txtDestinationPart: '移動先ノード指定',
        txtInterlockMode: 'インターロックモード',
        txtWriting: '書込み',
        txtWait: '待ち',
        txtMonitoredParameterNum: '監視パラメータ数',
        txtWaitParameterGCode: '待機パラメータ G-コード',
        txtGHL: 'GHL-',
        txtWriteParameterNum: '書きパラメータ数',
        txtWriteParameterGCode: '書込みパラメータ G-コード',
        txtShelfLabelAddress: '棚札アドレス',
        txtShelfLabelServerIP: '棚札サーバIP',
        txtShelfLabelControlTelegram: '棚札制御電文',
        txtScreenID: '画面ID',
        txtPageNo: 'ページNo',
        txtVariableText: '可変テキスト',
        txtStartMethod: 'タイマー設定',
        txtInstructionTime: '指示時間',
        txtUpperLimitCheck: '上限チェック',
        txtManufacturingRecordSet: '製造記録記載設定',
        txtReasonCode: '事由コード',
        txtZoneType: 'ゾーン種別',
        txtForAllRegion: '全ゾーン対象',
        txtForOnSiteRegion: '現場ゾーン対象',
        txtDesignationRegion: 'ゾーン指定',
        txtRegionCode: 'ゾーンコード',
        txtReferentLabelName: '指値値ラベル名称',
        txtReferentLabelSetType: '指値値ラベル指定有無',
        txtDeviantTreatment: '逸脱扱い',
        txtRecordedValueSetMethod: '記録値入力方法',
        txtJudgmentValueX: '判定値：X',
        txtBranchNumber: '分岐数',
        txtExpressionInput: '演算式入力',
        txtOutputSetting: '出力先設定',
        txtProgressRate: '進捗率 入力',
        txtInputSourceSet: '入力元設定',
        txtInputValue: '入力値',
        txtHelpFileType: 'ヘルプタイプ',
        txtHelpText: 'ヘルプテキスト（任意）',
        txtMessage: 'メッセージ',
        PartInstructionConfirm: {
          //
          txtConfirmRecordOption: '確認記録種別',
          txtConfirmRecordOptionSelect: '確認記録選択肢',
          txtConditionBranchLabel: '分岐設定',
          txtConditionBranch: '条件分岐',
          txtConditionBranchWithout: 'なし',
          txtConditionBranchExist: 'あり',
          txtDeviationBranchNodeId: '異状時 飛び先ノードID/ノード名',
          txtDeviationMessageText: '異状時 メッセージ表示テキスト',
        },
        PartSopTimer: {
          //
          txtInstructionTime: '指示時間',
          txtInstructionTimeFixedValue: '固定値',
          txtInstructionTimeNodeId: 'ノードID',
          txtStartMethod: 'タイマー開始方法',
          txtStartMethodDisplay: 'SOP表示時',
          txtStartMethodButton: '計測開始ボタン',
          txtUpperLowerLimitCheck: '上下限チェック',
          txtUpperLowerLimitCheckWithout: 'なし',
          txtUpperLowerLimitCheckExist: 'あり',
          txtJudgeValueShowFlg: '判定値の実行画面表示',
          txtJudgeValueShowFlgNo: '非表示',
          txtJudgeValueShowFlgYes: '表示',
          txtJudgeType: '判定種別',
          txtAbsoluteValue: '絶対値',
          txtRelativeValue: '相対値(値)',
          txtRelativePercent: '相対値(%)',
          txtLowerLimit: '限界値下限 (≦記録値)',
          txtDeviationLowerLimit: '異状値下限 (≦記録値)',
          txtDeviationUpperLimit: '異状値上限 (記録値≦)',
          txtUpperLimit: '限界値上限 (記録値≦)',
          txtConditionBranch: '条件分岐',
          txtConditionBranchWithout: 'なし',
          txtConditionBranchExist: 'あり',
          txtDeviationBranchNodeId: '異状時 飛び先ノードID/ノード名',
          txtDeviationMessageText: '異状時 メッセージ表示テキスト',
        },
        PartNumericTextInput: {
          //
          txtInputSelection: '入力選択',
          txtInputSelectionNumeric: '数値',
          txtInputSelectionCharactor: '文字',
          txtInstructionValueSetting: '指示値設定',
          txtInstructionNone: '指示値なし',
          txtInstructionFixedValue: '固定値',
          txtNodeIDValue: 'ノードID',
          txtInstructionGCode: 'G-コード',
          txtSelectGCode: 'G-コードメモから選択',
          txtInstructionValueUnit: '指示値単位',
          txtInputMethod: '記録値入力方法',
          txtInputMethodManualInput: '手入力 ',
          txtInputMethodDecimalPlaces: '小数点以下桁数',
          txtInputMethodInstructionFixedValue: '固定値',
          txtInputMethodNodeID: 'ノードID',
          txtInputMethodGCode: 'G-コード',
          txtInputMethodArithmeticInput: '演算入力',
          txtInputMethodFormula: '式入力',
          txtInputMethodTotalVolume: '出来高合計',
          txtInputMethodTotalActualInput: '投入実績合計',
          txtInputMethodMatNo: '品目CD／品名',
          txtInputMethodInstructionInfo: '指図情報から取得',
          txtInputMethodOrderItem: '取得項目',
          txtOutputSetting: '出力先設定',
          txtOutputSettingWithout: 'なし',
          txtOutputVolumeSetting: '出来高',
          txtVolumeInstructionLabel: '出来高容器指定',
          txtVolumePaletteLabel: '出来高パレット指定',
          txtVolumeWithoutExist: 'あり',
          txtVolumeWithoutNo: 'なし',
          txtVolumePalette: '出来高パレット',
          txtVolumePaletteNodeId: 'パレット番号取得ノードID',
          txtVolumeContainer: '出来高容器',
          txtVolumeContainerNodeId: '容器番号取得ノードID',
          txtVolumeZoneCd: '出来高設定先ゾーンCD／ゾーン',
          txtOutputSettingGCode: 'G-コード',
          txtOutputSettingYield: '理論収率',
          txtOutputSettingManHour: '作業工数登録',
          txtOutputSettingWorkItem: '登録項目',
        },
        PartDateRecord: {
          //
          txtRecordedMethod: '記録方法',
        },
        PartReceiveConsumption: {
          //
          txtItemSetting: '品目設定',
          txtItemSettingLabel: '品目CD/品名を指定',
          txtItemUnit: '品目単位',
          txtConsumptionSequence: '投入番号',
          txtInstructionValueSetting: '指示値設定',
          txtInstructionRxValue: '処方の値',
          txtInstructionFixedValue: '固定値',
          txtNodeIDValue: 'ノードID',
          txtInstructionGCode: 'G-コード',
          txtSelectGCode: 'G-コードメモから選択',
          txtInventoryZone: '在庫移動先ゾーン',
          txtInventoryZoneMaster: 'マスタから選択',
          txtInventoryZoneMasterLabel: 'ゾーンCD/ゾーン名称',
          txtInventoryZoneNode: 'ノードIDからゾーンCDを参照',
          txtInventoryZoneNodeLabel: 'ノードID/ノード名称',
          txtQualityStatusCheck: '品質ステータスチェック（適のみ可）',
          txtQualityStatusCheckExist: 'あり',
          txtQualityStatusCheckWithout: 'なし',
          txtInventoryConsumption: '在庫消費',
          txtInventoryConsumptionExist: 'あり',
          txtInventoryConsumptionWithout: 'なし',
          txtFIFOCheckZone: '先入れ先出しチェックゾーン',
          txtFIFOZoneGroup: 'ゾーングループCD/ゾーングループ名称',
        },
        PartResultConfirm: {
          //
          txtPreReceive: '事前受入',
          txtPreReceiveExists: 'あり',
          txtNonPreReceiveType: 'なし',
          txtPreReceiveNodeId: '受入設定済み投入SOPのノードID/ノード名称を指定',
          txtNonPreReceiveMatNo: '品名を指定',
          txtNonPreReceiveMatNoLabel: '品目CD/品名を指定',
          txtNonPreReceiveNodeId: 'ノードIDを指定',
          txtNonPreReceiveNodeIdLabel: 'ノードIDを指定',
          txtPreReceiveItemUnit: '品目単位',
          txtNonPreReceiveConsumptionSeq: '投入番号',
          txtFIFOCheckZone: '先入れ先出しチェックゾーン',
          txtFIFOZoneGroup: 'ゾーングループCD/ゾーングループ名称',
          txtInstructionValueSetting: '指示値設定',
          txtInstructionRxValue: '処方の値',
          txtInstructionFixedValue: '固定値',
          txtNodeIDValue: 'ノードID',
          txtInstructionGCode: 'G-コード',
          txtSelectGCode: 'G-コードメモから選択',
          txtInventoryZone: '消費元（移動先）ゾーン',
          txtInventoryZoneMaster: 'マスタから選択',
          txtInventoryZoneMasterLabel: 'ゾーンCD/ゾーン名称',
          txtInventoryZoneNode: 'ノードIDからゾーンCDを参照',
          txtInventoryZoneNodeLabel: 'ノードID/ノード名称',
          txtQualityStatusCheck: '品質ステータスチェック（適のみ可）',
          txtQualityStatusCheckExist: 'あり',
          txtQualityStatusCheckWithout: 'なし',
          txtInventoryDrawdown:
            '在庫一部消費（ラベルを消さない消費）：精製水で使用',
          txtInventoryDrawdownExist: 'あり',
          txtInventoryDrawdownWithout: 'なし',
          txtReturnInventory: '消費モード',
          txtReturnInventoryWithout: '全数消費',
          txtReturnInventoryExist: '残戻し有',
          txtReturnInventoryZoneCd: '残戻しゾーン',
          txtReturnInventoryLabel: '残ラベル発行',
          txtReturnInventoryLabelExist: 'あり',
          txtReturnInventoryLabelWithout: 'なし',
          txtDisposalAmount: '使用量設定',
          txtDisposalAmountDisposalInput: '廃棄量入力',
          txtDisposalAmountManualInput: '使用量手入力',
          txtDisposalAmountDisposal: '残量',
          txtDisposalAmountDisposalLabel: '廃棄内訳',
          txtDisposalAmountDisposalReason: '事由コード',
          txtDisposalAmountSelectExist: 'あり',
          txtDisposalAmountSelectWithout: 'なし',
          txtNumberOfDecimalPlaces: '実績小数点以下桁数',
        },
        PartElectronicFile: {
          //
          txtProcessingOriginalData: '元データの処理',
          txtProcessingOriginalDataDeleteAfter: '登録後削除',
          txtProcessingOriginalDataSaveAfter: '登録後も保持',
          txtAttachment: '記録書ヘの添付',
          txtAttachmentExist: 'あり',
          txtAttachmentWithout: 'なし',
          txtDataFileDefine: 'データファイルの定義',
          txtDataFileDefineCamera: 'カメラ起動',
          txtDataFileDefineFile: '作業者がファイルを指定',
          txtDataFileFolder: '表示フォルダを指定',
          txtDisplayFolder: '表示フォルダ',
          txtNodePath: '他ノード記録値からパスを取得',
          txtNodeLabel: 'ノードID/ノード名称',
          txtGCodeSelect: 'G-コード参照',
          txtGCode: 'G-コード',
          txtSelectGCode: 'G-コードメモから選択',
        },
        PartButtonBranch: {
          //
          txtBranchingMethod: '分岐方法',
          txtBranchingMethodMenu: 'メニュー選択',
          txtBranchingMethodSystem: 'システム条件分岐',
          txtBranchNumSetting: '分岐数設定',
          txtBranchingSetting: '分岐設定',
          txtMenuItemName: 'メニュー項目名称',
          txtConditional: '条件式',
          txtDestinationNodeId: '移動先ノードID',
        },
        PartSystemBranch: {
          //
          txtBranchNumSetting: '分岐数設定',
          txtBranchingSetting: '分岐設定',
          txtConditional: '条件式',
          txtExpressionInput: '式入力',
          txtDestinationNodeId: '移動先ノードID',
          txtMessage: 'メッセージ',
          txtExceptionNode: '全てFalse時の移動先ノードID',
        },
        PartExternalDevice: {
          //
          txtNo: 'No',
          txtText: 'テキスト',
          txtIndicativeType: '指示値種別',
          txtIndicativeValue: '指示値',
          txtInputName: '取得先',
          txtOutputType: '出力先種別',
          txtOutputValue: '出力先',
          txtRangeValueType: '判定種別',
          txtLowerLimit: '限界下限',
          txtDeviationLowerLimit: '異状下限',
          txtDeviationUpperLimit: '異状上限',
          txtUpperLimit: '限界上限',
          txtType: 'タイプ',
          txtSettingDevice: '設定先',
          txtConnectedSetting: '接続先機器設定',
          txtConnectedDeviceRead: '接続機器から読み込み',
          txtConnectedDeviceReadLabel: '接続先機器設定',
          txtConnectedDeviceWrite: '接続機器へ書込み',
          txtConnectedDeviceWriteLabel: '接続先機器設定',
          txtDeviceReadLabel: '読み込み',
          txtDeviceWriteLabel: '書込み',
          txtConnectedSettingDataNum: '取得データ数',
          txtConnectedSettingTimeOut: 'タイムアウト時間(秒)',
          txtUpperLowerLimitCheck: '上下限チェック',
          txtUpperLowerLimitCheckWithout: 'なし',
          txtUpperLowerLimitCheckExist: 'あり',
          txtConditionBranch: '条件分岐',
          txtConditionBranchWithout: 'なし',
          txtConditionBranchExist: 'あり',
          txtDeviationBranchNodeId: '異状時 飛び先ノードID/ノード名',
          txtNumericSOPList: '数値文字SOP一覧',
          txtNumericTextInput: 'テキスト',
          txtNumericSOPListIndicativeType: '指示値種別',
          txtInstructionValue: '指示値',
          txtNourceItemTag: '設定先(タグ)',
          txtNumericSOPListConnectedTag: '取得先(タグ)',
          txtSourceItemTag: '取得項目名',
          txtOutputSetting: '出力先種別',
          txtJudgeType: '判定種別',
          txtInstructionValueType: 'タイプ',
          txtBtnDelete: '削除',
          txtBtnApply: '反映',
          txtBtnCSVImport: 'CSVインポート',
        },
        PartElectronicShelfLabel: {
          //
          txtShelfGateWayIP: 'GateWayIP',
          txtShelfLabelServerIP: '棚札サーバIP',
          txtShelfID: '画面ID',
          txtShelfJP: '製品説明',
          txtShelfIdFixedValue: '固定値',
          txtShelfIdNodeId: 'ノードID',
          txtShelfIdGCode: 'G-コード',
          txtSelectGCode: 'G-コードメモから選択',
          txtSendTextSetting: '送信テキスト',
          txtSendTextFixedValue: '固定値',
          txtSendTextTextNodeId: 'ノードID',
        },
        PartInventoryConsumption: {
          //
          txtConsumptionType: '消費品目指定モード',
          txtConsumptionTypeMatNo: '品名を指定',
          txtConsumptionTypeMatNoLabel: '品目CD／品名を指定',
          txtConsumptionTypeNodeId: 'ノードIDから品目を指定',
          txtConsumptionTypeNodeIdLabel: 'ノードIDを指定',
          txtConsumptionTypeItemClass: 'ユーザ品目分類',
          txtConsumptionTypeItemClassLabel: '品目区分',
          txtZoneType: 'ゾーン種別',
          txtZoneTypeAll: '全ゾーン対象',
          txtZoneTypeForOnSite: '現場ゾーン対象',
          txtZoneTypeSelect: 'ゾーングループ',
          txtZoneTypeZoneCd: 'ゾーングループコード／ゾーングループ名',
          txtInputMethodDecimalPlaces: '小数点以下桁数',
          txtReasonCode: '事由コード',
          txtInstructionValueSetting: '指示値設定',
          txtInstructionFixedValue: '固定値',
          txtNodeIDValue: 'ノードID',
          txtInstructionGCode: 'G-コード',
          txtSelectGCode: 'G-コードメモから選択',
          txtFifoCheckSetting: '先入れ先出しチェック',
          txtLabelFifoCheckSettingNo: 'なし',
          txtLabelFifoCheckSettingExist: 'あり',
        },
        PartEquipmentContainer: {
          //
          txtDeviceContainerType: '装置、容器種別',
          txtScale: '計量器',
          txtVessel: '容器',
          txtDeviceAndVesselSetting: '装置、容器設定',
          txtSelectMaster: 'マスタから選択',
          txtDeviceContainerNodeId: 'ノードID',
          txtInstructionSelection: '指示選択',
          txtExpirationUpdate: '有効期限更新',
          txtCleaningStatusUpdate: '洗浄日更新',
          txtContainerCondition: '容器状態取得',
          txtTareCondition: '風袋重量取得',
        },
        PartLabelOutput: {
          //
          txtLabelSetting: 'ラベル設定',
          txtLabelType: 'ラベル種別',
          txtVolumeLabel: '出来高ラベル',
          txtProductReceiptSlip: '製品入庫伝票',
          txtSampling: 'サンプリング',
          txtCardboardLabel: '段ボールラベル',
          txtOutputMethod: '発行枚数',
          txtOutputMethodFixed: '固定',
          txtOutputMethodNonFixed: '非固定',
          txtInPlantVolumeLabel: '工場内出来高ラベル',
          txtProductYieldLabelA: '製品出来高ラベル(A用)',
          txtProductYieldLabelB: '製品出来高ラベル(B用)',
          txtSOPCustomize1: 'SOPカスタマイズ_1',
          txtSOPCustomize2: 'SOPカスタマイズ_2',
          txtSOPCustomize3: 'SOPカスタマイズ_3',
          txtSOPCustomize4: 'SOPカスタマイズ_4',
          txtSOPCustomize5: 'SOPカスタマイズ_5',
          txtProductDeliveryVoucher: '製品受払伝票',
          txtNumberOfCopies: '固定とする発行枚数を手入力',
          txtLabelPrinterIP: 'プリンタデバイス',
          txtPrinterDeviceSelectMaster: 'コード定義から選択',
          txtLabelPrinterIPInstructionFixedValue: '固定値',
          txtLabelPrinterIPNodeId: 'ノードID',
          txtLabelItem: 'ラベル項目',
          txtLabelItemInstructionFixedValue: '固定値',
          txtLabelItemNodeId: 'ノードID',
          txtLabelContainerSetting: '容器指定',
          txtLabelContainerSettingNo: 'なし',
          txtLabelContainerSettingExist: 'あり',
          txtLabelContainerNodeId: '容器番号取得ノードID',
          txtLabelPaletteSetting: 'パレット指定',
          txtLabelPaletteSettingNo: 'なし',
          txtLabelPaletteSettingExist: 'あり',
          txtLabelPaletteNodeId: 'パレット番号取得ノードID',
          txtIsOutputPendingNo: 'なし',
          txtIsOutputPendingExist: 'あり',
          txtIsOutputPending: '出力待ち',
          txtTimeoutSeconds: 'タイムアウト時間[s]',
        },
        PartWeightCalibration: {
          //
          txtDeviceSetting: '秤量器接続設定',
          txtDeviceMaster: '装置マスタ',
          txtStandardValue: '標準分銅の基準値',
          txtCalibSetting: '点検モード',
          txtCalibSettingWithout: 'ゼロ設定',
          txtCalibSettingExist: '標準分銅',
          txtWgtLogDIFlg: '点検記録への記載',
          txtWgtLogDIFlgWithout: 'なし',
          txtWgtLogDIFlgExist: 'あり',
          txtMonitoredValue: '監視値',
          txtJudgeType: '判定種別',
          txtAbsoluteValue: '絶対値',
          txtRelativeValue: '相対値(値)',
          txtRelativePercent: '相対値(%)',
          txtMonitoredManualInput: '判定値情報(手入力)',
          txtLowerLimit: '限界値下限 (≦記録値)',
          txtDeviationLowerLimit: '異状値下限 (≦記録値)',
          txtDeviationUpperLimit: '異状値上限 (記録値≦)',
          txtUpperLimit: '限界値上限 (記録値≦)',
          txtConditionBranch: '条件分岐',
          txtConditionBranchWithout: 'なし',
          txtConditionBranchExist: 'あり',
          txtDeviationBranchNodeId: '異状時 飛び先ノードID/ノード名',
          txtDeviationMessageText: '異状時 メッセージ表示テキスト',
        },
        PartPalletCargo: {
          //
          txtPalletMode: 'パレット積み付けモード',
          txtPalletModeLabel: 'モード',
          txtPalletModeOn: '積み付け',
          txtPalletModeOff: '積み付け解除',
          txtPalletIdSetting: 'パレットID設定',
          txtPalletIdFixedValue: '固定値',
          txtPalletIdNodeId: 'ノードID',
          txtPalletIdGCode: 'G-コード',
          txtSelectGCode: 'G-コードメモから選択',
          txtCargoCheck: '積み付け時チェック項目',
          txtMixedCargo: '品目混載',
          txtMixedCargoDisable: '混載不可',
          txtMixedCargoEnable: '混載可能',
        },
      },
      SOPConditionSetting: {
        //
        txtConditionjudge: '条件判定',
        txtInputjudge: '入力値判定',
        txtShowDisp: '判定値の実行画面表示',
        txtInputValueJudgeNo: 'なし',
        txtInputValueJudgeExist: 'あり',
        txtJudgeValueShowExist: '表示',
        txtJudgeValueShowNo: '非表示',
        txtMoniterValue: '監視値',
        txtJudgeMethod: '判定方法(範囲は数値のときのみ選択可能)',
        txtEqual: '＝',
        txtRange: '範囲',
        txtJudgeType: '判定種別',
        txtAbsoluteValue: '絶対値',
        txtRelativeValue: '相対値(値)',
        txtRelativePer: '相対値(％)',
        txtDecisionInfoInput: '判定値情報(手入力)',
        txtLowerLimit: '限界値下限(≦記録値)',
        txtDeviationLowerLimit: '異状値下限(≦記録値)',
        txtDeviationUpperLimit: '異状値上限(記録値≦)',
        txtUpperLimit: '限界値上限(記録値≦)',
        txtConditionBranch: '条件分岐',
        txtConditionBranchWithout: 'なし',
        txtConditionBranchExist: 'あり',
        txtDeviationBranchNodeId: '条件分岐異状時飛び先ノード',
        txtDestinationNodeName: '条件分岐異状時飛び先ノード',
        txtUpperLimitError: '限界上限異状時',
        txtRegulationMaxSpecified: '規格上限異状時',
        txtRegulationsLowerLimitSpecified: '規格下限異状時',
        txtLowerLimitError: '限界下限異状時',
        txtDeviationMessageText: '異状時 メッセージ表示テキスト',
        txtError: '異状時',
        txtPermissionGroup: '異状時 対応者権限グループ',
        Msg: {
          //
          upperLimit: '限界値の上限を超えています。',
          lowerLimit: '限界値の下限を超えています。',
          upperLimitSpecified: '規格値の上限を超えています。',
          lowerLimitSpecified: '規格値の下限を超えています。',
          differValue: '判定値と異なります。',
        },
      },
      SOPExpressionDialog: {
        //
        txtExpressionInputNodeId: '使用可能ノードID(NC)',
        txtExpressionInputGcode: '使用可能G-コード(GC)',
        txtSelectGCode: 'G-コードメモから選択',
      },
    },
    Menu: {
      // キャラクタ
      txtSOPParts: 'SOPパーツ',
      txtSOPBlock: 'SOPブロック',
      txtSOPControlParts: 'SOP制御パーツ',
      txtInstructionConfirm: '指示内容確認',
      txtSopTimer: 'SOPタイマー',
      txtNumericTextInput: '数値文字入力',
      txtDateRecord: '日時記録',
      txtReceiveConsumption: '受入投入',
      txtResultConfirm: '実績確定',
      txtElectronicFile: '電子ファイル登録',
      txtButtonBranch: 'ボタン分岐',
      txtSystemBranch: 'システム分岐',
      txtExternalDevice: '外部機器通信',
      txtElectronicShelfLabel: '電子棚札制御',
      txtInventoryConsumption: '在庫消費',
      txtEquipmentContainer: '装置容器状態',
      txtLabelOutput: 'ラベル出力',
      txtWeightCalibration: '計測器点検',
      txtPalletCargo: 'パレット積み付け',
      txtBegin: 'スタート',
      txtEnd: 'エンド',
      txtBranch: '分岐',
      txtCopyNode: 'コピー貼り付け',
      txtActualDevoted: '投入実績確認',
      txtConfirmerSetting: '確認者記録',
      txtMenuBranching: 'SOP分岐',
      txtInterlock: 'インターロックSOP',
      txtWeighingExecution: '秤量作業実行',
      txtConditionBranch: '条件分岐',
      txtNumericalCalculation: '数値演算',
      txtProgressCheck: '進捗確認',
      txtPlaceholder: 'オブジェクトを検索',
      txtStencilTitle: 'フローチャート',
      txtNotFoundText: '見つかり',
      txtSopTitle: 'SOP 管理',
      txtFunctionsListTitle: '使用可能関数一覧',
    },
    Msg: {
      // メッセージ
      copySucceeded: 'コピー成功',
      selectNode: 'ノードを選択してください',
      cannotPasted: 'クリップボードは空で、貼り付けることができません',
      reachTheMax: 'ノードの最大数に達しました',
      pasteSucceeded: 'ペースト成功',
      cannotBlockSet: 'ブロックを作成できません',
      cannotBeDragged: '条件分岐ノード、ドラッグ不可',
      cannotSetCondBranch: '{0}条件分岐は再設定できません',
      SOPNodeNameUnique: 'ノード名はもう存在しています。',
      SOPNodeNameUniqueWhenSave: '同一ノード名が複数存在しています。',
      cellRemove: '削除しますか',
      unSelectNode: 'ノードが選択されていません。',
      selectErrorRange:
        '選択範囲において、開始が１つ、終了が１つとなるよう選択してください。',
      selectErorrStart: '選択範囲において、スタートが選択されています。',
      selectErorrEnd: '選択範囲において、エンドが選択されています。',
      selectErorrBlock:
        '選択範囲において、テンプレート／ブロックが選択されています。',
      deleteTemplate: '選択中の{0}を削除します。',
      unSelectTemplate: '削除対象が選択されていません。',
      noSelectedTemp: 'テンプレートが選択されていません。',
      noSelectedBlock: 'ブロックが選択されていません。',
      deleteAddParts: 'プラスボタンのため削除できません。',
      deleteMultipleSelect: '複数選択状態のため削除できません。',
      errorDisplayMessage: '異状値が確認されました。',
      setSopSetting: '処方情報が設定されていません。',
      setSopPrcSetting: '工程情報が設定されていません。',
      setSopWgtSetting: '秤量室情報が設定されていません。',
      comfirmTableDataMessage: 'のテーブルデータが消えます。\nよろしいですか？',
      unConnected:
        'フローが終了まで到達できません。\n分岐の少なくとも一つが次のノードに連携していることを確認してください。',
      settingError:
        'SOPノードの中に設定が確定していないSOPノードがあります。\n入力チェックを確認し、設定を確定してください。',
      branchNGLoop: 'フローがループになります。\n分岐設定を見直してください。',
      confirmPageLeave: '編集中のフローが破棄されます。\nよろしいですか？',
      timeFormat: 'mm:ss / mmm:ss',
      timeFormatError: '時間形式が正しくありません',
      titleListDelete: '削除確認',
      confirmListDelete: '選択データを削除します。\nよろしいですか？',
      consumptionSeqRangeError: '投入番号が範囲の中にありません。',
      noSopSettingValue: 'SOP設定が未入力です。',
      filePathValidationError: '{0}ファイルパスが正しく設定されていません。',
      filePathSOPSettingValidationError:
        '「SOPフローヘルプ」のファイルパスが正しく設定されていません。',
      editError:
        '該当フローは、他のユーザにより編集中です。\nフローNo：{0}\n編集日時:{1}\n編集者：{2}',
      inventoryDrawdownMessage:
        'ラベルを消さない消費モードが選択されています。\n設定した品目がラベルを消費しない品目か注意してください。\n例：精製水',
      branchRemoveError:
        '分岐内にノードが存在します。先に分岐内ノードを削除してください。',
      sopSaveConfirm: '確定処理を実行します。\nよろしいですか？',
    },
  },
  Mst: {
    // マスタ
    Chr: {
      // キャラクタ
      txtMatNoNm: '品目コード/品名',
      txtMatNo: '品目コード',
      txtMatNm: '品名',
      txtPrescriptionCode: '処方コード',
      txtPrescriptionName: '処方名',
      txtComparisonMBRNo: '比較MBR番号',
      txtMasterName: 'マスタ名',
      txtIsChanged: '変更',
      txtResults: '結果',
      txtDetailedContents: '詳細内容',
      txtMBRNo: 'MBR番号',
      txtMBRCreateDate: 'MBR作成日時',
      txtMBRCreateUser: 'MBR作成者',
      txtMBRApplicationDate: 'MBR申請日時',
      txtMBRApplicant: 'MBR申請者',
      txtMBRApprovedDeniedDate: 'MBR承認/否認日時',
      txtMBRApprovedDeniedUser: 'MBR承認/否認者',
      txtOrderSettingRangeStartDate: '指図設定範囲 開始日',
      txtOrderSettingRangeEndDate: '指図設定範囲 終了日',
      txtApplicationComment: '申請コメント',
      txtApplicationStatus: '申請状況',
      txtApplying: '申請中',
      txtApproved: '承認済',
      txtDenied: '否認済',
      txtApprovalDenialComment: '承認/否認コメント',
      txtUnapprovedMaster: '未承認マスタ',
      txtApprovedMaster: '承認済マスタ',
      txtMatPrescriptionList: '品目・処方一覧',
      txtMBRMasterList: 'MBRマスタ一覧',
      txtMBRList: 'MBR一覧',
      txtAwaitingApprovalMBRList: '承認待ちMBR一覧',
      txtMasterList: 'マスタ一覧',
      txtConfirmMBRMasterComparison: 'MBRマスタ比較確認',
      txtConsistencyCheckResults: '整合性チェック結果',
      txtInputApplicationComment: '申請コメント入力',
      txtMBRApplicationConfirmation: 'MBR申請確認',
      txtApproveMaster: 'マスタ承認',
      txtLegendApproveMaster: '※「変更」は最新10件のみ比較して表示しています。',
      txtVerificationEnvironment: '検証環境',
      txtProductionEnvironment: '本番環境',
      txtAdditionColor: 'red',
      txtModificationColor: 'red',
      txtDeletionColor: '#999999',
      txtDelimiter: '：',
      btnCompare: '比較',
      btnCreate: '作成',
      btnBack: '戻る',
      btnApplication: '申請',
      btnDelete: '削除',
      btnConfirm: '確認',
      btnApproval: '承認',
      btnDenial: '否認',
      btnDetailConfirm: '詳細確認',
    },
    Msg: {
      // メッセージ
      titleCreateConfirm: '作成',
      contentCreateConfirm: 'MBRを作成します。よろしいですか？',
      titleApplicationConfirm: '申請',
      contentApplicationConfirm:
        '本番環境への反映の承認依頼を申請します。よろしいですか？',
      titleDeleteConfirm: '削除',
      contentDeleteConfirm: 'MBRを削除します。よろしいですか？',
      titleApprovalConfirm: '承認',
      contentApprovalConfirm: '本番環境へマスタを反映します。よろしいですか？',
      titleDenialConfirm: '否認',
      contentDenialConfirm: '本番環境への反映を否認します。よろしいですか？',
    },
  },
};
export default lang;
