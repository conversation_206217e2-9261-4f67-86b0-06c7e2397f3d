<template>
  <!-- 強制出荷 -->
  <DialogWindow
    :title="$t('Sog.Chr.txtSogForceShipment')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkForm()"
    @visible="updateDialogChangeFlagRef"
  >
    <p>{{ t('Sog.Chr.txtSogForceDialogConfirm') }}</p>
    <p>{{ t('Sog.Chr.txtSogForceDialogInfo') }}</p>

    <CustomForm
      class="Util_mt-16"
      :formModel="sogForceShipmentTextFormRef.formModel"
      :formItems="sogForceShipmentTextFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          sogForceShipmentTextFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
    <div class="label-container Util_mt-16">
      <span>{{ t('Sog.Chr.txtSogDts') }}</span>
      <span
        class="Util_ml-8 custom-form_tag el-tag el-tag--danger el-tag--dark is-round"
      >
        {{ t('Cm.Chr.txtTagOfRequired') }}
      </span>
    </div>
    <el-form ref="timeFormRef" :model="timeFormModelRef">
      <div class="autocomplete-container Util_mt-16">
        <el-form-item prop="hour" :rules="timeFormItemsRef.hour.rules">
          <SelectEx
            v-if="timeFormItemsRef.hour.formRole === 'selectComboBox'"
            v-model="timeFormItemsRef.hour.formModelValue"
            :optionsData="hourSelectOptions"
            @blur="validateField('hour')"
            placeholder=""
            @update:modelValue="(v: string) => updateFormModelValue(v, 'hour')"
          />
        </el-form-item>
        <span>:</span>
        <el-form-item prop="minute" :rules="timeFormItemsRef.minute.rules">
          <SelectEx
            v-if="timeFormItemsRef.minute.formRole === 'selectComboBox'"
            v-model="timeFormItemsRef.minute.formModelValue"
            :optionsData="minuteSelectOptions"
            @blur="validateField('minute')"
            placeholder=""
            @update:modelValue="
              (v: string) => updateFormModelValue(v, 'minute')
            "
          />
        </el-form-item>
      </div>
    </el-form>
    <CustomForm
      class="Util_mt-16"
      :formModel="sogForceShipmentExplFormRef.formModel"
      :formItems="sogForceShipmentExplFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          sogForceShipmentExplFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- 強制出荷の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.sogForceShipmentConfirm"
    :dialogProps="messageBoxSogForceShipmentConfirmProps"
    :cancelCallback="() => closeDialog('sogForceShipmentConfirm')"
    :submitCallback="
      () => confirmApiHandler(messageBoxSogForceShipmentConfirmProps)
    "
  />
  <!-- 異常 -->
  <MessageBox
    v-if="dialogVisibleRef.errorConfirm"
    :dialogProps="messageBoxErrorPropsRef"
    :submitCallback="() => closeDialog('errorConfirm')"
  />
  <!-- 情報 -->
  <MessageBox
    v-if="dialogVisibleRef.infoConfirm"
    :dialogProps="messageBoxInfoPropsRef"
    :submitCallback="closeAllDialog"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { FormInstance } from 'element-plus';
import onValidateHandler from '@/utils/validateHandler';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  GetSogConfirmShipmentRecordData,
  GetSogForceShipmentData,
  ModifySogForceReq,
} from '@/types/HookUseApi/SogTypes';
import {
  CommonRequestType,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import SelectEx from '@/components/base/SelectEx.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import {
  useGetComboBoxDataStandard,
  useGetSogForce,
  useModifySogForce,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  SelectOption,
  getSogForceShipmentTextFormItems,
  sogForceShipmentTextFormModel,
  getSogForceShipmentExplFormItems,
  sogForceShipmentExplFormModel,
  getSogForceShipmentTimeFormItems,
  sogForceShipmentTimeFormModel,
} from './sogForceShipment';

const sogForceShipmentExplFormRef = ref<CustomFormType>({
  formItems: getSogForceShipmentExplFormItems(),
  formModel: sogForceShipmentExplFormModel,
});

const sogForceShipmentTextFormRef = ref<CustomFormType>({
  formItems: getSogForceShipmentTextFormItems(),
  formModel: sogForceShipmentTextFormModel,
});

type Props = {
  selectedRowsData: GetSogConfirmShipmentRecordData[];
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
};

const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

const timeFormRef = ref<FormInstance>();
const hourSelectOptions: SelectOption[] = [];
const minuteSelectOptions: SelectOption[] = [];
const timeFormModelRef = ref<CustomFormType['formModel']>(
  sogForceShipmentTimeFormModel,
);
const timeFormItemsRef = ref<CustomFormType['formItems']>(
  getSogForceShipmentTimeFormItems(),
);

let sogForceShipmentFormData: GetSogForceShipmentData = {
  sogSlipGrpNo: '',
  sogInstNo: '',
  sogPlanYmd: '',
  bpTrfNm: '',
  sogSlipGrpUpdDts: '',
};

type DialogRefKey =
  | 'errorConfirm'
  | 'infoConfirm'
  | 'sogForceShipmentConfirm'
  | 'fragmentDialogVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  errorConfirm: false,
  infoConfirm: false,
  sogForceShipmentConfirm: false,
  fragmentDialogVisible: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxErrorPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const messageBoxInfoPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxSogForceShipmentConfirmProps: DialogProps = {
  title: t('Sog.Chr.txtSogForceShipmentConfirm'),
  content: t('Sog.Msg.sogForceShipmentConfirm'),
  type: 'question',
};

const validateField = async (fieldId: string) => {
  try {
    if (fieldId) await timeFormRef.value?.validateField(fieldId);
  } catch (e) {
    console.log(e);
  }
};

const updateFormModelValue = async (v: string, fieldId: string) => {
  const fieldValue = v !== '' ? v : '';
  timeFormModelRef.value[fieldId] = fieldValue;
  timeFormItemsRef.value[fieldId].formModelValue = fieldValue;
  updateDialogChangeFlagRef(true);
};

const checkForm = async () => {
  const formValidate =
    sogForceShipmentExplFormRef.value.customForm !== undefined &&
    (await sogForceShipmentExplFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  // 出荷時間の検証
  const timeValidate =
    timeFormRef.value !== undefined &&
    (await timeFormRef.value.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (formValidate && timeValidate) {
    openDialog('sogForceShipmentConfirm');
  }
  return false;
};

const confirmApiHandler = async (messageBoxProps: DialogProps) => {
  closeDialog('sogForceShipmentConfirm');
  showLoading();
  // ※出荷日時は出荷予定＋出荷日時(左:時)＋出荷日時(右:分)の「yyyy/MM/dd HH:mm:00」に編集した結果を設定
  const sogForceDts = `${sogForceShipmentTextFormRef.value.formModel.sogPlanYmd.toString()} ${timeFormModelRef.value.hour.toString()}:${timeFormModelRef.value.minute.toString()}:00`;
  const modifysogForceShipmentExplFormModel: ExtendCommonRequestType<ModifySogForceReq> =
    {
      ...props.privilegesBtnRequestData,
      sogSlipGrpNo:
        sogForceShipmentTextFormRef.value.formModel.sogSlipGrpNo.toString(),
      sogForceDts,
      sogForceExpl:
        sogForceShipmentExplFormRef.value.formModel.sogForceExpl.toString(),
      sogSlipGrpUpdDts: sogForceShipmentFormData.sogSlipGrpUpdDts,
      msgboxTitleTxt: messageBoxProps.title,
      msgboxMsgTxt: messageBoxProps.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
    };
  const { responseRef, errorRef } = await useModifySogForce(
    modifysogForceShipmentExplFormModel,
  );
  if (errorRef.value) {
    closeLoading();
    messageBoxErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('errorConfirm');
    return false;
  }
  if (responseRef.value) {
    messageBoxInfoPropsRef.value.title = responseRef.value.data.rTitle;
    messageBoxInfoPropsRef.value.content = responseRef.value.data.rMsg;
    openDialog('infoConfirm');
  }
  closeLoading();
  return true;
};

const closeAllDialog = () => {
  closeDialog('infoConfirm');
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData);
};

/**
 * 初期設定
 */
const sogForceShipmentInit = async () => {
  const sogSlipGrpNos = new Set(
    props.selectedRowsData.map((item) => item.sogSlipGrpNo),
  );
  if (sogSlipGrpNos.size !== 1) {
    messageBoxErrorPropsRef.value.title = t('Cm.Chr.txtUnselectedSingleData');
    messageBoxErrorPropsRef.value.content = t('Cm.Msg.unselectedSingleData');
    openDialog('errorConfirm');
    return;
  }
  updateDialogChangeFlagRef(false);
  showLoading();
  sogForceShipmentExplFormRef.value.formItems =
    getSogForceShipmentExplFormItems();
  timeFormItemsRef.value = getSogForceShipmentTimeFormItems();

  // 強制出荷データ取得
  const { responseRef, errorRef } = await useGetSogForce({
    ...props.privilegesBtnRequestData,
    sogSlipGrpNo: props.selectedRowsData.at(0)!.sogSlipGrpNo,
  });
  if (errorRef.value) {
    closeLoading();
    messageBoxErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('errorConfirm');
    return;
  }
  if (responseRef.value) {
    sogForceShipmentFormData = responseRef.value.data.rData;

    setFormModelValueFromApiResponse(
      sogForceShipmentTextFormRef,
      sogForceShipmentFormData,
    );

    setFormModelValueFromApiResponse(
      sogForceShipmentExplFormRef,
      sogForceShipmentFormData,
    );
  }
  // 標準コンボボックスデータ取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtSogForce',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'SOG_INST_SHIP_FORCE' },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setCustomFormComboBoxOptionList(
      sogForceShipmentExplFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  for (let i = 0; i < 24; i++) {
    hourSelectOptions.push({
      value: i.toString().padStart(2, '0'),
      label: i.toString().padStart(2, '0'),
    });
  }
  for (let i = 0; i <= 55; i += 5) {
    minuteSelectOptions.push({
      value: i.toString().padStart(2, '0'),
      label: i.toString().padStart(2, '0'),
    });
  }

  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, sogForceShipmentInit);
</script>
<style scoped>
.autocomplete-container {
  display: flex;
  align-items: center;
  gap: 8px;
  .el-form-item {
    margin-bottom: 0;
    width: 70px;
  }
}
.label-container {
  display: flex;
  align-items: top;
  .custom-form_tag {
    font-size: 10px;
    height: 14px;
  }
}
</style>
