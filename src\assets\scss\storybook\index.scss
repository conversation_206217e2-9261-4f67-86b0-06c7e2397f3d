@import "@/assets/scss/system/variables.module.scss";
.innerZoomElementWrapper:has(.el-overlay) {
  min-height: 200px;
}
.innerZoomElementWrapper .table-search-btn,
.sb-main-padded .table-search-btn {
  display: none;
}
.svg-icon-container {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  background-color: $gray720;
}
.svg-icon-div {
  position: relative;
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  padding: $padding;
}
.svg-icon-item {
  float: left;
  width: 200px;
  height: 60px;
  text-align: center;
  padding: $padding;
  font-size: $fontSize;
}
.svg-icon-all {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background-color: $gray720;
}
.innerZoomElementWrapper:has(.sider-condition) {
  min-height: calc(900px + #{$padding} + #{$padding});
}
.sider-condition {
  float: left;
  width: 300px;
}
