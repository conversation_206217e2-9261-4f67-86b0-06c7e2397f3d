<template>
  <el-dialog
    v-model="state.SOPChartDialogVisible"
    :show-close="false"
    :close-on-click-modal="false"
    :append-to-body="true"
    width="60%"
    :style="getDialogMinWidth()"
    center
  >
    <template #header>
      <div class="dialog-title">
        <div v-if="state.SOPChartFlag === 'NEW'" class="dialog-title-main">
          {{ $t('SOP.Chr.txtSopTempRegistration') }}
        </div>
        <div v-else>
          <div class="dialog-title-left">
            {{ $t('SOP.Chr.txtSopTempName') }} :
            {{ state.chartOptions.blockData.sopFlowNmJp }}
          </div>
          <div class="dialog-title-right">
            <span v-if="state.SOPChartFlag === 'BE-PREVIEW'">{{
              $t('SOP.Chr.txtSopBlockDetail')
            }}</span>
          </div>
        </div>
      </div>
    </template>
    <el-card class="box-card">
      <div class="box-card-nosetting">
        <div class="box-card-input" v-if="state.SOPChartFlag === 'NEW'">
          <el-form
            ref="dataSourceRef"
            :rules="dataRules"
            :model="state.SOPBlockOption"
            label-position="top"
            label-width="110px"
          >
            <el-row class="common-row-padding-top">
              <el-col :span="7">
                <el-form-item
                  :label="$t('SOP.Chr.txtSopTempName')"
                  prop="sopFlowNmJp"
                >
                  <el-input
                    v-model="state.SOPBlockOption.sopFlowNmJp"
                    :placeholder="$t('Cm.Msg.placeholderInput')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="7" :offset="1">
                <el-form-item
                  :label="$t('SOP.Chr.txtSopCategory')"
                  prop="sopCatTxtJp"
                >
                  <el-select
                    v-model="state.SOPBlockOption.sopCatTxtJp"
                    :placeholder="$t('Cm.Msg.placeholderInput')"
                    clearable
                    filterable
                    allow-create
                  >
                    <el-option
                      v-for="item in state.category"
                      :key="item.value"
                      :label="$t(item.label)"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="7" :offset="1">
                <el-form-item
                  :label="$t('SOP.Chr.txtSopType')"
                  prop="untSopCat"
                >
                  <el-select
                    v-model="state.SOPBlockOption.untSopCat"
                    :placeholder="$t('Cm.Msg.placeholderInput')"
                  >
                    <el-option
                      v-for="item in state.columnOptions"
                      :key="item.value"
                      :label="$t(item.label)"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="box-card-chart" ref="boxChardGraph"></div>
      </div>
    </el-card>
    <template #footer>
      <div class="dialog-footer">
        <div class="dialog-footer-left">
          <ButtonEx
            type="secondary"
            size="normal"
            :text="t('Cm.Chr.btnCancel')"
            icon-class="cancel_dark"
            @click="SOPAddCancel"
          />
        </div>
        <div class="dialog-footer-right" v-if="state.SOPChartFlag === 'NEW'">
          <ButtonEx
            type="primary"
            size="normal"
            :text="t('Cm.Chr.btnDecision')"
            icon-class="ok"
            :disabled="false"
            @click="SOPAddSave"
          />
        </div>
      </div>
    </template>
    <MessageBox
      v-show="dialogVisibleRef.singleButtonRef"
      :dialog-props="messageBoxSingleButtonRef"
      :cancelCallback="() => closeDialog('singleButtonRef')"
      :submitCallback="() => closeDialog('singleButtonRef')"
    />
    <MessageBox
      v-show="dialogVisibleRef.sopAddSaveRef"
      :dialog-props="messageBoxSopAddSaveRef"
      :cancelCallback="() => closeDialog('sopAddSaveRef')"
      :submitCallback="
        () => {
          closeDialog('sopAddSaveRef');
          SOPAddCancel();
        }
      "
    />
  </el-dialog>
</template>
<script setup lang="ts">
import { Graph, Shape, Cell, Node } from '@antv/x6';
import { useI18n } from 'vue-i18n';
import { reactive, ref, watch, nextTick, onMounted } from 'vue';
import { rules } from '@/utils/validator';
import { v4 as uuidv4 } from 'uuid';
import { FormInstance } from 'element-plus';
import ButtonEx from '@/components/parts/ButtonEx.vue';
import {
  getSOPSetting,
  setSOPFlowItemModel,
  setNewTemplateSOPFlowItemValue as setTemplateNewSOPFlowItemValue,
  getImageMap,
  checkPartCode,
  getSOPFlowChartData,
  checkConditionPartCode,
  TemplateNodesIdMapping,
} from '@/components/model/SopPA/SopChartSetting';
import {
  SelectOption,
  ImageOption,
  SopPartDataSource,
  NodeProperty,
  ChartOptions,
  SopBlockDataOption,
  SopRectPartsOption,
  NeighborsOption,
  NodeIdOption,
  PositionOption,
  SopFlowDataOption,
  SopJoinDstOption,
  // [課題345] ADD ST NODEの連結の設計をリファクタリング
  SopControlPartOption,
  PartExternalDeviceProps,
  NumericTextInputChildNodeProps,
  SizeOption,
  SopFlowGetDataOption,
  // [課題345] ADD ED
} from '@/types/SopDialogInterface';
import svgMenuHome from '@/assets/icons/svg/menuHome.svg';
import svgSopTemplate from '@/assets/icons/svg/sopTemplate.svg';
import svgQuestion from '@/assets/icons/svg/icon_question_1_blue.svg';
import svgSopBlock from '@/assets/icons/svg/sopBlock.svg';
import svgAdd from '@/assets/icons/svg/add.svg';
import svgWCheck from '@/assets/icons/svg/wCheck.svg';
import svgWrite from '@/assets/icons/svg/write.svg';
import svgAbnormalityLevel1 from '@/assets/icons/svg/abnormalityLevel1.svg';
import svgAbnormalityLevel2 from '@/assets/icons/svg/abnormalityLevel2.svg';
import svgAbnormalityLevel3 from '@/assets/icons/svg/abnormalityLevel3.svg';
import svgAbnormalityLevel4 from '@/assets/icons/svg/abnormalityLevel4.svg';
import svgAbnormalityLevel5 from '@/assets/icons/svg/abnormalityLevel5.svg';
import svgFormula from '@/assets/icons/svg/formula.svg';
import svgGvalNval from '@/assets/icons/svg/gvalNval.svg';
import svgEdit from '@/assets/icons/svg/edit.svg';
import svgTrash from '@/assets/icons/svg/trashBox.svg';
import svgCopy from '@/assets/icons/svg/copy.svg';
import PartNumericTextInput from '@/assets/icons/svg/PartNumericTextInput.svg';
import PartInstructionConfirm from '@/assets/icons/svg/PartInstructionConfirm.svg';
import PartSopTimer from '@/assets/icons/svg/PartSopTimer.svg';
import PartDateRecord from '@/assets/icons/svg/PartDateRecord.svg';
import PartElectronicFile from '@/assets/icons/svg/PartElectronicFile.svg';
import PartReceiveConsumption from '@/assets/icons/svg/PartReceiveConsumption.svg';
import PartResultConfirm from '@/assets/icons/svg/PartResultConfirm.svg';
import svgTestPartButtonBranch from '@/assets/icons/svg/testpartButtonBranch.svg';
import svgTestPartSystemBranch from '@/assets/icons/svg/testpartSystemBranch.svg';
import PartElectronicShelfLabel from '@/assets/icons/svg/PartElectronicShelfLabel.svg';
import PartInventoryConsumption from '@/assets/icons/svg/PartInventoryConsumption.svg';
import svgTestPartUpdDevice from '@/assets/icons/svg/testpartUpdDevice.svg';
import svgTestPartLabelOutput from '@/assets/icons/svg/testpartLabelOutput.svg';
import svgTestPartCommDevice from '@/assets/icons/svg/testpartCommDevice.svg';
import svgTestPartWeighingCalib from '@/assets/icons/svg/testpartWeighingCalib.svg';
import svgTestPartPalletCargo from '@/assets/icons/svg/testpartPalletCargo.svg';
import PartCopyNode from '@/assets/icons/svg/PartCopyNode.svg';
import CONST from '@/constants/utils';
import {
  ComboBoxDataOptionData,
  CommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
// [課題399] DEL ST
import { GetSopFlowData, InsertSopBlock } from '@/types/HookUseApi/SopTypes';
import {
  useInsertSopBlock,
  useGetSopFlowData,
  useGetComboBoxDataStandard,
} from '@/hooks/useApi';
// [課題399] DEL ED
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import SOP_PARTS_VARIABLES from '@/constants/sopPartsVariables';
import {
  BranchList,
  Matrix,
  createNodesMatrixFromDb,
  calcNodePosY,
  findNearParentBranchId,
  addBlockTargetEdge4Temp,
  findNodeRow,
  addPathTargetEdge,
  findNodeCol,
  findParentBranchId,
  getEdgeNodeType,
  getLoopTargetNode,
  createBranchList,
  createNodeIdToAttrMapFromDB,
} from '@/utils/sopDrawGraph';
import { SelectBranchOption } from '@/types/SopInterface';

type DialogRefKey = 'singleButtonRef' | 'sopAddSaveRef';
const initialState: InitialDialogState<DialogRefKey> = {
  singleButtonRef: false,
  sopAddSaveRef: false,
};

interface Props {
  SOPAddFlag: boolean;
  SOPChartType: string;
  SOPFlowType: string;
  chartOptions: ChartOptions;
  screenWidth: number;
  screenHeight: number;
  commonRequest: CommonRequestType;
  blockSeleckValue: string;
}
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
const { t } = useI18n();
const sopSetting = getSOPSetting(t);
/**
 * テンプレート登録/テンプレート詳細
 * @vue-prop {Boolean} SOPAddFlag -  ブロック編集ページの表示制御
 * @vue-prop {String} SOPChartType -  NEW or EDIT PREVIEW(未使用？)
 * @vue-prop {Dataect} options -  SOP data options
 * @vue-prop {Number} [screenWidth=1280] - screen Width
 * @vue-prop {Number} [screenHeight=1024] - screen Height
 */
const props = withDefaults(defineProps<Props>(), {
  SOPAddFlag: false,
  SOPChartType: '',
  SOPFlowType: '',
  chartOptions: () => ({
    blockData: {
      sopFlowNo: '',
      sopFlowNmJp: '',
      sopCatTxtJp: '',
      untSopCat: '',
      updDts: '',
      flowList: [],
    },
    cellData: {},
    blockNo: '',
  }),
  screenWidth: 1280,
  screenHeight: 1024,
  sopFlowNmJp: '',
  sopCatTxtJp: '',
  untSopCat: '',
});

const emit = defineEmits(['SOPAddVisible']);
let selectList: SopFlowGetDataOption[] = [];
interface State {
  localeEditGraph: Graph | null;
  chartOptions: ChartOptions;
  imageMap: Map<string, string>;
  SOPChartFlag: string;
  screenWidthVal: number;
  screenHeightVal: number;
  columnOptions: SelectOption[];
  SOPChartDialogVisible: boolean;
  SOPBlockOption: SopBlockDataOption;
  SOPPartSetData: SopPartDataSource;
  SOPSetData: NodeProperty;
  images: ImageOption[];
  category: ComboBoxDataOptionData[];
  blockSelectValue: string;
}
const state = reactive<State>({
  localeEditGraph: null,
  chartOptions: props.chartOptions,
  imageMap: new Map<string, string>(),
  SOPChartFlag: '',
  screenWidthVal: 0,
  screenHeightVal: 0,
  columnOptions: [
    {
      label: 'SOP.Chr.txtSopTemplate',
      value: 'T',
    },
    {
      label: 'SOP.Chr.txtSopBlock',
      value: 'B',
    },
  ],
  SOPChartDialogVisible: false,
  SOPBlockOption: {
    sopFlowNo: '',
    sopFlowNmJp: '',
    sopCatTxtJp: '',
    untSopCat: '',
    updDts: '',
    flowList: [],
  },
  SOPPartSetData: {
    id: '',
    settingNodeCd: '',
    incomingList: [],
    outgoingList: [],
    destinationNodeNames: [],
    receiveConsumptionNodes: [],
    commonSetting: sopSetting.commonSetting,
    individualPara: null,
    nodeClassName: '',
    dispNodeId: '',
    sopNodeNo: '',
    dispFlg: false,
    sopCondition: {
      condition1: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition2: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition3: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition4: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition5: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition6: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
    },
    conditionProps: {
      judgeValueShowFlg: '',
      deviationMessageText: '',
      conditionBranch: '',
      deviationBranchNodeId: '',
    },
    upperLowerSetting: {
      thJudgeFlg: '',
      thRangeType: '',
      thValType: '',
      thValLlmt: '',
      thValUlmt: '',
    },
    instUnitTxt: '',
    privGroupList: [],
    // ADD ED
    errorDestinationNodeList: [],
  },
  SOPSetData: { ...sopSetting },
  images: [
    {
      fileName: 'menuHome',
      path: svgMenuHome,
    },
    {
      fileName: 'sopTemplate',
      path: svgSopTemplate,
    },
    {
      fileName: 'question',
      path: svgQuestion,
    },
    {
      fileName: 'add',
      path: svgAdd,
    },
    {
      fileName: 'sopBlock',
      path: svgSopBlock,
    },
    {
      fileName: 'write',
      path: svgWrite,
    },
    {
      fileName: 'wCheck',
      path: svgWCheck,
    },
    {
      fileName: 'abnormalityLevel1',
      path: svgAbnormalityLevel1,
    },
    {
      fileName: 'abnormalityLevel2',
      path: svgAbnormalityLevel2,
    },
    {
      fileName: 'abnormalityLevel3',
      path: svgAbnormalityLevel3,
    },
    {
      fileName: 'abnormalityLevel4',
      path: svgAbnormalityLevel4,
    },
    {
      fileName: 'abnormalityLevel5',
      path: svgAbnormalityLevel5,
    },
    {
      fileName: 'formula',
      path: svgFormula,
    },
    {
      fileName: 'gvalNval',
      path: svgGvalNval,
    },
    {
      fileName: 'edit',
      path: svgEdit,
    },
    {
      fileName: 'trash',
      path: svgTrash,
    },
    {
      fileName: 'copy',
      path: svgCopy,
    },
    {
      fileName: 'partNumericTextInput',
      path: PartNumericTextInput,
    },
    {
      fileName: 'partInstructionConfirm',
      path: PartInstructionConfirm,
    },
    {
      fileName: 'partSopTimer',
      path: PartSopTimer,
    },
    {
      fileName: 'partDateRecord',
      path: PartDateRecord,
    },
    {
      fileName: 'partElectronicFile',
      path: PartElectronicFile,
    },
    {
      fileName: 'partReceiveConsumption',
      path: PartReceiveConsumption,
    },
    {
      fileName: 'partResultConfirm',
      path: PartResultConfirm,
    },
    {
      fileName: 'testpartButtonBranch',
      path: svgTestPartButtonBranch,
    },
    {
      fileName: 'testpartSystemBranch',
      path: svgTestPartSystemBranch,
    },
    {
      fileName: 'partElectronicShelfLabel',
      path: PartElectronicShelfLabel,
    },
    {
      fileName: 'partInventoryConsumption',
      path: PartInventoryConsumption,
    },
    {
      fileName: 'testpartUpdDevice',
      path: svgTestPartUpdDevice,
    },
    {
      fileName: 'testpartLabelOutput',
      path: svgTestPartLabelOutput,
    },
    {
      fileName: 'testpartCommDevice',
      path: svgTestPartCommDevice,
    },
    {
      fileName: 'testpartWeighingCalib',
      path: svgTestPartWeighingCalib,
    },
    {
      fileName: 'testpartPalletCargo',
      path: svgTestPartPalletCargo,
    },
    {
      fileName: 'partCopyNode',
      path: PartCopyNode,
    },
  ],
  category: [],
  blockSelectValue: '',
});

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});
const messageBoxSopAddSaveRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const boxChardGraph = ref<HTMLDivElement | null>(null);
const dataSourceRef = ref<FormInstance>();
const dataRules = {
  sopFlowNmJp: [
    rules.required('textBox'),
    rules.length(16),
    rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
  ],
  sopCatTxtJp: [rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS])],
  untSopCat: [rules.required('selectComboBox')],
};
/**
 * キャンバスサイズの計算
 * @param {*} sizeWidth - screen Width
 * @param {*} sizeHeight - screen Height
 */
const getLocaleGraphSize = (sizeWidth: number, sizeHeight: number) => {
  // -40(dialog padding) -20(card padding)
  let localeWidthNum: number = 0;
  const defWidth: number = 60;
  const pageSizeWidthData: string = '0';
  const pageSizeWidthValue: number = Number(
    pageSizeWidthData.replace('px', ''),
  );
  if (sizeWidth > pageSizeWidthValue) {
    localeWidthNum = sizeWidth * 0.6 - defWidth;
  } else {
    localeWidthNum = pageSizeWidthValue * 0.6 - defWidth;
  }
  let defHeight: number = 0;
  if (state.SOPChartFlag === 'NEW') {
    // -54(dialog header) -20(dialog padding) -60(dialog footer) -200(form) -10(Graph padding)
    defHeight = 120;
  } else {
    // -54(dialog header) -20(dialog padding) -10(Graph padding)
    defHeight = 84;
  }
  const pageSizeHeightData: string = '0';
  const pageSizeHeightValue: number = Number(
    pageSizeHeightData.replace('px', ''),
  );
  let localeHeightNum: number = 0;
  if (sizeHeight > pageSizeHeightValue) {
    localeHeightNum = sizeHeight * 0.6 - defHeight;
  } else {
    localeHeightNum = pageSizeHeightValue * 0.6 - defHeight;
  }
  return {
    localeWidth: localeWidthNum,
    localeHeight: localeHeightNum,
  };
};
/**
 * set Locale Graph Size
 * @param {*} widthVal - screen width
 * @param {*} heightVal - screen height
 */
const setLocaleGraphSize = (widthVal: number, heightVal: number) => {
  const localeData = getLocaleGraphSize(widthVal, heightVal);
  if (state.localeEditGraph !== null && props.SOPAddFlag !== false) {
    state.localeEditGraph!.resize(
      localeData.localeWidth,
      localeData.localeHeight,
    );
    state.localeEditGraph!.zoomToFit({ maxScale: 0.9 });
    state.localeEditGraph!.centerContent();
  }
};
/**
 * part node code Check
 * @param {*} sopPartsCD - node Code
 */
const checkPartNodeCode = (sopPartsCD: string) => {
  const partList: string[] = state.SOPSetData.partCds;
  const sameCd = partList.filter((item: string) => item === sopPartsCD);
  let isExit = false;
  if (sameCd.length > 0) {
    isExit = true;
  }
  return isExit;
};
/**
 * ノードの2次元配列（ノードマトリックス）からグラフを再描画する
 *
 * @param allNodesMatrix - ノードIDの2次元配列（行: Y方向, 列: X方向）
 * @param nodeAttrMap - ノードIDをキーとした属性情報マップ（位置・種別など）
 *
 * 概要:
 * 1. 既存のノード・エッジを全削除し、allNodesMatrixの内容に基づきノードを再生成する。
 * 2. ノード種別ごとにX/Y座標を計算し、ノードを配置する。
 *    - Start/Endノードは特別なY計算
 *    - Join/Branch/Confluenceなどaddpart系は個別の間隔定数で調整可能
 *    - 通常パーツは等間隔、直前が収束点の場合は間隔を個別調整可能
 * 3. outgoingIdsやbranchOption.valueなどの情報を元にエッジを再接続する。
 * 4. 必要に応じてラベルや属性の復元、レイアウト調整も行う。
 * 5. 最後に全ノードの位置情報をログ出力（デバッグ用）
 *
 * 主な用途:
 * - パーツ追加・削除・編集後のグラフ再描画
 * - ノードの位置や接続関係の再構築
 */
const redrawFromNodesMatrix = (
  allNodesMatrix: Matrix,
  nodeAttrMap: Record<string, unknown>,
  branchList: BranchList,
  parentToConfluenceMap: Record<string, string>,
  outputLog = false,
) => {
  // state.sopPartAddType = 'redrawFromNodesMatrixAdd';
  if (outputLog)
    console.log(
      'redrawFromNodesMatrix: 開始',
      'nodeAttrMap:',
      nodeAttrMap,
      'branchList:',
      branchList,
    );
  // 1. 既存ノード・エッジを全削除
  state.localeEditGraph!.getNodes().forEach((node) => {
    state.localeEditGraph!.removeNode(node.id);
    node.removeTool('button');
    node.removeTool('boundary');
  });
  state
    .localeEditGraph!.getEdges()
    .forEach((edge) => state.localeEditGraph!.removeEdge(edge.id));
  const localeData = getLocaleGraphSize(
    state.screenWidthVal,
    state.screenHeightVal,
  );
  // 位置情報（パーツ幅＋パーツ間のスペース）を定数化
  const sopPartConst = state.SOPSetData?.sopPartConst;
  const partWidth = sopPartConst?.partWidth ?? 376;
  const partSpaceX = sopPartConst?.marginLeft ?? 20;
  const addPartWidth = sopPartConst?.addPartWidth ?? 24;
  const startPartWidth = sopPartConst?.startPartWidth ?? partWidth;
  const xInterval = partWidth + partSpaceX;
  const baseX = localeData.localeWidth / 2 - startPartWidth / 2;

  // 2. 2次元配列からノードを再作成
  const idToNodeMap = new Map<string, Node>();
  allNodesMatrix.forEach((row, yIdx) => {
    row.forEach((id, xIdx) => {
      if (!id) return;
      // ノード種別ごとに位置を調整
      let posX = baseX;
      // let posY = baseY;
      let sopPartsCD = '';
      if (nodeAttrMap && nodeAttrMap[id]) {
        sopPartsCD =
          // @ts-expect-error sopPartsCD/shape属性ある
          nodeAttrMap[id]?.sopPartsCD || nodeAttrMap[id]?.shape || '';
      }

      // --- X位置計算 ---
      if (
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_START_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_START_CD ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_END_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_END_CD
      ) {
        posX = baseX + xIdx * xInterval;
      } else if (
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
      ) {
        posX = baseX + (startPartWidth - addPartWidth) / 2 + xIdx * xInterval;
      } else {
        posX = baseX + (startPartWidth - partWidth) / 2 + xIdx * xInterval;
      }
      // --- Y位置計算 ---
      // オプション指定
      const options = {
        baseY: 10, // ベースY
        marginY: 104, // マージン（Y間隔）
      };
      // 呼び出し
      const posY = calcNodePosY(
        sopPartsCD,
        yIdx,
        allNodesMatrix,
        nodeAttrMap,
        options,
      );
      // --- ノード追加処理 ---
      // @ts-expect-error nodeAttrMap[id]はオブジェクトです。
      const attr = { ...nodeAttrMap[id] };
      const node = state.localeEditGraph!.addNode({
        id,
        ...attr,
        position: { x: posX, y: posY },
      });
      // nodeAttrMapが関数パラメータの場合、直接代入せずにObject.assignを使う
      if (nodeAttrMap[id]) {
        Object.assign(nodeAttrMap[id], {
          position: { x: posX, y: posY }, // ノード位置を更新
        });
      }
      // ノードの位置を設定
      if (node && typeof node.setPosition === 'function') {
        node.setPosition(posX, posY);
      }
      idToNodeMap.set(id, node);
    });
  });
  if (outputLog) console.log('ノード配置完了:', Array.from(idToNodeMap.keys()));

  // 3. ノード間のエッジを再接続
  // ループ設定時の出力先リスト
  const loopOutputList: { id: string; outputId: string }[] = [];

  state
    .localeEditGraph!.getEdges()
    .forEach((edge) => state.localeEditGraph!.removeEdge(edge.id));
  allNodesMatrix.forEach((row, yIdx) => {
    row.forEach((id, xIdx) => {
      if (!id) return;
      // ノード種別判定
      const rowNode = idToNodeMap.get(id);
      if (!rowNode) {
        return;
      }
      const sopPartsCd = rowNode.getProp<string>('sopPartsCD');
      const nodeType = getEdgeNodeType(sopPartsCd, state.SOPSetData.partCds);

      // 入力エッジ
      let inputSourceId: string | null = null;
      // 出力エッジ
      let outputTargetId: string | null = null;

      if (nodeType === 'normal') {
        // 入力エッジ: 前の行・同じ列
        if (yIdx > 0) {
          inputSourceId = allNodesMatrix[yIdx - 1][xIdx];
        }
        // 出力エッジ: 次の行・同じ列
        if (yIdx < allNodesMatrix.length - 1) {
          outputTargetId = allNodesMatrix[yIdx + 1][xIdx];
        }
      } else if (nodeType === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME) {
        // 入力エッジ: 前の行・同じ列
        if (yIdx > 0) {
          inputSourceId = allNodesMatrix[yIdx - 1][xIdx];
        }
        // 出力エッジ: 次の行・同じ列
        if (
          yIdx < allNodesMatrix.length - 1 &&
          allNodesMatrix[yIdx + 1][xIdx] !== undefined &&
          allNodesMatrix[yIdx + 1][xIdx] !== null &&
          allNodesMatrix[yIdx + 1][xIdx] !== ''
        ) {
          outputTargetId = allNodesMatrix[yIdx + 1][xIdx];
        } else {
          // 関連する収束ノード
          const parentBranchId =
            findNearParentBranchId(branchList, allNodesMatrix[yIdx][xIdx]) ||
            allNodesMatrix[yIdx][xIdx];
          const confluenceNodeId = parentToConfluenceMap[parentBranchId];
          if (confluenceNodeId) {
            outputTargetId = confluenceNodeId;
          }
        }
      } else if (nodeType === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME) {
        // 入力エッジ: 親分岐ノード
        // 分岐親パーツ特定
        const parentBranchId =
          findNearParentBranchId(branchList, allNodesMatrix[yIdx][xIdx]) ||
          allNodesMatrix[yIdx][xIdx];
        const parentRow = findNodeRow(allNodesMatrix, parentBranchId);
        const parentBranchList = allNodesMatrix[parentRow + 1].filter(
          (cell) => cell !== undefined && cell !== null && cell !== '',
        );
        if (
          parentBranchList.length > 0 &&
          parentBranchList.includes(allNodesMatrix[yIdx][xIdx])
        ) {
          inputSourceId = parentBranchId;
        }
        // 出力エッジ
        // 次の行・同じ列
        if (
          yIdx < allNodesMatrix.length - 1 &&
          allNodesMatrix[yIdx + 1][xIdx] !== undefined &&
          allNodesMatrix[yIdx + 1][xIdx] !== null &&
          allNodesMatrix[yIdx + 1][xIdx] !== ''
        ) {
          outputTargetId = allNodesMatrix[yIdx + 1][xIdx];
        } else {
          // ループ設定がある場合
          const loopTarget = getLoopTargetNode(
            id,
            parentBranchId,
            parentBranchList,
            nodeAttrMap,
            // @ts-expect-error localeEditGraph
            state.localeEditGraph,
          );
          if (loopTarget) {
            outputTargetId = loopTarget.id;
            loopOutputList.push({ id, outputId: outputTargetId });
          } else {
            // 関連する収束ノード
            const confluenceNodeId = parentToConfluenceMap[parentBranchId];
            if (confluenceNodeId) {
              outputTargetId = confluenceNodeId;
            }
          }
        }
      } else if (nodeType === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME) {
        // 入力エッジ: 前の行・同じ列
        if (yIdx > 0) {
          inputSourceId = allNodesMatrix[yIdx - 1][xIdx];
        }
        // 出力エッジ: 次の行・同じ列
        if (
          yIdx < allNodesMatrix.length - 1 &&
          allNodesMatrix[yIdx + 1][xIdx] !== undefined &&
          allNodesMatrix[yIdx + 1][xIdx] !== null &&
          allNodesMatrix[yIdx + 1][xIdx] !== ''
        ) {
          outputTargetId = allNodesMatrix[yIdx + 1][xIdx];
        } else {
          // 関連する収束ノード
          const parentBranchId =
            findParentBranchId(branchList, allNodesMatrix[yIdx][xIdx]) ||
            allNodesMatrix[yIdx][xIdx];
          const rowFrom = findNodeRow(
            allNodesMatrix,
            allNodesMatrix[yIdx][xIdx],
          );
          const rowTo = findNodeRow(
            allNodesMatrix,
            parentToConfluenceMap[parentBranchId],
          );
          // 収束ノードの行を探す
          for (let i = rowFrom + 1; i <= rowTo; i++) {
            const nextRow = allNodesMatrix[i];
            if (
              nextRow &&
              nextRow[0] !== undefined &&
              nextRow[0] !== null &&
              nextRow[0] !== ''
            ) {
              const nextNode = idToNodeMap.get(nextRow[0]);
              if (!nextNode) {
                return;
              }
              const partsCd = rowNode.getProp<string>('sopPartsCD');
              if (partsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD) {
                const [firstId] = nextRow;
                outputTargetId = firstId;
                break;
              }
            }
          }
        }
      }
      // エッジ追加
      if (outputTargetId && outputTargetId !== id) {
        const targetNode = state.localeEditGraph!.getCellById(outputTargetId);
        const targetPartsCd = targetNode.getProp<string>('sopPartsCD');
        const sameCdFlag = checkPartCode(
          state.SOPSetData.partCds,
          targetPartsCd,
        );
        if (
          sameCdFlag ||
          [
            SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD,
            SOP_PARTS_VARIABLES.PART_SPEC_END_CD,
          ].includes(targetPartsCd)
        ) {
          // @ts-expect-error localeEditGraph
          addBlockTargetEdge4Temp(id, outputTargetId, state.localeEditGraph);
        } else {
          // @ts-expect-error localeEditGraph
          addPathTargetEdge(id, outputTargetId, state.localeEditGraph);
        }
      }
      if (inputSourceId && inputSourceId !== id) {
        const targetNode = state.localeEditGraph!.getCellById(id);
        const targetPartsCd = targetNode.getProp<string>('sopPartsCD');
        if (targetPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
          // @ts-expect-error localeEditGraph
          addPathTargetEdge(inputSourceId, id, state.localeEditGraph);
        }
      }
    });
  });

  // 4. ノード属性やラベルなどの追加復元が必要な場合はここで行う
  allNodesMatrix.forEach((row) => {
    row.forEach((id) => {
      if (!id) return;
      let sopPartsCD = '';
      if (nodeAttrMap && nodeAttrMap[id]) {
        sopPartsCD =
          // @ts-expect-error sopPartsCD/shape属性ある
          nodeAttrMap[id]?.sopPartsCD || nodeAttrMap[id]?.shape || '';
      }
      if (sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
        // branchOptionが配列の場合、各branchOptionにlabelをセット
        // @ts-expect-error branchOption属性ある
        const branchOption = nodeAttrMap[id]?.branchOption;
        if (Array.isArray(branchOption)) {
          branchOption.forEach((opt: SelectBranchOption) => {
            // ノードのラベルや他の情報からlabelをセット
            // @ts-expect-error label属性ある
            // eslint-disable-next-line no-param-reassign
            opt.label = nodeAttrMap[id]?.label ?? '';
          });
          // @ts-expect-error branchOption属性ある
          // eslint-disable-next-line no-param-reassign
          nodeAttrMap[id].branchOption = branchOption;
        }
      }
    });
  });

  // 5. レイアウト調整やツール追加なども必要に応じて
  // 収束ノードに対する.verticesプロパティの設定
  const allConfluenceNodes = state
    .localeEditGraph!.getNodes()
    .filter(
      (node) =>
        node.getProp('sopPartsCD') ===
          SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME ||
        node.getProp('sopPartsCD') ===
          SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
    );
  allConfluenceNodes.forEach((confluenceNode) => {
    const incomingEdges = state.localeEditGraph!.getConnectedEdges(
      confluenceNode,
      {
        incoming: true,
      },
    );
    const confluencePos = confluenceNode.getProp<PositionOption>('position');
    incomingEdges.forEach((edge) => {
      // 収束ノードのY座標と異なる場合のみverticesを設定
      const sourceNode = state.localeEditGraph!.getCellById(
        edge.getSourceCellId(),
      );
      const sourcePos = sourceNode.getProp<PositionOption>('position');
      if (sourcePos.y !== confluencePos.y) {
        // eslint-disable-next-line no-param-reassign
        edge.vertices = [
          {
            x: sourcePos.x,
            y:
              confluencePos.y -
              (state.SOPSetData?.sopPartConst?.addPartHeight ?? 24),
          },
        ];
      }
    });
  });
  // ループ設定パーツに対する.verticesプロパティの設定
  loopOutputList.forEach(({ id, outputId }) => {
    const columnIndex = findNodeCol(allNodesMatrix, id);
    if (columnIndex === 0) {
      return;
    }
    const outGoingEdges = state.localeEditGraph!.getConnectedEdges(id, {
      outgoing: true,
    });
    const outputIdNode = state.localeEditGraph!.getCellById(outputId);
    if (!outputIdNode) return;
    const outputPos = outputIdNode.getProp<PositionOption>('position');
    outGoingEdges.forEach((edge) => {
      // ループ設定ノードのY座標と異なる場合のみverticesを設定
      const sourceNode = state.localeEditGraph!.getCellById(
        edge.getSourceCellId(),
      );
      if (!sourceNode) return;
      const sourcePos = sourceNode.getProp<PositionOption>('position');
      if (sourcePos.y !== outputPos.y) {
        // eslint-disable-next-line no-param-reassign
        edge.vertices = [
          {
            x: sourcePos.x + 20,
            y:
              outputPos.y -
              (state.SOPSetData?.sopPartConst?.addPartHeight ?? 24),
          },
        ];
      }
    });
  });

  // tools追加
  const addToolsNodes = state
    .localeEditGraph!.getNodes()
    .filter(
      (node) =>
        node.getProp('sopPartsCD') !== SOP_PARTS_VARIABLES.PART_SPEC_START_CD &&
        node.getProp('sopPartsCD') !== SOP_PARTS_VARIABLES.PART_SPEC_END_CD &&
        node.getProp('sopPartsCD') !==
          SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD &&
        node.getProp('sopPartsCD') !==
          SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD &&
        node.getProp('sopPartsCD') !== SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
    );
  addToolsNodes.forEach((node) => {
    node.addTools({
      name: 'button',
      args: {
        zIndex: 1000,
        markup: [
          {
            tagName: 'image',
            selector: 'copy',
            attrs: {
              'xlink:href': state.imageMap.get('copy'),
              width: 20,
              height: 20,
              cursor: 'pointer',
              'pointer-events': 'all',
            },
          },
        ],
        x: '100%',
        y: 0,
        offset: { x: -25, y: 8 },
        onClick() {},
      },
    });
    node.addTools({
      name: 'button',
      args: {
        zIndex: 1000,
        markup: [
          {
            tagName: 'image',
            selector: 'trash',
            attrs: {
              'xlink:href': state.imageMap.get('trash'),
              width: 20,
              height: 20,
              cursor: 'pointer',
              'pointer-events': 'all',
            },
          },
        ],
        x: '100%',
        y: 0,
        offset: { x: -55, y: 8 },
        onClick() {},
      },
    });
    node.addTools({
      name: 'button',
      args: {
        zIndex: 1000,
        markup: [
          {
            tagName: 'image',
            selector: 'edit',
            attrs: {
              'xlink:href': state.imageMap.get('edit'),
              width: 20,
              height: 20,
              cursor: 'pointer',
              'pointer-events': 'all',
            },
          },
        ],
        x: '100%',
        y: 0,
        offset: { x: -85, y: 8 },
        onClick() {},
      },
    });
  });

  // 位置情報チェック処理
  const nodeAll = state.localeEditGraph!.getNodes();
  const diffNodes: string[] = [];
  nodeAll.forEach((node) => {
    const { id } = node;
    const graphPos = node.getProp<PositionOption>('position');
    const attrPos =
      nodeAttrMap &&
      nodeAttrMap[id] &&
      // @ts-expect-error 既にnodeAttrMap[id]にposition追加
      (nodeAttrMap[id] as SopControlPartOption).position
        ? // @ts-expect-error 既にnodeAttrMap[id]にposition追加
          (nodeAttrMap[id] as SopControlPartOption).position
        : undefined;
    if (attrPos && (graphPos.x !== attrPos.x || graphPos.y !== attrPos.y)) {
      diffNodes.push(id);
      // 位置が異なる場合はnodeAttrMapの座標で更新
      if (typeof node.setPosition === 'function') {
        node.setPosition(attrPos.x, attrPos.y);
        console.log(
          `[位置修正] ID: ${id}, graphPos: x=${graphPos.x}, y=${graphPos.y} → attrPos: x=${attrPos.x}, y=${attrPos.y}`,
        );
      }
    }
  });

  if (outputLog) {
    // 全ノードのエッジをノード毎にログ表示（ノードID、入力エッジの接続先ノードID、出力エッジの接続先ノードID）
    const allEdges = state.localeEditGraph!.getNodes();
    console.log('--- 全ノードのエッジ情報 ---');
    allEdges.forEach((node) => {
      // 入力エッジの接続元ノードID
      const incomingEdges =
        state.localeEditGraph!.getIncomingEdges(node.id) || [];
      const incomingNodeIds = incomingEdges.map((e) => e.getSourceCellId());

      // 出力エッジの接続先ノードID
      const outgoingEdges =
        state.localeEditGraph!.getOutgoingEdges(node.id) || [];
      const outgoingNodeIds = outgoingEdges.map((e) => e.getTargetCellId());
      console.log(
        `ノードID: ${node.id}, 入力エッジ接続元: [${incomingNodeIds.join(', ')}], 出力エッジ接続先: [${outgoingNodeIds.join(', ')}]`,
      );
    });

    // 全ノードの位置情報をログ出力（デバッグ用)
    const allNodes = state.localeEditGraph!.getNodes();
    console.log('--- 全ノードの位置情報 ---');
    allNodes.forEach((node) => {
      const pos = node.getProp<PositionOption>('position');
      const partsCD = node.getProp<string>('sopPartsCD');
      console.log(
        `ID: ${node.id}, パーツ種別: ${partsCD}, 位置: x=${pos.x}, y=${pos.y}`,
      );
    });
  }
};

/**
 * ノードマトリックスとノード属性マップからグラフを再描画する（ラップ関数）
 * @param nodesMatrix - ノードIDの2次元配列
 */
const redrawGraphFromMatrix = (
  nodesMatrix: Matrix,
  branchList: BranchList,
  parentToConfluenceMap: Record<string, string>,
  node: SopFlowDataOption[],
) => {
  // @ts-expect-error 一旦assignします
  const nodeAttrMap = createNodeIdToAttrMapFromDB(node);
  redrawFromNodesMatrix(
    nodesMatrix,
    nodeAttrMap,
    branchList,
    parentToConfluenceMap,
    false,
  );
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const flowListUpdate = (array: any[]) => {
  let returnData = [];
  // TODO ST バックエンドの対応完了したら、この部分を削除できます。
  returnData = array.reduce(
    (cleanList, current) => {
      const key = `${current.sopNodeNo}-${current.sopJoin.nextCondSeq}-${current.sopJoin.nextNodeNo}`;
      if (!cleanList.seen[key]) {
        cleanList.result.push(current);
        // eslint-disable-next-line no-param-reassign
        cleanList.seen[key] = true;
      }
      return cleanList;
    },
    { result: [], seen: {} },
  ).result;
  // TODO ED バックエンドの対応完了したら、この部分を削除できます。
  returnData = returnData.reduce(
    (
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      dataItem: any[],
      current: {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        sopNodeNo: any;
        sopJoin: {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          nextCondSeq: any;
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          nextNodeNo: any;
        };
      },
    ) => {
      // 条件分岐のnextNodeNoを結合します。
      const existingItem = dataItem.find(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (item: { sopNodeNo: any }) => item.sopNodeNo === current.sopNodeNo,
      );
      if (existingItem) {
        if (existingItem.sopJoin.nextCondSeq < current.sopJoin.nextCondSeq) {
          existingItem.sopJoin.nextNodeNo = `${existingItem.sopJoin.nextNodeNo},${current.sopJoin.nextNodeNo}`;
        } else {
          existingItem.sopJoin.nextNodeNo = `${current.sopJoin.nextNodeNo},${existingItem.sopJoin.nextNodeNo}`;
        }
      } else {
        dataItem.push(current);
      }
      return dataItem;
    },
    [],
  );
  returnData.forEach((item: { sopJoin: { nextNodeNo: string } }) => {
    const branchNodeIdList = item.sopJoin.nextNodeNo.split(',');
    // @ts-expect-error nextNodeNoを初期化
    // eslint-disable-next-line no-param-reassign
    item.sopJoin.nextNodeNo = {};
    branchNodeIdList.forEach(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (branchItem: { toString: () => any }, index: number) => {
        if (branchItem) {
          // @ts-expect-error この形でnextNodeNoの値を取得します
          // eslint-disable-next-line no-param-reassign
          item.sopJoin.nextNodeNo[`condBrDst${index + 1}`] =
            branchItem.toString();
        } else {
          // eslint-disable-next-line no-param-reassign
          item.sopJoin.nextNodeNo = '';
        }
      },
    );
    // eslint-disable-next-line no-param-reassign
    item.sopJoin.nextNodeNo = JSON.stringify(item.sopJoin.nextNodeNo);
  });
  return returnData;
};

const getBlockItemSelect = async (sopFlowNo: string) => {
  const apiRequestData: GetSopFlowData = { sopFlowNo };
  const { responseRef, errorRef } = await useGetSopFlowData({
    ...props.commonRequest,
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  if (responseRef.value) {
    let flowList = responseRef.value.data.rData.rList;
    if (flowList.length > 0) {
      flowList = flowListUpdate(JSON.parse(JSON.stringify(flowList)));
      selectList = JSON.parse(JSON.stringify(flowList));
    }
  } else {
    messageBoxSingleButtonRef.value.content = t('Tt.Chr.txtNoDataSet');
    messageBoxSingleButtonRef.value.type = 'info';
    openDialog('singleButtonRef');
  }
};
/**
 * 指定パーツ種別のG-コード/ノード指定有無を判定
 * @param partsCD パーツ種別
 * @param parent ノード
 * @returns boolean
 */
function isGvalNval(partsCD: string, node: Node): boolean {
  const indiv = node.getProp('individualPara');
  if (!indiv && typeof indiv !== 'object') {
    return false;
  }
  // --[パーツ種別] Gコード/ノード指定
  if (partsCD === SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD) {
    // --[指示時間]
    if (indiv.instructionTime === '2') {
      return true;
    }
  } else if (partsCD === SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD) {
    // --[指示値設定]
    if (
      indiv.instructionValueSetting === '2' ||
      indiv.instructionValueSetting === '3'
    ) {
      return true;
    }
    // --[記録値設定]
    if (indiv.inputMethod === '3' || indiv.inputMethod === '4') {
      return true;
    }
    // --[出力先設定]
    if (indiv.outputSetting === '2') {
      return true;
    }
  } else if (partsCD === SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD) {
    // --[指示値設定]
    if (
      indiv.instructionValueSetting === '3' ||
      indiv.instructionValueSetting === '4'
    ) {
      return true;
    }
  } else if (partsCD === SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD) {
    // --[指示値設定]
    if (
      indiv.instructionValueSetting === '3' ||
      indiv.instructionValueSetting === '4'
    ) {
      return true;
    }
  } else if (partsCD === SOP_PARTS_VARIABLES.PART_ELECTRONIC_FILE_CD) {
    // --[データファイルの定義]
    if (indiv.dataFileDefine === '3' || indiv.dataFileDefine === '4') {
      return true;
    }
  } else if (partsCD === SOP_PARTS_VARIABLES.PART_ELECTRONIC_SHELF_LABEL_CD) {
    // --[画面ID]
    if (indiv.shelfIdSetting === '2' || indiv.shelfIdSetting === '3') {
      return true;
    }
  } else if (partsCD === SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD) {
    // --[指示値設定]
    if (
      indiv.instructionValueSetting === '2' ||
      indiv.instructionValueSetting === '3'
    ) {
      return true;
    }
  } else if (partsCD === SOP_PARTS_VARIABLES.PART_EQUIPMENT_CONTAINER_CD) {
    // --[装置、容器設定]
    if (indiv.deviceAndVesselSetting === '2') {
      return true;
    }
  } else if (partsCD === SOP_PARTS_VARIABLES.PART_LABEL_OUTPUT_CD) {
    // --[プリンタデバイス、容器設定、パレット設定]
    if (
      indiv.labelPrinterIP === '2' ||
      indiv.labelContainerSetting === '1' ||
      indiv.labelPaletteSetting === '1'
    ) {
      return true;
    }
    // --[ラベル項目1-10]
    for (let i = 1; i <= 10; i++) {
      if (indiv[`labelItemSetting${i}`] === '2') {
        return true;
      }
    }
  } else if (partsCD === SOP_PARTS_VARIABLES.PART_PALLET_CARGO_CD) {
    // --[パレットID設定]
    if (indiv.palletIdSetting === '2' || indiv.palletIdSetting === '3') {
      return true;
    }
  }
  return false;
}
/**
 * flow chartの作成
 * @param {*} flowList - flowのデータ
 * @param {*} startNode - flowのstart Node
 */
const setSOPFlowChart = (flowList: SopFlowDataOption[], startNode: string) => {
  state.localeEditGraph!.disableHistory();
  const startCell: SopFlowDataOption[] = [];
  const otherCell: SopFlowDataOption[] = [];
  const endCell: SopFlowDataOption[] = [];
  // // [課題345] ADD ST NODEの連結の設計をリファクタリング
  // const { sopPartConst } = state.SOPSetData;
  // const flowListLastNode = flowList[flowList.length - 1];
  // flowList.forEach((node) => {
  //   const addpartObj = {
  //     nodeId: '',
  //     sopNodeNo: '',
  //     sopCieX: 0,
  //     sopCieY: 0,
  //     sopPartsCd: '',
  //     sopPartsNm: '',
  //     helpSetting: {
  //       helpFileType: 'N',
  //     },
  //     sopJoin: {
  //       nextCondSeq: 1,
  //       nextNodeNo: '',
  //     },
  //   };
  //   /**
  //    * sopPartsCd 91: controlPartStart
  //    * sopPartsCd 94: controlPartEnd
  //    * sopPartsCd 92: PartSpecConfluence
  //    * sopPartsCd 02: PartSopTimer
  //    * sopPartsCd 01: PartInstructionConfirm
  //    * sopPartsCd 03: PartNumericTextInput
  //    * sopPartsCd 08: PartButtonBranch
  //    * sopPartsCd 09: PartSystemBranch
  //    * sopPartsCd 10: PartExternalDevice
  //    * sopPartsCd 15: PartWeightCalibration
  //    */
  //   const sopPartsCdList = [
  //     SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD,
  //     SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD,
  //     SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD,
  //     SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD,
  //     SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD,
  //     SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD,
  //     SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD,
  //     SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD,
  //     SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD,
  //     SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD,
  //   ];
  //   if (node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_START_CD) {
  //     const addPart = createAddPart(node.sopCieX, node.sopCieY);
  //     addpartObj.nodeId = addPart.id;
  //     addpartObj.sopNodeNo = addPart.id;
  //     addpartObj.sopCieX =
  //       node.sopCieX +
  //       sopPartConst!.startPartHeight / 2 -
  //       sopPartConst!.addPartWidth / 2;
  //     addpartObj.sopCieY = node.sopCieY + 95;
  //     addpartObj.sopPartsCd = SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD;
  //     addpartObj.sopPartsNm = SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME;
  //     addpartObj.sopJoin.nextNodeNo = JSON.stringify({
  //       condBrDst1: JSON.parse(node.sopJoin.nextNodeNo).condBrDst1,
  //     });
  //     // eslint-disable-next-line no-param-reassign
  //     node.sopJoin.nextNodeNo = JSON.stringify({ condBrDst1: addPart.id });
  //     // @ts-expect-error 簡潔なobjectをflowListに保存します。
  //     flowList.push(addpartObj);
  //     // @ts-expect-error 動的に変数判断
  //   } else if (sopPartsCdList.includes(node.sopPartsCd)) {
  //     const branchNextNodes = node.sopJoin.nextNodeNo
  //       ? JSON.parse(node.sopJoin.nextNodeNo)
  //       : '';
  //     const branchNodeLength = Object.values(branchNextNodes).filter(
  //       (item) => item !== '',
  //     ).length;
  //     Object.keys(branchNextNodes).forEach((item, index) => {
  //       if (branchNextNodes[item] !== '') {
  //         const positionX =
  //           node.sopCieX +
  //           sopPartConst!.blockWidth +
  //           sopPartConst!.addPartWidth;
  //         const positionY = node.sopCieY + 80 + sopPartConst!.marginTop;
  //         const position = getPartPosition(
  //           { x: positionX, y: positionY },
  //           branchNodeLength,
  //           index,
  //         );
  //         const addPart = createAddPart(position.x, position.y);
  //         const branchAddpart = {
  //           nodeId: addPart.id,
  //           sopNodeNo: addPart.id,
  //           sopCieX: position.x,
  //           sopCieY: position.y,
  //           sopPartsCd: SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
  //           helpSetting: {
  //             helpFileType: 'N',
  //           },
  //           sopPartsNm: SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME,
  //           sopJoin: {
  //             nextNodeNo: JSON.stringify({
  //               condBrDst1: JSON.parse(node.sopJoin.nextNodeNo)[item],
  //             }),
  //           },
  //         };
  //         // eslint-disable-next-line no-param-reassign
  //         node.sopJoin.nextNodeNo = JSON.parse(node.sopJoin.nextNodeNo);
  //         Object.assign(node.sopJoin.nextNodeNo, { [`${item}`]: addPart.id });
  //         // eslint-disable-next-line no-param-reassign
  //         node.sopJoin.nextNodeNo = JSON.stringify(node.sopJoin.nextNodeNo);
  //         // @ts-expect-error 簡潔なobjectをflowListに保存します。
  //         flowList.push(branchAddpart);
  //         // }
  //       }
  //     });
  //   } else if (
  //     node.sopPartsCd !== SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD &&
  //     node.sopPartsCd !== SOP_PARTS_VARIABLES.PART_SPEC_END_CD &&
  //     node !== flowListLastNode
  //   ) {
  //     const addPart = createAddPart(node.sopCieX, node.sopCieY);
  //     addpartObj.nodeId = addPart.id;
  //     addpartObj.sopNodeNo = addPart.id;
  //     addpartObj.sopCieX =
  //       node.sopCieX +
  //       sopPartConst!.defaultWidth / 2 -
  //       sopPartConst!.addPartWidth / 2;
  //     addpartObj.sopCieY = node.sopCieY + sopPartConst!.marginTop + 80;
  //     addpartObj.sopPartsCd = SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD;
  //     addpartObj.sopPartsNm = SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME;
  //     addpartObj.sopJoin.nextNodeNo = JSON.stringify({
  //       condBrDst1: node.sopJoin.nextNodeNo
  //         ? JSON.parse(node.sopJoin.nextNodeNo).condBrDst1
  //         : '',
  //     });
  //     // eslint-disable-next-line no-param-reassign
  //     node.sopJoin.nextNodeNo = JSON.stringify({ condBrDst1: addPart.id });
  //     // @ts-expect-error 簡潔なobjectをflowListに保存します。
  //     flowList.push(addpartObj);
  //   }
  // });
  // // [課題345] ADD ED
  flowList.forEach((node) => {
    const cell = node;
    cell.nodeId = node.sopNodeNo.replace(`${node.sopFlowNo}-`, '');
    cell.sopPartsCd = node.sopPartsCd;
    if (node.sopNodeNo === startNode) {
      startCell.push(cell);
    } else if (node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_END_CD) {
      endCell.push(cell);
    } else {
      otherCell.push(cell);
    }
  });

  selectList.forEach((selectItem) => {
    const flowListItem = flowList.find(
      (flowItem) =>
        flowItem.sopCieX === selectItem.sopCieX &&
        flowItem.sopCieY === selectItem.sopCieY,
    );
    if (flowListItem) {
      flowListItem.parentSopNodeNo = selectItem.parentSopNodeNo;
      flowListItem.sopNodeNo = selectItem.sopNodeNo;
      flowListItem.nodeId = selectItem.sopNodeNo;
    }
  });
  // const FlowObj = formatSOPFlowEdges(startCell, otherCell, endCell);
  // --- 収束ノードIDを分岐親パーツに追加 ---
  const confluenceList = flowList.filter(
    (node) => node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
  );
  confluenceList.forEach((confluenceNode) => {
    const parentNodeId = confluenceNode.parentSopNodeNo;
    if (!parentNodeId) return;
    // 分岐親パーツを型安全に取得
    const parentNode = flowList.find((node) => node.sopNodeNo === parentNodeId);
    if (parentNode) {
      (
        parentNode as SopFlowGetDataOption & { confluenceNodeId?: string }
      ).confluenceNodeId = confluenceNode.sopNodeNo;
    }
  });

  // --- 収束ノードのプロパティに親ノードIDを設定し、親ノードのプロパティに収束ノードIDを設定（x6 Node型オブジェクトに対して） ---
  const confluenceNodes = state
    .localeEditGraph!.getNodes()
    .filter(
      (node) =>
        node.getProp<string>('sopPartsCD') ===
        SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
    );
  confluenceNodes.forEach((confluenceNode) => {
    // 収束ノードの親ノードIDを取得
    const confluenceItem = confluenceList.find(
      (item) => item.sopNodeNo === confluenceNode.id,
    );
    const parentNodeId = confluenceItem
      ? confluenceItem.parentSopNodeNo
      : undefined;
    if (!parentNodeId) return;
    // 親ノード（Node型）を取得
    const parentNode = state.localeEditGraph!.getCellById(parentNodeId);
    if (parentNode && parentNode.isNode()) {
      // 親ノードに収束ノードIDをセット
      parentNode.prop('confluenceNodeId', confluenceNode.id);
      // 収束ノードに親ノードIDをセット
      confluenceNode.prop('parentNodeId', parentNode.id);
    }
  });
  const nodeData = getSOPFlowChartData(
    flowList,
    state.SOPSetData,
    state.imageMap,
    t,
  );
  // @ts-expect-error 一旦assignします
  const newNodeMatrix = createNodesMatrixFromDb(nodeData);

  // 分岐パーツと収束ノードのマッピング情報を作成する（行の昇順で作成）
  const { branchList, parentToConfluenceMap } = createBranchList(
    newNodeMatrix,
    // @ts-expect-error state.localeEditGraph
    state.localeEditGraph,
    flowList,
  );
  // state.localeSelectGraph!.fromJSON({ nodes: nodeData });
  // createSOPFlowEdges(FlowObj.edges);

  redrawGraphFromMatrix(
    newNodeMatrix,
    branchList,
    parentToConfluenceMap,
    // @ts-expect-error 一旦assignします
    nodeData,
  );
  // state.localeEditGraph!.fromJSON({ nodes: nodeData });
  // createSOPFlowEdges(FlowObj.edges);
  // 各パーツデザイン調整
  const allNodes = state.localeEditGraph!.getNodes();
  allNodes.forEach((item) => {
    const currentCell = state.localeEditGraph!.getCellById(item.id);
    if (currentCell.isNode()) {
      const partsCD = currentCell.getProp<string>('sopPartsCD');
      const titleText = currentCell.getAttrByPath<string>('title/text');
      const commonSetting = currentCell.getProp('commonSetting');
      const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
      if (sameCdFlag) {
        const rectPartsData: SopRectPartsOption[] = state.SOPSetData.rectParts;
        const rectItem = rectPartsData.filter(
          (v) => v.sopPartsCD === partsCD,
        )[0];
        currentCell.setAttrs({
          line: rectItem.attrs.line,
          body: {
            stroke: '#a8b0c2',
            fill: '#f0ffff',
          },
          image: {
            refX: 5,
            refY: 5,
          },
          title: {
            text: titleText,
            refX: 55,
            refY: 40,
            fill: '#000000',
            fontSize: 16,
            fontWeight: 'bold',
            textAnchor: 'left',
          },
          text: {
            text: '',
          },
          wCheck: {
            display: commonSetting.dcheckFlg === '1' ? 'block' : 'none',
            refX: 150,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          abnormalityLevel1: {
            display: commonSetting.devCorrLv === '1' ? 'block' : 'none',
            refX: 180,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          abnormalityLevel2: {
            display: commonSetting.devCorrLv === '2' ? 'block' : 'none',
            refX: 180,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          abnormalityLevel3: {
            display: commonSetting.devCorrLv === '3' ? 'block' : 'none',
            refX: 180,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          // パーツ種別が数値入力の場合、記録値入力方法:「演算入力」ならformulaアイコンを表示
          formula: {
            display: (() => {
              if (partsCD === SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD) {
                const indiv = item.getProp('individualPara');
                if (
                  indiv &&
                  typeof indiv === 'object' &&
                  indiv.inputMethod === '5'
                ) {
                  return 'block';
                }
              }
              return 'none';
            })(),
            'xlink:href': state.imageMap.get('formula'),
            refX: 210,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          // G-コード/ノード指定ありの場合gvalNvalアイコンを表示
          gvalNval: {
            display: isGvalNval(partsCD, item) ? 'block' : 'none',
            'xlink:href': state.imageMap.get('gvalNval'),
            refX: 240,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          write: {
            display: commonSetting.recFillFlg === '1' ? 'block' : 'none',
            refX: 120,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
        });
        if (commonSetting.scnShowFlg === '0') {
          currentCell.addTools({
            name: 'boundary',
            args: {
              zIndex: 0,
              attrs: {
                fill: '#f0f8ff',
                stroke: '#4169e1',
                'stroke-dasharray': '7, 8',
                strokeWidth: 3,
                fillOpacity: 0,
              },
            },
          });
        }
      }
    }
  });
};
// [課題399] DEL ST ブロックの詳細を取得するとき、APIを呼ばなくて、フローに取得します。
// /**
//  * 選択されたブロック情報を取得
//  */
// const getBlockItemSelect = (flowList) => {
// if (blockNo === undefined) {
//   return;
// }
// const apiRequestData: GetSopFlowData = { sopFlowNo: blockNo };
// const { responseRef, errorRef } = await useGetSopFlowData(apiRequestData);
// const resData = responseRef.value?.data;
// if (resData === undefined) {
//   ElMessage({
//     // Tt.Chr.txtApiResponseErr は具体的なエラーメッセージが決まっていないので仮
//     message: t('Tt.Chr.txtApiResponseErr', errorRef.value!.code!),
//     type: 'error',
//   });
//   return;
// }
// if (resData.rCode === CONST.API_STATUS_CODE.SUCCESS) {
//   const flowList = resData.rData.rList;
// if (flowList.length > 0) {
//   state.localeEditGraph!.clearCells();
//   const localeData = getLocaleGraphSize(
//     state.screenWidthVal,
//     state.screenHeightVal,
//   );
//   if (flowList.length > 0) {
// setSOPFlowChart(flowList, flowList[0].sopNodeNo);
//     }
//     state.localeEditGraph!.resize(
//       localeData.localeWidth,
//       localeData.localeHeight,
//     );
//     state.localeEditGraph!.zoomToFit({ maxScale: 0.6 });
//     state.localeEditGraph!.centerContent();
//   }
// } else {
//   ElMessage({
//     message: t('Tt.Chr.txtNoDataSet'),
//     type: 'info',
//   });
// }
// };
// [課題399] DEL ED
/**
 * ノードのアイコン表示を初期化する
 * 呼び出すメソッドcreateNewGraph
 */
const updatePartNode = () => {
  const allNodes = state.localeEditGraph!.getNodes();
  allNodes.forEach((item) => {
    const currentCell = state.localeEditGraph!.getCellById(item.id);
    if (currentCell.isNode()) {
      const partsCD = currentCell.getProp<string>('sopPartsCD');
      const titleText = currentCell.getAttrByPath<string>('title/text');
      const commonSetting = currentCell.getProp('commonSetting');

      const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
      if (sameCdFlag) {
        const rectPartsData: SopRectPartsOption[] = state.SOPSetData.rectParts;
        const rectItem = rectPartsData.filter(
          (v) => v.sopPartsCD === partsCD,
        )[0];
        currentCell.setAttrs({
          line: rectItem.attrs.line,
          body: {
            stroke: '#a8b0c2',
            fill: '#f0ffff',
          },
          image: {
            refX: 5,
            refY: 5,
          },
          title: {
            text: titleText,
          },
          text: {
            text: '',
          },
          wCheck: {
            display: commonSetting.dcheckFlg === '1' ? 'block' : 'none',
            refX: 150,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          abnormalityLevel1: {
            display: commonSetting.devCorrLv === '1' ? 'block' : 'none',
            refX: 180,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          abnormalityLevel2: {
            display: commonSetting.devCorrLv === '2' ? 'block' : 'none',
            refX: 180,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          abnormalityLevel3: {
            display: commonSetting.devCorrLv === '3' ? 'block' : 'none',
            refX: 180,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          write: {
            display: commonSetting.recFillFlg === '1' ? 'block' : 'none',
            refX: 120,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
        });
        if (commonSetting.scnShowFlg === '0') {
          currentCell.addTools({
            name: 'boundary',
            args: {
              zIndex: 0,
              attrs: {
                fill: '#f0f8ff',
                stroke: '#4169e1',
                'stroke-dasharray': '7, 8',
                strokeWidth: 3,
                fillOpacity: 0,
              },
            },
          });
        }
      }
    }
  });
};
/**
 * キャンバスの新規作成
 */
const createNewGraph = async () => {
  // すべてのノードタイプをビルドする
  state.SOPSetData = getSOPSetting(t);
  state.imageMap = getImageMap(state.images);
  const localeData = getLocaleGraphSize(
    state.screenWidthVal,
    state.screenHeightVal,
  );
  if (boxChardGraph.value) {
    const LocalEditGraph = new Graph({
      container: boxChardGraph.value,
      width: localeData.localeWidth,
      height: localeData.localeHeight,
      translating: {
        restrict: true,
      },
      connecting: {
        router: 'manhattan',
        connector: {
          name: 'rounded',
          args: {
            radius: 28,
          },
        },
        anchor: 'center',
        connectionPoint: 'anchor',
        allowBlank: false,
        snap: {
          radius: 20,
          anchor: 'center',
        },
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: '#A2B1C3',
                strokeWidth: 2,
                targetMarker: {
                  name: 'block',
                  width: 12,
                  height: 8,
                },
              },
            },
            zIndex: 3,
          });
        },
        validateConnection({ targetMagnet }) {
          return !!targetMagnet;
        },
      },
      interacting: {
        nodeMovable() {
          return false;
        },
      },
    });
    LocalEditGraph!.clearCells();
    LocalEditGraph!.cleanClipboard();
    LocalEditGraph!.cleanSelection();

    state.localeEditGraph = LocalEditGraph;
    state.localeEditGraph!.clearCells();
    if (state.SOPChartFlag === 'NEW') {
      state.SOPBlockOption = state.chartOptions.blockData;
      state.localeEditGraph!.fromJSON(state.chartOptions.cellData);
      // 不要ボタンを削除
      const allNodes = state.localeEditGraph!.getNodes();
      allNodes.forEach((item) => {
        const currentCell = state.localeEditGraph!.getCellById(item.id);
        if (currentCell.isNode()) {
          currentCell.removeTools();
        }
      });
      updatePartNode();
    } else if (state.SOPChartFlag === 'PREVIEW') {
      state.SOPBlockOption = state.chartOptions.blockData;
      state.localeEditGraph!.fromJSON(state.chartOptions.cellData);
    } else if (state.SOPChartFlag === 'BE-PREVIEW') {
      // [課題399] MOD ST ブロックの詳細を取得するとき、APIを呼ばなくて、フローに取得します。
      //  await getBlockItemSelect(state.chartOptions.blockData.sopFlowNo);
      await getBlockItemSelect(props.blockSeleckValue);
      setSOPFlowChart(
        state.chartOptions.blockData.flowList,
        state.chartOptions.blockData.flowList[0].sopNodeNo,
      );
      // [課題399] MOD ST
    }
    state.localeEditGraph!.zoomToFit({ maxScale: 0.9 });
    state.localeEditGraph!.centerContent();
  }
};
/**
 * コンボボックスデータ取得
 */
const getComboBoxData = async () => {
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.commonRequest,
    condList: [
      {
        cmbId: 'category',
        condKey: 'm_unt_sop_flow',
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    // --[テンプレートカテゴリ]
    const categoryList = comboBoxResData.rData.rList.filter(
      (item: ComboBoxDataOptionData) => item.condKey === 'm_unt_sop_flow',
    );
    categoryList.forEach((option) => {
      state.category.push(option);
    });
  }
};
onMounted(() => {
  getComboBoxData();
});
/**
 * screen Width変更の場合は実行
 */
watch(
  () => props.screenWidth,
  (newValue: Props['screenWidth']) => {
    if (newValue) {
      state.screenWidthVal = newValue;
      state.screenHeightVal = props.screenHeight;
      setLocaleGraphSize(newValue, props.screenHeight);
    }
  },
  { deep: true },
);
/**
 * screen Height変更の場合は実行
 */
watch(
  () => props.screenHeight,
  (newValue: Props['screenHeight']) => {
    if (newValue) {
      state.screenWidthVal = props.screenWidth;
      state.screenHeightVal = newValue;
      setLocaleGraphSize(props.screenWidth, newValue);
    }
  },
  { deep: true },
);
/**
 * ブロック編集ページの表示制御の場合は実行
 */
watch(
  () => props.SOPAddFlag,
  (newOC: Props['SOPAddFlag']) => {
    if (newOC) {
      state.SOPChartDialogVisible = true;
      state.SOPChartFlag = props.SOPChartType;
      state.chartOptions = props.chartOptions;
      state.screenWidthVal = props.screenWidth;
      state.screenHeightVal = props.screenHeight;
      state.SOPBlockOption = {
        sopFlowNo: '',
        sopFlowNmJp: '',
        sopCatTxtJp: '',
        untSopCat: '',
        updDts: '',
        flowList: [],
      };
      nextTick(() => {
        createNewGraph();

        // 必須タグ生成
        const requiredList = [
          t('SOP.Chr.txtSopTempName'),
          t('SOP.Chr.txtSopType'),
        ];
        const elements = Array.from(
          document.querySelectorAll('.el-form-item__label'),
        );
        elements.forEach((item) => {
          const label = item.textContent ? `${item.textContent}` : '';
          if (requiredList.indexOf(label) < 0) {
            return;
          }
          const tag = document.createElement('span');
          tag.textContent = t('Cm.Chr.txtTagOfRequired');
          tag.className = `Util_ml-8 custom-form_tag el-tag el-tag--danger el-tag--dark is-round`;
          item.appendChild(tag);
        });
      });
    }
  },
  { deep: true },
);

/**
 * Dialog Min Width
 */
const getDialogMinWidth = () => {
  const minWidth = 1280 * 0.6;
  const minWidthStr: string = `min-width:${minWidth}px`;
  return minWidthStr;
};
/**
 * Current node Neighbors
 * @param {*} currentNode - データ
 */
const getCurrentNeighbors = (currentNode: Cell) => {
  const incoming: Cell[] = state.localeEditGraph!.getNeighbors(currentNode, {
    incoming: true,
  });
  const incomingValue: SelectOption[] = incoming.map((item: Cell) => {
    const sopPartsCD = item.getProp<string>('sopPartsCD');
    const itemCdFlag: boolean = checkPartNodeCode(sopPartsCD);
    let textValue = item.getAttrByPath<string>('text/text');
    if (itemCdFlag) {
      const titleText = item.getAttrByPath<string>('title/text');
      if (titleText !== '') {
        textValue = titleText;
      }
    }
    return {
      label: textValue,
      value: item.id,
    };
  });
  const outgoing: Cell[] = state.localeEditGraph!.getNeighbors(currentNode, {
    outgoing: true,
  });
  const outgoingValue: SelectOption[] = outgoing.map((item: Cell) => {
    const sopPartsCD = item.getProp<string>('sopPartsCD');
    const itemCdFlag: boolean = checkPartNodeCode(sopPartsCD);
    let textValue = item.getAttrByPath<string>('text/text');
    if (itemCdFlag) {
      const titleText = item.getAttrByPath<string>('title/text');
      if (titleText !== '') {
        textValue = titleText;
      }
    }
    return {
      label: textValue,
      value: item.id,
    };
  });
  return {
    incomingList: incomingValue,
    outgoingList: outgoingValue,
  };
};
/**
 * 選択したノードとエッジのNeighbors
 * @param {*} selectedCells - 選択したノードとエッジ
 */
const checkAllSelectedCellData = (selectedCells: Cell[]) => {
  const allNeighborsData: NeighborsOption[] = [];
  const itemIdData: NodeIdOption[] = [];
  if (selectedCells && selectedCells.length > 0) {
    // 選択したノードの入力ノードと出力ノードのデータを取得
    selectedCells.forEach((item: Cell) => {
      if (item.isVisible()) {
        if (item.isNode()) {
          // [課題2][No.2] ST parentFlagはTrueのノードもJOIN関係を生成する
          // const parentFlag = item.hasParent();
          // if (parentFlag === false) {
          const itemNeighbors = getCurrentNeighbors(item);
          const incomingVal = itemNeighbors.incomingList;
          const outgoingVal = itemNeighbors.outgoingList;
          allNeighborsData.push({
            incoming: incomingVal,
            outgoing: outgoingVal,
            id: item.id,
          });
          const uuid = uuidv4();
          itemIdData.push({
            itemV4Id: uuid,
            itemId: item.id,
          });
          // }
          // [課題2][No.2] ED parentFlagはTrueのノードもJOIN関係を生成する
        }
      }
    });
  }
  return {
    allNeighbors: allNeighborsData,
    itemIds: itemIdData,
  };
};
/**
 * グラフデータの登録処理
 * @param {*}  data - Flow Chart data
 */
const insertSOPChart = async (data: SopFlowDataOption[]) => {
  const apiRequestData: InsertSopBlock = {
    sopFlowNmJp: state.SOPBlockOption.sopFlowNmJp,
    sopCatTxtJp: state.SOPBlockOption.sopCatTxtJp,
    // [課題369] DEL ST ブロック/テンプレート登録のリファクタリング
    // sopFlowVer: 1,
    // [課題369] DEL ED
    untSopCat: state.SOPBlockOption.untSopCat,
    sopNodeList: data,
    sopFlowType: props.SOPFlowType,
  };
  const { responseRef, errorRef } = await useInsertSopBlock({
    ...props.commonRequest,
    btnId: 'btnDecision',
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSopAddSaveRef.value.title = errorRef.value.response.rTitle;
    messageBoxSopAddSaveRef.value.content = errorRef.value.response.rMsg;
    messageBoxSopAddSaveRef.value.type = 'error';
    openDialog('sopAddSaveRef');
    return;
  }
  if (responseRef.value?.data.rCode === CONST.API_STATUS_CODE.SUCCESS) {
    messageBoxSopAddSaveRef.value.title = responseRef.value.data.rTitle;
    messageBoxSopAddSaveRef.value.content = responseRef.value.data.rMsg;
    openDialog('sopAddSaveRef');
  }
};
// [課題345] ADD ST NODEの連結の設計をリファクタリング
/**
 * outgoingを置き換える
 * @param cell
 */
const getOutgoing = (cell: Cell) => {
  const outgoing = state.localeEditGraph!.getNeighbors(cell, {
    outgoing: true,
  });
  const incoming = state.localeEditGraph!.getNeighbors(cell, {
    incoming: true,
  });
  const addpartArr = [
    SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
    SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
  ];
  const filterPart = [
    SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
    SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
    SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
    SOP_PARTS_VARIABLES.PART_SPEC_START_CD,
    SOP_PARTS_VARIABLES.PART_SPEC_END_CD,
  ];
  const sopPartsCds = cell.getProp<string>('sopPartsCD');
  let outgoingId: string = '';
  let sopPartsCd;
  if (outgoing.length === 1) {
    sopPartsCd = outgoing[0].getProp<string>('sopPartsCD');
    if (
      addpartArr.includes(sopPartsCd) &&
      incoming[0].getProp<string>('sopPartsCD') !==
        SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
    ) {
      getOutgoing(outgoing[0]);
    } else if (sopPartsCds === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD) {
      outgoingId = cell.id;
    } else if (
      !filterPart.includes(sopPartsCds) &&
      incoming[0].getProp<string>('sopPartsCD') ===
        SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
    ) {
      outgoingId = cell.id;
    } else {
      outgoingId = outgoing[0].id;
    }
  } else {
    outgoingId = cell.id;
  }
  return outgoingId;
};
/**
 * 2次元配列に変換（X/Y座標グルーピング時に許容誤差を指定可能）
 * @param {Cell[]} nodes - ノードの配列
 * @returns {(string | null)[][]} 2次元配列形式のノードマトリックス
 */
const createNodesMatrix = (nodes: Cell[]): (string | null)[][] => {
  const nodesMatrix: (string | null)[][] = [];
  const usedIds = new Set<string>();

  const xTolerance: number = 0;
  const yTolerance: number = 2;

  // Y座標グループ化（許容誤差対応）
  const yGroupCenters: number[] = [];
  const yGroups: { [y: number]: Cell[] } = {};
  nodes.forEach((node) => {
    const pos = node.getProp<PositionOption>('position');
    if (pos.y === 0) return;
    // 許容誤差内の既存y中心を探す
    let groupY = yGroupCenters.find((yc) => Math.abs(yc - pos.y) <= yTolerance);
    if (groupY === undefined) {
      yGroupCenters.push(pos.y);
      groupY = pos.y;
    }
    if (!yGroups[groupY]) yGroups[groupY] = [];
    yGroups[groupY].push(node);
  });

  // Y座標の昇順で処理
  const sortedY = yGroupCenters.slice().sort((a, b) => a - b);

  // パーツの中央X座標でグルーピング（許容誤差あり）
  const centerXList: number[] = [];
  nodes.forEach((node) => {
    const pos = node.getProp<PositionOption>('position');
    if (pos.y === 0) return;
    const size = node.getProp<SizeOption>('size');
    const centerX = pos.x + (size?.width ?? 0) / 2;
    // 許容誤差内の既存centerXがあればそれを使う
    const found = centerXList.find((x) => Math.abs(x - centerX) <= xTolerance);
    if (found === undefined) {
      centerXList.push(centerX);
    }
  });
  centerXList.sort((a, b) => a - b);

  // 中央X座標→indexのマップを作成（許容誤差対応）
  const xIndexMap = new Map<number, number>();
  centerXList.forEach((x, idx) => {
    xIndexMap.set(x, idx);
  });
  const maxLen = centerXList.length;

  // 各行を中央X座標のindexでセット（全行同じ長さ）
  sortedY.forEach((yVal, yIdx) => {
    nodesMatrix[yIdx] = Array(maxLen).fill(null);
    yGroups[yVal].forEach((node) => {
      if (usedIds.has(node.id)) return;
      const pos = node.getProp<PositionOption>('position');
      const size = node.getProp<SizeOption>('size');
      const centerX = pos.x + (size?.width ?? 0) / 2;
      // 許容誤差内のcenterXを探す
      let xIdx = -1;
      for (let i = 0; i < centerXList.length; i++) {
        if (Math.abs(centerXList[i] - centerX) <= xTolerance) {
          xIdx = i;
          break;
        }
      }
      if (xIdx === -1) xIdx = 0;
      nodesMatrix[yIdx][xIdx] = node.id;
      usedIds.add(node.id);
    });
  });

  return nodesMatrix;
};
/**
 * 値により2次元配列内のインデックスを検索
 * @param {T[][]} array - 2次元配列
 * @param {T} value - 検索する値
 * @returns {[number, number] | null} - 値が見つかった場合はそのインデックスの配列、見つからなかった場合はnull
 * @template T - 検索する値の型
 */
const findIndexIn2DArray = <T,>(
  array: T[][],
  value: T,
): [number, number] | null => {
  for (let i = 0; i < array.length; i++) {
    for (let j = 0; j < array[i].length; j++) {
      if (array[i][j] === value) {
        return [i, j];
      }
    }
  }
  return null;
};
// [課題345] ADD ED
/**
 * 分岐の場合、分岐パーツを探して、-1のではない場合、分岐内のパーツです。
 * <<呼び出しメソッド>>
 * nodeDelete
 */
const branchNodeIndex = (array: Node[] | Cell[]) => {
  let skipCount = 0;
  const conditionBranchArr = [
    SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD,
    SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD,
    SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD,
    SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD,
    SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD,
    SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD,
    SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD,
    SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD,
    SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD,
    SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD,
  ];
  for (let i = 0; i < array.length; i++) {
    const partsName = array[i].getProp<string>('sopPartsCD');
    const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsName);
    let nodeBranchCount = '0';
    if (
      sameCdFlag &&
      // @ts-expect-error 既にこのリストを定義しました。
      (array[i].getProp('individualPara')!.branchNumSetting ||
        // @ts-expect-error 既にこのリストを定義しました。
        array[i].getProp('individualPara')!.conditionBranch ||
        // @ts-expect-error 既にこのリストを定義しました。
        array[i].getProp('conditionProps')!.conditionBranch ||
        (Object.prototype.hasOwnProperty.call(
          array[i].getProp('individualPara')!,
          'branchMenu1',
        ) &&
          // @ts-expect-error 既にこのリストを定義しました。
          array[i].getProp('individualPara')!.branchMenu1 !== ''))
    ) {
      nodeBranchCount =
        // @ts-expect-error 既にこのリストを定義しました
        array[i].getProp('individualPara')!.branchNumSetting ||
        // @ts-expect-error 既にこのリストを定義しました。
        array[i].getProp('individualPara')!.conditionBranch ||
        // @ts-expect-error 既にこのリストを定義しました。
        array[i].getProp('conditionProps')!.conditionBranch;
    }
    if (
      partsName === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD ||
      (conditionBranchArr.includes(
        partsName as (typeof conditionBranchArr)[number],
      ) &&
        nodeBranchCount === '0')
    ) {
      skipCount += 1;
    }
    if (
      conditionBranchArr.includes(
        partsName as (typeof conditionBranchArr)[number],
      )
    ) {
      if (skipCount === 0) {
        return i;
      }
      skipCount -= 1;
    }
  }
  return -1;
};
/**
 * [キャンセル] ボタン
 */
const SOPAddCancel = () => {
  state.SOPChartDialogVisible = false;
  state.localeEditGraph!.clearCells();
  emit('SOPAddVisible', false);
};
/**
 * OKボタンクリックイベント
 */
const SOPAddSave = async () => {
  try {
    await Promise.all([dataSourceRef.value?.validate()]);
  } catch (e) {
    messageBoxSingleButtonRef.value.title = `${t('Cm.Chr.txtValidationError')}`;
    messageBoxSingleButtonRef.value.content = `${t('Cm.Msg.dialogValidationError')}`;
    openDialog('singleButtonRef');
    return;
  }
  const nodes = state.localeEditGraph!.getNodes();
  // 登録事前チェック
  const nodeModel = checkAllSelectedCellData(nodes);
  const allNodesMatrix = createNodesMatrix(nodes);
  const settingCheck = nodes.filter((node) => {
    const sopPartsCD = node.getProp<string>('sopPartsCD');
    const sameCdFlag = checkPartCode(state.SOPSetData.partCds, sopPartsCD);
    if (!sameCdFlag) {
      return false;
    }
    return !node.getProp('settingConfirmFlg');
  });
  if (settingCheck.length > 0) {
    messageBoxSingleButtonRef.value.title = `${t('SOP.Chr.txtSettingStatusError')}`;
    messageBoxSingleButtonRef.value.content = `${t('SOP.Msg.settingError')}`;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  const notSavePartsCd: string[] = [
    SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
    SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
  ];
  const allNeighborsData = nodeModel.allNeighbors;
  // ブロック中の全てのノードを新しい値をアサインしない
  // const itemIdData = nodeModel.itemIds;
  const flowList: SopFlowDataOption[] = allNeighborsData.map((item) => {
    const itemNode = state.localeEditGraph!.getCellById(item.id);
    const cellPosition = itemNode.getProp<PositionOption>('position');
    const cellSopPartsCD = itemNode.getProp<string>('sopPartsCD');
    const cellIndividualPara = itemNode.getProp('individualPara');
    const cellCommonSetting = itemNode.getProp('commonSetting');
    const cellConditionPara = itemNode.getProp('conditionProps');
    const cellUpperLowerSetting = itemNode.getProp('upperLowerSetting');
    const cellSopCondition = itemNode.getProp('sopCondition');
    const cellInstUnitTxt = itemNode.getProp('instUnitTxt');
    const cellhelpSetting = {
      helpFileType: cellCommonSetting.helpFileType,
      helpBinPath1: cellCommonSetting.helpBinPath1,
      helpBinPath2: cellCommonSetting.helpBinPath2,
      helpBinPath3: cellCommonSetting.helpBinPath3,
      helpBinPath4: cellCommonSetting.helpBinPath4,
      helpBinPath5: cellCommonSetting.helpBinPath5,
      helpTxt1: cellCommonSetting.helpTxt1,
      helpTxt2: cellCommonSetting.helpTxt2,
      helpTxt3: cellCommonSetting.helpTxt3,
    };
    const cellLabel = itemNode.getProp('label');
    const conditionFlag = checkConditionPartCode(
      state.SOPSetData,
      cellSopPartsCD,
    );
    const cellDispNodeId =
      itemNode.getProp('dispNodeId') !== undefined
        ? Number(itemNode.getProp('dispNodeId'))
        : 0;
    const itemNeighbors = getCurrentNeighbors(itemNode);
    // 次ノード情報をセット
    const sopJoinOption: SopJoinDstOption = {
      condBrDst1: '',
      condBrDst2: '',
      condBrDst3: '',
      condBrDst4: '',
      condBrDst5: '',
      condBrDst6: '',
      condBrDst7: '',
      condBrDst8: '',
    };
    itemNeighbors.outgoingList.forEach((outItem, index) => {
      const dstValue = outItem.value;
      switch (index) {
        case 0:
          sopJoinOption.condBrDst1 = dstValue;
          break;
        case 1:
          sopJoinOption.condBrDst2 = dstValue;
          break;
        case 2:
          sopJoinOption.condBrDst3 = dstValue;
          break;
        case 3:
          sopJoinOption.condBrDst4 = dstValue;
          break;
        case 4:
          sopJoinOption.condBrDst5 = dstValue;
          break;
        case 5:
          sopJoinOption.condBrDst6 = dstValue;
          break;
        case 6:
          sopJoinOption.condBrDst7 = dstValue;
          break;
        case 7:
          sopJoinOption.condBrDst8 = dstValue;
          break;
        default:
          break;
      }
    });
    const itemNodeData = {
      position: cellPosition,
      sopPartsCd: cellSopPartsCD,
      individualPara: {
        IndividualSetting: cellIndividualPara,
        ConditionSetting: conditionFlag ? cellConditionPara : '',
      },
      commonSetting: cellCommonSetting,
      helpSetting: cellhelpSetting,
      sopCondition: cellSopCondition,
      label: cellLabel,
      dspSeq: cellDispNodeId,
      upperLowerSetting: cellUpperLowerSetting,
      instUnitTxt: cellInstUnitTxt,
    };
    // [課題345] ADD ST NODEの連結の設計をリファクタリング
    Object.keys(sopJoinOption).forEach((key) => {
      // @ts-expect-error keyを利用して、valueを取得します。
      if (sopJoinOption[key] !== '') {
        // @ts-expect-error keyを利用して、valueを取得します。
        const cell = state.localeEditGraph!.getCellById(sopJoinOption[key]);
        // @ts-expect-error keyを利用して、valueを取得します。
        sopJoinOption[key] = getOutgoing(cell);
      }
    });
    // [課題345] ADD ED
    const nextNodeId =
      itemNeighbors.outgoingList.length !== 0
        ? JSON.stringify(sopJoinOption)
        : '';
    const flowItem = setSOPFlowItemModel(
      item,
      state.chartOptions.blockNo,
      // @ts-expect-error 強制アサイン問題ありません。
      itemNodeData,
      nextNodeId,
      state.SOPSetData,
      0,
      nodes,
    );
    // ブロック中の全てのノードを新しい値をアサインしない
    const flowItemData = JSON.stringify(flowItem);
    // itemIdData.forEach((newIdItem) => {
    //   flowItemData = flowItemData.replace(
    //     new RegExp(newIdItem.itemId, 'g'),
    //     newIdItem.itemV4Id,
    //   );
    // });
    return JSON.parse(flowItemData);
  });
  // [課題345] ADD ST NODEの連結の設計をリファクタリング
  // const addpartArr = [
  //   SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
  //   SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
  // ];
  // // [課題426] MOD ADD ブロック、テンプレートもDBにJson形式で保存しない。
  // const renderData = flowList.filter(
  //   // @ts-expect-error 動的に変数判断
  //   (item) => !addpartArr.includes(item.sopPartsCd),
  // );
  const renderData = flowList;
  const allBranchNodeList: SopFlowDataOption[] = [];
  const numericItemList: SopFlowGetDataOption[] = [];
  const indicesToDelete: number[] = [];
  const DEVIATION_BRANCH_NO = 9;
  renderData.forEach((renderItem, index) => {
    if (renderItem.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD) {
      const currentCell = state.localeEditGraph!.getCellById(
        renderItem.sopNodeNo,
      );
      const coverPredecessors = state.localeEditGraph!.getPredecessors(
        currentCell,
        {
          deep: true,
        },
      );
      const branchNode = coverPredecessors[branchNodeIndex(coverPredecessors)];
      // eslint-disable-next-line no-param-reassign
      renderItem.parentSopNodeNo = branchNode ? branchNode.id : '';
    }
    const branchNodeList = JSON.parse(
      renderItem.sopJoin.nextNodeNo ? renderItem.sopJoin.nextNodeNo : '{}',
    );
    if (
      Object.values(branchNodeList).filter((item) => item !== '').length > 1
    ) {
      Object.values(branchNodeList).forEach(
        (branchNodeItemValue, branchNodeItemIndex) => {
          if (branchNodeItemValue) {
            const renderItemCopy = { ...renderItem };
            renderItemCopy.sopJoin = { ...renderItem.sopJoin };
            // @ts-expect-error 既にこのリストを定義しました。
            renderItemCopy.childNodeFlg = '0';
            const currentNextCell = state.localeEditGraph!.getCellById(
              branchNodeItemValue.toString(),
            );
            // @ts-expect-error 既にこのリストを定義しました。
            renderItemCopy.sopJoin.nextNodeNo = !notSavePartsCd.includes(
              currentNextCell.getProp<string>('sopPartsCD'),
            )
              ? branchNodeItemValue
              : '';
            if (
              renderItemCopy.sopPartsCd ===
              SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD
            ) {
              renderItemCopy.sopJoin.nextCondSeq = branchNodeItemIndex + 1;
            } else {
              renderItemCopy.sopJoin.nextCondSeq =
                renderItemCopy.sopJoin.nextCondSeq === branchNodeItemIndex + 1
                  ? DEVIATION_BRANCH_NO
                  : branchNodeItemIndex + 1;
            }
            allBranchNodeList.push(renderItemCopy);
          }
        },
      );
      indicesToDelete.push(index);
    } else if (
      Object.values(branchNodeList).filter((item) => item !== '').length === 1
    ) {
      const newNextNodeNo = JSON.parse(
        renderItem.sopJoin.nextNodeNo,
      ).condBrDst1;
      const currentCell = state.localeEditGraph!.getCellById(newNextNodeNo);
      // eslint-disable-next-line no-param-reassign
      renderItem.sopJoin.nextNodeNo = !notSavePartsCd.includes(
        currentCell.getProp<string>('sopPartsCD'),
      )
        ? newNextNodeNo
        : '';
      // if (
      //   renderItem.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
      // ) {
      //   const currentCell = state.localeEditGraph!.getCellById(renderItem.sopNodeNo);
      //   const coverPredecessors = state.localeEditGraph!.getPredecessors(currentCell, {
      //     deep: true,
      //   });
      //   const branchNode =
      //     coverPredecessors[branchNodeIndex(coverPredecessors)];
      //   // eslint-disable-next-line no-param-reassign
      //   renderItem.parentSopNodeNo = branchNode ? branchNode.id : '';
      // }
    }
    // @ts-expect-error 既にこのリストを定義しました。
    // eslint-disable-next-line no-param-reassign
    renderItem.childNodeFlg = '0';
    // 「課題399」MOD ED
    if (renderItem.sopPartsCd === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD) {
      // 外部機器通信の数値文字SOP一覧の登録データを設定します。
      const renderItemCopy = { ...renderItem };
      const jsonIndiv = JSON.parse(renderItemCopy.individualPara);
      const individualPara =
        jsonIndiv.IndividualSetting as PartExternalDeviceProps;
      individualPara.numericTextInput?.forEach((numericItem, numericIndex) => {
        const nodeNo = `${renderItemCopy.nodeId}-${numericIndex + 1}`;
        const childNodeNumericItem: NumericTextInputChildNodeProps = {
          itemText: numericItem.itemText,
          instructionValueSetting: numericItem.instructionValueSetting,
          instructionValue: numericItem.instructionValue,
          sourceItemNode: numericItem.sourceItemNode,
          sourceItemTag: numericItem.sourceItemTag,
          sourceItem: numericItem.sourceItemId,
          outputSetting: numericItem.outputSetting,
          outputValue: numericItem.outputValue,
          instructionValueType: numericItem.instructionValueType,
        };
        const individualNumeric = {
          IndividualSetting: childNodeNumericItem,
        };
        renderItemCopy.sopJoin.nextCondSeq = 1; // 外部機器通信で分岐設定されている場合、登録対象とするため1を設定します。
        const numericItemCopy: SopFlowGetDataOption = {
          childNodeFlg: '1',
          nodeId: nodeNo,
          commonSetting: renderItemCopy.commonSetting,
          helpSetting: renderItemCopy.helpSetting,
          individualPara: JSON.stringify(individualNumeric),
          sopJoin: renderItemCopy.sopJoin,
          sopCondition: renderItemCopy.sopCondition,
          parentSopNodeNo: renderItemCopy.sopNodeNo,
          sopCieX: renderItemCopy.sopCieX,
          sopCieY: renderItemCopy.sopCieY,
          sopFlowNo: renderItemCopy.sopFlowNo,
          sopNodeNo: nodeNo,
          sopPartsCd: renderItemCopy.sopPartsCd,
          blkFlowNo: renderItemCopy.blkFlowNo,
          blkSopSeqNo: renderItemCopy.blkSopSeqNo,
          dspSeq: renderItemCopy.dspSeq,
          upperLowerSetting: {
            thJudgeFlg: '1',
            thRangeType: '',
            thValType: numericItem.judgeType,
            thValLlmt:
              numericItem.deviationLowerLimit === ''
                ? null
                : Number(numericItem.deviationLowerLimit),
            thValUlmt:
              numericItem.deviationUpperLimit === ''
                ? null
                : Number(numericItem.deviationUpperLimit),
          },
          instUnitTxt: renderItemCopy.instUnitTxt,
        };
        numericItemList.push(numericItemCopy);
      });
      // jsonからnumericTextInputを削除
      delete individualPara.numericTextInput;
      // jsonからupperLowerLimitCheckを削除
      delete individualPara.upperLowerLimitCheck;
      // eslint-disable-next-line
      renderItem.individualPara = JSON.stringify(jsonIndiv);
    }
  });
  // Indexによって、削除します。
  const mapAllBranchNodeList = {};
  allBranchNodeList.forEach((item) => {
    // @ts-expect-error keyを利用して、valueを取得します
    if (!mapAllBranchNodeList[item.nodeId]) {
      // @ts-expect-error keyを利用して、valueを取得します
      mapAllBranchNodeList[item.nodeId] = [];
    }
    // @ts-expect-error keyを利用して、valueを取得します
    mapAllBranchNodeList[item.nodeId].push(item);
  });

  const resultMap: SopFlowDataOption[] = [];
  renderData.forEach((itemB) => {
    // @ts-expect-error keyを利用して、valueを取得します
    if (mapAllBranchNodeList[itemB.nodeId]) {
      // @ts-expect-error keyを利用して、valueを取得します
      resultMap.push(...mapAllBranchNodeList[itemB.nodeId]);
    } else {
      resultMap.push(itemB);
    }
  });
  const idMap = {};
  let newNodeId = 1;
  const templateNodesIdMapping: TemplateNodesIdMapping[] = [];
  resultMap.forEach((item) => {
    const cellSopCieXYArray = findIndexIn2DArray(allNodesMatrix, item.nodeId);
    let cellSopCieX = 0;
    let cellSopCieY = 0;
    if (cellSopCieXYArray !== null && cellSopCieXYArray.length >= 2) {
      // eslint-disable-next-line prefer-destructuring
      cellSopCieX = cellSopCieXYArray[1];
      // eslint-disable-next-line prefer-destructuring
      cellSopCieY = cellSopCieXYArray[0];
    }
    // eslint-disable-next-line no-param-reassign
    item.sopCieX = cellSopCieX;
    // eslint-disable-next-line no-param-reassign
    item.sopCieY = cellSopCieY;
    const addpartArr = [
      SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
      SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
    ];
    // @ts-expect-error keyを利用して、valueを取得します
    if (!idMap[item.sopNodeNo]) {
      // @ts-expect-error sopPartsCdを取得します
      if (addpartArr.includes(item.sopPartsCd)) {
        // @ts-expect-error keyを利用して、valueを取得します
        idMap[item.sopNodeNo] = uuidv4();
      } else {
        const id = newNodeId.toString().padStart(4, '0');
        // @ts-expect-error keyを利用して、valueを取得します
        idMap[item.sopNodeNo] = id;
        const itemV4Id = item.sopNodeNo;
        const itemId = id;
        templateNodesIdMapping.push({ itemV4Id, itemId });
        newNodeId++;
      }
    }
  });
  // @ts-expect-error これらの属性は考慮しなくて結構です
  setTemplateNewSOPFlowItemValue(templateNodesIdMapping, resultMap);
  const updatedArray = resultMap.map((item) => {
    const updatedItem = { ...item };
    if (item.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD) {
      // @ts-expect-error keyを利用して、valueを取得します
      updatedItem.parentSopNodeNo = idMap[item.parentSopNodeNo];
    }
    // @ts-expect-error keyを利用して、valueを取得します
    updatedItem.sopNodeNo = idMap[item.sopNodeNo];
    // @ts-expect-error keyを利用して、valueを取得します
    updatedItem.nodeId = idMap[item.sopNodeNo];
    updatedItem.sopJoin.nextNodeNo = item.sopJoin.nextNodeNo
      ? // @ts-expect-error keyを利用して、valueを取得します
        idMap[item.sopJoin.nextNodeNo]
      : '';
    return updatedItem;
  });
  numericItemList.forEach((numericItemValue) => {
    const updatedNumericItem = { ...numericItemValue };
    // @ts-expect-error keyを利用して、valueを取得します
    if (idMap[numericItemValue.parentSopNodeNo]) {
      const { sopNodeNo } = numericItemValue;
      const match = sopNodeNo.match(/^(\d{4})-(\d+)$/);
      if (match) {
        // @ts-expect-error keyを利用して、valueを取得します
        const newPrefix = idMap[numericItemValue.parentSopNodeNo];
        updatedNumericItem.sopNodeNo = `${newPrefix}-${match[2]}`;
        updatedNumericItem.nodeId = `${newPrefix}-${match[2]}`;
      }
      updatedNumericItem.parentSopNodeNo =
        // @ts-expect-error keyを利用して、valueを取得します
        idMap[numericItemValue.parentSopNodeNo];
    }
    if (
      numericItemValue.sopJoin?.nextNodeNo &&
      // @ts-expect-error keyを利用して、valueを取得します
      idMap[numericItemValue.sopJoin.nextNodeNo]
    ) {
      updatedNumericItem.sopJoin.nextNodeNo =
        // @ts-expect-error keyを利用して、valueを取得します
        idMap[numericItemValue.sopJoin.nextNodeNo];
    }
    updatedArray.push(updatedNumericItem);
  });
  // [課題426] MOD ED
  insertSOPChart(updatedArray);
  // [課題345] ADD ED
};
defineExpose({
  validateConditionForm: () => dataSourceRef.value?.validate(),
});
</script>
<style lang="scss" scoped>
.common-row-padding-top {
  padding-top: 20px;
}
.dialog-footer {
  height: 20px;
  margin-bottom: 10px;
}
.dialog-footer-left {
  float: left;
}
.dialog-footer-right {
  float: right;
}
.dialog-title {
  width: 100%;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}
.dialog-title-main {
  width: 100%;
  height: 100%;
  text-align: center;
}
.dialog-title-left {
  float: left;
  width: calc(40% - 20px);
  height: 40px;
  line-height: 40px;
  margin-left: 20px;
  text-align: left;
}
.dialog-title-right {
  float: left;
  width: calc(60% - 20px);
  height: 40px;
  line-height: 40px;
  margin-left: 20px;
  text-align: left;
  font-size: 18px;
}
.box-card-nosetting {
  position: relative;
  width: 100%;
  height: 100%;
}
.box-card-model {
  position: relative;
  width: calc(100% - 350px);
  float: left;
  height: 100%;
}
.box-card-chart {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
