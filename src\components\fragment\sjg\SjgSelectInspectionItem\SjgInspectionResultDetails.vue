<template>
  <!-- 照査結果詳細ダイアログ -->
  <!-- 見出し 照査結果詳細 -->
  <DialogWindow
    :title="$t('Sjg.Chr.txtSjgInspectionResultsDetails')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    :class="'odr-regist-rebatch-instruction'"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    width="1400"
  >
    <div>
      <!-- 照査結果詳細の見出し+テキスト項目表示 -->
      <BaseHeading
        level="2"
        :text="$t('Sjg.Chr.txtInfoItem')"
        fontSize="24px"
      />
      <InfoShow
        class="Util_mt-16"
        :infoShowItems="processDataInfoShowRef.infoShowItems"
        :isLabelVertical="processDataInfoShowRef.isLabelVertical"
        fontSizeLabel="12px"
        fontSizeContent="16px"
      />
    </div>
    <!-- 照査結果一覧 -->
    <BaseHeading
      level="2"
      :text="$t('Sjg.Chr.btnInspectionResults')"
      fontSize="24px"
      class="Util_mt-48"
    />
    <!--  照査結果一覧 テーブル -->
    <TabulatorTable :propsData="tablePropsDataVerifyResultListRef" />

    <!-- 見出し GMP確認結果詳細 -->
    <BaseHeading
      level="2"
      :text="$t('Sjg.Chr.txtGMPInformationResults')"
      fontSize="24px"
      class="Util_mt-24"
    />
    <!-- GMP確認結果詳細テーブル -->
    <TabulatorTable :propsData="tablePropsDataGMPInfoListRef" />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { useGetVerifyResult } from '@/hooks/useApi';
import {
  SelectInspectionItemData,
  VerifyRsltList,
} from '@/types/HookUseApi/SjgTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import {
  tablePropsDataVerifyResultList,
  tablePropsDataGMPInfoList,
  getProcessDataInfoShowItems,
} from './sjgInspectionResultDetails';

// [W181310]照査結果詳細
/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey = 'fragmentDialogVisible' | 'messageBoxApiErrorVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

const processDataInfoShowRef = ref<InfoShowType>({
  infoShowItems: getProcessDataInfoShowItems(),
  isLabelVertical: true,
});

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  privilegesBtnRequestData: CommonRequestType;
  selectedInspectionItemData: SelectInspectionItemData | null;
};

const props = defineProps<Props>();

// 照査結果一覧テーブル設定
const tablePropsDataVerifyResultListRef = ref<TabulatorTableIF>({
  ...tablePropsDataVerifyResultList,
});
// GMP確認結果詳細テーブル設定
const tablePropsDataGMPInfoListRef = ref<TabulatorTableIF>({
  ...tablePropsDataGMPInfoList,
});

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
];

// 初期処理で呼び出される
// 照査結果詳細取得APIリクエストとレスポンス情報を格納
const requestApiGetVerifyResult = async () => {
  // 照査結果詳細取得のAPIを行う。
  const { responseRef, errorRef } = await useGetVerifyResult({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
    addBomFlg: '1',
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return false; // エラーの場合継続処理させない
  }
  if (responseRef.value) {
    // ダイアログ表示初期値として、レスポンス情報を格納
    const initResponseData = responseRef.value.data.rData;

    // テーブル設定
    tablePropsDataVerifyResultListRef.value.tableData =
      initResponseData.verifyRsltList;

    const newVerifyRsltList = initResponseData.verifyRsltList.map(
      (item: VerifyRsltList) => {
        const rectItem = { ...item };
        if (rectItem.verifyOdrRsltNm === '') {
          rectItem.verifyOdrRsltNm = '-';
        }
        return rectItem;
      },
    );
    // 照査結果一覧
    tablePropsDataVerifyResultListRef.value.tableData = newVerifyRsltList;
    // GMP確認結果詳細一覧
    tablePropsDataGMPInfoListRef.value.tableData = initResponseData.gmpInfoList;
  }
  return true;
};

/**
 * 照査結果詳細ダイアログの初期設定
 */
const sjgInspectionResultsDetailsInit = async () => {
  if (!props.selectedInspectionItemData) return;
  showLoading();
  // 照査結果詳細情報レイアウト用初期値設定
  processDataInfoShowRef.value.infoShowItems = getProcessDataInfoShowItems();
  Object.entries(props.selectedInspectionItemData).forEach(([key, value]) => {
    if (key in processDataInfoShowRef.value.infoShowItems) {
      processDataInfoShowRef.value.infoShowItems[key].infoShowModelValue =
        value?.toString() ?? '';
    }
  });
  // 照査結果詳細取得APIリクエストとレスポンス情報を格納
  const result = await requestApiGetVerifyResult();
  if (!result) {
    return;
  }
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, sjgInspectionResultsDetailsInit);
</script>
<style lang="scss" scoped></style>
