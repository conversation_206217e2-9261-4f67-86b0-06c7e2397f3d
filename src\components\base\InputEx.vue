<template>
  <el-tooltip
    :effect="CONST.TOOLTIP.EFFECT"
    :visible="visibleRef"
    :content="vBindProps.modelValue"
    :popperOptions="popperOption"
    :placement="CONST.TOOLTIP.PLACEMENT"
    popperClass="tooltip-popper"
  >
    <el-input
      :class="inputClass"
      v-bind="vBindProps"
      :placeholder="placeholder"
      @input="handleInputChange"
      @change="(v: string) => emit('change', v)"
      @blur="(v: FocusEvent) => emit('blur', v)"
      @clear="() => emit('update:modelValue', '')"
      @focus="handleTooltipVisible(true)"
      @focusout="handleTooltipVisible(false)"
      @mouseenter="handleTooltipVisible(true)"
      @mouseleave="handleTooltipVisible(false)"
    />
  </el-tooltip>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { InputExProps } from '@/types/InputExTypes';
import { inputEmits } from 'element-plus';
import SCSS from '@/constants/scssVariables';
import CONST from '@/constants/utils';
import popperOption from '@/utils/tooltip';
import excludeProps from '@/utils/excludeProps';

const props = withDefaults(defineProps<InputExProps>(), {
  size: 'small',
  width: SCSS.widthSmall,
  clearable: true,
  validateEvent: true,
  disabled: false,
});

const visibleRef = ref<boolean>(false);
const vBindProps = computed(() => excludeProps(props, ['size', 'width']));
const inputClass = computed(() => `input-ex_${props.size}`);
const handleTooltipVisible = (flag: boolean) => {
  if (props.isTooltipDisabled) return;
  visibleRef.value = String(vBindProps.value.modelValue) === '' ? false : flag;
};

const placeholder = computed(() => (props.disabled ? '' : props.placeholder));

const emit = defineEmits(inputEmits);

const handleInputChange = (v: string) => {
  emit('update:modelValue', v);
  if (!props.isTooltipDisabled) {
    visibleRef.value = !!v && v !== '';
  }
};
</script>
<style lang="scss" scoped>
$namespace: 'input-ex';
$widthVal: v-bind('props.width');

@mixin setSizeMixin($width, $height) {
  width: $width !important;
  height: $height !important;
}
.#{$namespace} {
  &_custom {
    @include setSizeMixin($widthVal, $height);
  }
  &_large {
    @include setSizeMixin($widthLarge, $height);
  }
  &_middle {
    @include setSizeMixin($widthMiddle, $height);
  }
  &_small {
    @include setSizeMixin($widthSmall, $height);
  }
}
</style>
