import END_POINT from '@/constants/endPoint';
import {
  GetConfirmWeighingRecordModifyInfoRequestData,
  GetConfirmConfirmWeighingRecordModifyInfoRes,
} from '@/types/HookUseApi/PrdTypes';
import {
  ExtendCommonRequestType,
  ExtendCommonRequestWithMainApiFlagType,
} from '@/types/HookUseApi/CommonTypes';
import { useApi } from './util';

// 製造記録確認_秤量記録修正履歴一覧
const useGetConfirmWeighingRecordModifyInfo = (
  data: ExtendCommonRequestType<GetConfirmWeighingRecordModifyInfoRequestData>,
) =>
  useApi<
    ExtendCommonRequestWithMainApiFlagType<GetConfirmWeighingRecordModifyInfoRequestData>,
    GetConfirmConfirmWeighingRecordModifyInfoRes
  >(END_POINT.GET_CONF_WGT_REC_MOD_INFO, 'post', { ...data, mainApiFlg: 0 });
export default useGetConfirmWeighingRecordModifyInfo;
