<template>
  <!-- SOP作業詳細ダイアログ -->
  <!-- 見出し SOP作業詳細 -->
  <DialogWindow
    :title="$t('Sjg.Chr.txtSjgSOPOperationDetails')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <div class="custom-form-container">
      <InfoShow
        :infoShowItems="sjgSOPOperationDetailsInfoShowRef.infoShowItems"
        :isLabelVertical="sjgSOPOperationDetailsInfoShowRef.isLabelVertical"
        useWhiteSpace="pre-line"
      />
      <CustomForm
        class="Util_mt-16"
        :formModel="attBinNoFormRef.formModel"
        :formItems="attBinNoFormRef.formItems"
        @visible="
          (v: CustomFormType['customForm']) => {
            attBinNoFormRef.customForm = v;
          }
        "
      />
    </div>
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import CustomForm from '@/components/parts/CustomForm.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { SopRecListData } from '@/types/HookUseApi/SjgTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { CustomFormType } from '@/types/CustomFormTypes';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';

import {
  getSjgSOPOperationDetailsInfoShowItems,
  attBinNoFormModel,
  getAttBinNoFormItems,
} from './sjgSOPOperationDetails';

// [W181310]SOP作業詳細
/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey = 'fragmentDialogVisible' | 'messageBoxApiErrorVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

const sjgSOPOperationDetailsInfoShowRef = ref<InfoShowType>({
  infoShowItems: getSjgSOPOperationDetailsInfoShowItems(),
  isLabelVertical: true,
});

const attBinNoFormRef = ref<CustomFormType>({
  formItems: getAttBinNoFormItems(),
  formModel: attBinNoFormModel,
});

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  privilegesBtnRequestData: CommonRequestType;
  detailLogList: SopRecListData | null;
};

const props = defineProps<Props>();

const FILE_SOP_ATT_BIN = {
  MODEL_KEY: 'sopAttBinList',
  NAME_KEY: 'attBinFileNm',
  UNIQUE_KEY: 'attBinNo',
} as const;

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
];

/**
 * SOP作業詳細ダイアログの初期設定
 */
const sjgSOPOperationDetailsInit = async () => {
  if (!props.detailLogList) return;
  showLoading();
  Object.entries(props.detailLogList).forEach(([key, value]) => {
    if (key in sjgSOPOperationDetailsInfoShowRef.value.infoShowItems) {
      sjgSOPOperationDetailsInfoShowRef.value.infoShowItems[
        key
      ].infoShowModelValue = value?.toString().replace(/<br>/g, '\n') ?? '';
    }
  });
  const sopAttBinData = {
    [FILE_SOP_ATT_BIN.MODEL_KEY]: [
      {
        attBinFileNm: props.detailLogList.attBinFileNm,
        attBinNo: props.detailLogList.attBinNo,
      },
    ],
  };
  setFormModelValueFromApiResponse(attBinNoFormRef, sopAttBinData, {
    fileKeys: [
      {
        formModelKey: FILE_SOP_ATT_BIN.MODEL_KEY,
        fileNameKey: FILE_SOP_ATT_BIN.NAME_KEY,
        fileKeyPropName: FILE_SOP_ATT_BIN.UNIQUE_KEY,
      },
    ],
    commonRequestData: props.privilegesBtnRequestData,
  });
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, sjgSOPOperationDetailsInit);
</script>
<style lang="scss" scoped>
.custom-form-container {
  height: 673px;
  overflow-y: auto;
}
</style>
