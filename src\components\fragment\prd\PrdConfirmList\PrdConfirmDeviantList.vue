<template>
  <!-- 製造記録確認 異状履歴一覧ダイアログ -->
  <DialogWindow
    :title="$t('Prd.Chr.txtConfirmDeviantList')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 見出し 異状履歴一覧 -->
    <BaseHeading
      level="2"
      :text="$t('Prd.Chr.txtDeviationHistoryList')"
      fontSize="24px"
    />
    <!-- 異状履歴一覧テーブル -->
    <TabulatorTable :propsData="tablePropsDataConfDevListRef" />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  TabulatorTableIF,
  CustomOptionsData,
} from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { useGetConfirmDeviantInfo } from '@/hooks/useApi';
import { GetConfirmDeviantInfoRequestData } from '@/types/HookUseApi/PrdTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey = 'fragmentDialogVisible' | 'messageBoxApiErrorVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  odrNo?: string; // 親ダイアログのodrNo
  prcSeq?: number; // 親ダイアログのprcSeq
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

// 異状履歴一覧用テーブル設定
const tablePropsDataConfDevListRef = ref<TabulatorTableIF>({
  pageName: 'PrdConfirmDeviantList',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'edDts',

  column: [
    // 異状レベル
    {
      title: 'Prd.Chr.txtDeviationLevel',
      field: 'devCorrLv',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.DEV_LV,
    },
    // 処方
    {
      title: 'Prd.Chr.txtPrescriptionName',
      field: 'rxNmJp',
      width: COLUMN_WIDTHS.RX_NM,
    },
    // SOPフロー名
    {
      title: 'Prd.Chr.txtSopFlowName',
      field: 'sopFlowNmJp',
      width: COLUMN_WIDTHS.SOP_FLOW_NM,
    },
    // 作業指示内容
    {
      title: 'Prd.Chr.txtWorkInstructionDetail',
      field: 'cmtMain',
      width: COLUMN_WIDTHS.PRD.CMT_MAIN,
    },
    // 指示値
    {
      title: 'Prd.Chr.txtInstructionValue',
      field: 'instVal',
      width: COLUMN_WIDTHS.INST_VAL,
    },
    // 単位
    {
      title: 'Prd.Chr.txtUnit',
      field: 'unitNmJp',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    // 記録値
    {
      title: 'Prd.Chr.txtRecordValue',
      field: 'recVal1',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 判定種別
    {
      title: 'Prd.Chr.txtJudgementType',
      field: 'thValType',
      width: COLUMN_WIDTHS.PRD.VAL_TYPE,
    },
    // 異状値下限
    {
      title: 'Prd.Chr.txtDeviationLowerLimit',
      field: 'thValLlmt',
      hozAlign: 'right',
      formatter: 'number',
      width: COLUMN_WIDTHS.PRD.VAL_LMT,
    },
    // 異状値上限
    {
      title: 'Prd.Chr.txtDeviationUpperLimit',
      field: 'thValUlmt',
      hozAlign: 'right',
      formatter: 'number',
      width: COLUMN_WIDTHS.PRD.VAL_LMT,
    },
    // 異状発生日時
    {
      title: 'Prd.Chr.txtDeviationDate',
      field: 'edDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // 確認者
    {
      title: 'Prd.Chr.txtConfirmUser',
      field: 'modUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 異状コメント
    {
      title: 'Prd.Chr.txtDeviationComment',
      field: 'modExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 確認日時
    {
      title: 'Prd.Chr.txtConfirmDate',
      field: 'cmtDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // 背景色指定隠しカラムとして運用
    {
      title: '',
      field: 'backgroundColor',
      hidden: true,
    },
  ],
  rowColor: {
    useColor: true,
    colorColumn: 'backgroundColor',
  },
  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
  textWrapColumns: ['cmtMain'],
  rowHeight: 60,
});

/**
 * 作業指示コメント結合
 */
const joinCmtMain = (tableData: CustomOptionsData[]) => {
  tableData.forEach((res) => {
    // 作業指示コメント結合
    const comments = [res.cmtMain1, res.cmtMain2, res.cmtMain3].filter(Boolean);
    res.cmtMain = comments.join('<br>');
  });
};

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
];

/**
 * 異状履歴ダイアログの初期設定
 */
const PrdConfirmDeviantListInit = async () => {
  if (props.odrNo === undefined || props.prcSeq === undefined) {
    console.error('odrNoまたはprcSeqが未定義です。');
    return;
  }

  const requestData: GetConfirmDeviantInfoRequestData = {
    odrNo: props.odrNo,
    prcSeq: props.prcSeq,
  };

  showLoading();
  const { responseRef, errorRef } = await useGetConfirmDeviantInfo({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });

  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // ダイアログ表示初期値として、レスポンス情報を格納
    tablePropsDataConfDevListRef.value.tableData =
      responseRef.value.data.rData.devLogList;
    joinCmtMain(tablePropsDataConfDevListRef.value.tableData);
  }

  closeLoading();
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

// ダイアログ起動条件の監視。isClickedの真偽値が切り替わりを監視
watch(() => props.isClicked, PrdConfirmDeviantListInit);
</script>
