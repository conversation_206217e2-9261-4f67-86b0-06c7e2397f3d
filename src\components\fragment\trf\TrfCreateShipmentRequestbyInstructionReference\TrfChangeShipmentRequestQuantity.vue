<template>
  <!-- 出庫依頼量変更ダイアログ -->
  <DialogWindow
    :title="$t('Trf.Chr.txtChangeTransferShipmentRequestQuantity')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="async () => resolveClickHandler()"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    @visible="updateDialogChangeFlagRef"
  >
    <CustomForm
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="shipmentRequestQuantityFormRef.formModel"
      :formItems="shipmentRequestQuantityFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          shipmentRequestQuantityFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
    <BaseHeading
      level="2"
      fontSize="24px"
      class="Util_mt-16"
      :text="$t('Trf.Chr.txtZoneEachInvQty')"
    />
    <p>{{ $t('Trf.Chr.txtZoneEachInvQtyExplain') }}</p>
    <TabulatorTable
      :propsData="tablePropsDialogRef"
      @selectRow="
        (v: TabulatorTableIF | null) => updateDialogChangeFlagRef(v !== null)
      "
    />
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import onValidateHandler, {
  handleValidationBySingleRowSelect,
} from '@/utils/validateHandler';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  ComboBoxDataStandardReturnData,
  CommonRequestType,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import {
  useGetTransferZoneInventoryList,
  useGetComboBoxDataStandard,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import {
  ComponentListData,
  GetTransferZoneInventoryListReq,
} from '@/types/HookUseApi/TrfTypes';
import BaseHeading from '@/components/base/BaseHeading.vue';
import {
  getShipmentRequestQuantityFormRefFormItems,
  shipmentRequestQuantityFormRefFormModel,
  tablePropsData,
} from './trfChangeShipmentRequestQuantity';

const shipmentRequestQuantityFormRef = ref<CustomFormType>({
  formItems: getShipmentRequestQuantityFormRefFormItems(),
  formModel: shipmentRequestQuantityFormRefFormModel,
});

type Props = {
  isClicked: boolean;
  commonActionRequestData: CommonRequestType;
  selectedRowsData: ComponentListData[];
  srcZoneGrpNo: string;
};
const props = defineProps<Props>();
const emit = defineEmits(['submit']);
const customFormRenderingTriggerRef = ref(false);

type DialogRefKey =
  | 'singleButton'
  | 'inventoryMoveLotConfirm'
  | 'inventoryMoveLotInfo'
  | 'fragmentDialogVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  inventoryMoveLotConfirm: false,
  inventoryMoveLotInfo: false,
  fragmentDialogVisible: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);
const tablePropsDialogRef = ref<TabulatorTableIF>({
  ...tablePropsData,
});

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;

/**
 * 実行 押下時処理
 */
const resolveClickHandler = async () => {
  // 下記チェックを行い、チェックOKなら処理継続する。
  // 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    shipmentRequestQuantityFormRef.value.customForm !== undefined &&
    (await shipmentRequestQuantityFormRef.value.customForm.validate(
      (isValid) => {
        onValidateHandler(isValid);
      },
    ));

  if (!validate) {
    return false;
  }

  closeDialog('fragmentDialogVisible');
  emit('submit', {
    matNo: shipmentRequestQuantityFormRef.value.formModel.matNo,
    lotNo: props.selectedRowsData[0].lotNo,
    edNo: props.selectedRowsData[0].edNo,
    trfQty: shipmentRequestQuantityFormRef.value.formModel.trfQty,
    planExpl: shipmentRequestQuantityFormRef.value.formModel.planExpl,
  });
  return false;
};

/**
 * 初期設定
 */
const trfChangeShipmentRequestQuantityInit = async () => {
  // 単一選択されていなかった場合はエラー表示とする
  try {
    await handleValidationBySingleRowSelect(props.selectedRowsData);
  } catch (error) {
    return;
  }

  // NOTE:単一チェック済みなので確実に単一行。先頭取得する
  const selectedRowData = props.selectedRowsData.at(0)!;
  showLoading();
  updateDialogChangeFlagRef(false);
  shipmentRequestQuantityFormRef.value.formItems =
    getShipmentRequestQuantityFormRefFormItems();
  shipmentRequestQuantityFormRef.value.formItems.matNo.formModelValue =
    selectedRowData.matNo;
  shipmentRequestQuantityFormRef.value.formItems.matNm.formModelValue =
    selectedRowData.matNm;
  shipmentRequestQuantityFormRef.value.formItems.unitNm.formModelValue =
    selectedRowData.unitNm;
  shipmentRequestQuantityFormRef.value.formItems.planExpl.formModelValue =
    selectedRowData.planExpl ?? '';
  if (selectedRowData.trfQty !== null)
    shipmentRequestQuantityFormRef.value.formItems.trfQty.formModelValue =
      selectedRowData.trfQty.toString();
  const apiRequestData: ExtendCommonRequestType<GetTransferZoneInventoryListReq> =
    {
      ...props.commonActionRequestData,
      srcZoneGrpNo: props.srcZoneGrpNo,
      matNo: selectedRowData.matNo,
      lotSid: selectedRowData.lotSid,
      edNo: selectedRowData.edNo,
    };
  // 2．ゾーン毎在庫量データ取得
  const { responseRef, errorRef } =
    await useGetTransferZoneInventoryList(apiRequestData);
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    openDialog('singleButton');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    tablePropsDialogRef.value.tableData =
      responseRef.value.data.rData.srcZoneList;
  }

  // 3．コメントマスタ取得
  // 標準コンボボックスデータ取得
  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.commonActionRequestData,
    condList: [
      {
        cmbId: 'cmtTrfPlan',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'TRF_PLANDTL_MOD' },
      },
    ],
  });

  if (
    comboBoxDataStandardReturnData &&
    comboBoxDataStandardReturnData.rData.rList.length > 0
  ) {
    setCustomFormComboBoxOptionList(
      shipmentRequestQuantityFormRef.value.formItems,
      comboBoxDataStandardReturnData!.rData.rList,
    );
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, trfChangeShipmentRequestQuantityInit);
</script>
