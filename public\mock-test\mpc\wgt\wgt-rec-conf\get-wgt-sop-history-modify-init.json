{"rDts": "2024/11/21 14:58:18", "rCode": 200, "rTitle": "秤量前後SOP修正履歴初期表示", "rMsg": "秤量前後SOP修正履歴初期表示メッセージ", "rData": {"modHistoryList": [{"cmtTimes": 1, "sopFlowNmJp": "造粒-本作業 1", "sopNodeTimes": 1, "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "部屋の湿度を入力してください", "cmtMain3": "部屋の湿度を入力してください", "unitNmJp": "mg", "bRecVal1": "24", "aRecVal1": "26", "bRecVal2": "24", "aRecVal2": "26", "bRecVal3": "24", "aRecVal3": "26", "bRecVal4": "24", "aRecVal4": "26", "bRecVal5": "24", "aRecVal5": "26", "cmtDts": "2023/10/03 10:11:53", "modUsr": "松下 次郎", "modExpl": "温度が高いため再検査"}, {"cmtTimes": 1, "sopFlowNmJp": "造粒-本作業 1", "sopNodeTimes": 1, "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "", "cmtMain3": "", "unitNmJp": "mg", "bRecVal1": "24", "aRecVal1": "26", "bRecVal2": "24", "aRecVal2": "26", "bRecVal3": "24", "aRecVal3": "26", "bRecVal4": "24", "aRecVal4": "26", "bRecVal5": "24", "aRecVal5": "26", "cmtDts": "2023/10/03 10:11:53", "modUsr": "松下 次郎", "modExpl": "温度が高いため再検査"}, {"cmtTimes": 1, "sopFlowNmJp": "造粒-本作業 1", "sopNodeTimes": 1, "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "", "cmtMain3": "", "unitNmJp": "mg", "bRecVal1": "24", "aRecVal1": "26", "bRecVal2": "24", "aRecVal2": "26", "bRecVal3": "24", "aRecVal3": "26", "bRecVal4": "24", "aRecVal4": "26", "bRecVal5": "24", "aRecVal5": "26", "cmtDts": "2023/10/03 10:11:53", "modUsr": "松下 次郎", "modExpl": "温度が高いため再検査"}, {"cmtTimes": 1, "sopFlowNmJp": "造粒-本作業 1", "sopNodeTimes": 1, "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "", "cmtMain3": "", "unitNmJp": "mg", "bRecVal1": "24", "aRecVal1": "26", "bRecVal2": "24", "aRecVal2": "26", "bRecVal3": "24", "aRecVal3": "26", "bRecVal4": "24", "aRecVal4": "26", "bRecVal5": "24", "aRecVal5": "26", "cmtDts": "2023/10/03 10:11:53", "modUsr": "松下 次郎", "modExpl": "温度が高いため再検査"}, {"cmtTimes": 1, "sopFlowNmJp": "造粒-本作業 1", "sopNodeTimes": 1, "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "", "cmtMain3": "", "unitNmJp": "mg", "bRecVal1": "24", "aRecVal1": "26", "bRecVal2": "24", "aRecVal2": "26", "bRecVal3": "24", "aRecVal3": "26", "bRecVal4": "24", "aRecVal4": "26", "bRecVal5": "24", "aRecVal5": "26", "cmtDts": "2023/10/03 10:11:53", "modUsr": "松下 次郎", "modExpl": "温度が高いため再検査"}, {"cmtTimes": 1, "sopFlowNmJp": "造粒-本作業 1", "sopNodeTimes": 1, "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "", "cmtMain3": "", "unitNmJp": "mg", "bRecVal1": "24", "aRecVal1": "26", "bRecVal2": "24", "aRecVal2": "26", "bRecVal3": "24", "aRecVal3": "26", "bRecVal4": "24", "aRecVal4": "26", "bRecVal5": "24", "aRecVal5": "26", "cmtDts": "2023/10/03 10:11:53", "modUsr": "松下 次郎", "modExpl": "温度が高いため再検査"}]}}