<template>
  <svg class="svg-icon">
    <use :href="hrefVal"></use>
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { IconTypes } from '@/types/IconTypes';

type Props = {
  iconName: IconTypes;
  width?: string;
  height?: string;
};
const props = withDefaults(defineProps<Props>(), {
  width: '24px',
  height: '24px',
});

const hrefVal = computed(() => `#icon-${props.iconName}`);
</script>

<style lang="scss" scoped>
.svg-icon {
  width: v-bind('props.width');
  height: v-bind('props.height');
  min-width: v-bind('props.width');
  min-height: v-bind('props.height');
  vertical-align: -2.4px;
  fill: currentColor;
  overflow: hidden;
}
.svg-use-icon {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  position: relative;
}
.svg-external-icon {
  background-color: currentColor;
  mask-size: cover !important;
  display: inline-block;
}
</style>
