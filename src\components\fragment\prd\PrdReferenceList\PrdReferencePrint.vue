<template>
  <!-- 製造記録参照_印刷設定ダイアログ -->
  <!-- 見出し 記録書再出力 -->
  <DialogWindow
    :title="$t('Prd.Chr.txtRecordRePrint')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 見出し 出力する製造記録帳票を選んでください -->
    <BaseHeading
      level="2"
      :text="$t('Prd.Chr.txtSelectGuide')"
      fontSize="24px"
      class="title-box"
    />

    <!-- 製造記録のチェックボックス -->
    <el-row>
      <el-col :span="24">
        <CheckboxEx
          v-if="props.privilegesBtnRequestData"
          class="Util_mt-16"
          :modelValue="checkboxProductModelValueRef"
          :disabled="false"
          :isHorizontal="false"
          :optionsData="{
            value: printProductCheckBox.value,
            label: printProductCheckBox.label,
          }"
          :size="'small'"
          @update:modelValue="updateProductCheckbox"
        />
      </el-col>
      <el-col :span="24">
        <!-- 秤量記録のチェックボックス -->
        <CheckboxEx
          v-if="props.privilegesBtnRequestData"
          class="Util_mt-16"
          :modelValue="checkboxWeightModelValueRef"
          :disabled="disabledWeighingButtonRef"
          :isHorizontal="false"
          :optionsData="{
            value: printWeightCheckBox.value,
            label: printWeightCheckBox.label,
          }"
          :size="'small'"
          @update:modelValue="updateWeightCheckbox"
        />
      </el-col>
    </el-row>
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- チェックボックスが選択されていないメッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.messageNoBoxCheckBoxVisible"
    :dialogProps="msgBoxNoBoxCheckBoxPropsRef"
    :submitCallback="() => closeDialog('messageNoBoxCheckBoxVisible')"
  />
  <!-- 製造記録未承認エラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxProductInstructionErrorUnapprovedVisible"
    :dialogProps="messageBoxProductInstructionErrorUnapprovedPropsRef"
    :submitCallback="
      () => closeDialog('messageBoxProductInstructionErrorUnapprovedVisible')
    "
  />
  <!-- 記録書再出力押下時の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxProductInstructionConfirmVisible"
    :dialogProps="messageBoxProductInstructionConfirmPropsRef"
    :cancelCallback="
      () => closeDialog('messageBoxProductInstructionConfirmVisible')
    "
    :submitCallback="requestApiDownLoadPrint"
  />
  <!-- 記録書再出力APIの完了表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxProductInstructionFinishedVisible"
    :dialogProps="messageBoxProductInstructionFinishedPropsRef"
    :submitCallback="
      () => closeDialog('messageBoxProductInstructionFinishedVisible')
    "
  />
</template>
<script setup lang="ts">
import BaseHeading from '@/components/base/BaseHeading.vue';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { useGetReferencePrint } from '@/hooks/useApi';
import useFileDownload from '@/hooks/useApi/fileDownLoad';
import {
  GetReferencePrintRequestData,
  GetReferencePrintData,
} from '@/types/HookUseApi/PrdTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import CheckboxEx from '@/components/base/CheckboxEx.vue';
import {
  PRINT_ID,
  printProductCheckBox,
  printWeightCheckBox,
} from './prdReferencePrint';

/**
 * 多言語
 */
const { t } = useI18n();

// 印刷設定_製造記録参照帳票出力のレスポンスデータ
let initResponseData: GetReferencePrintData = {
  recPdfFile: '',
  wgtPdfFile: '',
};

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageNoBoxCheckBoxVisible'
  | 'messageBoxProductInstructionErrorUnapprovedVisible'
  | 'messageBoxProductInstructionConfirmVisible'
  | 'messageBoxProductInstructionFinishedVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageNoBoxCheckBoxVisible: false,
  messageBoxProductInstructionErrorUnapprovedVisible: false,
  messageBoxProductInstructionConfirmVisible: false,
  messageBoxProductInstructionFinishedVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

const msgBoxNoBoxCheckBoxPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleNotSelectError'),
  content: t('Prd.Msg.contentNotSelectError'),
  isSingleBtn: true,
  type: 'error',
});

// NOTE:共通API使用のため特例でFEでチェックし、用意したエラーメッセージを出す
// 製造記録未承認エラー表示
const messageBoxProductInstructionErrorUnapprovedPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleProductInstructionErrorUnapproved'),
  content: t('Prd.Msg.contentProductInstructionErrorUnapproved'),
  isSingleBtn: true,
  type: 'error',
});

// 記録書再出力押下時の確認メッセージボックス
const messageBoxProductInstructionConfirmPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleProductInstructionConfirm'),
  content: t('Prd.Msg.contentProductInstructionConfirm'),
  type: 'question',
});

// NOTE:共通API使用のため特例でFEで用意したエラーメッセージを出す
// 記録書再出力APIの完了メッセージボックス
const messageBoxProductInstructionFinishedPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleProductInstructionFinished'),
  content: t('Prd.Msg.contentProductInstructionFinished'),
  isSingleBtn: true,
  type: 'info',
});

// チェックボックス値
// NOTE:初期値を入れておくと、デフォルトで選択状態になる
// NOTE:optionsData.valueと同じ値を設定する。
const checkboxProductModelValueRef = ref<string[]>([PRINT_ID.CHECKED]);
const checkboxWeightModelValueRef = ref<string[]>([PRINT_ID.CHECKED]);

// 秤量記録書チェックボックス活性・非活性切り替え
const disabledWeighingButtonRef = ref<boolean>(true);

// チェックボックス更新処理
// NOTE:この関数用意しないとチェックボックスが無反応になる
const updateProductCheckbox = (val: string[]) => {
  checkboxProductModelValueRef.value = val;
};
const updateWeightCheckbox = (val: string[]) => {
  checkboxWeightModelValueRef.value = val;
};

// 製造記録にチェックが入っているか
const isCheckedProduct = () =>
  checkboxProductModelValueRef.value.includes(PRINT_ID.CHECKED);

// 秤量記録にチェックが入っているか
const isCheckedWeight = () =>
  checkboxWeightModelValueRef.value.includes(PRINT_ID.CHECKED);

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  odrNo?: string; // 親ダイアログのodrNo
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

/**
 * 製造記録参照_印刷設定ダイアログの初期設定
 */
const prdReferencePrintInit = async () => {
  if (!props.odrNo) {
    return;
  }

  // チェックボックス初期値設定
  checkboxProductModelValueRef.value = [PRINT_ID.CHECKED];
  checkboxWeightModelValueRef.value = [PRINT_ID.CHECKED];

  const requestData: GetReferencePrintRequestData = {
    odrNo: props.odrNo,
  };

  showLoading();

  const { responseRef, errorRef } = await useGetReferencePrint({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });

  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }
  if (responseRef.value) {
    const resData = responseRef.value.data;
    initResponseData = resData.rData;
    // NOTE:空文字なら秤量記録書チェックボックス非活性
    disabledWeighingButtonRef.value = initResponseData.wgtPdfFile === '';
    if (disabledWeighingButtonRef.value) {
      checkboxWeightModelValueRef.value = [''];
    }
  }

  closeLoading();

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    prdReferencePrintInit();
  },
);

// 実行 押下時処理
const resolveClickHandler = async () => {
  if (!props.odrNo) {
    return false;
  }

  if (!isCheckedProduct() && !isCheckedWeight()) {
    // チェックボックスが選択されていないメッセージ表示
    openDialog('messageNoBoxCheckBoxVisible');
    return false;
  }

  // NOTE:空文字なら未承認 秤量記録は非活性になるためチェック不要
  if (initResponseData.recPdfFile === '') {
    // 製造記録未承認エラー表示
    openDialog('messageBoxProductInstructionErrorUnapprovedVisible');
    return false;
  }

  // 記録書再出力押下時の確認表示
  openDialog('messageBoxProductInstructionConfirmVisible');

  return false;
};

// 記録書PDFダウンロードAPIリクエスト
const requestApiDownLoadPrint = async () => {
  closeDialog('messageBoxProductInstructionConfirmVisible');

  showLoading();

  if (isCheckedProduct()) {
    await useFileDownload({
      commonRequestData: {
        ...props.privilegesBtnRequestData,
        msgboxTitleTxt: messageBoxProductInstructionConfirmPropsRef.value.title,
        msgboxMsgTxt: messageBoxProductInstructionConfirmPropsRef.value.content,
        msgboxBtnTxt: t('Prd.Chr.btnRecordRePrint'),
      },
      sysBinNo: initResponseData.recPdfFile, // 製造記録書PDF
    });
  }
  if (isCheckedWeight()) {
    await useFileDownload({
      commonRequestData: {
        ...props.privilegesBtnRequestData,
        msgboxTitleTxt: messageBoxProductInstructionConfirmPropsRef.value.title,
        msgboxMsgTxt: messageBoxProductInstructionConfirmPropsRef.value.content,
        msgboxBtnTxt: t('Prd.Chr.btnRecordRePrint'),
      },
      sysBinNo: initResponseData.wgtPdfFile, // 秤量記録書PDF
    });
  }

  closeLoading();

  // 完了メッセージ表示
  openDialog('messageBoxProductInstructionFinishedVisible');
};
</script>
