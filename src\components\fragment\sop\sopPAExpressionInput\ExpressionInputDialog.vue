<template>
  <!-- 式入力ダイアログ -->
  <DialogWindow
    :title="$t('SOP.Chr.txtExpressionInput')"
    :dialogVisible="props.dialogVisible"
    :onReject="() => onRejectClickHandler()"
    :onResolve="async () => resolveClickHandler()"
    width="1200"
  >
    <CustomForm
      :formModel="sopExpressionInputDialogFormRef.formModel"
      :formItems="sopExpressionInputDialogFormRef.formItems"
      width="750px"
      @visible="
        (v: CustomFormType['customForm']) => {
          sopExpressionInputDialogFormRef.customForm = v;
        }
      "
    />
    <el-row class="expression-input">
      <el-col :span="6">
        <!-- [ノードID] -->
        <small>{{
          $t('SOP.Chr.SOPExpressionDialog.txtExpressionInputNodeId')
        }}</small>
        <el-form-item prop="expressionInputNodeId">
          <el-select
            v-model="state.expressionInputNodeId"
            @change="datasourceChange"
            clearable
            :placeholder="$t('Cm.Msg.pleaseSelect')"
            filterable
          >
            <el-option
              v-for="item in state.nodeNames"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="2" />
      <el-col :span="4">
        <!-- [G-コード] -->
        <small>{{
          $t('SOP.Chr.SOPExpressionDialog.txtExpressionInputGcode')
        }}</small>
        <el-button class="btn-gCode top-wide" @click="handleGCodeDialog()">
          {{ $t('SOP.Chr.SOPExpressionDialog.txtSelectGCode') }}
        </el-button>
        <GCodeDialog
          :title="t('SOP.Chr.txtGCodeMemoTitle')"
          :isGCodeDialogVisible="state.isGCodeDialogVisible"
          :commonRequest="props.commonRequest"
          @GCodeDialogVisible="GCodeDialogVisible"
          @valueApply="valueApply"
        />
      </el-col>
    </el-row>
    <el-collapse class="functions-list">
      <el-collapse-item
        :title="$t('SOP.Menu.txtFunctionsListTitle')"
        class="functions-list-header"
      >
        <div class="functions-list">
          <el-table :data="functions" height="260">
            <el-table-column prop="name" label="関数名" width="150" />
            <el-table-column prop="description" label="関数機能">
              <template #default="{ row }">
                <div style="white-space: pre-line">{{ row.description }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="example" label="計算式<例>" />
          </el-table>
          <el-table :data="variables" height="120">
            <el-table-column prop="name" label="変数名" width="150" />
            <el-table-column prop="description" label="変数機能" />
            <el-table-column prop="example" label="計算式<例>" />
          </el-table>
        </div>
      </el-collapse-item>
    </el-collapse>
    <MessageBox
      v-show="dialogVisibleRef.singleButtonRef"
      :dialog-props="messageBoxSingleButtonRef"
      :submitCallback="() => handleMessageBoxClose()"
    />
  </DialogWindow>
</template>
<script setup lang="ts">
import { ref, watch, reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import { useCheckSopFormula } from '@/hooks/useApi';
import { CheckSopFormula } from '@/types/HookUseApi/SopTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { SelectOption } from '@/types/SopDialogInterface';
import GCodeDialog from '@/components/fragment/sop/sopPAGCode/GCodeListDialog.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  sopExpressionInputDialogFormItems,
  sopExpressionInputDialogFormModel,
} from './expressionInputDialog';

const { t } = useI18n();

type Props = {
  dialogVisible: boolean;
  inputMethodFormula: string;
  commonRequest: CommonRequestType;
  destinationNodeNames: SelectOption[];
};

const props = withDefaults(defineProps<Props>(), {
  dialogVisible: false,
  inputMethodFormula: '',
  destinationNodeNames: () => [],
});

interface State {
  nodeNames: SelectOption[];
  isGCodeDialogVisible: boolean;
  expressionInputNodeId: string;
}

const state = reactive<State>({
  nodeNames: props.destinationNodeNames,
  isGCodeDialogVisible: false,
  expressionInputNodeId: '',
});

const functions = ref([
  { name: 'SUM', description: '合計', example: '=SUM(1,2,3,4,5)' },
  { name: 'AVERAGE', description: '平均', example: '=AVERAGE(1,2,3,4,5)' },
  { name: 'COUNT', description: '個数', example: '=COUNT(1,2,3,4,)' },
  { name: 'MAX', description: '最大値', example: '=MAX(1,2,3,4,5)' },
  { name: 'MIN', description: '最小値', example: '=MIN(1,2,3,4,5)' },
  { name: 'MOD', description: '余剰', example: '=MOD(10,4)' },
  { name: '+-*/', description: '四則演算', example: '=(1+2)*10/2' },
  {
    name: 'IF',
    description: `論理式`,
    example: '=IF(MOD(10,2)>=6,"5-OVER","5-MINUS")',
  },
  {
    name: 'MID',
    description: '文字列切り出し(指定位置・文字数)',
    example: '=MID("ABCDEF",2,3)',
  },
  {
    name: 'LEFT',
    description: '文字列切り出し(先頭より)',
    example: '=LEFT("ABCDEF",3)',
  },
  {
    name: 'RIGHT',
    description: '文字列切り出し(末尾より)',
    example: '=RIGHT("ABCDEF",3)',
  },
  { name: '&', description: '文字列連結', example: '="ABC"&"123"' },
  {
    name: 'LEN',
    description:
      'セルに書かれた文字数をカウントする\n・全角、半角どちらも「1文字」カウント\n・スペース、改行も「1文字」カウント',
    example: '=LEN("①②③")',
  },
  {
    name: 'VALUE(val)',
    description: '文字列を数値に変換',
    example: '=VALUE("123")',
  },
  {
    name: 'DATEDIFF',
    description:
      '2つの日付間の差分\n※"YD"や"MD"で日数を計算させると、間違った値になる(閏年など)',
    example: '=DATEDIFF("2023/9/1","2023/9/30","D")',
  },
  {
    name: 'HOUR',
    description: '時間の取得',
    example: '=HOUR("14:30:00") - HOUR("06:00:00")',
  },
  {
    name: 'MINUTE',
    description: '分の取得',
    example: '=MINUTE("14:30:00") - MINUTE("06:00:00")',
  },
  {
    name: 'DATETIMEDIFF',
    description: '2つの日付・時間の差分',
    example: '={DateTimeDiff("2024/02/28 12:00:00","2024/03/03 18:00:00","h")}',
  },
  {
    name: '{INSTR',
    description: '文字列内の部分文字列のヒットした先頭の位置を検索',
    example: '={INSTR("Hello, World!", "World")}',
  },
  {
    name: '{SPLIT',
    description:
      '文字列を指定した文字で区切り、指定した位置の区切り文字列を取得',
    example: '={SPLIT(" Hello , World! ", ",", 1)}',
  },
  {
    name: '{LENB',
    description:
      'セルに書かれた文字数をバイト数で返す\n・半角「1文字」カウント\n・全角「2文字」カウント',
    example: '={LENB("①②③")}',
  },
]);
const variables = ref([
  {
    name: 'NC',
    description: 'ノードIDを指定してSOPパーツの実行結果を取得',
    example: '=NC(0005)',
  },
  {
    name: 'GC',
    description: 'G-コードを指定してG-コードに対して設定した値を取得',
    example: '=GC("HLT-前工程開始CHK")',
  },
]);

const emit = defineEmits(['dispExpression']);

type DialogRefKey = 'singleButtonRef';
const initialState: InitialDialogState<DialogRefKey> = {
  singleButtonRef: false,
};
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  type: 'error',
  isSingleBtn: true,
});
const handleMessageBoxClose = () => {
  closeDialog('singleButtonRef');
};

const sopExpressionInputDialogFormRef = ref<CustomFormType>({
  formItems: sopExpressionInputDialogFormItems,
  formModel: sopExpressionInputDialogFormModel,
});

const handleGCodeDialog = () => {
  state.isGCodeDialogVisible = true;
};
const GCodeDialogVisible = (isShow: boolean) => {
  state.isGCodeDialogVisible = isShow;
};
const valueApply = (data: string) => {
  if (data !== null) {
    navigator.clipboard.writeText(data);
    // sopExpressionInputDialogFormRef.value.formItems.expression.formModelValue = `=GC("${data}")`;
  }
};
const datasourceChange = () => {
  const nodeId = state.expressionInputNodeId;
  if (nodeId !== undefined && nodeId !== '') {
    navigator.clipboard.writeText(nodeId);
    // sopExpressionInputDialogFormRef.value.formItems.expression.formModelValue = `=NC(${nodeId})`;
  }
};

/**
 * 実行ボタンクリックイベント
 */
const resolveClickHandler = async () => {
  const validate =
    sopExpressionInputDialogFormRef.value.customForm !== undefined &&
    (await sopExpressionInputDialogFormRef.value.customForm.validate(
      (isValid) => {
        onValidateHandler(isValid);
      },
    ));
  if (!validate) {
    return false;
  }

  const expression = <string>(
    sopExpressionInputDialogFormRef.value.formItems.expression.formModelValue
  );
  if (expression !== undefined && expression !== '') {
    const apiRequestData: CheckSopFormula = {
      inputFormula: expression.trim(),
    };
    const { responseRef, errorRef } = await useCheckSopFormula({
      ...props.commonRequest,
      ...apiRequestData,
    });
    if (errorRef.value) {
      messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
      messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
      openDialog('singleButtonRef');
      messageBoxSingleButtonRef.value.type = 'error';
      return false;
    }
    if (
      responseRef.value &&
      responseRef.value.data.rData.checkResult.checkStatus !== 0
    ) {
      messageBoxSingleButtonRef.value.title = t('SOP.Chr.txtExpressionInput');
      messageBoxSingleButtonRef.value.content =
        responseRef.value.data.rData.checkResult.formulaResult;
      openDialog('singleButtonRef');
      messageBoxSingleButtonRef.value.type = 'error';
      return false;
    }
  }

  emit(
    'dispExpression',
    sopExpressionInputDialogFormRef.value.formItems.expression.formModelValue,
  );
  sopExpressionInputDialogFormRef.value.formItems.expression.formModelValue =
    '';
  return true;
};

/**
 * キャンセルボタンクリックイベント
 */
const onRejectClickHandler = () => {
  sopExpressionInputDialogFormRef.value.formItems.expression.formModelValue =
    props.inputMethodFormula;
  return true;
};
watch(
  () => props.inputMethodFormula,
  (newVal) => {
    if (newVal) {
      sopExpressionInputDialogFormRef.value.formItems.expression.formModelValue =
        newVal;
    }
  },
  { deep: true },
);
watch(
  () => props.dialogVisible,
  (newVal) => {
    if (newVal) {
      sopExpressionInputDialogFormRef.value.formItems.expression.formModelValue =
        props.inputMethodFormula;
      sopExpressionInputDialogFormRef.value.customForm?.validate();
    }
  },
  { deep: true },
);
</script>
<style lang="scss" scoped>
.functions-list {
  margin-top: 20px;
}
.functions-list-header :deep(.el-collapse-item__header) {
  height: 30px !important;
  line-height: 30px !important;
  padding: 0 12px;
  font-size: 13px;
}
.expression-input {
  margin-top: 20px;
  margin-bottom: 20px;
}

$namespace: 'order-addition-dialog';
.#{$namespace} {
  background-color: $white750;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
::v-deep .el-table th {
  color: $white750;
  background-color: $blue100;
}
</style>
