import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';

const { t } = i18n.global;

// 小日程計画詳細の縦並び項目定義
const getOrderDetailInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // オーダー番号
    skdNo: {
      label: { text: t('Odr.Chr.txtScheduleNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
    // 品名
    dspNmJp: {
      label: { text: t('Odr.Chr.txtMaterialName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
    // 処方名
    rxNmJp: {
      label: { text: t('Odr.Chr.txtPrescriptionName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
    // 標準生産量
    stdPrdQty: {
      label: { text: t('Odr.Chr.txtStandardProductionQuantity') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
    // 単位
    unitNmJp: {
      label: { text: t('Odr.Chr.txtUnit') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
  });

export default getOrderDetailInfoShowItems;
