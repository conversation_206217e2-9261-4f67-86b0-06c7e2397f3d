import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import CONST_FLAGS from '@/constants/flags';

const { t } = i18n.global;

// GMP確認ダイアログのアイテム定義
export const getSjgGMPConfirmationFormRadioItems = (
  verifyCat: string,
): CustomFormType['formItems'] => {
  const formItems: CustomFormType['formItems'] = {};
  if (verifyCat === CONST_FLAGS.SJG.VERIFY_CAT_STATUS.GMP_DEVIATION_HANDLING) {
    formItems.gmpRslt = {
      label: { text: t('Sjg.Chr.txtVerifyGmpRslt3') },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      formModelValue: '',
      rules: [rules.required('radio')],
      props: {
        isHorizontal: true,
        size: 'small',
        modelValue: '',
        optionsData: {
          label: [t('Cm.Chr.txtHave'), t('Cm.Chr.txtNotHave')],
          value: [
            CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.PRESENCE,
            CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.ABSENCE,
          ],
        },
      },
      formRole: 'radio',
      span: 10,
    };
  } else if (verifyCat === CONST_FLAGS.SJG.VERIFY_CAT_STATUS.GMP_VALIDATION) {
    formItems.gmpRslt = {
      label: { text: t('Sjg.Chr.txtVerifyGmpRslt4') },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      formModelValue: '',
      rules: [rules.required('radio')],
      props: {
        isHorizontal: true,
        size: 'small',
        modelValue: '',
        optionsData: {
          label: [t('Cm.Chr.txtHave'), t('Cm.Chr.txtNotHave')],
          value: [
            CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.PRESENCE,
            CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.ABSENCE,
          ],
        },
      },
      formRole: 'radio',
      span: 10,
    };
  } else if (
    verifyCat === CONST_FLAGS.SJG.VERIFY_CAT_STATUS.GMP_CHANGE_CONTROL
  ) {
    formItems.gmpRslt = {
      label: { text: t('Sjg.Chr.txtVerifyGmpRslt5') },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      formModelValue: '',
      rules: [rules.required('radio')],
      props: {
        isHorizontal: true,
        size: 'small',
        modelValue: '',
        optionsData: {
          label: [t('Cm.Chr.txtHave'), t('Cm.Chr.txtNotHave')],
          value: [
            CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.PRESENCE,
            CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.ABSENCE,
          ],
        },
      },
      formRole: 'radio',
      span: 10,
    };
  } else if (
    verifyCat === CONST_FLAGS.SJG.VERIFY_CAT_STATUS.GMP_SPECIAL_OPERATION
  ) {
    formItems.gmpRslt = {
      label: { text: t('Sjg.Chr.txtVerifyGmpRslt6') },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      formModelValue: '',
      rules: [rules.required('radio')],
      props: {
        isHorizontal: true,
        size: 'small',
        modelValue: '',
        optionsData: {
          label: [t('Cm.Chr.txtHave'), t('Cm.Chr.txtNotHave')],
          value: [
            CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.PRESENCE,
            CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.ABSENCE,
          ],
        },
      },
      formRole: 'radio',
      span: 10,
    };
  } else if (
    verifyCat === CONST_FLAGS.SJG.VERIFY_CAT_STATUS.GMP_MATERIAL_REVISION
  ) {
    formItems.gmpRslt = {
      label: { text: t('Sjg.Chr.txtVerifyGmpRslt7') },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      formModelValue: '',
      rules: [rules.required('radio')],
      props: {
        isHorizontal: true,
        size: 'small',
        modelValue: '',
        optionsData: {
          label: [t('Cm.Chr.txtHave'), t('Cm.Chr.txtNotHave')],
          value: [
            CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.PRESENCE,
            CONST_FLAGS.SJG.VERIFY_GMP_RSLT_STATUS.ABSENCE,
          ],
        },
      },
      formRole: 'radio',
      span: 10,
    };
  }
  return formItems;
};

// GMP確認ダイアログのモデル定義
export const sjgGMPConfirmationFormRadioModel = (
  verifyCat: string,
): CustomFormType['formModel'] =>
  createFormModelByFormItems(getSjgGMPConfirmationFormRadioItems(verifyCat));

export const tablePropsData: TabulatorTableIF = {
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'uniqueKey',
  height: '163px',
  showRadio: true,
  radioCondition: {
    condition: 'gmpInactiveFlg',
    conditionValue: 0,
  },
  column: [
    {
      title: 'Sjg.Chr.txtMatNo',
      field: 'matNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    {
      title: 'Sjg.Chr.txtMatNm',
      field: 'matNm',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    {
      title: 'Sjg.Chr.txtShtNm',
      field: 'shtNm',
      width: COLUMN_WIDTHS.SJG.SHT_NM,
    },
    {
      title: 'Sjg.Chr.txtGmpDocNeedTypeVerify',
      field: 'gmpDocNeedTypeVerifyNm',
      width: COLUMN_WIDTHS.SJG.VER_GMP_DOC_NEED_TYPE_NM,
    },
    {
      title: 'Sjg.Chr.txtGMPMngNo',
      field: 'gmpMngNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    {
      title: 'Sjg.Chr.txtGmpTitle',
      field: 'gmpTitle',
      width: COLUMN_WIDTHS.SJG.GMP_TITLE,
    },
    {
      title: 'Sjg.Chr.txtGmpDes',
      field: 'gmpDes',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    { title: '', field: 'uniqueKey', hidden: true },
  ],
  tableData: [],
  noUseConditionSearch: true, // 絞り込み条件無し
  showConditionSearch: false, // ConditionSearch不要
};
