import { LogModListType } from './CommonTypes';
// import { UsrGrid } from '@/components/model/common/TabulatorTable/TabulatorTable';

// 秤量指示書作成検索 リクエストデータ
export type GetWeighingInstructionRequestData = {
  odrStYmdFrom: string; // 製造開始日(FROM)
  odrStYmdTo: string; // 製造開始日(TO)
  matNo?: string; // 製造品目コード
  lotNo?: string; // 製造番号
  odrNo?: string; // 製造指図番号
  prcNo?: string; // 製造工程番号(製造工程)
  reWgtFlg: string; // 再秤量
  eraseWgtInstGrpNo?: string; // 取消元秤量指示書番号
  roomReqStsFlg?: string; // 秤量室変更依頼中
};

// 秤量指示書作成検索 レスポンス データ部分
export type GetWeighingInstructionResData =
  | {
      odrStYmd: string; // 製造開始日
      matNo: string; // 製造品目コード
      dspNmJp: string; // 製造品名
      lotNo: string; // 製造番号
      batchNo: number | null; // バッチ番号
      odrNo: string; // 製造指図番号
      prcSeq: number | null; // 製造工程順
      prcNmJp: string; // 工程名
      wgtSopsetKey: string; // 秤量セット
      wgtSopsetNmJp: string; // 秤量セット(表示用)
      wgtRoomNo: string; // 秤量室No
      wgtRoomNmJp: string; // 秤量室
      wgtbSopFlowNo: string; // 秤量前SOPフローNo
      wgtbSopFlowNmJp: string; // 秤量前SOPフロー名
      wgtaSopFlowNo: string; // 秤量後SOPフローNo
      wgtaSopFlowNmJp: string; // 秤量後SOPフローNo
      docWgtFormat: string; // 秤量指示書フォーマット
      wgtMatGrpCd: string; // 品目まとめグループ
      wgtMatGrpCdDsp: string; // 品目まとめグループ(表示用)
      wgtCoOdrFlgDsp: string; // 別指図まとめ可能フラグ
      reWgtFlg: string; // 再秤量フラグ
      reWgtFlgDsp: string; // 再秤量フラグ(表示用)
      grpEraseFlg: string; // 取消フラグ
      eraseWgtInstGrpNo: string; // 取消元秤量指示書番号
      reBatchFlg: string; // 再バッチ指示フラグ
      roomReqStsDsp: string; // 変更依頼状態
      wgtInstCnt: string; // 明細数
    }
  | Record<string, never>;

// 秤量指示書作成検索 レスポンスデータ
export type GetWeighingInstructionRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: { wgtInstGrpList: GetWeighingInstructionResData[] }; // 小日程計画一覧
};

// 秤量指示書登録初期表示 リクエスト データ情報
export type GetWeighingInstructionEntryInitListData =
  | {
      batchNo: number; // バッチ番号
      odrNo: string; // 製造指図番号
      prcSeq: number; // 製造工程順
      wgtSopsetKey: string; // 秤量セット
      wgtMatGrpCd: string; // 品目まとめグループ
      reWgtFlg: string; // 再秤量フラグ
      grpEraseFlg: string; // 取消フラグ
      reBatchFlg: string; // 再バッチ指示フラグ
    }
  | Record<string, never>;

// 秤量指示書登録初期表示 リクエストデータ
export type GetWeighingInstructionEntryInitRequestData = {
  wgtInstGrpList: GetWeighingInstructionEntryInitListData[]; // 秤量指示明細一覧
};

// 秤量指示書登録初期表示 レスポンスデータ
export type GetWeighingInstructionEntryInitResListData = {
  odrStYmd: string; // 製造開始日
  wgtInstNo: string; // 秤量指示明細番号
  matNo: string; // 製造品目コード
  dspNmJp: string; // 製造品名
  lotNo: string; // 製造番号
  odrNo: string; // 製造指図番号
  prcNmJp: string; // 工程名
  wgtSopsetKey: string; // 秤量セットキー
  wgtSopsetNmJp: string; // 秤量セット名
  wgtRoomNmJp: string; // 秤量室
  wgtbSopFlowNo: string; // 秤量前SOPフローNo
  wgtbSopFlowNmJp: string; // 秤量前SOPフロー名
  wgtaSopFlowNo: string; // 秤量後SOPフローNo
  wgtaSopFlowNmJp: string; // 秤量後SOPフローNo
  docWgtFormat: string; // 秤量指示書フォーマット
  wgtDeviceNo: string; // 計量器
  deviceNmJp: string; // 計量器(表示用)
  wgtMtdNmJp: string; // 秤量方法
  batchNo: number | null; // バッチ番号
  bomMatNo: string; // 秤量品目コード
  wgtDspNmJp: string; // 秤量品名
  bomPlanQty: string; // 指図指示量
  unitNmJp: string; // 単位
  wgtSeq: number | null; // 秤量順
  bomMatSeq: number | null; // 秤量品投入番号(秤量品目投入順)
  wgtTiterKeyDsp: string; // 補正計算
  roomApprTimes: number | null; // 秤量室変更回数
  updDts: string; // 更新日時
};

// 秤量まとめグループ一覧 リスト部分
export type GetWeighingInstructionEntryInitResList = {
  // 秤量まとめグループ一覧 続く階層2のリストを保持
  wgtInstList: GetWeighingInstructionEntryInitResListData[];
};

// 秤量指示書登録初期表示 レスポンスデータ
export type GetWeighingInstructionEntryInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingInstructionEntryInitResList; // 秤量指示書明細一覧
};

// 秤量指示書登録 リクエスト データ情報
export type AddWeighingInstructionEntryListData =
  | {
      wgtInstNo: string; // 秤量指示明細番号
      updDts: string; // 更新日時
    }
  | Record<string, never>;

// 秤量指示書登録 リクエストデータ
export type AddWeighingInstructionEntryRequestData = {
  wgtInstList: AddWeighingInstructionEntryListData[]; // 秤量指示明細一覧
  wgtPlanYmd: string; // 秤量予定日
  wgtExpl: string; // 秤量指示書コメント
  attBinFileNm: string; // ファイル名
  attBinFile: string; // ファイルオブジェクト
  binExpl: string; // 特別作業指示コメント
};

// 秤量指示書登録 レスポンスデータ
export type AddWeighingInstructionEntryRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
};

// 秤量指示書一覧検索 リクエストデータ
export type GetWeighingInstructionListRequestData = {
  wgtPlanYmdFrom: string; // 秤量予定日(FROM)
  wgtPlanYmdTo: string; // 秤量予定日(TO)
  wgtInstGrpNo: string; // 秤量指示書番号
  wgtInstGrpSts: string; // 秤量指示書状態
  wgtSopsetKey: string; // 秤量セットキー
  reWgtFlg: string; // 再秤量フラグ
  lotNo: string; // 製造番号
};

// 秤量指示書一覧検索 レスポンス データ部分
export type GetWeighingInstructionListResData = {
  wgtPlanYmd: string; // 秤量予定日
  odrStYmd: string; // 指図開始予定日
  matNo: string; // 製造品目コード
  dspNmJp: string; // 製造品名
  lotNo: string; // 製造番号
  wgtInstGrpNo: string; // 秤量指示書番号
  wgtInstGrpStsDsp: string; // 秤量指示書状態(表示用)
  wgtSopsetKey: string; // 秤量セットキー
  wgtSopsetNmJp: string; // 秤量セット名
  wgtRoomNmJp: string; // 秤量室
  wgtbSopFlowNo: string; // 秤量前SOPフローNo
  wgtbSopFlowNmJp: string; // 秤量前SOPフロー名
  wgtaSopFlowNo: string; // 秤量後SOPフローNo
  wgtaSopFlowNmJp: string; // 秤量後SOPフロー名
  docWgtFormat: string; // 秤量指示書フォーマット
  wgtExpl: string; // 秤量指示書コメント
  reWgtFlgDsp: string; // 再秤量フラグ(表示用)
  wgtInstCnt: number | null; // 明細数
  approvDts: string; // 確定日時
  approvUsr: string; // 確定者
};

// 秤量指示書作成検索 レスポンスデータ
export type GetWeighingInstructionListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: { wgtInstGrpList: GetWeighingInstructionListResData[] }; // 小日程計画一覧
};

// 秤量指示書詳細(確定)初期表示 リクエストデータ
export type GetWeighingInstructionConfirmInitRequestData = {
  wgtInstGrpNo: string; // 秤量指示書番号
};

// 秤量指示書詳細(確定)初期表示 レスポンスデータ
export type GetWeighingInstructionConfirmInitResListData = {
  odrStYmd: string; // 指図開始予定日
  wgtInstNo: string; // 秤量指示明細番号
  matNo: string; // 製造品目コード
  dspNmJp: string; // 製造品名
  lotNo: string; // 製造番号
  odrNo: string; // 製造指図番号
  prcNmJp: string; // 工程名
  wgtSopsetKey: string; // 秤量セットキー
  wgtSopsetNmJp: string; // 秤量セット名
  wgtRoomNmJp: string; // 秤量室
  wgtbSopFlowNo: string; // 秤量前SOPフローNo
  wgtbSopFlowNmJp: string; // 秤量前SOPフロー名
  wgtaSopFlowNo: string; // 秤量後SOPフローNo
  wgtaSopFlowNmJp: string; // 秤量後SOPフローNo
  docWgtFormat: string; // 秤量指示書フォーマット
  wgtDeviceNo: string; // 計量器
  deviceNmJp: string; // 計量器(表示用)
  wgtMtdNmJp: string; // 秤量方法
  batchNo: number | null; // バッチ番号
  bomMatNo: string; // 秤量品目コード
  wgtDspNmJp: string; // 秤量品名
  bomPlanQty: string; // 指図指示量
  unitNmJp: string; // 単位
  wgtSeq: number | null; // 秤量順
  bomMatSeq: number | null; // 秤量品投入番号(秤量品目投入順)
  wgtTiterKeyDsp: string; // 補正計算
  roomApprTimes: number | null; // 秤量室変更回数
};

// 秤量指示明細一覧 リスト部分
export type GetWeighingInstructionConfirmInitResList = {
  // 秤量指示明細一覧 続く階層2のリストを保持
  wgtInstList: GetWeighingInstructionConfirmInitResListData[];
  updDts: string; // 更新日時(秤量指示書)
  wgtPlanYmd: string; // 秤量予定日
  wgtExpl: string; // 秤量指示書コメント
  attBinNo: string; // 特別作業指示管理No
  attBinFileNm: string; // ファイル名
};

// 秤量指示書詳細(確定)初期表示 レスポンスデータ
export type GetWeightInstructionConfirmInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingInstructionConfirmInitResList; // 秤量指示明細一覧
};

// 秤量指示書確定 リクエストデータ
export type ModifyWeighingInstructionConfirmRequestData = {
  wgtInstGrpNo: string; // 秤量指示書番号
  wgtPlanYmd: string; // 秤量予定日
  wgtExpl: string; // 秤量指示書コメント
  updDts: string; // 更新日時
  addAttBinFileNm: string; // ファイル名
  addAttBinFile: string; // ファイルオブジェクト
  delAttBinNo: string; // 削除ファイル管理No
  binExpl: string; // 特別作業指示コメント
};

// 秤量指示書確定 レスポンスデータ
export type ModifyWeighingInstructionConfirmRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
};

// 秤量指示書詳細(取消)初期表示 リクエストデータ
export type GetWeighingInstructionEraseInitRequestData = {
  wgtInstGrpNo: string; // 秤量指示書番号
};

// 秤量指示書詳細(取消)初期表示 レスポンスデータ
export type GetWeighingInstructionEraseInitResListData = {
  odrStYmd: string; // 指図開始予定日
  wgtInstNo: string; // 秤量指示明細番号
  matNo: string; // 製造品目コード
  dspNmJp: string; // 製造品名
  lotNo: string; // 製造番号
  odrNo: string; // 製造指図番号
  prcNmJp: string; // 工程名
  wgtSopsetKey: string; // 秤量セットキー
  wgtSopsetNmJp: string; // 秤量セット名
  wgtRoomNmJp: string; // 秤量室
  wgtbSopFlowNo: string; // 秤量前SOPフローNo
  wgtbSopFlowNmJp: string; // 秤量前SOPフロー名
  wgtaSopFlowNo: string; // 秤量後SOPフローNo
  wgtaSopFlowNmJp: string; // 秤量後SOPフローNo
  docWgtFormat: string; // 秤量指示書フォーマット
  wgtDeviceNo: string; // 計量器
  deviceNmJp: string; // 計量器(表示用)
  wgtMtdNmJp: string; // 秤量方法
  batchNo: number | null; // バッチ番号
  bomMatNo: string; // 秤量品目コード
  wgtDspNmJp: string; // 秤量品名
  bomPlanQty: string; // 指図指示量
  unitNmJp: string; // 単位
  wgtSeq: number | null; // 秤量順
  bomMatSeq: number | null; // 秤量品投入番号(秤量品目投入順)
  wgtTiterKeyDsp: string; // 補正計算
  roomApprTimes: number | null; // 秤量室変更回数
};

// 秤量指示書詳細(取消) 秤量指示明細一覧 リスト部分
export type GetWeighingInstructionEraseInitResList = {
  // 秤量指示明細一覧 続く階層2のリストを保持
  wgtInstList: GetWeighingInstructionEraseInitResListData[];
  updDts: string; // 更新日時(秤量指示書)
  wgtPlanYmd: string; // 秤量予定日
  wgtExpl: string; // 秤量指示書コメント
  attBinNo: string; // 特別作業指示管理No
  attBinFileNm: string; // ファイル名
};

// 秤量指示書詳細(取消)初期表示 レスポンスデータ
export type GetWeighingInstructionEraseInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingInstructionEraseInitResList; // 秤量指示明細一覧
};

// 秤量指示書取消 リクエストデータ
export type DeleteWeighingInstructionEraseRequestData = {
  wgtInstGrpNo: string; // 秤量指示書番号
  eraseExpl: string; // 取消コメント
  updDts: string; // 更新日時
};

// 秤量指示書取消 レスポンスデータ
export type DeleteWeighingInstructionEraseRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
};

// 秤量指示書詳細(印刷)初期表示 リクエストデータ
export type GetWeighingInstructionPrintInitRequestData = {
  wgtInstGrpNo: string; // 秤量指示書番号
};

// 秤量指示書詳細(印刷)初期表示 レスポンスデータ
export type GetWeighingInstructionPrintInitResListData = {
  odrStYmd: string; // 指図開始予定日
  wgtInstNo: string; // 秤量指示明細番号
  matNo: string; // 製造品目コード
  dspNmJp: string; // 製造品名
  lotNo: string; // 製造番号
  odrNo: string; // 製造指図番号
  prcNmJp: string; // 工程名
  wgtSopsetKey: string; // 秤量セットキー
  wgtSopsetNmJp: string; // 秤量セット名
  wgtRoomNmJp: string; // 秤量室
  wgtbSopFlowNo: string; // 秤量前SOPフローNo
  wgtbSopFlowNmJp: string; // 秤量前SOPフロー名
  wgtaSopFlowNo: string; // 秤量後SOPフローNo
  wgtaSopFlowNmJp: string; // 秤量後SOPフローNo
  docWgtFormat: string; // 秤量指示書フォーマット
  wgtDeviceNo: string; // 計量器
  deviceNmJp: string; // 計量器(表示用)
  wgtMtdNmJp: string; // 秤量方法
  batchNo: number | null; // バッチ番号
  bomMatNo: string; // 秤量品目コード
  wgtDspNmJp: string; // 秤量品名
  bomPlanQty: string; // 指図指示量
  unitNmJp: string; // 単位
  wgtSeq: number | null; // 秤量順
  bomMatSeq: number | null; // 秤量品投入番号(秤量品目投入順)
  wgtTiterKeyDsp: string; // 補正計算
  roomApprTimes: number | null; // 秤量室変更回数
};

// 秤量指示書詳細(印刷) 秤量指示明細一覧 リスト部分
export type GetWeighingInstructionPrintInitResList = {
  // 秤量指示明細一覧 続く階層2のリストを保持
  wgtInstList: GetWeighingInstructionPrintInitResListData[];
  updDts: string; // 更新日時(秤量指示書)
  wgtPlanYmd: string; // 秤量予定日
  wgtExpl: string; // 秤量指示書コメント
  attBinNo: string; // 特別作業指示管理No
  attBinFileNm: string; // ファイル名
};

// 秤量指示書詳細(印刷)初期表示 レスポンスデータ
export type GetWeighingInstructionPrintInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingInstructionPrintInitResList; // 秤量指示明細一覧
};

// 秤量指示書印刷 リクエストデータ
export type ModifyWeighingInstructionPrintRequestData = {
  wgtInstGrpNo: string; // 秤量指示書番号
  updDts: string; // 更新日時
};

// 秤量指示書印刷 レスポンスデータ
export type ModifyWeighingInstructionPrintRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
};

// 秤量室変更検索 リクエストデータ
export type GetWeighingRoomChangeRequestData = {
  wgtSopsetKey: string; // 秤量セット
  wgtRoomNo: string; // 秤量室
  wgtDeviceNo: string; // 計量器
  roomReqSts: string; // 変更依頼状態
  matNo: string; // 製造品目コード
  lotNo: string; // 製造番号
  eraseWgtInstGrpNo: string; // 取消元秤量指示書番号
  wgtRoomChgApprov: string; // 秤量室変更有無
};

// 秤量室変更検索 レスポンス データ部分
export type GetWeighingRoomChangeResData = {
  uniqueKey: string; // ユニークキー(ラジオボタン選択用)
  wgtSopsetKey: string; // 秤量セット
  wgtSopsetNmJp: string; // 秤量セット(表示用)
  wgtRoomNmJp: string; // 秤量室(表示用)
  wgtDeviceNo: string; // 計量器(通常秤量)
  deviceNmJp: string; // 計量器(通常秤量)(表示用)
  remDeviceNo: string; // 計量器(残計量)
  remDeviceNmJp: string; // 計量器(残計量)(表示用)
  roomReqSts: string; // 変更依頼状態
  roomReqStsDsp: string; // 変更依頼状態(表示用)
  odrStYmd: string; // 指図開始予定日
  matNo: string; // 製造品目コード
  dspNmJp: string; // 製造品名
  lotNo: string; // 製造番号
  batchNo: number | null; // バッチ番号
  odrNo: string; // 製造指図番号
  prcSeq: number | null; // 製造工程順
  prcNmJp: string; // 工程名
  reBatchFlg: string; // 再バッチ指示フラグ
  reWgtFlg: string; // 再秤量フラグ
  reWgtFlgDsp: string; // 再秤量フラグ(表示用)
  grpEraseFlg: string; // 取消フラグ
  eraseWgtInstGrpNo: string; // 取消元秤量指示書番号
  wgtRoomChgApprov: string; // 秤量室変更有無
  updDts: string; // 更新日時
};

// 秤量室変更検索 レスポンスデータ
export type GetWeighingRoomChangeRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: { wgtGrpList: GetWeighingRoomChangeResData[] }; // 秤量室変更一覧
};

// 秤量室変更詳細(依頼)初期表示 リクエストデータ
export type GetWeighingRoomChangeRequestInitRequestData = {
  odrNo: string; // 製造指図番号
  wgtSopsetKey: string; // 秤量セット
  wgtDeviceNo: string; // 計量器(通常秤量)
  remDeviceNo: string; // 計量器(残計量)
  roomReqSts: string; // 変更依頼状態
  batchNo: number; // バッチ番号
  prcSeq: number; // 製造工程順
  reBatchFlg: string; // 再バッチ指示フラグ
  reWgtFlg: string; // 再秤量フラグ
  grpEraseFlg: string; // 取消フラグ
};

// 秤量室変更詳細(依頼)初期表示 レスポンスデータ
export type GetWeighingRoomChangeRequestInitResListData = {
  wgtInstNo: string; // 秤量指示明細番号
  updDts: string; // 更新日時
};

// 秤量室変更詳細(依頼)初期表示 リスト部分
export type GetWeighingRoomChangeRequestInitResList = {
  // 秤量室変更詳細(依頼)初期表示 続く階層2のリストを保持
  wgtInstList: GetWeighingRoomChangeRequestInitResListData[];
  matNo: string; // 製造品目コード
  dspNmJp: string; // 製造品名
  lotNo: string; // 製造番号
  odrNo: string; // 製造指図番号
  prcNmJp: string; // 製造工程
  batchNo: number | null; // バッチ番号
  wgtSopsetNmJp: string; // 変更前の秤量セット
  wgtRoomNmJp: string; // 変更前の秤量室
  deviceNmJp: string; // 変更前の計量器(通常秤量)
  remDeviceNmJp: string; // 変更前の計量器(残計量)
  deviceCat: string; // 通信機器分類(通常秤量)
  remDeviceCat: string; // 通信機器分類(残計量)
};

// 秤量室変更詳細(依頼)初期表示 レスポンスデータ
export type GetWeighingRoomChangeRequestInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingRoomChangeRequestInitResList; // 秤量指示明細一覧
};

// 秤量室変更依頼前チェック 秤量指示書明細一覧 リスト部分
export type CheckWeighingRoomChangeRequestRequestListData = {
  wgtInstNo: string; // 秤量指示明細番号
  updDts: string; // 更新日時
};

// 秤量室変更依頼前チェック リクエストデータ
export type CheckWeighingRoomChangeRequestRequestData = {
  wgtInstList: CheckWeighingRoomChangeRequestRequestListData[];
  afterWgtSopsetKey: string; // 変更後の秤量セット
  afterWgtDeviceNo: string; // 変更後の計量器(通常秤量)
  afterRemDeviceNo: string; // 変更後の計量器(残計量)
};

// 秤量室変更依頼前 レスポンスデータ
export type CheckWeighingRoomChangeRequestRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 秤量室変更依頼 秤量室指示書明細一覧
export type ModifyWeighingRoomChangeRequestRequestListData = {
  wgtInstNo: string; // 秤量指示書明細番号
  updDts: string; // 更新日時
};

// 秤量室変更依頼 リクエストデータ
export type ModifyWeighingRoomChangeRequestRequestData = {
  wgtInstList: ModifyWeighingRoomChangeRequestRequestListData[]; // 秤量指示書明細一覧
  afterWgtSopsetKey: string; // 変更後の秤量セット
  afterWgtDeviceNo: string; // 変更後の計量器(通常秤量)
  afterRemDeviceNo: string; // 変更後の計量器(残計量)
  modExpl: string; // 変更コメント
  logModList: LogModListType;
};

// 秤量室変更依頼 レスポンスデータ
export type ModifyWeighingRoomChangeRequestRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 秤量室変更依頼取消 リクエストデータ
export type ModifyWeighingRoomChangeRequestEraseRequestData = {
  updDts: string; // 更新日時
  odrNo: string; // 製造指図番号
  wgtSopsetKey: string; // 秤量セット
  wgtDeviceNo: string; // 計量器(通常秤量)
  remDeviceNo: string; // 計量器(残計量)
  roomReqSts: string; // 変更依頼状態
  batchNo: number; // バッチ番号
  prcSeq: number; // 製造工程順
  reBatchFlg: string; // 再バッチ指示フラグ
  reWgtFlg: string; // 再秤量フラグ
  grpEraseFlg: string; // 取消フラグ
  eraseExpl: string; // 取消コメント
};

// 秤量室変更依頼取消 レスポンスデータ
export type ModifyWeighingRoomChangeRequestEraseRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 秤量室変更詳細(承認)初期表示 リクエストデータ
export type GetWeighingRoomChangeApprovalInitRequestData = {
  odrNo: string; // 製造指図番号
  wgtSopsetKey: string; // 秤量セット
  wgtDeviceNo: string; // 計量器(通常秤量)
  remDeviceNo: string; // 計量器(残計量)
  roomReqSts: string; // 変更依頼状態
  batchNo: number; // バッチ番号
  prcSeq: number; // 製造工程順
  reBatchFlg: string; // 再バッチ指示フラグ
  reWgtFlg: string; // 再秤量フラグ
  grpEraseFlg: string; // 取消フラグ
};

// 秤量室変更詳細(承認)初期表示 レスポンスデータ
export type GetWeighingRoomChangeApprovalInitResListData = {
  wgtInstNo: string; // 秤量指示明細番号
  updDts: string; // 更新日時
};

// 秤量室変更詳細(承認)初期表示 リスト部分
export type GetWeighingRoomChangeApprovalInitResList = {
  // 秤量室変更詳細(承認)初期表示 続く階層2のリストを保持
  wgtInstList: GetWeighingRoomChangeApprovalInitResListData[];
  matNo: string; // 製造品目コード
  dspNmJp: string; // 製造品名
  lotNo: string; // 製造番号
  odrNo: string; // 製造指図番号
  prcNmJp: string; // 製造工程
  batchNo: number | null; // バッチ番号
  beforeWgtSopsetNmJp: string; // 変更前の秤量セット(表示用)
  beforeWgtRoomNmJp: string; // 変更前の秤量室(表示用)
  beforeWgtDeviceNmJp: string; // 変更前の計量器(通常秤量)(表示用)
  beforeRemDeviceNmJp: string; // 変更前の計量器(残計量)(表示用)
  afterWgtSopsetWgtRoomDsp: string; // 変更後の秤量セット/秤量室(表示用)
  afterWgtDeviceNmJp: string; // 変更後の計量器(通常秤量)(表示用)
  afterRemDeviceNmJp: string; // 変更後の計量器(残計量)(表示用)
  modExpl: string; // 変更コメント
};

// 秤量室変更詳細(承認)初期表示 レスポンスデータ
export type GetWeighingRoomChangeApprovalInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingRoomChangeApprovalInitResList; // 秤量指示明細一覧
};

// 秤量室変更承認 秤量室指示書明細一覧
export type ModifyWeighingRoomChangeApprovalRequestListData = {
  wgtInstNo: string; // 秤量指示書明細番号
  updDts: string; // 更新日時
};

// 秤量室変更承認 リクエストデータ
export type ModifyWeighingRoomChangeApprovalRequestData = {
  wgtInstList: ModifyWeighingRoomChangeApprovalRequestListData[]; // 秤量指示書明細一覧
};

// 秤量室変更承認 レスポンスデータ
export type ModifyWeighingRoomChangeApprovalRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 再秤量指示作成検索 リクエストデータ
export type GetReWeighingInstructionRequestData = {
  wgtInstGrpNo: string; // 秤量指示書番号
};

// 再秤量指示作成検索 レスポンス データ部分
export type GetReWeighingInstructionResListData = {
  wgtInstNo: string; // 秤量指示明細番号
  wgtInstStsDsp: string; // 秤秤量指示明細状態(表示用)
  matNo: string; // 製造品目コード
  dspNmJp: string; // 製造品名
  lotNo: string; // 製造番号
  odrNo: string; // 製造指図番号
  prcNmJp: string; // 工程名
  wgtSopsetKey: string; // 秤量セット
  wgtSopsetNmJp: string; // 秤量セット(表示用)
  wgtRoomNmJp: string; // 秤量室
  wgtbSopFlowNo: string; // 秤量前SOPフローNo
  wgtbSopFlowNmJp: string; // 秤量前SOPフロー名
  wgtaSopFlowNo: string; // 秤量後SOPフローNo
  wgtaSopFlowNmJp: string; // 秤量後SOPフローNo
  docWgtFormat: string; // 秤量指示書フォーマット
  wgtDeviceNo: string; // 計量器
  deviceNmJp: string; // 計量器(表示用)
  wgtMtdNmJp: string; // 秤量方法
  batchNo: number | null; // バッチ番号
  bomMatNo: string; // 秤量品目コード
  wgtDspNmJp: string; // 秤量品名
  bomPlanQty: string; // 指図指示量
  unitNmJp: string; // 単位
  wgtSeq: number | null; // 秤量順
  bomMatSeq: number | null; // 秤量品投入番号(秤量品目投入順)
  wgtTiterKeyDsp: string; // 補正計算
  recConfirmFlgDsp: string; // 製造記録確認済フラグ
  lotoutFlgDsp: string; // 指図中止フラグ
};

// 再秤量指示作成検索 秤量指示明細一覧 リスト部分
export type GetReWeighingInstructionResList = {
  // 秤量指示明細一覧 続く階層2のリストを保持
  wgtDetailList: GetReWeighingInstructionResListData[];
  wgtPlanYmd: string; // 秤量予定日
  wgtInstGrpStsDsp: string; // 秤量指示書状態(表示用)
};

// 再秤量指示作成検索 レスポンスデータ
export type GetReWeighingInstructionRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetReWeighingInstructionResList; // 秤量指示明細一覧
};

// 秤量記録確認検索 リクエストデータ
export type GetWeighingRecordConfirmRequestData = {
  wgtInstGrpNo: string; // 秤量指示書番号
};

// 秤量記録確認検索 レスポンスデータ
export type GetWeighingRecordConfirmResListData = {
  wgtInstNo: string; // 秤量指示(明細)番号
  wgtCnt: number | null; // 秤量実施回数
  seqNo: number | null; // シーケンシャル番号
  bomMatNo: string; // 秤量品目コード
  wgtDspNmJp: string; // 秤量品名
  lotNo: string; // 管理番号
  batchNo: number | null; // バッチ番号
  wgtHandNo: number | null; // 小分け番号
  bomPlanQty: string; // 指図指示量
  scnInstHandVal: string; // 秤量指示量
  scnBatchTotalVal: string; // バッチ累積秤取量
  scnWgtVal: string; // 秤取量
  unitNmJp: string; // 秤量単位
  conWgt: string; // 風袋重量
  conUnitNmJp: string; // 風袋重量単位
  wgtDts: string; // 秤量日時
  matNo: string; // 製造品目コード
  dspNmJp: string; // 製造品名
  deviceNmJp: string; // 計量器
  recUsr: string; // 記録者
  reWgtFlgDsp: string; // 再秤量フラグ
  modExplDsp: string; // 修正コメント
};

// 秤量記録確認検索 リスト部分
export type GetWeighingRecordConfirmResList = {
  // 秤量記録確認検索 続く階層2のリストを保持
  wgtRecordList: GetWeighingRecordConfirmResListData[];
  instGrpUpdDts: string; // 更新日時(秤量指示書)
  logUpdDts: string; // 更新日時(秤量記録)
  wgtInstGrpStsDsp: string; // 秤量指示書状態(表示用)
  instGrpProcessedFlg: string; // 指示書処理済フラグ
  recordEditDisableFlg: string; // 記録修正非活性フラグ
};

// 秤量記録確認検索 レスポンスデータ
export type GetWeighingRecordConfirmResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingRecordConfirmResList;
};

// 秤量記録確認 リクエストデータ
export type ModifyWeighingRecordConfirmRequestData = {
  wgtInstGrpNo: string; // 秤量指示書番号
  instGrpUpdDts: string; // 更新日時(秤量指示書)
  logUpdDts: string; // 更新日時(秤量記録)
};

// 秤量記録確認 レスポンスデータ
export type ModifyWeighingRecordConfirmRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 再秤量指示登録(明細)初期表示 リクエストデータ
export type GetReWeighingInstructionDetailInitRequestData = {
  wgtInstNo: string; // 秤量指示明細番号
};

// 再秤量指示登録(明細)初期表示 レスポンスデータ
export type GetReWeighingInstructionDetailInitResList = {
  wgtInstNo: string; // 秤量指示明細番号
  bomMatNo: string; // 秤量品目コード
  wgtDspNmJp: string; // 秤量品名
  matNo: string; // 製造品目コード
  dspNmJp: string; // 製造品名
  lotNo: string; // 製造番号
  odrNo: string; // 製造指図番号
  prcNmJp: string; // 工程名
  batchNo: number | null; // バッチ番号
  bomMatSeq: number | null; // 秤量品投入番号
  updDts: string; // 更新日時
};

// 再秤量指示登録(明細)初期表示 レスポンスデータ
export type GetReWeighingInstructionDetailInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetReWeighingInstructionDetailInitResList;
};

// 再秤量指示登録(明細)実行(再秤量指示作成) リクエストデータ
export type ModifyReWeighingInstructionDetailRequestData = {
  wgtInstNo: string; // 秤量指示明細番号
  modExpl: string; // 再秤量指示書コメント
  updDts: string; // 更新日時
};

// 再秤量指示登録(明細)実行(再秤量指示作成)  レスポンスデータ
export type ModifyReWeighingInstructionDetailRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 秤量記録詳細初期表示 リクエストデータ
export type GetWeighingRecordDetailInitRequestData = {
  wgtInstNo: string; // 秤量指示明細番号
  wgtCnt: number; // 秤量実施回数
  seqNo: number; // シーケンシャル番号
};

// 秤量記録詳細初期表示 レスポンスデータ
export type GetWeighingRecordDetailInitResListData = {
  msgType: string; // 種別
  edExpl: string; // コメント
  crtUsrDsp: string; // 記録者
  crtDts: string; // 記録日時
};

// 秤量記録詳細初期表示 リスト部分
export type GetWeighingRecordDetailInitResList = {
  wgtDspNmJp: string; // 秤量品名
  lotNo: string; // 管理番号
  batchNo: number | null; // バッチ番号
  wgtHandNo: number | null; // 小分け番号
  bomPlanQty: string; // 指図指示量
  scnInstHandVal: string; // 秤量指示量
  scnBatchTotalVal: string; // バッチ累積秤取量
  scnWgtVal: string; // 秤取量
  unitNmJp: string; // 秤量単位
  conWgt: string; // 風袋重量
  conUnitNmJp: string; // 風袋重量単位
  wgtDts: string; // 秤量日時
  dspNmJp: string; // 製造品名
  deviceNmJp: string; // 計量器
  recUsrDsp: string; // 記録者
  workUsr1Dsp: string; // 作業者1
  workUsr2Dsp: string; // 作業者2
  workUsr3Dsp: string; // 作業者3
  workUsr4Dsp: string; // 作業者4
  workUsr5Dsp: string; // 作業者5
  // 秤量記録情報一覧 続く階層2のリストを保持
  cmtList: GetWeighingRecordDetailInitResListData[];
};

// 秤量記録詳細初期表示 レスポンスデータ
export type GetWeighingRecordDetailInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingRecordDetailInitResList; // 秤量記録情報一覧
};

// 秤量前後SOP記録初期表示 リクエストデータ
export type GetWeighingSOPConfirmInitRequestData = {
  wgtInstGrpNo: string; // 秤量指示書番号
};

// 秤量前後SOP記録初期表示 レスポンスデータ
export type GetWeighingSOPConfirmInitResListData = {
  uniqueKey: string; // ユニークキー(ラジオボタン選択用)
  sopFlowNo: string; // SOPフローNo
  sopFlowLnum: number | null; // SOPフロー実行ログ番号
  recConfirmFlgDsp: string; // 確認状態
  sopFlowStsDsp: string; // フロー状態
  devCorrLv: number | null; // 異状レベル
  backgroundColor: string; // 背景色
  sopFlowNmJp: string; // SOPフロー名
  wgtSopsetKey: string; // 秤量セット
  wgtRoomNmJp: string; // 秤量室
  deviceNmJp: string; // 計量器
  stDts: string; // 製造開始日時
  edDts: string; // 製造終了日時
  recConfirmDts: string; // 製造記録確認日時
  devExplDsp: string; // 異状コメント
  msgExplDsp: string; // 作業コメント
  modExplDsp: string; // 修正コメント
  recConfirmDevExpl: string; // 異状確認コメント
  recConfirmExpl: string; // 記録確認コメント
};

// 秤量前後SOP記録初期表示 リスト部分
export type GetWeighingSOPConfirmInitResList = {
  // 秤量前後SOP記録初期表示 続く階層2のリストを保持
  wgtSopList: GetWeighingSOPConfirmInitResListData[];
};

// 秤量前後SOP記録初期表示 レスポンスデータ
export type GetWeighingSOPConfirmInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingSOPConfirmInitResList;
};

// 再秤量指示登録(小分け)初期表示 リクエストデータ
export type GetReWeighingInstructionWrappingRequestData = {
  wgtInstNo: string; // 秤量指示明細番号
};

// 再秤量指示登録(小分け)初期表示 レスポンスデータ
export type GetReWeighingInstructionWrappingResListData = {
  wgtHandLblSid: string; // ラベル番号
};

// 再秤量指示登録(小分け)初期表示 リスト部分
export type GetReWeighingInstructionWrappingResList = {
  wgtInstNo: string; // 秤量指示明細番号
  bomMatNo: string; // 秤量品目コード
  wgtDspNmJp: string; // 秤量品名
  matNo: string; // 製造品目コード
  dspNmJp: string; // 製造品名
  lotNo: string; // 製造番号
  odrNo: string; // 製造指図番号
  prcNmJp: string; // 工程名
  batchNo: number | null; // バッチ番号
  bomMatSeq: number | null; // 秤量品投入番号
  // 再秤量指示登録(小分け) 続く階層2のリストを保持
  wgtHandLblSidList: GetReWeighingInstructionWrappingResListData[]; // ラベル番号リスト
  updDts: string; // 更新日時
};

// 再秤量指示登録(小分け)初期表示 レスポンスデータ
export type GetReWeighingInstructionWrappingResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetReWeighingInstructionWrappingResList;
};

// 再秤量指示登録(小分け)実行(再秤量指示作成) リクエストデータ
export type ModifyReWeighingInstructionWrappingRequestData = {
  wgtInstNo: string; // 秤量指示明細番号
  wgtHandLblSid: string; // ラベル番号
  modExpl: string; // 再秤量指示書コメント
  updDts: string; // 更新日時
};

// 再秤量指示登録(小分け)実行(再秤量指示作成)  レスポンスデータ
export type ModifyReWeighingInstructionWrappingRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 秤量前後SOP作業詳細初期表示 リクエストデータ
export type GetWeighingSOPDetailInitRequestData = {
  sopFlowNo: string; // SOPフローNo
  sopFlowLnum: number; // SOPフロー実行ログ番号
  wgtInstGrpNo: string; // 秤量指示書番号
};

// 秤量前後SOP作業詳細初期表示 レスポンスデータ
export type GetWeighingSOPDetailInitResListData = {
  sopFlowNo: string; // SOPフローNo
  sopFlowLnum: number | null; // SOPフロー実行ログ番号
  sopNodeLnum: number | null; // SOPノード実行ログ番号
  sopNodeTimes: number | null; // 実行数
  devCorrLv: number | null; // 異状レベル
  backgroundColor: string; // 背景色
  cmtMain1: string; // 指示内容1
  cmtMain2: string; // 指示内容2
  cmtMain3: string; // 指示内容3
  instVal: string; // 指示値
  recModTimes: number | null; // 修正回数
  unitNmJp: string; // 単位
  recVal1: string; // 記録値1
  recVal2: string; // 記録値2
  recVal3: string; // 記録値3
  recVal4: string; // 記録値4
  recVal5: string; // 記録値5
  thValLlmt: number | null; // 異状下限
  thValUlmt: number | null; // 異状上限
  refVal1: string; // 参照値1
  refVal2: string; // 参照値2
  refVal3: string; // 参照値3
  refVal4: string; // 参照値4
  refVal5: string; // 参照値5
  helpDts: string; // SOPヘルプ表示日時
  recDts: string; // 記録日時
  recUsr: string; // 記録者
  dcheckVal: string; // D記録
  dcheckDts: string; // D記録日時
  dcheckUsr: string; // D記録者
  workUsr: string; // 複数作業者
  devExpl: string; // 異状コメント
  msgExpl: string; // 作業コメント
  modExpl: string; // 修正コメント
  checkboxFlg: string; // チェックボックス非活性フラグ
  recModBtnFlg: string; // 記録修正ボタン非活性フラグ
};

// 秤量前後SOP作業詳細初期表示 リスト部分
export type GetWeighingSOPDetailInitResList = {
  // 秤量前後SOP作業詳細初期表示 続く階層2のリストを保持
  wgtSopDetail: GetWeighingSOPDetailInitResListData[];
  sopFlowNmJp: string; // SOPフロー名
  cmtFlowDev: number | null; // フロー内異状件数
  recConfirmFlgDsp: string; // 確認状態
  sopFlowStsDsp: string; // フロー状態(表示用)
  stDts: string; // SOPフロー開始日時
  edDts: string; // SOPフロー終了日時
  wgtSopsetKey: string; // 秤量セットキー
  wgtRoomNmJp: string; // 秤量室
  deviceNmJp: string; // 計量器
  modExistFlg: string; // SOP記録修正履歴存在フラグ
  devExistFlg: string; // SOP記録異状履歴存在フラグ
  devCorrLvFlg: boolean; // 異状レベル2以上存在フラグ
  recConfirmBtnFlg: boolean; // 記録検印ボタン非活性フラグ
  updDts: string; // 更新日時
};

// 秤量前後SOP作業詳細初期表示 レスポンスデータ
export type GetWeighingSOPDetailInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingSOPDetailInitResList;
};

// 秤量記録修正初期表示 リクエストデータ
export type GetWeighingRecordEditInitRequestData = {
  wgtInstNo: string; // 秤量指示明細番号
  wgtCnt: number; // 秤量実施回数
  seqNo: number; // シーケンシャル番号
};

// 秤量記録修正初期表示 レスポンスデータ
export type GetWeighingRecordEditInitResListData = {
  wgtInstNo: string; // 秤量指示(明細)番号
  wgtCnt: number | null; // 秤量実施回数
  seqNo: number | null; // シーケンシャル番号
  bomMatNo: string; // 秤量品目コード
  wgtDspNmJp: string; // 秤量品名
  lotNo: string; // 管理番号
  batchNo: number | null; // バッチ番号
  wgtHandNo: number | null; // 小分け番号
  bomPlanQty: string; // 指図指示量
  unitNmJp: string; // 秤量単位
  conUnitNmJp: string; // 風袋重量単位
  scnInstHandVal: string; // 秤量指示量
  scnWgtVal: string; // 秤取量
  scnBatchTotalVal: string; // バッチ累積秤取量
  wgtDts: string; // 秤量日時
  conWgt: string; // 風袋重量
  recUsr: string; // 記録者
  workUsr1: string; // 作業者1
  workUsr2: string; // 作業者2
  workUsr3: string; // 作業者3
  workUsr4: string; // 作業者4
  workUsr5: string; // 作業者5
  updDts: string; // 更新日時
};

// 秤量記録修正初期表示 レスポンスデータ
export type GetWeighingRecordEditInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingRecordEditInitResListData;
};

// 秤量記録修正修正確定 リクエストデータ
export type ModifyWeighingRecordEditConfirmRequestData = {
  wgtInstNo: string; // 秤量指示(明細)番号
  wgtCnt: number; // 秤量実施回数
  seqNo: number; // シーケンシャル番号
  scnWgtVal: string; // 修正後秤取量
  scnBatchTotalVal: string; // 修正後バッチ累積秤取量
  conWgt: string; // 修正後風袋重量
  recUsr: string; // 修正後記録者ID
  workUsr1: string; // 修正後作業者1ID
  workUsr2: string; // 修正後作業者2ID
  workUsr3: string; // 修正後作業者3ID
  workUsr4: string; // 修正後作業者4ID
  workUsr5: string; // 修正後作業者5ID
  modExpl: string; // 修正コメント
  updDts: string; // 更新日時
};

// 秤量記録修正修正確定 レスポンスデータ
export type ModifyWeighingRecordEditConfirmResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 秤量前後SOP修正履歴 リクエストデータ
export type GetWeighingSOPHistoryModifyInitRequestData = {
  sopFlowNo: string; // SOPフローNo
  sopFlowLnum: number; // SOPフロー実行ログ番号
};

// 秤量前後SOP修正履歴 レスポンス データ部分
export type GetWeighingSOPHistoryModifyInitResListData = {
  cmtTimes: number | null; // 修正回数
  sopFlowNmJp: string; // SOPフロー名
  sopNodeTimes: number | null; // 実行数
  cmtMain1: string; // 作業指示内容1
  cmtMain2: string; // 作業指示内容2
  cmtMain3: string; // 作業指示内容3
  unitNmJp: string; // 単位
  bRecVal1: string; // 修正前記録値1
  aRecVal1: string; // 修正後記録値1
  bRecVal2: string; // 修正前記録値2
  aRecVal2: string; // 修正後記録値2
  bRecVal3: string; // 修正前記録値3
  aRecVal3: string; // 修正後記録値3
  bRecVal4: string; // 修正前記録値4
  aRecVal4: string; // 修正後記録値4
  bRecVal5: string; // 修正前記録値5
  aRecVal5: string; // 修正後記録値5
  cmtDts: string; // 記録修正日時
  modUsr: string; // 修正者
  modExpl: string; // 修正コメント
};

// 秤量前後SOP修正履歴 リスト部分
export type GetWeighingSOPHistoryModifyInitResList = {
  // 秤量前後SOP記録初期表示 続く階層2のリストを保持
  modHistoryList: GetWeighingSOPHistoryModifyInitResListData[];
};

// 秤量前後SOP修正履歴 レスポンスデータ
export type GetWeighingSOPHistoryModifyInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingSOPHistoryModifyInitResList;
};

// 秤量前後SOP異状履歴初期表示 リクエストデータ
export type GetWeighingSOPHistoryDeviantInitRequestData = {
  sopFlowNo: string; // SOPフローNo
  sopFlowLnum: number; // SOPフロー実行ログ番号
};

//  秤量前後SOP異状履歴 レスポンス データ部分
export type GetWeighingSOPHistoryDeviantInitResListData = {
  devCorrLv: number | null; // 異状レベル
  backgroundColor: string; // 背景色
  sopFlowNmJp: string; // SOPフロー名
  cmtMain1: string; // 作業指示内容1
  cmtMain2: string; // 作業指示内容2
  cmtMain3: string; // 作業指示内容3
  instVal: string; // 指示値
  unitNmJp: string; // 単位
  recVal: string; // 記録値
  thValType: string; // 判定種別
  thValLlmt: number | null; // 異状値下限
  thValUlmt: number | null; // 異状値上限
  edDts: string; // 異状発生日時
  modUsr: string; // 確認者
  modExpl: string; // 異状コメント
  cmtDts: string; // 確認日時
};

// 秤量前後SOP異状履歴初期表示 リスト部分
export type GetWeighingSOPHistoryDeviantInitInitResList = {
  // 秤量前後SOP異状初期表示 続く階層2のリストを保持
  devHistoryList: GetWeighingSOPHistoryDeviantInitResListData[];
};

// 秤量前後SOP異状履歴初期表示 レスポンスデータ
export type GetWeighingSOPHistoryDeviantInitInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingSOPHistoryDeviantInitInitResList;
};

// 秤量前後SOP記録修正初期表示 リクエストデータ
export type GetWeighingSOPRecordEditInitRequestData = {
  sopFlowNo: string; // SOPフローNo
  sopFlowLnum: number; // SOPフロー実行ログ番号
  sopNodeLnum: number; // SOPノード実行ログ番号
};

// 秤量前後SOP記録修正初期表示 レスポンス データ部分
export type GetWeighingSOPRecordEditInitResList = {
  cmtMain1: string; // 作業指示内容1
  cmtMain2: string; // 作業指示内容2
  cmtMain3: string; // 作業指示内容3
  instVal: string; // 指示値
  recVal1: string; // 記録値1
  recVal2: string; // 記録値2
  recVal3: string; // 記録値3
  recVal4: string; // 記録値4
  recVal5: string; // 記録値5
  recVal1Flg: string; // 記録値1フラグ
  recVal2Flg: string; // 記録値2フラグ
  recVal3Flg: string; // 記録値3フラグ
  recVal4Flg: string; // 記録値4フラグ
  recVal5Flg: string; // 記録値5フラグ
  updDts: string; // 更新日時
};

// 秤量前後SOP記録修正初期表示 レスポンスデータ
export type GetWeighingSOPRecordEditInitResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingSOPRecordEditInitResList;
};

// 秤量前後SOP記録修正実行 リクエストデータ
export type ModifyWeighingSOPRecordEditRequestData = {
  sopFlowNo: string; // SOPフローNo
  sopFlowLnum: number; // SOPフロー実行ログ番号
  sopNodeLnum: number; // SOPノード実行ログ番号
  modExpl: string; // 修正コメント
  recVal1: string; // 記録値1
  recVal2: string; // 記録値2
  recVal3: string; // 記録値3
  recVal4: string; // 記録値4
  recVal5: string; // 記録値5
  recDevExpl: string; // 異状コメント
  updDts: string; // 更新日時
};

// 秤量前後SOP記録修正実行  レスポンスデータ
export type ModifyWeighingSOPRecordEditRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 秤量前後SOP作業詳細記録検印 リクエストデータ
export type ModifyWeighingSOPDetailRecordStampRequestData = {
  sopFlowNo: string; // 秤量指示明細番号
  sopFlowLnum: number; // ラベル番号
  devExpl: string; // 異状確認コメント入力
  sopExpl: string; // SOPフロー確認コメント入力
  updDts: string; // 更新日時
};

// 秤量前後SOP作業詳細記録検印  レスポンスデータ
export type ModifyWeighingSOPDetailRecordStampEditRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 秤量前後SOP一覧検索 リクエストデータ
export type GetWeighingSOPListRequestData = {
  wgtInstGrpNo: string; // 秤量指示書番号
  sopFlowNmJp: string; // SOPフロー名
  stDtsFrom: string; // SOPフロー開始日(From)
  stDtsTo: string; // SOPフロー開始日(To)
  recConfirmFlg: string; // 確認状態
  wgtRoomNo: string; // 秤量室
};

// 秤量前後SOP一覧検索 レスポンス データ部分
export type GetWeighingSOPListResListData = {
  sopFlowNo: string; // SOPフローNo
  sopFlowLnum: number | null; // SOPフロー実行ログ番号
  recConfirmFlgDsp: string; // 確認状態
  sopFlowStsDsp: string; // フロー状態(表示用)
  devCorrLv: number | null; // 異状レベル
  backgroundColor: string; // 背景色
  sopFlowNmJp: string; // SOPフロー名
  stDts: string; // SOPフロー開始日時
  edDts: string; // SOPフロー終了日時
  recConfirmDts: string; // SOPフロー記録検印日時
  devExplDsp: string; // 異状コメント
  msgExplDsp: string; // 作業コメント
  modExplDsp: string; // 修正コメント
  wgtInstGrpNo: string; // 秤量指示書番号
  wgtInstGrpSts: string; // 秤量指示書状態
  recConfirmDevExpl: string; // 異状確認コメント
  recConfirmExpl: string; // 記録確認コメント
  wgtRoomNmJp: string; // 秤量室
  deviceNmJp: string; // 計量器
};

// 秤量前後SOP一覧検索 レスポンス リスト部分
export type GetWeighingSOPListResList = {
  wgtSopRecList: GetWeighingSOPListResListData[]; // 秤量前後SOP記録
};

// 秤量前後SOP一覧検索 レスポンスデータ
export type GetWeighingSOPListResData = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingSOPListResList;
};

// NOTE:秤量、製造内で共通、または似た定義があり、ややこしい箇所についてのみここで記載
// NOTE:cond_keyがm_sys_cmtのwhere句(cmt_cat)に絞って定数定義
// 標準コンボボックスwhere句定義
export const COMBO_BOX_WHERE = {
  // 修正コメント
  MODIFY: {
    // 秤量前後SOP記録詳細用(製造に共通あり)
    PRD_ODR_SOP_MOD: 'PRD_ODR_SOP_MOD',
  },
  // 異状コメント(各画面共通)
  DEVIANT: { COMMON: 'SOP_REC_MOD_DEV' },
} as const;

export type CheckWeighingSOPRecordEditRequestData = {
  sopFlowNo: string; // SOPフローNo
  sopFlowLnum: number; // SOPフロー実行ログ番号
  sopNodeLnum: number; // SOPノード実行ログ番号
  recVal: string; // 記録値
};

export type CheckWeighingSOPRecordEditRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};
