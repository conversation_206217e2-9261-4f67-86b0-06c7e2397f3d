import { ref } from 'vue';
import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';

const { t } = i18n.global;

// 未承認マスタ情報の縦並び項目定義
export const getUnapprovedMasterInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 品目コード
    matNo: {
      label: { text: `${t('Mst.Chr.txtMatNo')} ${t('Mst.Chr.txtDelimiter')}` },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 品名
    matNm: {
      label: { text: `${t('Mst.Chr.txtMatNm')} ${t('Mst.Chr.txtDelimiter')}` },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 18,
    },
    // 処方コード
    rxNo: {
      label: {
        text: `${t('Mst.Chr.txtPrescriptionCode')} ${t('Mst.Chr.txtDelimiter')}`,
      },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 処方名
    rxNm: {
      label: {
        text: `${t('Mst.Chr.txtPrescriptionName')} ${t('Mst.Chr.txtDelimiter')}`,
      },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 18,
    },
  });

// 承認済マスタ情報の縦並び項目定義
export const getApprovedMasterInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 比較MBR番号
    compMbrNo: {
      label: {
        text: `${t('Mst.Chr.txtComparisonMBRNo')} ${t('Mst.Chr.txtDelimiter')}`,
      },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
    // 指図設定範囲 開始日
    validStYmd: {
      label: {
        text: `${t('Mst.Chr.txtOrderSettingRangeStartDate')} ${t('Mst.Chr.txtDelimiter')}`,
      },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 指図設定範囲 終了日
    validEdYmd: {
      label: {
        text: `${t('Mst.Chr.txtOrderSettingRangeEndDate')} ${t('Mst.Chr.txtDelimiter')}`,
      },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 18,
    },
  });

// 未承認マスタ一覧用テーブル設定
export const tablePropsDataUnapprovedMasterRef = ref<TabulatorTableIF>({
  pageName: 'UnapprovedMasterList',
  dataID: '', // 主キー。ユニークになるものを設定。
  height: '162px',
  column: [], // NOTE: カラムは動的に変わるため、画面表示時に作成する
  tableData: [],
  // NOTE:権限が親からprops経由で来るため、Init関数内でonSelectBtnsを生成する
  onSelectBtns: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
  // NOTE: TabulatorTable.vue の formatterCellColor を実行しセルの文字色を変更するためダミーで設定
  cellColor: {
    color: '#333',
    columns: [
      {
        modificationType: 'dummy',
      },
    ],
  },
});

// 承認済マスタ一覧用テーブル設定
export const tablePropsDataApprovedMasterRef = ref<TabulatorTableIF>({
  pageName: 'ApprovedMasterList',
  dataID: '', // 主キー。ユニークになるものを設定。
  height: '162px',
  column: [], // NOTE: カラムは動的に変わるため、画面表示時に作成する
  tableData: [],
  // NOTE:権限が親からprops経由で来るため、Init関数内でonSelectBtnsを生成する
  onSelectBtns: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
});
