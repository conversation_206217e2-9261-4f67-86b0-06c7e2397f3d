<template>
  <!-- 指図予定詳細ダイアログ -->
  <!-- 見出し 指図予定詳細 -->
  <DialogWindow
    :title="$t('Odr.Chr.txtOrderPlanDetail')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 見出し 小日程計画詳細 -->
    <BaseHeading
      level="2"
      :text="$t('Odr.Chr.txtScheduleDetail')"
      fontSize="24px"
    />
    <!-- 小日程計画詳細の見出し+テキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="processDataInfoShowRef.infoShowItems"
      :isLabelVertical="processDataInfoShowRef.isLabelVertical"
    />

    <!-- 見出し 製造工程一覧 -->
    <BaseHeading
      level="2"
      :text="$t('Odr.Chr.txtOrderProcessList')"
      fontSize="24px"
      class="Util_mt-16"
    />
    <!-- 製造工程一覧テーブル -->
    <TabulatorTable
      :propsData="tablePropsDataProcessListRef"
      :routerName="props.routerName"
      @selectRow="updateSelectedRow"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 構成品予定詳細ダイアログ -->
  <OdrMasterRxBomList
    :isClicked="isClickedShowMasterRxBomListDialogRef"
    :skdNo="initResponseData.skdNo"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    :routerName="props.routerName"
  />
  <!-- SOPフロー予定ダイアログ -->
  <OdrMasterRxSopFlowList
    :isClicked="isClickedShowMasterRxSopFlowListDialogRef"
    :skdNo="initResponseData.skdNo"
    :selectedRowData="selectedRow"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
  />
  <!-- @submit="reflectPrescriptionInfo" -->
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import { useGetMasterPrescriptionProcessList } from '@/hooks/useApi';
import {
  GetApprovalScheduleListData,
  GetMasterPrescriptionProcessListResData,
  GetMasterPrescriptionProcessListResProcessData,
  GetMasterPrescriptionProcessListRequestData,
} from '@/types/HookUseApi/OdrTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import OdrMasterRxBomList from '@/components/fragment/odr/OdrApprovedScheduleList/OdrMasterRxBomList.vue';
import OdrMasterRxSopFlowList from '@/components/fragment/odr/OdrApprovedScheduleList/OdrMasterRxSopFlowList.vue';
import getProcessDataInfoShowItems from './odrMasterRxProcessList';

/**
 * 多言語
 */
const { t } = useI18n();

// 選択行情報の格納
let selectedRow: GetMasterPrescriptionProcessListResProcessData | null = null;

// 指図予定詳細ダイアログ初期値用 指図予定詳細取得APIのレスポンス
let initResponseData: GetMasterPrescriptionProcessListResData = {
  skdNo: '',
  planPrcNo: '',
  matDspNmJp: '',
  odrDts: '',
  skdAddExpl: '',
  denialExpl: '',
  rxPrcList: [],
};

// '構成品予定詳細' クリック
const isClickedShowMasterRxBomListDialogRef = ref<boolean>(false);
// 'SOP予定詳細' クリック
const isClickedShowMasterRxSopFlowListDialogRef = ref<boolean>(false);

// ダイアログの表示切替用定義
type DialogRefKey = 'fragmentDialogVisible' | 'messageBoxApiErrorVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

const processDataInfoShowRef = ref<InfoShowType>({
  infoShowItems: getProcessDataInfoShowItems(),
  isLabelVertical: true,
});

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  selectedRowData: GetApprovalScheduleListData | null; // 親ページの選択行情報
  privilegesBtnRequestData: CommonRequestType;
  routerName: string; // TabulatorTable権限 未選択チェック用
};

const props = defineProps<Props>();

// 製造工程一覧用テーブル設定
const tablePropsDataProcessListRef = ref<TabulatorTableIF>({
  pageName: 'ProcessList',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'prcSeq', // 主キー。ユニークになるものを設定。
  showRadio: true, // ラジオボタンとして使用。

  column: [
    // 製造工程順
    {
      title: 'Odr.Chr.txtOrderProcessSequence',
      field: 'prcSeq',
      width: COLUMN_WIDTHS.ODR.PRC_SEQ,
    },
    // 製造工程
    {
      title: 'Odr.Chr.txtOrderProcess',
      field: 'prcNmJp',
      width: COLUMN_WIDTHS.PRC_NM,
    },
    // 仕掛品名/工程品名
    {
      title: 'Odr.Chr.txtProcessMaterialName',
      field: 'prdDspNmJp',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    // 標準バッチ回数
    {
      title: 'Odr.Chr.txtStandardBatchTimes',
      field: 'stdBatchTimes',
      width: COLUMN_WIDTHS.BAT_NO,
    },
    // 標準生産量
    {
      title: 'Odr.Chr.txtStandardProductionQuantity',
      field: 'stdPrdQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 単位
    {
      title: 'Odr.Chr.txtUnit',
      field: 'unitNmJp',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
  ],
  // NOTE:権限が親からprops経由で来るため、Init関数内でonSelectBtnsを生成する
  onSelectBtns: [],
  customActionBtns: [
    {
      text: 'Odr.Chr.btnBillOfMaterialsPlanDetail', // 構成品予定詳細
      clickHandler: async () => {
        // 構成品予定詳細ダイアログを表示開始
        // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
        isClickedShowMasterRxBomListDialogRef.value =
          !isClickedShowMasterRxBomListDialogRef.value;
      },
    },
  ],
  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
});

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
];

// 選択行情報の更新
const updateSelectedRow = (
  v: GetMasterPrescriptionProcessListResProcessData | null,
) => {
  selectedRow = v;
};

/**
 * 指図予定詳細ダイアログの初期設定
 */
const odrMasterRxProcessListInit = async () => {
  if (props.selectedRowData === null) {
    return;
  }

  // 自身の選択行情報を初期化
  selectedRow = null;

  showLoading();
  // 指図予定詳細取得のAPIを行う。
  const requestData: GetMasterPrescriptionProcessListRequestData = {
    skdNo: props.selectedRowData.skdNo,
  };
  const { responseRef, errorRef } = await useGetMasterPrescriptionProcessList({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // ダイアログ表示初期値として、レスポンス情報を格納
    initResponseData = responseRef.value.data.rData;
  }

  // 製造指図情報レイアウト用初期値設定
  Object.entries(initResponseData).forEach(([key, value]) => {
    if (key in processDataInfoShowRef.value.infoShowItems) {
      processDataInfoShowRef.value.infoShowItems[key].infoShowModelValue =
        value.toString();
    }
  });

  // NOTE:ボタンに権限付与は親から渡されたものを設定するため、初期化はここで行う。
  tablePropsDataProcessListRef.value.onSelectBtns = [
    {
      text: 'Odr.Chr.btnSOPPlanDetail', // SOP予定詳細
      // NOTE:選択チェック用に権限付与。
      tabulatorActionId: props.privilegesBtnRequestData.btnId,
      clickHandler() {
        // SOPフロー予定ダイアログを表示開始
        // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
        isClickedShowMasterRxSopFlowListDialogRef.value =
          !isClickedShowMasterRxSopFlowListDialogRef.value;
      },
    },
  ];

  // テーブル設定
  tablePropsDataProcessListRef.value.tableData = initResponseData.rxPrcList;

  closeLoading();
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await odrMasterRxProcessListInit();
  },
);
</script>
