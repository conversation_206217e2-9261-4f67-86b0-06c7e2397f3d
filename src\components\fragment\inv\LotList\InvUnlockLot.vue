<template>
  <!-- ロットロック解除ダイアログ -->
  <DialogWindow
    :title="$t('Inv.Chr.txtUnlockLotIndividual')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkUnlockLotForm()"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="inventoryUnlockLotFormRef.formModel"
      :formItems="inventoryUnlockLotFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          inventoryUnlockLotFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <!-- ロットロック解除の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.inventoryUnlockLotConfirm"
    :dialogProps="messageBoxInventoryUnlockLotConfirmProps"
    :cancelCallback="() => closeDialog('inventoryUnlockLotConfirm')"
    :submitCallback="apiHandler"
  />
  <!-- ロットロック解除のチェックメッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.inventoryUnlockLotInfo"
    :dialogProps="messageBoxInventoryUnlockLotPropsRef"
    :submitCallback="closeAllDialog"
  />
  <!-- 保管場所チェック警告メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.checkZoneConfirm"
    :dialogProps="messageCheckZoneConfirmProps"
    :cancelCallback="() => closeDialog('checkZoneConfirm')"
    :submitCallback="
      () => {
        closeDialog('checkZoneConfirm');
        invUnlockLotInit();
      }
    "
  />
</template>
<script setup lang="ts">
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import onValidateHandler from '@/utils/validateHandler';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  InventoryListData,
  InventoryUnlockLotData,
  ModifyInventoryLotUnlockReq,
} from '@/types/HookUseApi/InvTypes';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import {
  useGetComboBoxDataStandard,
  useModifyInventoryLotUnlock,
  useGetInventoryLotUnlock,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import {
  CommonRequestType,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import {
  getInventoryUnlockLotFormItems,
  inventoryUnlockLotFormModel,
} from './invUnlockLot';

const inventoryUnlockLotFormRef = ref<CustomFormType>({
  formItems: getInventoryUnlockLotFormItems(),
  formModel: inventoryUnlockLotFormModel,
});

type Props = {
  selectedRowData: InventoryListData | null;
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
  isShowWarningConfirm: boolean;
};
const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

let unlockLotData: InventoryUnlockLotData = {
  matNo: '',
  matNm: '',
  lotNo: '',
  lotUpdDts: '',
};

type DialogRefKey =
  | 'singleButton'
  | 'inventoryUnlockLotConfirm'
  | 'inventoryUnlockLotInfo'
  | 'fragmentDialogVisible'
  | 'checkZoneConfirm';
const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  inventoryUnlockLotConfirm: false,
  inventoryUnlockLotInfo: false,
  fragmentDialogVisible: false,
  checkZoneConfirm: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxInventoryUnlockLotConfirmProps: DialogProps = {
  title: t('Inv.Chr.txtConfirm'),
  content: t('Inv.Msg.unlockLotIndividualInventoryCorrectionConfirm'),
  type: 'question',
};

const messageBoxInventoryUnlockLotPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageCheckZoneConfirmProps: DialogProps = {
  title: t('Inv.Msg.editRangeConfirm'),
  content: t('Inv.Msg.editInventoryList'),
  type: 'warning',
};

/**
 * ロットロック解除
 */
const checkUnlockLotForm = async () => {
  const validate =
    inventoryUnlockLotFormRef.value.customForm !== undefined &&
    (await inventoryUnlockLotFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (validate) {
    openDialog('inventoryUnlockLotConfirm');
  }
  return false;
};

/**
 * 確認メッセージ
 */
const apiHandler = async () => {
  closeDialog('inventoryUnlockLotConfirm');
  showLoading();
  const unlockLotInventoryFormModel: ExtendCommonRequestType<ModifyInventoryLotUnlockReq> =
    {
      ...props.privilegesBtnRequestData,
      lotSid: props.selectedRowData!.lotSid,
      lotUnlockExpl:
        inventoryUnlockLotFormRef.value.formModel.lotUnlockExpl.toString(),
      lotUpdDts: unlockLotData.lotUpdDts,
      msgboxTitleTxt: messageBoxInventoryUnlockLotConfirmProps.title,
      msgboxMsgTxt: messageBoxInventoryUnlockLotConfirmProps.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
    };
  // ロットロック解除
  const { responseRef, errorRef } = await useModifyInventoryLotUnlock(
    unlockLotInventoryFormModel,
  );
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    messageBoxInventoryUnlockLotPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxInventoryUnlockLotPropsRef.value.content =
      responseRef.value.data.rMsg;
    openDialog('inventoryUnlockLotInfo');
  }
  closeLoading();
  return true;
};

const closeAllDialog = () => {
  closeDialog('inventoryUnlockLotInfo');
  // W1A2911ロットロック解除ダイアログを閉じる
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData);
};

/**
 * 初期設定
 */
const invUnlockLotInit = async () => {
  if (!props.selectedRowData) return;
  showLoading();
  updateDialogChangeFlagRef(false);
  inventoryUnlockLotFormRef.value.formItems = getInventoryUnlockLotFormItems();
  // ロット在庫情報取得(ロック解除用)
  const { responseRef, errorRef } = await useGetInventoryLotUnlock({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedRowData.lotSid,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    unlockLotData = responseRef.value.data.rData;
    setFormModelValueFromApiResponse(inventoryUnlockLotFormRef, unlockLotData);
  }

  // 標準コンボボックスデータ取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtCatInvLotLockOff',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'LOCK_LOT_OFF' },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setCustomFormComboBoxOptionList(
      inventoryUnlockLotFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(
  () => props.isClicked,
  () => {
    if (!props.selectedRowData) return;
    if (props.isShowWarningConfirm) {
      openDialog('checkZoneConfirm');
      return;
    }
    invUnlockLotInit();
  },
);
</script>
