$namespace: 'Util';

.#{$namespace} {
  &_w-full {
    width: 100%;
  }

  @for $i from 0 through 10 {
    $val: #{$i * 8};
    &_mt-#{$val} {
      margin-top: #{$val}px !important;
    }
    &_mr-#{$val} {
      margin-right: #{$val}px !important;
    }
    &_mb-#{$val} {
      margin-bottom: #{$val}px !important;
    }
    &_ml-#{$val} {
      margin-left: #{$val}px !important;
    }
    &_mx-#{$val} {
      margin-inline: #{$val}px !important;
    }
    &_my-#{$val} {
      margin-block: #{$val}px !important;
    }
    &_pt-#{$val} {
      padding-top: #{$val}px !important;
    }
    &_pr-#{$val} {
      padding-right: #{$val}px !important;
    }
    &_pb-#{$val} {
      padding-bottom: #{$val}px !important;
    }
    &_pl-#{$val} {
      padding-left: #{$val}px !important;
    }
    &_px-#{$val} {
      padding-inline: #{$val}px !important;
    }
    &_py-#{$val} {
      padding-block: #{$val}px !important;
    }
  }
}
