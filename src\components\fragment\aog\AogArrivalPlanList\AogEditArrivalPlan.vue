<template>
  <!-- 入荷予定修正ダイアログ -->
  <DialogWindow
    :title="$t('Aog.Chr.txtAogEditArrivalPlan')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkEditForm()"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="AogArrivalPlanEditFormRef.formModel"
      :formItems="AogArrivalPlanEditFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          AogArrivalPlanEditFormRef.customForm = v;
        }
      "
      @changeFormModel="
        (changeFlag, { changeLogModList }) => {
          updateDialogChangeFlagRef(changeFlag);
          updateCurrentChangedFormModelMap(changeLogModList);
        }
      "
    />
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <!-- 入荷予定修正の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.editAogArrivalPlanConfirm"
    :dialogProps="messageBoxEditAogArrivalPlanConfirmProps"
    :cancelCallback="() => closeDialog('editAogArrivalPlanConfirm')"
    :submitCallback="apiHandler"
  />
  <!-- 入荷予定修正のチェックメッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.editAogArrivalPlan"
    :dialogProps="messageBoxEditAogArrivalPlanPropsRef"
    :submitCallback="closeAllDialog"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  AddAttBinData,
  AogPlanListData,
  AogPlanModData,
  ModifyAogPlanReq,
} from '@/types/HookUseApi/AogTypes';
import {
  CommonRequestType,
  LogModListType,
} from '@/types/HookUseApi/CommonTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import { getFilesForApiRequest, getRemovedFileKeys } from '@/utils/fileUpload';
import {
  useGetComboBoxDataStandard,
  useGetAogPlanMod,
  useModifyAogPlan,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  getAogArrivalPlanEditFormItems,
  AogArrivalPlanEditFormModel,
} from './aogEditArrivalPlan';

const AogArrivalPlanEditFormRef = ref<CustomFormType>({
  formItems: getAogArrivalPlanEditFormItems(),
  formModel: AogArrivalPlanEditFormModel,
});
let logModList: LogModListType = [];
const updateCurrentChangedFormModelMap = (changeLogModList: LogModListType) => {
  logModList = changeLogModList;
};

type Props = {
  isClicked: boolean;
  selectedRowData: AogPlanListData | null;
  privilegesBtnRequestData: CommonRequestType;
};

const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

let aogPlanModData: AogPlanModData = {
  planYmd: '',
  aogPlanNo: '',
  poDtlNo: '',
  matNo: '',
  matNm: '',
  mBpId: '',
  bpNm: '',
  makerLotNo: '',
  edNo: '',
  erpAogQty: '',
  erpUnitNm: '',
  aogQty: '',
  unitNm: '',
  poApNo: '',
  poDtlExpl: '',
  poApExpl: '',
  aogAttBinList: [
    {
      attBinNo: '',
      attBinFileNm: '',
    },
  ],
  planExpl: '',
  aogPlanUpdDts: '',
};

type DialogRefKey =
  | 'singleButton'
  | 'editAogArrivalPlanConfirm'
  | 'editAogArrivalPlan'
  | 'fragmentDialogVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  editAogArrivalPlanConfirm: false,
  editAogArrivalPlan: false,
  fragmentDialogVisible: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxEditAogArrivalPlanConfirmProps: DialogProps = {
  title: t('Aog.Chr.txtAogEditArrivalPlan'),
  content: t('Aog.Msg.editAogArrivalPlanCorrectionConfirm'),
  type: 'question',
};

const messageBoxEditAogArrivalPlanPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const FILE_AOG_ATT_BIN = {
  MODEL_KEY: 'aogAttBinList',
  NAME_KEY: 'attBinFileNm',
  FILE_KEY: 'attBinFile',
  UNIQUE_KEY: 'attBinNo',
} as const;

/**
 * 入荷予定修正（実行）
 */
const checkEditForm = async () => {
  const validate =
    AogArrivalPlanEditFormRef.value.customForm !== undefined &&
    (await AogArrivalPlanEditFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (validate) {
    // 入荷予定修正の確認メッセージ
    openDialog('editAogArrivalPlanConfirm');
  }
  return false;
};

/**
 * 入荷予定修正
 */
const apiHandler = async () => {
  closeDialog('editAogArrivalPlanConfirm');
  showLoading();
  const addAogAttBinList = await getFilesForApiRequest<AddAttBinData>(
    AogArrivalPlanEditFormRef.value.formModel[FILE_AOG_ATT_BIN.MODEL_KEY],
    {
      fileNameKey: FILE_AOG_ATT_BIN.NAME_KEY,
      fileKey: FILE_AOG_ATT_BIN.FILE_KEY,
    },
  );
  const modifyAogPlanData: ModifyAogPlanReq = {
    planYmd: AogArrivalPlanEditFormRef.value.formModel.planYmd.toString(),
    aogPlanNo: aogPlanModData.aogPlanNo,
    makerLotNo: AogArrivalPlanEditFormRef.value.formModel.makerLotNo.toString(),
    aogQty: AogArrivalPlanEditFormRef.value.formModel.aogQty.toString(),
    planExpl: AogArrivalPlanEditFormRef.value.formModel.planExpl.toString(),
    aogPlanUpdDts: aogPlanModData.aogPlanUpdDts,
    addAogAttBinList,
    delAogAttBinList: getRemovedFileKeys(
      AogArrivalPlanEditFormRef.value.formModel[FILE_AOG_ATT_BIN.MODEL_KEY],
      {
        initialFileList: aogPlanModData.aogAttBinList,
        fileKeyPropName: FILE_AOG_ATT_BIN.UNIQUE_KEY,
      },
    ),
    logModList,
  };
  // 入荷予定修正
  const { responseRef, errorRef } = await useModifyAogPlan({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxEditAogArrivalPlanConfirmProps.title,
    msgboxMsgTxt: messageBoxEditAogArrivalPlanConfirmProps.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    ...modifyAogPlanData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    messageBoxEditAogArrivalPlanPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxEditAogArrivalPlanPropsRef.value.content =
      responseRef.value.data.rMsg;
    openDialog('editAogArrivalPlan');
  }
  closeLoading();
  return true;
};

/**
 * ダイアログウィンドウを閉じる
 */
const closeAllDialog = async () => {
  closeDialog('editAogArrivalPlan');
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData);
};

// 初期表示データ設定
const aogEditArrivalPlanInit = async () => {
  if (!props.selectedRowData) return;
  showLoading();
  updateDialogChangeFlagRef(false);
  logModList = [];

  // 連携テストダイアログの初期化必要なら書く
  AogArrivalPlanEditFormRef.value.formItems = getAogArrivalPlanEditFormItems();

  // 2.入荷予定修正データ取得
  const { responseRef, errorRef } = await useGetAogPlanMod({
    ...props.privilegesBtnRequestData,
    aogPlanNo: props.selectedRowData!.aogPlanNo,
  });

  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }

  if (responseRef.value) {
    aogPlanModData = responseRef.value.data.rData;
    setFormModelValueFromApiResponse(
      AogArrivalPlanEditFormRef,
      aogPlanModData,
      {
        fileKeys: [
          {
            formModelKey: FILE_AOG_ATT_BIN.MODEL_KEY,
            fileNameKey: FILE_AOG_ATT_BIN.NAME_KEY,
            fileKeyPropName: FILE_AOG_ATT_BIN.UNIQUE_KEY,
          },
        ],
        commonRequestData: props.privilegesBtnRequestData,
      },
    );
  }

  // 標準コンボボックスデータ取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtAogPlan',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'AOG_PLAN' },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setCustomFormComboBoxOptionList(
      AogArrivalPlanEditFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(
  () => props.isClicked,
  async () => {
    await aogEditArrivalPlanInit();
  },
);
</script>
