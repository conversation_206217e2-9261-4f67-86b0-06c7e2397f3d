<template>
  <!-- 工程作業記録_SOP作業詳細ダイアログ -->
  <DialogWindow
    :title="$t('Prd.Chr.txtProcessSopFlowInfoList')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 見出し SOPフロー情報 -->
    <BaseHeading
      level="2"
      :text="$t('Prd.Chr.txtSopFlowInformation')"
      fontSize="24px"
    />
    <!-- SOPフロー情報 テキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="detailInfoInfoShowRef.infoShowItems"
      :isLabelVertical="detailInfoInfoShowRef.isLabelVertical"
    />

    <!-- 見出し 作業実施記録 -->
    <BaseHeading
      class="Util_mt-16"
      level="2"
      :text="$t('Prd.Chr.txtWorkOperationRecord')"
      fontSize="24px"
    />
    <!-- 作業実施一覧リストテーブル -->
    <TabulatorTable
      :propsData="tablePropsDataPrcSOPFlowInfoListInitListRef"
      :routerName="props.routerName"
      @selectRows="updateSelectedRows"
      @displayedAllRows="displayedAllRowsRef"
      @clickBtnColumn="updateEmitSelectedRow"
    />
    <!-- 画面下部エリア -->
    <div>
      <!-- 異状確認補足コメント(2行) -->
      <div class="prc-sop-flow-info-list_cautionary-item">
        <div class="prc-sop-flow-info-list_cautionary">
          <span>{{ $t('Prd.Chr.txtRecordGuide') }}</span>
        </div>
        <div class="prc-sop-flow-info-list_cautionary-space"></div>
        <div class="prc-sop-flow-info-list_cautionary">
          <span>{{ $t('Prd.Chr.contentDifferentConfirmComent') }}</span>
        </div>
      </div>
      <div>
        <CustomForm
          :formModel="dialogFormRef.formModel"
          :formItems="dialogFormRef.formItems"
          @visible="
            (v: CustomFormType['customForm']) => {
              dialogFormRef.customForm = v;
            }
          "
          @changeFormModel="updateCustomFormChangeFlag"
        />
      </div>
      <!-- 工程SOP 承認ボタン -->
      <div class="prc-sop-flow-info-list_bottom-button">
        <ButtonEx
          type="primary"
          size="normal"
          :text="t('Prd.Chr.btnProcessSOPApprove')"
          :disabled="disabledRecordSealButtonRef"
          @click="handleSopFlowRecord"
        />
      </div>
    </div>
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 工程作業記録_SOP記録検印実行前確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPrcSOPFlowInfoListConfrimVisible"
    :dialogProps="msgBoxPrcSOPFlowInfoListConfirmPropsRef"
    :cancelCallback="
      () => closeDialog('messageBoxPrcSOPFlowInfoListConfrimVisible')
    "
    :submitCallback="() => openDialog('messageBoxPrcSOPFlowInfoListVisible')"
  />
  <!-- 工程作業記録_SOP作業詳細記録検印の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPrcSOPFlowInfoListVisible"
    :dialogProps="messageBoxPrcSOPFlowInfoListPropsRef"
    :cancelCallback="() => closeDialog('messageBoxPrcSOPFlowInfoListVisible')"
    :submitCallback="requestApiModifySOPFlowRecord"
  />
  <!-- 工程作業記録_SOP記録検印実行完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPrcSOPFlowInfoListFinishedVisible"
    :dialogProps="messageBoxPrcSOPFlowInfoListFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxModifySOPFlowRecordFinished"
  />
  <!-- 工程作業記録_製造記録修正ダイアログ -->
  <PrdPrcModify
    :selectedRowData="emitSelectedRow"
    :isClicked="isClickedPrcModListDialogRef"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    @submit="handlePrcSOPFlowInfoSubmit()"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import onValidateHandler from '@/utils/validateHandler';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import { rules } from '@/utils/validator';
import CONST from '@/constants/utils';
import { toNumberOrNull } from '@/utils/index';
import {
  TabulatorTableIF,
  CustomOptionsData,
} from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import createMessageBoxForm from '@/utils/commentMessageBox';
import ButtonEx from '@/components/parts/ButtonEx.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import {
  useGetSopFlowInit,
  useGetComboBoxDataStandard,
  useModifySopFlowRecord,
} from '@/hooks/useApi';
import InfoShow from '@/components/parts/InfoShow.vue';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import { InfoShowType } from '@/types/InfoShowTypes';
import {
  GetSopFlowInitData,
  GetProcessFlowListData,
  GetSopFlowInitResList,
  GetSopFlowInitRequestData,
  ModifySopFlowRecordRequestData,
} from '@/types/HookUseApi/PrdTypes';
import {
  CommonRequestType,
  ComboBoxDataStandardReturnData,
} from '@/types/HookUseApi/CommonTypes';
import PrdPrcModify from '@/components/fragment/prd/PrdPrcFlowList/PrdPrcModify.vue';
import {
  getInfoShowItems,
  getDialogFormItems,
  dialogFormModel,
} from './prdPrcSOPFlowInfoList';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxPrcSOPFlowInfoListConfrimVisible'
  | 'messageBoxPrcSOPFlowInfoListVisible'
  | 'messageBoxPrcSOPFlowInfoListFinishedVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxPrcSOPFlowInfoListConfrimVisible: false,
  messageBoxPrcSOPFlowInfoListVisible: false,
  messageBoxPrcSOPFlowInfoListFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  commonRejectHandler,
  updateDialogChangeFlagRef,
} = useDialog(initialState);

type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  sopFlowNo: string; // 親ダイアログのsopFlowNo
  sopFlowLnum: number; // 親ダイアログのsopFlowLnum
  routerName: string;
  selectedRowData: GetProcessFlowListData | null;
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

const emit = defineEmits(['submit']);

// 活性チェックボックス数
let enableCheckboxCount = 0;
// 検印ボタン活性・非活性切り替え
const disabledRecordSealButtonRef = ref<boolean>(true);

const detailInfoInfoShowRef = ref<InfoShowType>({
  infoShowItems: getInfoShowItems(),
  isLabelVertical: true,
});

// 工程作業記録_SOP作業詳細初期表示のレスポンス リスト部分
let initResponseData: GetSopFlowInitResList = {
  sopFlowList: [],
  cmtFlowDev: 0,
  devCorrLvFlg: false,
  recApprovBtnFlg: false,
  flowUpdDts: '',
  nodeUpdDts: '',
};

// カスタムフォーム変更状態 ダイアログ終了チェック用
let customFormChangeFlag: boolean = false;
// 選択行変更状態 ダイアログ終了チェック用
let selectedRowChangeFlag: boolean = false;
// 管理している状態フラグ全てを見てupdateDialogChangeFlagRefを更新する
const checkDialogChangeFlag = () => {
  updateDialogChangeFlagRef(customFormChangeFlag || selectedRowChangeFlag);
};
// カスタムフォーム状態更新
const updateCustomFormChangeFlag = (flag: boolean = false) => {
  customFormChangeFlag = flag;
  checkDialogChangeFlag();
};
// 選択行変更状態更新
const updateSelectedRowChangeFlag = (flag: boolean = false) => {
  selectedRowChangeFlag = flag;
  checkDialogChangeFlag();
};

// スクロールバー最下部検知
const isDisplayedAllRowsRef = ref<boolean>(false);

// TabulatorTableの行情報定義
type SOPDetailTableRowData = GetSopFlowInitData & {
  uniqueKey: string; // テーブル用主キー
};
// 選択行情報(複数)の格納
let selectedRows: SOPDetailTableRowData[] = [];

/**
 * 工程SOP承認ボタン活性・非活性切り替え
 */
const switchDisabledPrdRecSealButton = () => {
  if (
    isDisplayedAllRowsRef.value &&
    // NOTE:20250616時点、全チェックボタンの機能を利用していないが、利用した場合は無効化されたチェックボックスもカウントされる挙動となる。
    //      活性数と選択数の一致で見ていると検印ボタンが活性化出来なくなる可能性がある。
    //      エンバグを防ぐため修正しないが、全チェックボタンを利用する場合は確実に修正する必要がある。
    enableCheckboxCount === selectedRows.length &&
    // recApprovBtnFlgがfalseの場合、ボタンを活性化する
    initResponseData.recApprovBtnFlg === false
  ) {
    disabledRecordSealButtonRef.value = false;
  } else {
    disabledRecordSealButtonRef.value = true;
  }
};

// チェックボックス選択時処理
const updateSelectedRows = (v: SOPDetailTableRowData[]) => {
  selectedRows = v;
  // 活性化非活性化の切り替え処理
  switchDisabledPrdRecSealButton();

  if (v.length > 0) {
    updateSelectedRowChangeFlag(true);
  } else {
    updateSelectedRowChangeFlag(false);
  }
};

const dialogFormRef = ref<CustomFormType>({
  formItems: getDialogFormItems(),
  formModel: dialogFormModel,
});

// スクロールバー最下部検知時処理
const displayedAllRowsRef = () => {
  isDisplayedAllRowsRef.value = true;
  switchDisabledPrdRecSealButton();
};

let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;

const messageBoxForm = createMessageBoxForm('message', 'cmtWarning');
// 工程作業記録_SOP記録検印押下後の警告ダイアログ（コメントあり）
const messageBoxPrcSOPFlowInfoListPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleProcessSopFlowRecordConfirm'),
  content: t('Prd.Msg.contentProcessSopFlowRecordConfirm'),
  isPrompt: true,
  isSkipValidation: true, // 任意入力とする
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});
// 工程作業記録_SOP記録検印押下後の確認メッセージ
const msgBoxPrcSOPFlowInfoListConfirmPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleProcessSopFlowRecordConfirm'),
  content: t('Prd.Msg.contentSopStampRecordFixConfirm'),
  type: 'info',
});
// 工程作業記録_SOP記録検印の完了メッセージボックス
const messageBoxPrcSOPFlowInfoListFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});
// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 入力付き確認メッセージボックスの入力を初期化
const clearInputMessageBoxForm = () => {
  if ('isPrompt' in messageBoxPrcSOPFlowInfoListPropsRef.value) {
    messageBoxPrcSOPFlowInfoListPropsRef.value.formItems.message.formModelValue =
      '';
  }
};

// '記録修正' クリック 工程作業記録_製造記録修正ダイアログへ遷移
const isClickedPrcModListDialogRef = ref<boolean>(false);
/**
 * 記録修正ボタン押下時
 */
const clickBtnColumn = () => {
  isClickedPrcModListDialogRef.value = !isClickedPrcModListDialogRef.value;
};

// 選択行情報(単体)の格納
let emitSelectedRow: GetSopFlowInitData | null = null;
// 記録修正ボタン押下時処理
const updateEmitSelectedRow = (v: GetSopFlowInitData | null) => {
  emitSelectedRow = v;
  // 活性化非活性化の切り替え処理
  clickBtnColumn();
};

// 工程作業記録_SOP作業詳細用テーブル設定
const tablePropsDataPrcSOPFlowInfoListInitListRef = ref<TabulatorTableIF>({
  pageName: 'PrdPrcSOPFlowInfoList',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'uniqueKey', // 主キー。ユニークになるものを設定。
  tableBtns: [],
  showCheckbox: {
    show: true,
    condition: 'checkboxFlg',
    // 1: 非活性, 0: 活性
    conditionValue: 0,
    allAllowed: false,
  },
  btnColumn: {
    frozen: true, // 凍結列
    columnWidth: 70, // ボタン列幅
    condition: 'recModBtnFlg',
    // 1: 非活性, 0: 活性
    conditionValue: 0,
    btnProps: {
      text: t('Prd.Chr.btnRecordModify'),
      type: 'secondary',
      size: 'tabulator',
    },
  },

  column: [
    // 実行数
    {
      title: 'Prd.Chr.txtSopNodeTimes',
      field: 'sopNodeTimes',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.SOP_NODE_TIMES,
    },
    // 異状レベル
    {
      title: 'Prd.Chr.txtDeviationLevel',
      field: 'devCorrLv',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.DEV_LV,
    },
    // 作業指示内容
    {
      title: 'Prd.Chr.txtWorkInstructionDetail',
      field: 'cmtMain',
      width: COLUMN_WIDTHS.PRD.CMT_MAIN,
    },
    // 指示内容
    {
      title: 'Prd.Chr.txtInstructionDetail',
      field: 'instVal',
      width: COLUMN_WIDTHS.INST_VAL,
    },
    // 修正回数
    {
      title: 'Prd.Chr.txtModifyCount',
      field: 'recModTimes',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.PRD.CMT_TIMES,
    },
    // 記録値1
    {
      title: 'Prd.Chr.txtRecordValue1',
      field: 'recVal1',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 記録値2
    {
      title: 'Prd.Chr.txtRecordValue2',
      field: 'recVal2',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 記録値3
    {
      title: 'Prd.Chr.txtRecordValue3',
      field: 'recVal3',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 記録値4
    {
      title: 'Prd.Chr.txtRecordValue4',
      field: 'recVal4',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 記録値5
    {
      title: 'Prd.Chr.txtRecordValue5',
      field: 'recVal5',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 異状下限
    {
      title: 'Prd.Chr.txtDeviationLowerLimit',
      field: 'thValLlmt',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.PRD.VAL_LMT,
    },
    // 異状上限
    {
      title: 'Prd.Chr.txtDeviationUpperLimit',
      field: 'thValUlmt',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.PRD.VAL_LMT,
    },
    // 参考値1
    {
      title: 'Prd.Chr.txtReferenceValue1',
      field: 'refVal1',
      width: COLUMN_WIDTHS.PRD.REF_VAL,
    },
    // 参考値2
    {
      title: 'Prd.Chr.txtReferenceValue2',
      field: 'refVal2',
      width: COLUMN_WIDTHS.PRD.REF_VAL,
    },
    // 参考値3
    {
      title: 'Prd.Chr.txtReferenceValue3',
      field: 'refVal3',
      width: COLUMN_WIDTHS.PRD.REF_VAL,
    },
    // 参考値4
    {
      title: 'Prd.Chr.txtReferenceValue4',
      field: 'refVal4',
      width: COLUMN_WIDTHS.PRD.REF_VAL,
    },
    // 参考値5
    {
      title: 'Prd.Chr.txtReferenceValue5',
      field: 'refVal5',
      width: COLUMN_WIDTHS.PRD.REF_VAL,
    },
    // 単位
    {
      title: 'Prd.Chr.txtUnit',
      field: 'instUnitText',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    // SOPヘルプ表示日時
    {
      title: 'Prd.Chr.txtSopHelpDts',
      field: 'helpDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // 記録日時
    {
      title: 'Prd.Chr.txtRecordDate',
      field: 'recDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // 記録者
    {
      title: 'Prd.Chr.txtRecordUser',
      field: 'recUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // D記録
    {
      title: 'Prd.Chr.txtDRecord',
      field: 'dcheckVal',
      width: COLUMN_WIDTHS.PRD.D_CHK_VAL,
    },
    // D記録日時
    {
      title: 'Prd.Chr.txtDRecordDate',
      field: 'dcheckDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // D記録者
    {
      title: 'Prd.Chr.txtDRecordUser',
      field: 'dcheckUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 複数作業者
    {
      title: 'Prd.Chr.txtMultipleWorkUser',
      field: 'workUsr',
      width: COLUMN_WIDTHS.PRD.WORK_USR_EXIST,
    },
    // 異状コメント
    {
      title: 'Prd.Chr.txtDeviationComment',
      field: 'devExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 作業コメント
    {
      title: 'Prd.Chr.txtWorkComment',
      field: 'msgExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 修正コメント
    {
      title: 'Prd.Chr.txtModifyComment',
      field: 'modExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    {
      title: '',
      field: 'operations',
      formatter: 'btn',
      hozAlign: 'center',
    },
    // 背景色指定隠しカラムとして運用
    {
      title: '',
      field: 'backgroundColor',
      hidden: true,
    },
    // チェックボックス活性・非活性判定の隠しカラム
    {
      title: '',
      field: 'checkboxFlg',
      hidden: true,
    },
    // 記録修正ボタン活性・非活性フラグの隠しカラム
    {
      title: '',
      field: 'recModBtnFlg',
      hidden: true,
    },
    // セレクトボタン用key隠しカラムとして運用
    {
      title: '',
      field: 'uniqueKey',
      hidden: true,
    },
  ],
  rowColor: {
    useColor: true,
    colorColumn: 'backgroundColor',
  },
  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
  tableId: 'detail-table',
  hideCheckboxTitleFormatter: true,
  textWrapColumns: ['cmtMain'],
  rowHeight: 60,
});

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    clickHandler: () => {
      // キャンセルボタン押下時のチェックボックス選択解除
      tablePropsDataPrcSOPFlowInfoListInitListRef.value.selectRowsData = [];
      return commonRejectHandler();
    },
  },
];

/**
 * 作業指示コメント結合
 */
const joinCmtMain = (res: CustomOptionsData) => {
  const comments = [res.cmtMain1, res.cmtMain2, res.cmtMain3].filter(Boolean);
  res.cmtMain = comments.join('<br>');
};

/**
 * テーブルデータチェック
 */
const checkTableData = (tableData: CustomOptionsData[]) => {
  // 活性コンボボックス数初期化
  enableCheckboxCount = 0;
  // 異状件数数初期化
  tableData.forEach((res) => {
    res.recModBtnFlg = toNumberOrNull(res.recModBtnFlg) ?? 1;
    res.checkboxFlg = toNumberOrNull(res.checkboxFlg) ?? 1;
    if (res.checkboxFlg === 0) {
      enableCheckboxCount += 1;
    }
    // 作業指示コメント結合
    joinCmtMain(res);
  });
};

/**
 * 画面状態の初期化
 */
const resetStatus = () => {
  // 活性チェックボックス数
  enableCheckboxCount = 0;
  // スクロールバー最下部検知
  isDisplayedAllRowsRef.value = false;
  // 工程SOP 承認ボタン活性・非活性切り替え
  disabledRecordSealButtonRef.value = true;
  // 選択行情報(複数)
  selectedRows = [];
  updateDialogChangeFlagRef(false);
  customFormChangeFlag = false;
  selectedRowChangeFlag = false;
};

/**
 * 工程作業記録_SOP作業詳細ダイアログの初期設定
 */
const prdPrcSOPFlowInfoListInit = async () => {
  if (
    props.sopFlowNo === '' ||
    props.sopFlowLnum === 0 ||
    props.selectedRowData === null
  ) {
    return;
  }
  resetStatus();
  showLoading();

  // 工程作業記録_SOP作業詳細初期表示のAPIを行う。
  const requestData: GetSopFlowInitRequestData = {
    sopFlowNo: props.sopFlowNo,
    sopFlowLnum: props.sopFlowLnum,
    prcNo: props.selectedRowData.prcNo,
  };

  // 工程作業記録_SOP作業詳細初期表示API実行
  const { responseRef, errorRef } = await useGetSopFlowInit({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    initResponseData = responseRef.value.data.rData;

    if (initResponseData.devCorrLvFlg === false) {
      // 必須外す設定
      dialogFormRef.value.formItems.devModExpl.rules = [
        rules.length(64, t('Cm.Chr.txtLength', [64])),
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      ];
    } else {
      // 必須付与設定
      dialogFormRef.value.formItems.devModExpl.rules = [
        rules.required('textComboBox'), // 必須
        rules.length(64, t('Cm.Chr.txtLength', [64])),
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      ];
    }

    // 再検索時にバリデーション変更の可能性があるため状態初期化
    if (dialogFormRef.value.customForm) {
      dialogFormRef.value.customForm.clearValidate('devModExpl');
    }

    const tableData: SOPDetailTableRowData[] = [];
    // 行情報にユニークキーを追加して格納
    initResponseData.sopFlowList.forEach((res) => {
      const tableRowData: SOPDetailTableRowData = {
        ...res,
        uniqueKey: `${res.sopFlowNo}-${res.sopFlowLnum}-${res.sopNodeLnum}`,
      };
      tableData.push(tableRowData);
    });
    // ダイアログ表示初期値として、レスポンス情報を格納
    tablePropsDataPrcSOPFlowInfoListInitListRef.value.tableData = tableData;
    checkTableData(tablePropsDataPrcSOPFlowInfoListInitListRef.value.tableData);

    // SOPフロー情報レイアウト用初期値設定
    Object.entries(initResponseData).forEach(([key, value]) => {
      if (key in detailInfoInfoShowRef.value.infoShowItems) {
        detailInfoInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });
  }
  // SOPフロー情報レイアウト用初期値設定
  Object.entries(props.selectedRowData).forEach(([key, value]) => {
    if (key in detailInfoInfoShowRef.value.infoShowItems) {
      detailInfoInfoShowRef.value.infoShowItems[key].infoShowModelValue =
        value?.toString() ?? '';
    }
  });

  // 標準コンボボックスデータ取得
  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'devModExpl', // 異状確認コメント
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_PRC_DEV_CONF' },
      },
      {
        cmbId: 'cmtWarning',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_PRC_SOP_CONF' },
      },
    ],
  });

  // 標準コンボボックスのデータが取得できていれば画面上に反映
  if (
    comboBoxDataStandardReturnData &&
    comboBoxDataStandardReturnData.rData.rList.length > 0
  ) {
    setCustomFormComboBoxOptionList(
      dialogFormRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }

  if (
    'isPrompt' in messageBoxPrcSOPFlowInfoListPropsRef.value &&
    comboBoxDataStandardReturnData
  ) {
    const resetData = createMessageBoxForm('message', 'cmtWarning');
    messageBoxPrcSOPFlowInfoListPropsRef.value.formItems = resetData.formItems;

    // NOTE:共通コンボボックス情報を元にコメント付き警告メッセージを設定
    setCustomFormComboBoxOptionList(
      messageBoxPrcSOPFlowInfoListPropsRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }

  closeLoading();
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

const handlePrcSOPFlowInfoSubmit = () => {
  tablePropsDataPrcSOPFlowInfoListInitListRef.value.selectRowsData = []; // 選択行情報を初期化
  // 選択状態を復元
  if (selectedRows.length > 0) {
    // ユニークキーを取得して格納
    tablePropsDataPrcSOPFlowInfoListInitListRef.value.selectRowsData =
      selectedRows.map((row) => row.uniqueKey);
  }
  prdPrcSOPFlowInfoListInit();
};

const handleSopFlowRecord = async () => {
  // 下記チェックを行い、チェックOKなら処理継続する。
  // 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    dialogFormRef.value.customForm !== undefined &&
    (await dialogFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return;
  }

  // NOTE:「確認→コメント」の順序で開く
  // 次のダイアログを表示
  clearInputMessageBoxForm();
  openDialog('messageBoxPrcSOPFlowInfoListConfrimVisible');
};

// 工程作業記録_工程SOP承認実行のAPIリクエスト処理
const requestApiModifySOPFlowRecord = async () => {
  closeDialog('messageBoxPrcSOPFlowInfoListConfrimVisible');
  closeDialog('messageBoxPrcSOPFlowInfoListVisible');

  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    // 署名ダイアログがキャンセルされた場合の処理
    console.error('署名ダイアログがキャンセルされました', error);
    return;
  }

  // NOTE:型ガード用のチェック。
  if (
    props.sopFlowNo === null ||
    props.sopFlowLnum === null ||
    props.selectedRowData === null
  )
    return;
  if (!('isPrompt' in messageBoxPrcSOPFlowInfoListPropsRef.value)) return;

  showLoading();

  // 工程作業記録_工程SOP承認APIを実行する。
  const requestData: ModifySopFlowRecordRequestData = {
    prcNo: props.selectedRowData.prcNo,
    sopFlowNo: props.sopFlowNo,
    sopFlowLnum: props.sopFlowLnum,
    // 修正後のテキストボックスの内容を入力
    devExpl: dialogFormRef.value.formItems.devModExpl.formModelValue.toString(),
    sopExpl:
      messageBoxPrcSOPFlowInfoListPropsRef.value.formItems.message.formModelValue.toString(),
    recDocVer: props.selectedRowData.recDocVer,
    flowUpdDts: initResponseData.flowUpdDts,
    nodeUpdDts: initResponseData.nodeUpdDts,
  };

  // NOTE:直前メッセージをキャッシュせず直書きしている。仕様変更等でメッセージ順序を入れ替えた際に意図しないものを送る可能性あり。要注意。
  const { responseRef, errorRef } = await useModifySopFlowRecord({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxPrcSOPFlowInfoListPropsRef.value.title,
    msgboxMsgTxt: messageBoxPrcSOPFlowInfoListPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    msgboxInputCmt:
      messageBoxPrcSOPFlowInfoListPropsRef.value.formItems.message.formModelValue.toString(),
    ...requestData,
  });

  if (errorRef.value) {
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // ・データベースを更新した後、以下のメッセージを表示する。
    // 工程作業記録_工程SOP承認実行完了
    messageBoxPrcSOPFlowInfoListFinishedPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxPrcSOPFlowInfoListFinishedPropsRef.value.content =
      responseRef.value.data.rMsg;

    closeLoading();
    openDialog('messageBoxPrcSOPFlowInfoListFinishedVisible');
  }
};

// 工程作業記録_工程SOP承認完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxModifySOPFlowRecordFinished = () => {
  emit('submit');
  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxPrcSOPFlowInfoListFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

// ダイアログ起動条件の監視。isClickedの真偽値が切り替わりを監視
watch(
  () => props.isClicked,
  async () => {
    // NOTE:ダイアログ起動時限定処理をここに記載。再検索用初期化処理が関数分かれていないため。
    // FormItems初期化
    dialogFormRef.value.formItems = getDialogFormItems();

    prdPrcSOPFlowInfoListInit();
  },
);
</script>
<style lang="scss" scoped>
$namespace: 'prc-sop-flow-info-list';
.#{$namespace} {
  // 警告文
  &_cautionary {
    font-size: 14px;
    color: $red330;
  }
  // 警告文 行間調整用
  &_cautionary-space {
    padding-top: 2px;
  }
  // 画面下部要素 ボタン(工程SOP 承認)
  &_bottom-button {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
