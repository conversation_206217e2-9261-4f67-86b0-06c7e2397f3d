import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

const tablePropsData: TabulatorTableIF = {
  pageName: 'invReturnInstructionDetails',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'lotNo',
  column: [
    {
      title: 'Inv.Chr.txtItemCode',
      field: 'matNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    {
      title: 'Inv.Chr.txtItemName',
      field: 'matNm',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    {
      title: 'Inv.Chr.txtManageNo',
      field: 'lotNo',
      width: COLUMN_WIDTHS.LOT_NO,
    },
    {
      title: 'Inv.Chr.txtReturnQuantity',
      field: 'rtnQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.INV.RTN_QTY,
    },
    {
      title: 'Inv.Chr.txtMesUnit',
      field: 'unitNm',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    {
      title: 'Inv.Chr.txtIndividualAmount',
      field: 'pkgCnt',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.INV.PKG_CNT,
    },
    {
      title: 'Inv.Chr.txtReturnFrom',
      field: 'rtnSrcZoneNm',
      width: COLUMN_WIDTHS.ZONE_NM,
    },
    {
      title: 'Inv.Chr.txtReturnTo',
      field: 'rtnDstZoneNm',
      width: COLUMN_WIDTHS.ZONE_NM,
    },
  ],
  tableData: [],
  noUseConditionSearch: true, // 絞り込み条件無し
  showConditionSearch: false, // ConditionSearch不要
};

export default tablePropsData;
