<template>
  <DialogWindow
    :title="$t('Sjg.Chr.txtSjgComponentInformationAttachments')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    :leftButtons="dialogLeftButtons"
    :width="CONST.OVERRIDE_DIALOG_WIDTH.WIDTH_1600"
  >
    <BaseHeading
      level="2"
      fontSize="24px"
      :text="$t('Sjg.Chr.txtOrderBomInfo')"
    />
    <div class="Sjg-component-information-confirmation_content-inspectionInfo">
      <InfoShow
        class="Util_mt-16"
        :infoShowItems="inspectionInfoShowRef.infoShowItems"
        :isLabelVertical="inspectionInfoShowRef.isLabelVertical"
        :fontSizeLabel="inspectionInfoShowRef.fontSizeLabel"
        :fontSizeContent="inspectionInfoShowRef.fontSizeContent"
      />
    </div>
    <div class="Util_mt-48">
      <BaseHeading level="2" fontSize="24px" :text="$t('Sjg.Chr.txtLblList')" />
      <TabulatorTable :propsData="tablePropsDialogRef" />
    </div>
    <div class="Util_mt-16">
      <BaseHeading
        level="2"
        fontSize="24px"
        :text="$t('Sjg.Chr.txtAogAttExistFlgNm')"
      />
      <p v-if="!aogAttExistFlg">{{ $t('Sjg.Chr.txtAogAttNotExist') }}</p>
      <CustomForm
        v-else
        :formModel="sjgComponentInformationAttachmentShowRef.formModel"
        :formItems="sjgComponentInformationAttachmentShowRef.formItems"
        @visible="
          (v: CustomFormType['customForm']) => {
            sjgComponentInformationAttachmentShowRef.customForm = v;
          }
        "
        class="Util_mt-8"
      />
    </div>
    <!-- 異常 -->
    <MessageBox
      v-if="dialogVisibleRef.singleButton"
      :dialogProps="messageBoxSingleButtonRef"
      :submitCallback="() => closeDialog('singleButton')"
    />
  </DialogWindow>
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import {
  SjgGetModListData,
  SjgAttBinListDataType,
  BomLblListData,
} from '@/types/HookUseApi/SjgTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import InfoShow from '@/components/parts/InfoShow.vue';
import CustomForm from '@/components/parts/CustomForm.vue';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import { InfoShowType } from '@/types/InfoShowTypes';
import { DialogProps } from '@/types/MessageBoxTypes';
import { useGetBomLblList } from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import CONST_FLAGS from '@/constants/flags';
import CONST from '@/constants/utils';
import {
  getInspectionInfoShowItems,
  sjgComponentInformationAttachmentFormModel,
  getSjgComponentInformationAttachmentFormItems,
  tablePropsData,
} from './sjgComponentInformationAttachments';

type Props = {
  isClicked: boolean;
  selectedRow: SjgGetModListData | null;
  privilegesBtnRequestData: CommonRequestType;
};

const { t } = useI18n();
const props = defineProps<Props>();
let aogAttExistFlg: boolean = false;

type DialogRefKey = 'infoConfirm' | 'fragmentDialogVisible' | 'singleButton';
const initialState: InitialDialogState<DialogRefKey> = {
  infoConfirm: false,
  fragmentDialogVisible: false,
  singleButton: false,
};
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
const tablePropsDialogRef = ref<TabulatorTableIF>({
  ...tablePropsData,
});

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const dialogLeftButtons = ref<DialogWindowProps['buttons']>([
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    clickHandler() {
      closeDialog('fragmentDialogVisible');
    },
  },
]);

// CustomFormの表示用Ref
const sjgComponentInformationAttachmentShowRef = ref<CustomFormType>({
  formItems: getSjgComponentInformationAttachmentFormItems(),
  formModel: sjgComponentInformationAttachmentFormModel,
});

const inspectionInfoShowRef = ref<InfoShowType>({
  infoShowItems: getInspectionInfoShowItems(),
  isLabelVertical: true,
  fontSizeLabel: '12px',
  fontSizeContent: '16px',
});

/**
 * 初期設定
 */
const sjgComponentInformationAttachmentsInit = async () => {
  if (!props.selectedRow) return;
  showLoading();

  inspectionInfoShowRef.value.infoShowItems.bomMatNo.infoShowModelValue =
    props.selectedRow!.bomMatNo;
  inspectionInfoShowRef.value.infoShowItems.bomLotNo.infoShowModelValue =
    props.selectedRow!.bomLotNo;
  inspectionInfoShowRef.value.infoShowItems.bomMatNm.infoShowModelValue =
    props.selectedRow!.bomMatNm;

  const sjgAttBinData: SjgAttBinListDataType = {
    bomList: [],
  };
  const { responseRef, errorRef } = await useGetBomLblList({
    ...props.privilegesBtnRequestData,
    prdLotSid: props.selectedRow.prdLotSid,
    bomLotSid: props.selectedRow.bomLotSid,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    openDialog('singleButton');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    const { rData } = responseRef.value.data;
    tablePropsDialogRef.value.tableData = rData.bomLblList.map(
      (item: BomLblListData) => {
        const cells = [
          item.logLotSts === CONST_FLAGS.SJG.LOG_LOT_STS_STATUS.FAIL &&
            'logLotStsNm',
          item.lotLotSts === CONST_FLAGS.SJG.LOG_LOT_STS_STATUS.FAIL &&
            'lotLotStsNm',
        ]
          .filter(Boolean)
          .join(',');
        const rectItem = {
          ...item,
          cells,
        };
        return rectItem;
      },
    );

    Object.entries(rData).forEach(([key, value]) => {
      if (key in inspectionInfoShowRef.value.infoShowItems) {
        inspectionInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });

    const attBinNmList = [
      rData.attBinNm1,
      rData.attBinNm2,
      rData.attBinNm3,
      rData.attBinNm4,
      rData.attBinNm5,
    ];
    const attBinNoList = [
      rData.attBinNo1,
      rData.attBinNo2,
      rData.attBinNo3,
      rData.attBinNo4,
      rData.attBinNo5,
    ];

    const sjgAttBinDataList = attBinNmList
      .map((nm, idx) => ({
        attBinFileNm: nm?.toString() ?? '',
        attBinNo: attBinNoList[idx]?.toString() ?? '',
      }))
      .filter((item) => item.attBinFileNm !== '' && item.attBinNo !== '');

    sjgAttBinData.bomList = sjgAttBinDataList;

    if (sjgAttBinData.bomList.length > 0) {
      aogAttExistFlg = true;
    }
  }

  setFormModelValueFromApiResponse(
    sjgComponentInformationAttachmentShowRef, // 出力：CustomFormのRef
    sjgAttBinData, // 入力：Apiレスポンスの中間保持データ
    {
      /* NOTE:
            fileModel.fileKeys: ファイル情報：ファイル添付（初期値）が必要なときに設定する
              formModelKey: FormItems[key].formRole === 'fileUpload'のときのkey,
              fileNameKey: APIで指定されているファイル名キー,
              fileKeyPropName: APIで指定されているファイル管理Noキー
        */
      fileKeys: [
        {
          formModelKey: 'bomList',
          fileNameKey: 'attBinFileNm',
          fileKeyPropName: 'attBinNo',
        },
      ],
      // ファイルダウンロード時に渡すAPI共通リクエストパラメータ
      commonRequestData: props.privilegesBtnRequestData,
    },
  );
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, sjgComponentInformationAttachmentsInit);
</script>
<style lang="scss" scoped></style>
