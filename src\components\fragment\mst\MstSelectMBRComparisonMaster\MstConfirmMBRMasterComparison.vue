<template>
  <!-- MBRマスタ比較確認ダイアログ -->
  <DialogWindow
    :title="$t('Mst.Chr.txtConfirmMBRMasterComparison')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    :class="'confirm-mbr-master-comparison'"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    width="98vw"
  >
    <!-- 見出し マスタ名 -->
    <BaseHeading level="2" :text="props.masterName" fontSize="20px" />
    <!-- MBR申請情報のテキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="unapprovedMasterInfoShowRef.infoShowItems"
      :isLabelVertical="unapprovedMasterInfoShowRef.isLabelVertical"
      labelWidth="100px"
    />
    <!-- 見出し 未承認マスタ -->
    <BaseHeading
      level="2"
      :text="$t('Mst.Chr.txtUnapprovedMaster')"
      fontSize="20px"
      fontWeight="normal"
      class="Util_mt-24"
    />
    <!-- 未承認マスタ一覧テーブル -->
    <TabulatorTable
      :propsData="tablePropsDataUnapprovedMasterRef"
      :routerName="props.routerName"
    />
    <!-- 見出し 承認済マスタ -->
    <BaseHeading
      level="2"
      :text="$t('Mst.Chr.txtApprovedMaster')"
      fontSize="20px"
      fontWeight="normal"
    />
    <!-- 承認済マスタ情報のテキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="approvedMasterInfoShowRef.infoShowItems"
      :isLabelVertical="approvedMasterInfoShowRef.isLabelVertical"
      labelWidth="170px"
    />
    <!-- 承認済マスタ一覧テーブル -->
    <TabulatorTable
      :propsData="tablePropsDataApprovedMasterRef"
      :routerName="props.routerName"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <div style="visibility: hidden">
    <span ref="hiddenSpan"></span>
  </div>
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { ColumnLayout } from '@/components/model/common/TabulatorTable/TabulatorTable';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { ConfirmMasterListData } from '@/types/HookUseApi/MstTypes';
import {
  useGetConfirmMBRMasterListCreate,
  useGetConfirmMBRMasterListApplication,
  useGetConfirmMBRMasterListApprove,
} from '@/hooks/useApi';
import {
  getUnapprovedMasterInfoShowItems,
  getApprovedMasterInfoShowItems,
  tablePropsDataUnapprovedMasterRef,
  tablePropsDataApprovedMasterRef,
} from './mstConfirmMBRMasterComparison';

/**
 * 多言語
 */
const { t } = useI18n();
const hiddenSpan = ref<HTMLDivElement | null>(null);

// ダイアログの表示切替用定義
type DialogRefKey = 'fragmentDialogVisible' | 'messageBoxApiErrorVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

const unapprovedMasterInfoShowRef = ref<InfoShowType>({
  infoShowItems: getUnapprovedMasterInfoShowItems(),
  isLabelVertical: false,
});

const approvedMasterInfoShowRef = ref<InfoShowType>({
  infoShowItems: getApprovedMasterInfoShowItems(),
  isLabelVertical: false,
});

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnClose'),
    type: 'primary',
    size: 'normal',
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
];

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  menuMode: string; // メニューモード
  mbrNo: string; // MBR番号
  matNo: string; // 品目コード
  rxNo: string; // 処方コード
  compMbrNo: string; // 比較MBR番号
  masterName: string; // マスタ名
  tableName: string; // テーブル名
  cdId: string; // コードID
  privilegesBtnRequestData: CommonRequestType;
  routerName: string; // TabulatorTable権限 未選択チェック用
};

const props = defineProps<Props>();

/**
 * コンテンツの幅を計算
 */
const getTextWidth = (text: string) => {
  hiddenSpan.value!.textContent = text;
  hiddenSpan.value!.style.fontSize = `14px`;
  return hiddenSpan.value!.offsetWidth;
};

/**
 * 一番広い列の幅を取得
 */
const getMaxLength = (text: string[]) => {
  let maxLength = 0;
  // 各要素の幅を計算し格納
  text.forEach((name: string) => {
    if (name) {
      const offsetWidth = getTextWidth(name) + 60;
      if (offsetWidth) {
        maxLength = Math.max(maxLength, offsetWidth);
      }
    }
  });
  return maxLength;
};

/**
 * MBRマスタ確認結果一覧取得
 */
const getConfirmMBRMasterList = async () => {
  switch (props.menuMode) {
    case '1': {
      const masterList = await useGetConfirmMBRMasterListApplication({
        ...props.privilegesBtnRequestData,
        mbrNo: props.mbrNo,
        matNo: props.matNo,
        rxNo: props.rxNo,
        compMbrNo: props.compMbrNo,
        tableName: props.tableName,
        cdId: props.cdId,
      });
      return masterList;
    }
    case '2': {
      const masterList = await useGetConfirmMBRMasterListApprove({
        ...props.privilegesBtnRequestData,
        mbrNo: props.mbrNo,
        matNo: props.matNo,
        rxNo: props.rxNo,
        compMbrNo: props.compMbrNo,
        tableName: props.tableName,
        cdId: props.cdId,
      });
      return masterList;
    }
    default: {
      const masterList = await useGetConfirmMBRMasterListCreate({
        ...props.privilegesBtnRequestData,
        mbrNo: props.mbrNo,
        matNo: props.matNo,
        rxNo: props.rxNo,
        compMbrNo: props.compMbrNo,
        tableName: props.tableName,
        cdId: props.cdId,
      });
      return masterList;
    }
  }
};

// 初回表示、再検索で呼び出される
// MBRマスタ確認結果一覧取得API呼び出し
const requestApiGetConsistencyCheckResultsList = async () => {
  showLoading();

  // MBRマスタ確認結果一覧を取得
  const { responseRef, errorRef } = await getConfirmMBRMasterList();

  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return Promise.reject(); // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // 未承認マスタ、承認済マスタ情報レイアウト用初期値設定
    Object.entries(responseRef.value.data.rData).forEach(([key, value]) => {
      if (key in unapprovedMasterInfoShowRef.value.infoShowItems) {
        unapprovedMasterInfoShowRef.value.infoShowItems[
          key
        ].infoShowModelValue = value?.toString() ?? '';
      }
      if (key in approvedMasterInfoShowRef.value.infoShowItems) {
        approvedMasterInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });

    // 検証環境一覧の各列の幅を求める
    const values = new Map();
    responseRef.value.data.rData.unapprovedMasterColumns.forEach((column) =>
      values.set(column.field, [column.title]),
    );
    responseRef.value.data.rData.unapprovedMasterList.forEach((environment) =>
      Object.keys(environment).forEach((field) => {
        if (values.has(field)) {
          values.get(field).push(environment[field]);
        }
      }),
    );
    const columns: ColumnLayout[] =
      responseRef.value.data.rData.unapprovedMasterColumns.map((column) => ({
        title: column.title,
        field: column.field,
        hidden: column.hidden,
        width: getMaxLength(values.get(column.field)),
      }));

    // 各セルの文字色を設定する
    const datas: ConfirmMasterListData[] =
      responseRef.value.data.rData.unapprovedMasterList.map(
        (unapprovedMaster) => {
          const updatedUnapprovedMaster = { ...unapprovedMaster }; // 新しいオブジェクトを作成
          const fields = Object.keys(unapprovedMaster);
          fields.forEach((field) => {
            const fieldMod = `${field}Mod`;
            if (fieldMod in unapprovedMaster) {
              const modValue = unapprovedMaster[fieldMod] as string;
              const fieldValue = unapprovedMaster[field] ?? '';
              let style = '';
              switch (modValue?.toUpperCase()) {
                case 'A':
                  style = `style='color: ${t('Mst.Chr.txtAdditionColor')}'`;
                  break;
                case 'M':
                  style = `style='color: ${t('Mst.Chr.txtModificationColor')}'`;
                  break;
                case 'D':
                  style = `style='color: ${t('Mst.Chr.txtDeletionColor')}'`;
                  break;
                default:
                  break;
              }
              // NOTE: 文字色を変更するため <span>タグ を追加
              // NOTE: ソート順を維持するため、data-value属性に元の値をセット
              updatedUnapprovedMaster[field] =
                `<span data-value='${fieldValue}' ${style}>${fieldValue}</span>`;
              // NOTE: field には <span>タグ をセットするので fieldMod にバックエンドから受け取った値をセット
              updatedUnapprovedMaster[fieldMod] = unapprovedMaster[field];
            }
          });
          return updatedUnapprovedMaster; // 更新されたオブジェクトを返す
        },
      );

    // 未承認マスタカラム一覧をカラムに反映
    tablePropsDataUnapprovedMasterRef.value.column = columns;

    // 未承認マスタ一覧をテーブルに反映
    tablePropsDataUnapprovedMasterRef.value.tableData = datas;

    // 承認済マスタカラム一覧をカラムに反映
    tablePropsDataApprovedMasterRef.value.column =
      responseRef.value.data.rData.approvedMasterColumns;

    // 承認済マスタ一覧をテーブルに反映
    tablePropsDataApprovedMasterRef.value.tableData =
      responseRef.value.data.rData.approvedMasterList;
  }

  closeLoading();

  return Promise.resolve();
};

/**
 * MBRマスタ比較確認ダイアログの初期設定
 */
const mstConsistencyCheckResultsInit = async () => {
  // InfoShow初期化
  unapprovedMasterInfoShowRef.value.infoShowItems =
    getUnapprovedMasterInfoShowItems();

  approvedMasterInfoShowRef.value.infoShowItems =
    getApprovedMasterInfoShowItems();

  // MBRマスタ確認結果一覧取得のAPI呼び出しと反映
  try {
    await requestApiGetConsistencyCheckResultsList();
  } catch {
    return;
  }

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await mstConsistencyCheckResultsInit();
  },
);
</script>
