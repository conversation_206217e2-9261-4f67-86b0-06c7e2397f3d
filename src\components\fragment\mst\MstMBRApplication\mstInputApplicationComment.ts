import { ref } from 'vue';
import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules, check } from '@/utils/validator';

const { t } = i18n.global;

// MBR作成情報の縦並び項目定義
export const getMBRCreationInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // MBR番号
    mbrNo: {
      label: { text: t('Mst.Chr.txtMBRNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
    // 品目コード
    matNo: {
      label: { text: t('Mst.Chr.txtMatNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 品名
    matNm: {
      label: { text: t('Mst.Chr.txtMatNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 16,
    },
    // 処方コード
    rxNo: {
      label: { text: t('Mst.Chr.txtPrescriptionCode') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 処方名
    rxNm: {
      label: { text: t('Mst.Chr.txtPrescriptionName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 16,
    },
  });

// 申請コメント入力ダイアログのアイテム定義
export const getMBRCreationFormItems: () => CustomFormType['formItems'] =
  () => ({
    validStYmd: {
      formModelValue: '',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Mst.Chr.txtOrderSettingRangeStartDate') }, // 指図設定範囲 開始日
      rules: [rules.required('date')],
      formRole: 'date',
      props: { modelValue: '', type: 'date', size: 'small' },
      span: 8,
    },
    validEdYmd: {
      formModelValue: '',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Mst.Chr.txtOrderSettingRangeEndDate') }, // 指図設定範囲 終了日
      rules: [rules.required('date'), rules.fromToDate()],
      formRole: 'date',
      props: { modelValue: '', type: 'date', size: 'small' },
      span: 16,
    },
    applicationComment: {
      formModelValue: '',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Mst.Chr.txtApplicationComment') }, // 申請コメント
      rules: [
        rules.required('textBox'),
        rules.length(128, t('Cm.Chr.txtLength', [128])),
      ],
      formRole: 'textBox',
      props: { type: 'textarea' },
      span: 24,
    },
  });

// 申請コメント入力ダイアログのモデル定義
export const mbrCreationFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getMBRCreationFormItems());

export const mbrCreationFormRef = ref<CustomFormType>({
  formItems: getMBRCreationFormItems(),
  formModel: mbrCreationFormModel,
});

/**
 * カレンダー前後チェック
 * @param {string} rule.field - フィールド
 * @param {string} rule.message - メッセージ
 * @param {string} rule.dataId -  データID
 * @param {string} value - データ
 * @param {void} callback -コールバック
 */
export const fromToDate = (
  rule: { field: string; message: string; dataId: string },
  value: string,
  callback: (val?: Error) => void,
) => {
  if (rule.field !== '') {
    const startDate =
      mbrCreationFormRef.value.formModel[rule.dataId].toString();
    if (check.fromToDate(startDate, value)) {
      const errorVal = new Error(rule.message);
      callback(errorVal);
    } else {
      callback();
    }
  }
};
