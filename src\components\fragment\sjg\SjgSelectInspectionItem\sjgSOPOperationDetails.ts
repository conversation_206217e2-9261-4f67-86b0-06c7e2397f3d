import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';

const { t } = i18n.global;
export const getSjgSOPOperationDetailsInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 製造指図番号
    odrNo: {
      label: { text: t('Sjg.Chr.txtOdrNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 品目コード
    matNo: {
      label: { text: t('Sjg.Chr.txtMatNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 品名
    matNm: {
      label: { text: t('Sjg.Chr.txtMatNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // SOPフロー名称
    sopFlowNm: {
      label: { text: t('Sjg.Chr.txtSopFlowNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // バッチNo
    batchNo: {
      label: { text: t('Sjg.Chr.txtBatchNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 実行数
    sopNodeTimes: {
      label: { text: t('Sjg.Chr.txtSopNodeTimes') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 異状レベル
    devCorrLvNm: {
      label: { text: t('Sjg.Chr.txtDeviationLevel') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 作業指示内容
    cmtMain: {
      label: { text: t('Sjg.Chr.txtCmtMain') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 指示内容
    instVal: {
      label: { text: t('Sjg.Chr.txtInstVal') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 記録
    recVal: {
      label: { text: t('Sjg.Chr.txtRecVal') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 記録日時
    edDts: {
      label: { text: t('Sjg.Chr.txtEdDts') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 記録者
    recUsrNm: {
      label: { text: t('Sjg.Chr.txtRecUsr') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // D記録
    dcheckVal: {
      label: { text: t('Sjg.Chr.txtDcheckVal') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // D記録日時
    dcheckEdDts: {
      label: { text: t('Sjg.Chr.txtDcheckEdDts') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // D記録者
    dcheckUsrNm: {
      label: { text: t('Sjg.Chr.txtDcheckUsr') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 複数作業者
    multipleUsrFlgNm: {
      label: { text: t('Sjg.Chr.txtMultipleUsrFlg') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 異状コメント
    devExpl: {
      label: { text: t('Sjg.Chr.txtDevExplFlg') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 修正コメント
    modExpl: {
      label: { text: t('Sjg.Chr.txtModExplFlg') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 作業コメント
    logExpl: {
      label: { text: t('Sjg.Chr.txtLogExplFlg') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
    // 確認必須
    recConfFlgNm: {
      label: { text: t('Sjg.Chr.txtRecConfFlg') },
      infoShowModelValue: '',
      infoShowRole: 'text',
    },
  });

export const getAttBinNoFormItems: () => CustomFormType['formItems'] = () => ({
  sopAttBinList: {
    formModelValue: [],
    formRole: 'fileUpload',
    onClickHandler() {},
    props: {
      fileList: [],
      hideTriggerBtn: true,
    },
    label: { text: t('Sjg.Chr.txtAttBinNo') },
  },
});

export const attBinNoFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getAttBinNoFormItems());
