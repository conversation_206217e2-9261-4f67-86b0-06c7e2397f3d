<template>
  <!-- 管理番号/製造番号検索ダイアログ -->
  <DialogWindow
    :title="$t('Inv.Chr.txtManageNoSearch')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="onClickedResolve"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- カスタムフォームを利用して内容表示を行う -->
    <CustomForm
      :formModel="invSelectLotIndividualPackagingFormRef.formModel"
      :formItems="invSelectLotIndividualPackagingFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          invSelectLotIndividualPackagingFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
    <div class="inv-select-lot-addition-dialog_btn Util_my-8 Util_mx-8">
      <ButtonEx
        type="secondary"
        size="normal"
        :text="$t('Cc.Chr.btnSearch')"
        @click="searchBtnClickHandler()"
      />
    </div>
    <div class="inv-select-lot-addition-dialog_tabulator-wrapper">
      <!-- 管理番号/製造番号一覧 -->
      <BaseHeading
        level="2"
        :text="$t('Inv.Chr.txtManageNoList')"
        fontSize="24px"
        class="Util_mt-16"
      />
      <!-- 共通のテーブル -->
      <TabulatorTable
        :propsData="tablePropsDialogRef"
        @selectRow="updateSelectedRow"
      />
    </div>
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <!-- レコード未選択のエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxUnselectedDataVisible"
    :dialogProps="messageBoxUnselectedData"
    :submitCallback="() => closeDialog('messageBoxUnselectedDataVisible')"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { useGetLotSearchInit, useGetInventoryLot } from '@/hooks/useApi';
import {
  GetInventoryLotInvList,
  GetInventoryLotReq,
} from '@/types/HookUseApi/InvTypes';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  CommonRequestType,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import CustomForm from '@/components/parts/CustomForm.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  getTablePropsData,
  getInvSelectLotIndividualPackagingFormItems,
  invSelectLotIndividualPackagingFormModel,
} from './invSelectLotIndividualPackaging';

type Props = {
  isClicked: boolean;
  dspNarrowType: string;
  privilegesBtnRequestData: CommonRequestType;
};
/**
 * 多言語
 */
const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

type DialogRefKey =
  | 'singleButton'
  | 'fragmentDialogVisible'
  | 'messageBoxUnselectedDataVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  fragmentDialogVisible: false,
  messageBoxUnselectedDataVisible: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// レコード未選択のメッセージボックス
const messageBoxUnselectedData: DialogProps = {
  title: t('Cm.Chr.txtUnselectedData'),
  content: t('Cm.Msg.unselectedData'),
  isSingleBtn: true,
  type: 'error',
};

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const invSelectLotIndividualPackagingFormRef = ref<CustomFormType>({
  formItems: getInvSelectLotIndividualPackagingFormItems(),
  formModel: invSelectLotIndividualPackagingFormModel,
});
// ダイアログ内テーブル設定
const tablePropsDialogRef = ref<TabulatorTableIF>(getTablePropsData());
// 選択行情報
let selectedRowData: GetInventoryLotInvList | null = null;

const onClickedResolve = () => {
  // レコード選択チェック
  if (selectedRowData === null || Object.keys(selectedRowData).length === 0) {
    openDialog('messageBoxUnselectedDataVisible');
    return false;
  }
  emit('submit', selectedRowData);
  return true;
};

/**
 * ロット情報取得(管理番号/製造番号検索用)
 */
const searchBtnClickHandler = async () => {
  showLoading();
  // ロット情報取得
  const apiRequestData: ExtendCommonRequestType<GetInventoryLotReq> = {
    ...props.privilegesBtnRequestData,
    dspNarrowType: props.dspNarrowType,
    matNo:
      invSelectLotIndividualPackagingFormRef.value.formModel.matNo.toString(),
    matNm:
      invSelectLotIndividualPackagingFormRef.value.formModel.matNm.toString(),
    lotNo:
      invSelectLotIndividualPackagingFormRef.value.formModel.lotNo.toString(),
  };
  const { responseRef, errorRef } = await useGetInventoryLot(apiRequestData);
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    tablePropsDialogRef.value.tableData = responseRef.value.data.rData.invList;
    updateDialogChangeFlagRef(true);
  }
  closeLoading();
  return true;
};

/**
 * 選択行情報の更新
 */
const updateSelectedRow = (v: GetInventoryLotInvList | null) => {
  selectedRowData = v;
};

/**
 * 初期設定
 */
const InvSelectLotIndividualPackagingInit = async () => {
  showLoading();
  updateDialogChangeFlagRef(false);

  // 連携テストダイアログの初期化必要なら書く
  invSelectLotIndividualPackagingFormRef.value.formItems =
    getInvSelectLotIndividualPackagingFormItems();
  // 画面の初期表示の時、一覧は何も表示しない。
  tablePropsDialogRef.value = getTablePropsData();
  // 管理番号/製造番号検索初期表示データ取得
  const { responseRef, errorRef } = await useGetLotSearchInit(
    props.privilegesBtnRequestData,
  );
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    openDialog('fragmentDialogVisible');
  }
  closeLoading();
  return true;
};

watch(
  () => props.isClicked,
  async () => {
    await InvSelectLotIndividualPackagingInit();
  },
);
</script>
<style lang="scss" scoped>
$namespace: 'inv-select-lot-addition-dialog';

.#{$namespace} {
  &_btn {
    display: flex;
    justify-content: flex-end;
  }
  &_tabulator-wrapper {
    border-top: 1px solid var(--el-border-color);
  }
}
</style>
