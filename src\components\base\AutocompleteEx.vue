<template>
  <el-tooltip
    :effect="CONST.TOOLTIP.EFFECT"
    :visible="visibleRef"
    :content="vBindProps.modelValue"
    :popperOptions="popperOption"
    :placement="CONST.TOOLTIP.PLACEMENT"
    popperClass="tooltip-popper"
  >
    <div :class="autocompleteClass">
      <el-autocomplete
        v-bind="vBindProps"
        :teleported="true"
        :placeholder="placeholder"
        @input="handleInputChange"
        @select="
          (v: Record<'value', string>) => emit('update:modelValue', v.value)
        "
        @change="(v: string) => emit('change', v)"
        @blur="(v: FocusEvent) => emit('blur', v)"
        @clear="() => emit('update:modelValue', '')"
        @focus="handleTooltipVisible(true)"
        @focusout="handleTooltipVisible(false)"
        @mouseenter="handleTooltipVisible(true)"
        @mouseleave="handleTooltipVisible(false)"
      >
        <template #default="{ item }">
          <div class="autocomplete-ex_item">{{ item.value }}</div>
        </template>
      </el-autocomplete>
    </div>
  </el-tooltip>
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue';
import { autocompleteEmits } from 'element-plus';
import { AutocompleteExProps } from '@/types/AutocompleteExTypes';
import SCSS from '@/constants/scssVariables';
import CONST from '@/constants/utils';
import popperOption from '@/utils/tooltip';
import excludeProps from '@/utils/excludeProps';

const props = withDefaults(defineProps<AutocompleteExProps>(), {
  size: 'small',
  width: SCSS.widthSmall,
  clearable: true,
  triggerOnFocus: true,
  validateEvent: false,
  disabled: false,
});

const visibleRef = ref<boolean>(false);
const vBindProps = computed(() => excludeProps(props, ['size', 'width']));
const autocompleteClass = computed(() => `autocomplete-ex_${props.size}`);
const handleTooltipVisible = (flag: boolean) => {
  if (props.isTooltipDisabled) return;
  visibleRef.value = vBindProps.value.modelValue === '' ? false : flag;
};
const placeholder = computed(() => (props.disabled ? '' : props.placeholder));

const emit = defineEmits(autocompleteEmits);

const handleInputChange = (v: string) => {
  emit('update:modelValue', v);
  if (!props.isTooltipDisabled) {
    visibleRef.value = !!v && v !== '';
  }
};
</script>
<style lang="scss" scoped>
$namespace: 'autocomplete-ex';
$widthVal: v-bind('props.width');

@mixin setSizeMixin($width, $height) {
  width: $width;
  height: $height;
}

.#{$namespace} {
  &_custom {
    @include setSizeMixin($widthVal, $height);
  }
  &_large {
    @include setSizeMixin($widthLarge, $height);
  }
  &_middle {
    @include setSizeMixin($widthMiddle, $height);
  }
  &_small {
    @include setSizeMixin($widthSmall, $height);
  }
  &_item {
    width: 100%;
    white-space: pre-wrap;
  }
}
:deep(.el-autocomplete) {
  width: 100%;
  .el-input__wrapper {
    height: calc($height - ($defaultBorderWidth * 2));
  }
}
</style>
<style lang="scss">
.el-autocomplete-suggestion {
  width: $widthLarge;
  li > div {
    height: 34px;
    line-height: 1.25;
    overflow: visible;
    white-space: pre-wrap;
    display: grid;
    align-items: center;
  }
}
</style>
