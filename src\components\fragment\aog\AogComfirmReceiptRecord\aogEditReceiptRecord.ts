import CONST from '@/constants/utils';
import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import { rules } from '@/utils/validator';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

const { t } = i18n.global;

export const getAogEditReceiptRecordFormItems: () => CustomFormType['formItems'] =
  () => ({
    aogInstNo: {
      label: { text: t('Aog.Chr.txtAogInstNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    aogInstGrpNo: {
      label: { text: t('Aog.Chr.txtAogInstGrpNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    aogYmd: {
      label: { text: t('Aog.Chr.txtAogYmd') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    lotNo: {
      label: { text: t('Aog.Chr.txtLotNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    aogInstGrpPrtDts: {
      label: { text: t('Aog.Chr.txtAogInstGrpPrtDts') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    poDtlNo: {
      label: { text: t('Aog.Chr.txtPurchaseDetailOrderNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    matNo: {
      label: { text: t('Aog.Chr.txtItemCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    matNm: {
      label: { text: t('Aog.Chr.txtItemName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    mBpId: {
      label: { text: t('Aog.Chr.txtBusinessPartnerCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    bpNm: {
      label: { text: t('Aog.Chr.txtBusinessPartnerName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    makerLotNo: {
      label: { text: t('Aog.Chr.txtBusinessPartnerLotNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    edNo: {
      label: { text: t('Aog.Chr.txtEditionNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
  });

export const getAogEditContentInputFormItems = (
  expiryDspCtrl: string,
  shelfLifeDspCtrl: string,
): CustomFormType['formItems'] => {
  const formItems: CustomFormType['formItems'] = {
    unitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    rsltQty: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Aog.Chr.txtAogQty') },
      formModelValue: '',
      formRole: 'textBox',
      suffix: { formModelProp: 'unitNm' },
      rules: [
        rules.required('textBox'),
        rules.nonNegativeRealNumericOnly(),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
      ],
    },
  };

  if (shelfLifeDspCtrl !== '0') {
    formItems.shelfLifeYmd = {
      label: { text: '' },
      formModelValue: '',
      formRole: 'date',
      props: { modelValue: '', type: 'date' },
    };
  }
  if (expiryDspCtrl !== '0') {
    formItems.expiryYmd = {
      label: { text: '' },
      formModelValue: '',
      formRole: 'date',
      props: { modelValue: '', type: 'date' },
    };
  }

  formItems.modExpl = {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    label: { text: t('Aog.Chr.txtComment') },
    formModelValue: '',
    rules: [
      rules.required('textComboBox'),
      rules.length(64, t('Cm.Chr.txtLength', [64])),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ],
    formRole: 'textComboBox',
    props: { clearable: true },
    selectOptions: [],
    cmbId: 'cmtAogRsltMod',
  };

  formItems.aogAttBinList = {
    formModelValue: [],
    label: { text: t('Aog.Chr.txtAttachments') },
    formRole: 'fileUpload',
    props: { fileList: [], fileType: 'application/pdf' },
  };

  return formItems;
};

export const aogEditReceiptRecordFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getAogEditReceiptRecordFormItems());

export const aogEditContentInputFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getAogEditContentInputFormItems('0', '0'));

export const tablePropsData: TabulatorTableIF = {
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: '',
  height: '163px',
  column: [
    {
      title: 'Aog.Chr.txtPltNo',
      field: 'pltNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    {
      title: 'Aog.Chr.txtQty',
      field: 'ioaQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.AOG_QTY,
    },
    {
      title: 'Aog.Chr.txtMesUnit',
      field: 'unitNm',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    {
      title: 'Aog.Chr.txtComment',
      field: 'pckExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    {
      title: 'Aog.Chr.txtIoaHtUsr',
      field: 'liftUsrNm',
      width: COLUMN_WIDTHS.USR_NM,
    },
    {
      title: 'Aog.Chr.txtZoneNm',
      field: 'storZoneNm',
      width: COLUMN_WIDTHS.ZONE_NM,
    },
    {
      title: 'Aog.Chr.txtLocNm',
      field: 'storLocNm',
      width: COLUMN_WIDTHS.LOC_NM,
    },
  ],
  tableData: [],
  noUseConditionSearch: true, // 絞り込み条件無し
  showConditionSearch: false, // ConditionSearch不要
};
