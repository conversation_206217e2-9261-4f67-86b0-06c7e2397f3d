import CONST from '@/constants/utils';
import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

export const getAogArrivalPlanCopyFormItems: () => CustomFormType['formItems'] =
  () => ({
    erpUnitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    unitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    planYmd: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Aog.Chr.txtAogDate') },
      formModelValue: '',
      rules: [rules.required('date'), rules.futureDate()],
      formRole: 'date',
      props: { modelValue: '', type: 'date' },
    },
    poDtlNo: {
      label: { text: t('Aog.Chr.txtPurchaseDetailOrderNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    matNo: {
      label: { text: t('Aog.Chr.txtItemCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    matNm: {
      label: { text: t('Aog.Chr.txtItemName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    mBpId: {
      label: { text: t('Aog.Chr.txtBusinessPartnerCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bpNm: {
      label: { text: t('Aog.Chr.txtBusinessPartnerName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    makerLotNo: {
      label: { text: t('Aog.Chr.txtBusinessPartnerLotNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { clearable: true },
      rules: [
        rules.length(64, t('Cm.Chr.txtLength', [64])),
        rules.upperCaseSingleByteAlphanumeric(),
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      ],
    },
    edNo: {
      label: { text: t('Aog.Chr.txtEditionNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    erpAogQty: {
      label: { text: t('Aog.Chr.txtErpAogAmount') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
      suffix: { formModelProp: 'erpUnitNm' },
    },
    aogQty: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Aog.Chr.txtAogQuantity') },
      formModelValue: '',
      formRole: 'textBox',
      props: { clearable: true },
      suffix: { formModelProp: 'unitNm' },
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.positiveRealNumericOnly(),
      ],
    },
    poApNo: {
      label: { text: t('Aog.Chr.txtPurchaseOrderAvailableToPromiseNumber') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    poDtlExpl: {
      label: { text: t('Aog.Chr.txtPurchaseDetailOrderExpl') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    poApExpl: {
      label: { text: t('Aog.Chr.txtPurchaseOrderAvailableToPromiseExpl') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    files: {
      formModelValue: [],
      label: { text: t('Aog.Chr.txtAttachments') },
      formRole: 'fileUpload',
      props: { fileList: [], fileType: 'application/pdf' },
    },
    planExpl: {
      label: { text: t('Aog.Chr.txtAogExpl') },
      formModelValue: '',
      rules: [
        rules.length(64, t('Cm.Chr.txtLength', [64])),
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      ],
      formRole: 'textComboBox',
      props: { clearable: true },
      selectOptions: [],
      cmbId: 'cmtAogPlan',
    },
  });

export const aogArrivalPlanCopyFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getAogArrivalPlanCopyFormItems());
