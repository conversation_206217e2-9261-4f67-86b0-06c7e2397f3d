import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

const { t } = i18n.global;

// 管理番号/製造番号検索ダイアログのアイテム定義
export const getInvSelectLotIndividualPackagingFormItems: () => CustomFormType['formItems'] =
  () => ({
    matNo: {
      label: { text: t('Inv.Chr.txtItemCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { clearable: true },
    },
    matNm: {
      label: { text: t('Inv.Chr.txtItemName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { clearable: true },
    },
    lotNo: {
      label: { text: t('Inv.Chr.txtManageNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { clearable: true },
    },
  });

// 管理番号/製造番号検索ダイアログのモデル定義
export const invSelectLotIndividualPackagingFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getInvSelectLotIndividualPackagingFormItems());

export const getTablePropsData: () => TabulatorTableIF = () => ({
  pageName: 'InvSelectLotIndividualPackaging',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'lotNo',
  showRadio: true,
  column: [
    {
      title: 'Inv.Chr.txtItemCode',
      field: 'matNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    {
      title: 'Inv.Chr.txtItemName',
      field: 'matNm',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    {
      title: 'Inv.Chr.txtManageNo',
      field: 'lotNo',
      width: COLUMN_WIDTHS.LOT_NO,
    },
    {
      title: 'Inv.Chr.txtAmount',
      field: 'invQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.INV_QTY,
    },
    {
      title: 'Inv.Chr.txtMesUnit',
      field: 'unitNm',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
  ],
  searchData: [],
  tableData: [],
  noUseConditionSearch: true, // 絞り込み条件無し
  showConditionSearch: false, // ConditionSearchの表示/非表示
});
