<template>
  <div class="prd-confirm-info-list-dialog">
    <!-- 製造記録確認 記録詳細ダイアログ -->
    <DialogWindow
      :title="$t('Prd.Chr.txtConfirmList')"
      :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
      :buttons="[
        {
          type: 'secondary',
          size: 'normal',
          text: $t('Cm.Chr.btnCancel'),
        },
      ]"
      :width="OVERRIDE_DIALOG_WIDTH"
      @closeDialog="() => closeDialog('fragmentDialogVisible')"
    >
      <!-- 見出し 製造指図情報 -->
      <BaseHeading
        level="2"
        :text="$t('Prd.Chr.txtOrderInformation')"
        fontSize="24px"
      />
      <!-- 製造指図情報の見出し+テキスト項目表示 -->
      <InfoShow
        class="Util_mt-16"
        :infoShowItems="orderDetailInfoShowRef.infoShowItems"
        :isLabelVertical="orderDetailInfoShowRef.isLabelVertical"
      />
      <div class="prd-confirm-info-list-dialog_box-card-margin">
        <!-- 秤量記録エリア -->
        <div class="prd-confirm-info-list-dialog_button-box">
          <span class="prd-confirm-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtWeightRecord')
          }}</span>
          <div class="prd-confirm-info-list-dialog_button-border-box">
            <div class="prd-confirm-info-list-dialog_button-area">
              <div>
                <!-- 確認・修正ボタン-->
                <ButtonEx
                  class="prd-confirm-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnRecordConfirmFix')"
                  :iconName="weightingRecordButtonIconRef"
                  @click="clickWeightingRecordButton()"
                />
              </div>
              <div>
                <!-- 修正履歴ボタン-->
                <ButtonEx
                  class="prd-confirm-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnFixHistory')"
                  :iconName="wgtRecModifyListButtonIconRef"
                  @click="clickWgtRecModifyListButton()"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- SOP記録エリア -->
        <div class="prd-confirm-info-list-dialog_button-box">
          <span class="prd-confirm-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtSopRec')
          }}</span>
          <div class="prd-confirm-info-list-dialog_button-border-box">
            <div class="prd-confirm-info-list-dialog_button-area">
              <div>
                <!-- 確認・修正ボタン-->
                <ButtonEx
                  class="prd-confirm-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnRecordConfirmFix')"
                  :iconName="prdConfirmSOPFlowListButtonIconRef"
                  @click="clickSopRecordButton()"
                />
              </div>
              <div>
                <!-- 修正履歴ボタン-->
                <ButtonEx
                  class="prd-confirm-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnFixHistory')"
                  :iconName="sopRecordModifyHistoryButtonIconRef"
                  @click="clickSopRecordModifyHistoryButton()"
                />
              </div>
              <div>
                <!-- 異状履歴ボタン-->
                <ButtonEx
                  class="prd-confirm-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnDeviationHistory')"
                  :iconName="sopRecordDeviantHistoryButtonIconRef"
                  @click="clickSopRecordDeviantHistoryButton()"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 投入実績エリア -->
        <div class="prd-confirm-info-list-dialog_button-box">
          <span class="prd-confirm-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtActualInput')
          }}</span>
          <div class="prd-confirm-info-list-dialog_button-border-box">
            <div class="prd-confirm-info-list-dialog_button-area">
              <div>
                <!-- 確認・修正ボタン-->
                <ButtonEx
                  class="prd-confirm-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnRecordConfirmFix')"
                  :iconName="inputResultButtonIconRef"
                  @click="clickInputResultButton()"
                />
              </div>
              <div>
                <!-- 修正履歴ボタン-->
                <ButtonEx
                  class="prd-confirm-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnFixHistory')"
                  :iconName="inputResultModifyHistoryButtonIconRef"
                  @click="clickPrdBomModifyHistoryButton()"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 出来高記録エリア -->
        <div class="prd-confirm-info-list-dialog_button-box">
          <span class="prd-confirm-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtProduction')
          }}</span>
          <div class="prd-confirm-info-list-dialog_button-border-box">
            <div class="prd-confirm-info-list-dialog_button-area">
              <div>
                <!-- 確認・修正ボタン-->
                <ButtonEx
                  class="prd-confirm-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnRecordConfirmFix')"
                  :disabled="disabledProdFixButtonRef"
                  @click="clickInputProdFixButton()"
                />
              </div>
              <div>
                <!-- 修正履歴ボタン-->
                <ButtonEx
                  class="prd-confirm-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnFixHistory')"
                  :iconName="prodModifyHistoryButtonIconRef"
                  @click="clickPrdPrdModifyHistoryButton()"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 作業工数エリア -->
        <div class="prd-confirm-info-list-dialog_button-box">
          <span class="prd-confirm-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtWorkCost')
          }}</span>
          <div class="prd-confirm-info-list-dialog_button-border-box">
            <div class="prd-confirm-info-list-dialog_button-area">
              <div>
                <!-- 確認・修正ボタン-->
                <ButtonEx
                  class="prd-confirm-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnRecordConfirmFix')"
                  :disabled="disabledWorkCostButtonRef"
                  @click="clickPrdConfirmWorkButton()"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 収率エリア -->
        <div class="prd-confirm-info-list-dialog_button-box">
          <span class="prd-confirm-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtYieldValue')
          }}</span>
          <div class="prd-confirm-info-list-dialog_button-border-box">
            <div class="prd-confirm-info-list-dialog_button-area">
              <div>
                <!-- 確認・修正ボタン-->
                <ButtonEx
                  class="prd-confirm-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnRecordConfirmFix')"
                  :disabled="disabledYieldButtonRef"
                  @click="clickPrdConfirmYieldButton()"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 枠外下部ボタンエリア -->
      <div class="prd-confirm-info-list-dialog_button-area-bottom">
        <!-- 左側ボタン -->
        <div class="prd-confirm-info-list-dialog_button-area-bottom-list">
          <!-- コメント確認ボタン -->
          <ButtonEx
            type="secondary"
            size="normal"
            :text="t('Prd.Chr.btnCommentConfirm')"
            @click="clickCommentHistoryButton()"
          />
        </div>
        <!-- 右側ボタン -->
        <div class="prd-confirm-info-list-dialog_button-area-bottom-list">
          <!-- 指図記録検印ボタン -->
          <ButtonEx
            type="primary"
            size="normal"
            :text="t('Prd.Chr.btnOrderRecordStamp')"
            :disabled="disabledPrdRecordSealButtonRef"
            @click="clickPrdRecordSealButton()"
          />
        </div>
      </div>
    </DialogWindow>
    <!-- 秤量記録ダイアログ -->
    <PrdConfirmWeighing
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :infoData="initResponseData"
      :isClicked="isClickedShowWeightingRecordDialogRef"
      :wgtInstConfirmFlg="isResponseWeightConfirmed()"
      :routerName="props.routerName"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      @clickCancel="prdConfirmInfoListRedisplayInit"
    />
    <!-- 秤量記録修正履歴ダイアログ -->
    <PrdWgtRecModifyList
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :isClicked="isClickedShowWgtRecModifyListDialogRef"
      :dspNarrowType="PRD_WGT_REC_MODIFY_LIST_NARROW_TYPE.CONFIRM"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- SOP記録ダイアログ -->
    <PrdConfirmSOPFlowList
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :prcNo="getPrcNo()"
      :odrSts="getOdrSts()"
      :infoData="initResponseData"
      :isClicked="isClickedShowPrdConfirmSOPFlowListDialogRef"
      :routerName="props.routerName"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      @clickCancel="prdConfirmInfoListRedisplayInit"
    />
    <!-- SOP修正履歴ダイアログ -->
    <PrdModifyLogList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowSopRecordModifyHistoryDialogRef"
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :dspNarrowType="MODIFY_NARROW_TYPE.CONFIRM"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- 異状履歴ダイアログ -->
    <PrdConfirmDeviantList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowSopRecordDeviantHistoryDialogRef"
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- 投入修正履歴ダイアログ -->
    <PrdBomModifyList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowPrdBomModifyHistoryDialogRef"
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :dspNarrowType="BOM_LOG_NARROW_TYPE.CONFIRM"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- 出来高修正履歴ダイアログ -->
    <PrdProductModifyList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowPrdPrdModifyHistoryDialogRef"
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :dspNarrowType="PRD_LOG_NARROW_TYPE.CONFIRM"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- コメント確認ダイアログ -->
    <PrdCommentLogList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowCommentHistoryDialogRef"
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :dspNarrowType="COMMENT_NARROW_TYPE.CONFIRM"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- 製造記録確認 投入実績ダイアログ -->
    <PrdBomList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowPrdBomListDialogRef"
      :orderDetailInfo="orderDetailInfoShowItems"
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :routerName="props.routerName"
      :dspNarrowType="PRDBOMLIST_NARROW_TYPE.CONFIRM"
      @clickCancel="prdConfirmInfoListRedisplayInit"
    />
    <!-- 製造記録確認 出来高記録ダイアログ -->
    <PrdProductList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowPrdProductListDialogRef"
      :orderDetailInfo="orderDetailInfoShowItems"
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :routerName="props.routerName"
      :dspNarrowType="PRDPRODUCTLIST_NARROW_TYPE.CONFIRM"
      @clickCancel="prdConfirmInfoListRedisplayInit"
    />
    <!-- 作業工数ダイアログ -->
    <PrdConfirmWork
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedPrdConfirmWorkDialogRef"
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :infoData="initResponseData"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      @submit="prdConfirmInfoListRedisplayInit"
    />
    <!-- 収率修正ダイアログ -->
    <PrdConfirmYield
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedPrdConfirmYieldDialogRef"
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      @submit="prdConfirmInfoListRedisplayInit"
    />
    <!-- APIのエラー表示 -->
    <MessageBox
      v-if="dialogVisibleRef.messageBoxApiErrorVisible"
      :dialogProps="messageBoxApiErrorPropsRef"
      :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
    />
    <!-- 記録詳細ダイアログ遷移前の権限チェックエラーメッセージ -->
    <MessageBox
      v-if="dialogVisibleRef.productRecordWarningVisible"
      :dialogProps="messageBoxProductRecordWarningPropsRef"
      :cancelCallback="() => closeDialog('productRecordWarningVisible')"
      :submitCallback="() => updatePrdRecordDetail()"
    />
    <!-- 指図記録更新完了メッセージ -->
    <MessageBox
      v-if="dialogVisibleRef.updateProductRecordResultVisible"
      :dialogProps="messageBoxUpdateProductRecordResultPropsRef"
      :submitCallback="() => ReturnPreviousPage()"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import PrdConfirmSOPFlowList from '@/components/fragment/prd/PrdConfirmList/PrdConfirmSOPFlowList.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  MODIFY_NARROW_TYPE,
  BOM_LOG_NARROW_TYPE,
  PRD_LOG_NARROW_TYPE,
  COMMENT_NARROW_TYPE,
  PRD_WGT_REC_MODIFY_LIST_NARROW_TYPE,
  GetConfirmListData,
  GetConfirmInfoListRequestData,
  GetConfirmInfoListData,
  ModifyConfRecInfoRequestData,
  PRDBOMLIST_NARROW_TYPE,
  PRDPRODUCTLIST_NARROW_TYPE,
} from '@/types/HookUseApi/PrdTypes';
import {
  useGetConfRecInfoInit,
  useModifyConfirmRecInfo,
  useGetComboBoxDataStandard,
} from '@/hooks/useApi';
import BaseHeading from '@/components/base/BaseHeading.vue';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import createMessageBoxForm from '@/utils/commentMessageBox';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import ButtonEx from '@/components/parts/ButtonEx.vue';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import PrdConfirmWeighing from '@/components/fragment/prd/PrdConfirmList/PrdConfirmWeighing.vue';
import PrdWgtRecModifyList from '@/components/include/prd/PrdWgtRecModifyList.vue';
import PrdModifyLogList from '@/components/include/prd/PrdModifyLogList.vue';
import PrdConfirmDeviantList from '@/components/fragment/prd/PrdConfirmList/PrdConfirmDeviantList.vue';
import PrdBomModifyList from '@/components/include/prd/PrdBomModifyList.vue';
import PrdProductModifyList from '@/components/include/prd/PrdProductModifyList.vue';
import PrdCommentLogList from '@/components/include/prd/PrdCommentLogList.vue';
import PrdBomList from '@/components/include/prd/PrdBomList.vue';
import PrdProductList from '@/components/include/prd/PrdProductList.vue';
import PrdConfirmWork from '@/components/fragment/prd/PrdConfirmList/PrdConfirmWork.vue';
import PrdConfirmYield from '@/components/fragment/prd/PrdConfirmList/PrdConfirmYield.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { InfoShowType, InfoShowItem } from '@/types/InfoShowTypes';
import { IconTypes } from '@/types/IconTypes';
import getOrderDetailInfoShowItems from './prdConfirmInfoList';

/**
 * 多言語
 */
const { t } = useI18n();

const messageBoxForm = createMessageBoxForm('message', 'cmtConfirm');

// ボタンアイコン指定値
const iconGray = 'icon_gray';
const iconGreen = 'icon_green';
const iconRed = 'icon_red';
const iconYellow = 'icon_yellow';

const orderDetailInfoShowRef = ref<InfoShowType>({
  infoShowItems: getOrderDetailInfoShowItems(),
  isLabelVertical: true,
});

// 製造記録確認 投入実績ダイアログ用 製造指図情報表示用データ
let orderDetailInfoShowItems: Record<string, InfoShowItem>;

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 記録詳細ダイアログ遷移前の権限チェックエラーメッセージ
const messageBoxProductRecordWarningPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleOrderRecordConfirm'),
  content: t('Prd.Msg.contentOrderRecordConfirm'),
  isPrompt: true,
  isSkipValidation: true, // 任意入力とする
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

// 指図記録更新完了メッセージ
const messageBoxUpdateProductRecordResultPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

// 入力付き確認メッセージボックスの入力を初期化
const clearInputMessageBoxForm = () => {
  if ('isPrompt' in messageBoxProductRecordWarningPropsRef.value) {
    messageBoxProductRecordWarningPropsRef.value.formItems.message.formModelValue =
      '';
  }
};

// ダイアログの表示切替用定義
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  productRecordWarningVisible: false,
  updateProductRecordResultVisible: false,
  messageBoxApiErrorVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'productRecordWarningVisible'
  | 'updateProductRecordResultVisible'
  | 'messageBoxApiErrorVisible';

type Props = {
  selectedRow: GetConfirmListData | null; // 遷移元選択行
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  routerName: string; // TabulatorTable権限
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

const emit = defineEmits(['submit']);

// 記録詳細初期表示APIレスポンスデータのキャッシュ
let initResponseData: GetConfirmInfoListData = {
  matNo: '', // 品目CD
  dspNmJp: '', // 品名
  rxNmJp: '', // 処方
  lotNo: '', // 製造番号
  lotSid: '', // ロット番号
  prcNmJp: '', // 製造工程名
  prdPlanQty: '', // 生産予定量
  rsltQty: '', // 出来高
  prdStYmd: '', // 製造開始日予定
  rsltDts: '', // 製造開始日実績
  accgYmd: '', // 実績計上日
  yieldVal: '', // 収率
  recConfirmFlg: '', // 記録確認フラグ
  wgtInstConfirmFlg: false, // 秤量記録確認フラグ
  sopRecConfirmFlg: '', // SOP記録確認フラグ
  modExistFlg: '', // SOP記録修正履歴存在フラグ
  bomExistFlg: '', // 投入実績修正履歴存在フラグ
  prdExistFlg: '', // 出来高修正履歴存在フラグ
  wgtRecExistFlg: false, // 秤量記録修正履歴存在フラグ
  devExistFlg: '', // SOP記録異状履歴存在フラグ
  updDts: '', // 更新日時
  flowUpdDts: '', // SOPフロー更新日時
  prdUpdDts: '', // 出来高更新日時
  bomUpdDts: '', // 投入記録更新日時
  wgtInstUpdDts: '', // 秤量指示更新日時
  wgtLogUpdDts: '', // 秤量記録更新日時
  nodeUpdDts: '', // SOPノード更新日時
};

// NOTE: 画面仕様としてサイズ指定されているため、デフォルトを上書きする
const OVERRIDE_DIALOG_WIDTH = '1200px';

// 秤量記録確認・修正ボタン緑丸条件を満たしたかチェック
let checkGreenWeightingRecordButton = false;
// 投入実績確認・修正ボタン緑丸条件を満たしたかチェック
let checkGreenBomListButton = false;
// 指図記録 検印ボタン活性・非活性切り替え
const disabledPrdRecordSealButtonRef = ref<boolean>(true);
// 出来高修正ボタン活性・非活性切り替え
const disabledProdFixButtonRef = ref<boolean>(false);
// 作業工数確認・修正ボタン活性・非活性切り替え
const disabledWorkCostButtonRef = ref<boolean>(false);
// 収率確認・修正ボタン活性・非活性切り替え
const disabledYieldButtonRef = ref<boolean>(false);

// 秤量記録確認・修正ボタンアイコン
const weightingRecordButtonIconRef = ref<IconTypes>(iconRed);
// SOP記録確認・修正ボタンアイコン
const prdConfirmSOPFlowListButtonIconRef = ref<IconTypes>(iconRed);
// 投入実績確認・修正ボタンアイコン
const inputResultButtonIconRef = ref<IconTypes>(iconRed);
// 秤量記録修正履歴ボタンアイコン
const wgtRecModifyListButtonIconRef = ref<IconTypes>(iconGray);
// SOP修正履歴ボタンアイコン
const sopRecordModifyHistoryButtonIconRef = ref<IconTypes>(iconGray);
// 投入実績修正履歴ボタンアイコン
const inputResultModifyHistoryButtonIconRef = ref<IconTypes>(iconGray);
// 出来高修正履歴ボタンアイコン
const prodModifyHistoryButtonIconRef = ref<IconTypes>(iconGray);
// SOP記録異状履歴ボタンアイコン
const sopRecordDeviantHistoryButtonIconRef = ref<IconTypes>(iconGray);

// 秤量記録確認・修正ボタン' クリック
const isClickedShowWeightingRecordDialogRef = ref<boolean>(false);
// 秤量記録修正履歴ボタン クリック
const isClickedShowWgtRecModifyListDialogRef = ref<boolean>(false);
// 'SOP記録確認・修正ボタン' クリック
const isClickedShowPrdConfirmSOPFlowListDialogRef = ref<boolean>(false);
// 'SOP記録修正履歴ボタン' クリック
const isClickedShowSopRecordModifyHistoryDialogRef = ref<boolean>(false);
// '異状履歴ボタン' クリック
const isClickedShowSopRecordDeviantHistoryDialogRef = ref<boolean>(false);
// '投入修正履歴ボタン' クリック
const isClickedShowPrdBomModifyHistoryDialogRef = ref<boolean>(false);
// '出来高修正履歴ボタン' クリック
const isClickedShowPrdPrdModifyHistoryDialogRef = ref<boolean>(false);
// 'コメント確認ボタン' クリック
const isClickedShowCommentHistoryDialogRef = ref<boolean>(false);
// '投入実績 確認・修正ボタン' クリック
const isClickedShowPrdBomListDialogRef = ref<boolean>(false);
// '出来高記録 確認ボタン' クリック
const isClickedShowPrdProductListDialogRef = ref<boolean>(false);
// '作業工数確認ボタン' クリック
const isClickedPrdConfirmWorkDialogRef = ref<boolean>(false);
// '収率修正 確認・修正ボタン' クリック
const isClickedPrdConfirmYieldDialogRef = ref<boolean>(false);

const getOdrNo = () => {
  if (props.selectedRow === null) {
    return undefined;
  }

  return props.selectedRow.odrNo;
};
const getPrcSeq = () => {
  if (props.selectedRow === null) {
    return undefined;
  }
  if (props.selectedRow.prcSeq === null) return undefined;
  return props.selectedRow.prcSeq;
};
const getPrcNo = () => {
  if (props.selectedRow === null) {
    return undefined;
  }
  if (props.selectedRow.prcNo === null) return undefined;
  return props.selectedRow.prcNo;
};
const getOdrSts = () => {
  if (props.selectedRow === null) {
    return undefined;
  }
  if (props.selectedRow.odrSts === null) return undefined;
  return props.selectedRow.odrSts;
};

// レスポンス内で秤量確認済みか
const isResponseWeightConfirmed = () => initResponseData.wgtInstConfirmFlg;

// NOTE:緑アイコンと検印の条件を連動させるための関数
// 秤量確認済みか
const isWeightConfirmed = () => checkGreenWeightingRecordButton;

// 投入実績確認済みか
const isBomListConfirmed = () => checkGreenBomListButton;

// NOTE:処理の流れとして、指図記録検印押下済みならtrueになる
// 指図記録確認済みか
const isRecordConfirmed = () => initResponseData.recConfirmFlg === '1';

// NOTE:処理の流れとして、全てのSOPフローが確認済み指図記録検印押下済みならtrueになる
// 全てのSOPフローの状態が確認済みか
const isAllSopRecordConfirmed = () => initResponseData.sopRecConfirmFlg === '1';

// 秤量記録修正履歴存在しているか
const isExistModifyWeightRecord = () => initResponseData.wgtRecExistFlg;

// SOP記録修正履歴存在しているか
const isExistModifySopRecord = () => initResponseData.modExistFlg === '1';

// 投入実績修正履歴存在しているか
const isExistModifyBomList = () => initResponseData.bomExistFlg === '1';

// 出来高修正履歴存在しているか
const isExistModifyProductList = () => initResponseData.prdExistFlg === '1';

// SOP記録異状履歴存在しているか
const isExistDeviantSopRecord = () => initResponseData.devExistFlg === '1';

// 指図記録検印ボタンが非活性となるか判定
const isDisabledPrdRecordSealButton = () => {
  // 非活性条件
  // 秤量記録確認状態でない
  if (!isWeightConfirmed()) return true;
  // 投入実績確認状態でない
  if (!isBomListConfirmed()) return true;
  // 全てのSOPフローの状態が確認済でない
  if (!isAllSopRecordConfirmed()) return true;
  // 指図記録が確認済（承認待ちの状態）
  if (isRecordConfirmed()) return true;

  // ここに来たら活性
  return false;
};
/**
 * 指図記録 検印ボタン活性・非活性切り替え
 */
const switchDisabledPrdRecordSealButton = () => {
  disabledPrdRecordSealButtonRef.value = isDisabledPrdRecordSealButton();
};

/**
 * 秤量記録確認・修正ボタン押下時
 */
const clickWeightingRecordButton = () => {
  // 秤量記録確認状態で確認ボタン押下時にアイコンが緑色となる
  if (isResponseWeightConfirmed()) {
    checkGreenWeightingRecordButton = true;
    weightingRecordButtonIconRef.value = iconGreen;

    // 指図記録検印の活性切り替え
    switchDisabledPrdRecordSealButton();
  }

  // 秤量記録確認画面へ遷移
  isClickedShowWeightingRecordDialogRef.value =
    !isClickedShowWeightingRecordDialogRef.value;
};

/**
 * 秤量記録修正履歴ボタン押下時
 */
const clickWgtRecModifyListButton = () => {
  isClickedShowWgtRecModifyListDialogRef.value =
    !isClickedShowWgtRecModifyListDialogRef.value;
};

/**
 * SOP記録確認・修正ボタン押下時
 */
const clickSopRecordButton = () => {
  isClickedShowPrdConfirmSOPFlowListDialogRef.value =
    !isClickedShowPrdConfirmSOPFlowListDialogRef.value;
};

/**
 * SOP修正履歴ボタン押下時
 */
const clickSopRecordModifyHistoryButton = () => {
  isClickedShowSopRecordModifyHistoryDialogRef.value =
    !isClickedShowSopRecordModifyHistoryDialogRef.value;
};

/**
 * 異状履歴ボタン押下時
 */
const clickSopRecordDeviantHistoryButton = () => {
  isClickedShowSopRecordDeviantHistoryDialogRef.value =
    !isClickedShowSopRecordDeviantHistoryDialogRef.value;
};

/**
 * 投入修正履歴ボタン押下時
 */
const clickPrdBomModifyHistoryButton = () => {
  isClickedShowPrdBomModifyHistoryDialogRef.value =
    !isClickedShowPrdBomModifyHistoryDialogRef.value;
};

/**
 * 出来高修正履歴ボタン押下時
 */
const clickPrdPrdModifyHistoryButton = () => {
  isClickedShowPrdPrdModifyHistoryDialogRef.value =
    !isClickedShowPrdPrdModifyHistoryDialogRef.value;
};

/**
 * コメント確認ボタン押下時
 */
const clickCommentHistoryButton = () => {
  isClickedShowCommentHistoryDialogRef.value =
    !isClickedShowCommentHistoryDialogRef.value;
};

/**
 * 作業工数確認ボタン押下時
 */
const clickPrdConfirmWorkButton = () => {
  isClickedPrdConfirmWorkDialogRef.value =
    !isClickedPrdConfirmWorkDialogRef.value;
};

/**
 * 収率確認・修正ボタン押下時
 */
const clickPrdConfirmYieldButton = () => {
  isClickedPrdConfirmYieldDialogRef.value =
    !isClickedPrdConfirmYieldDialogRef.value;
};

/**
 * 投入実績確認・修正ボタン押下時
 */
const clickInputResultButton = () => {
  // 全てのSOPフローの状態が確認済みで確認ボタン押下時にアイコンが緑色となる
  if (isAllSopRecordConfirmed()) {
    checkGreenBomListButton = true;
    inputResultButtonIconRef.value = iconGreen;

    // 指図記録検印の活性切り替え
    switchDisabledPrdRecordSealButton();
  }

  // 製造記録確認 投入実績ダイアログ用 製造指図情報表示用データをセット
  orderDetailInfoShowItems = orderDetailInfoShowRef.value.infoShowItems;

  isClickedShowPrdBomListDialogRef.value =
    !isClickedShowPrdBomListDialogRef.value;
};

/**
 * 出来高修正ボタン押下時
 */
const clickInputProdFixButton = () => {
  // 製造記録確認 出来高記録ダイアログ用 製造指図情報表示用データをセット
  orderDetailInfoShowItems = orderDetailInfoShowRef.value.infoShowItems;

  isClickedShowPrdProductListDialogRef.value =
    !isClickedShowPrdProductListDialogRef.value;
};

/**
 * 指図記録 検印ボタン押下時処理
 */
const clickPrdRecordSealButton = async () => {
  clearInputMessageBoxForm();

  // 指図記録確認のダイアログを開く
  openDialog('productRecordWarningVisible');
};

/**
 * 指図記録を更新
 */
const updatePrdRecordDetail = async () => {
  closeDialog('productRecordWarningVisible');

  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    // 署名ダイアログがキャンセルされた場合の処理
    return;
  }

  // NOTE:型ガード用のチェック。
  if (
    props.selectedRow === null ||
    props.selectedRow.odrNo === null ||
    props.selectedRow.prcSeq === null
  ) {
    return;
  }
  if (!('isPrompt' in messageBoxProductRecordWarningPropsRef.value)) return;

  showLoading();
  const requestData: ModifyConfRecInfoRequestData = {
    odrNo: props.selectedRow.odrNo,
    prcNo: props.selectedRow.prcNo,
    prcSeq: props.selectedRow.prcSeq,
    lotSid: initResponseData.lotSid,
    updDts: initResponseData.updDts,
    modExpl:
      messageBoxProductRecordWarningPropsRef.value.formItems.message.formModelValue.toString(),
    flowUpdDts: initResponseData.flowUpdDts,
    prdUpdDts: initResponseData.prdUpdDts,
    bomUpdDts: initResponseData.bomUpdDts,
    wgtInstUpdDts: initResponseData.wgtInstUpdDts,
    wgtLogUpdDts: initResponseData.wgtLogUpdDts,
    nodeUpdDts: initResponseData.nodeUpdDts,
  };

  const { responseRef, errorRef } = await useModifyConfirmRecInfo({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxProductRecordWarningPropsRef.value.title,
    msgboxMsgTxt: messageBoxProductRecordWarningPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    msgboxInputCmt:
      messageBoxProductRecordWarningPropsRef.value.formItems.message.formModelValue.toString(),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }

  if (responseRef.value) {
    // 更新時のメッセージはBEから取得
    messageBoxUpdateProductRecordResultPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxUpdateProductRecordResultPropsRef.value.content =
      responseRef.value.data.rMsg;
  }

  closeLoading();

  openDialog('updateProductRecordResultVisible');
};

/**
 * 指図記録更新終了後に前画面に戻る処理
 */
const ReturnPreviousPage = () => {
  closeDialog('updateProductRecordResultVisible');
  closeDialog('fragmentDialogVisible');
  emit('submit');
};

/**
 * ボタンステータスの初期化処理（再表示用）
 */
const resetButtonStatus = () => {
  // NOTE:緑アイコンは再表示でも維持するため、意図的に初期化しない
  // checkGreenWeightingRecordButton = false;
  // NOTE:緑アイコンは再表示でも維持するため、意図的に初期化しない
  // checkGreenBomListButton = false;
  // NOTE:ボタン押下したかは再表示でも維持するため、意図的に初期化しない
  // checkClickedWeightingRecordButton = false;
  // NOTE:ボタン押下したかは再表示でも維持するため、意図的に初期化しない
  // checkClickedWeightingRecordButton = false;
  disabledProdFixButtonRef.value = false;
  disabledWorkCostButtonRef.value = false;
  disabledYieldButtonRef.value = false;
  prdConfirmSOPFlowListButtonIconRef.value = iconRed;
  wgtRecModifyListButtonIconRef.value = iconGray;
  sopRecordModifyHistoryButtonIconRef.value = iconGray;
  inputResultModifyHistoryButtonIconRef.value = iconGray;
  prodModifyHistoryButtonIconRef.value = iconGray;
  sopRecordDeviantHistoryButtonIconRef.value = iconGray;
};

/**
 * 全てのボタンステータスの初期化処理（初期表示用）
 */
const resetAllButtonStatus = () => {
  disabledPrdRecordSealButtonRef.value = true;

  // 秤量確認アイコンは初期は必ず赤から開始
  checkGreenWeightingRecordButton = false;
  weightingRecordButtonIconRef.value = iconRed;

  // 投入実績確認アイコンは初期は必ず赤から開始
  checkGreenBomListButton = false;
  inputResultButtonIconRef.value = iconRed;

  // NOTE:以下、仕様変更等で共通でなくなるなら処理見直してください。
  // 初期表示、再表示共通の初期化処理
  resetButtonStatus();
};

// NOTE:内部でローディング対応しています。終わったらcloseLoading()されるため注意。
// 標準コンボボックスAPIのリクエストと反映
const requestApiGetComboBoxDataStandard = async () => {
  // ローディング表示
  showLoading();

  // 標準コンボボックス取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        // 指図記録検印コメント
        cmbId: 'cmtConfirm',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_ODR_PRC_CONF' },
      },
    ],
  });

  if (
    'isPrompt' in messageBoxProductRecordWarningPropsRef.value &&
    comboBoxResData
  ) {
    // NOTE: 暫定対応 formItemsを都度生成したフォームで上書きして初期化する
    const resetData = createMessageBoxForm('message', 'cmtConfirm');
    messageBoxProductRecordWarningPropsRef.value.formItems =
      resetData.formItems;

    // NOTE:共通コンボボックス情報を元にコメント付き警告メッセージを設定
    setCustomFormComboBoxOptionList(
      messageBoxProductRecordWarningPropsRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  closeLoading();
};

// NOTE:内部でローディング対応しています。終わったらcloseLoading()されるため注意。
// 記録詳細初期表示APIのリクエストと反映
const requestApiGetConfRecInfoInit = async () => {
  // 型ガード用の判定
  if (props.selectedRow === null) return Promise.reject();
  if (props.selectedRow.prcSeq === null) return Promise.reject();

  // ローディング表示
  showLoading();

  // 記録詳細初期表示APIを呼び出す
  const apiRequestData: GetConfirmInfoListRequestData = {
    odrNo: props.selectedRow.odrNo,
    prcSeq: props.selectedRow.prcSeq,
  };
  const { responseRef, errorRef } = await useGetConfRecInfoInit({
    ...props.privilegesBtnRequestData,
    ...apiRequestData,
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return Promise.reject(); // エラーの場合継続処理させない
  }
  // NOTE:工数削減のためresponseRefがnullであることを考慮しない。必要なら処理書いてください。
  if (responseRef.value) {
    // 記録詳細初期表示APIのレスポンスデータをキャッシュ
    initResponseData = responseRef.value.data.rData;
  }

  // 指図詳細情報レイアウト用初期値設定
  Object.entries(initResponseData).forEach(([key, value]) => {
    if (key in orderDetailInfoShowRef.value.infoShowItems) {
      orderDetailInfoShowRef.value.infoShowItems[key].infoShowModelValue =
        value?.toString() ?? '';
    }
  });
  orderDetailInfoShowRef.value.infoShowItems.odrNo.infoShowModelValue =
    props.selectedRow.odrNo;

  if (isRecordConfirmed()) {
    // NOTE:画面仕様書記載 製造指図が「確認済み」の場合、ボタンを非活性にする
    disabledWorkCostButtonRef.value = true; // 作業工数ボタン
    disabledYieldButtonRef.value = true; // 収率ボタン
  }
  // NOTE:ここで行う各種色設定については、後で切り替わらない。
  if (isAllSopRecordConfirmed()) {
    prdConfirmSOPFlowListButtonIconRef.value = iconGreen;
  }
  if (isExistModifyWeightRecord()) {
    wgtRecModifyListButtonIconRef.value = iconYellow;
  }
  if (isExistModifySopRecord()) {
    sopRecordModifyHistoryButtonIconRef.value = iconYellow;
  }
  if (isExistModifyBomList()) {
    inputResultModifyHistoryButtonIconRef.value = iconYellow;
  }
  if (isExistModifyProductList()) {
    prodModifyHistoryButtonIconRef.value = iconYellow;
  }
  if (isExistDeviantSopRecord()) {
    sopRecordDeviantHistoryButtonIconRef.value = iconYellow;
  }

  // 指図記録検印の活性状態を設定
  switchDisabledPrdRecordSealButton();

  closeLoading();
  return Promise.resolve();
};

/**
 * 記録詳細ダイアログ再表示用の初期化処理
 */
const prdConfirmInfoListRedisplayInit = async () => {
  // NOTE:ボタン状態はAPIレスポンスの結果によって再度上書きされます
  // 再表示時に初期化するボタン設定
  resetButtonStatus();

  try {
    // 記録詳細初期表示APIのリクエスト
    await requestApiGetConfRecInfoInit();
  } catch (error) {
    // Promise.rejectされていたら後続処理させない
    return;
  }

  // NOTE:秤量は再検索時に緑から赤になる可能性あり。フラグが折れていたら赤にする。それ以外なら現状維持
  // 秤量アイコンの状態チェックと変更
  if (!isResponseWeightConfirmed()) {
    checkGreenWeightingRecordButton = false;
    weightingRecordButtonIconRef.value = iconRed;
  }

  // NOTE:投入確認は子ダイアログから戻ってきた時にフラグ確認をしない。
  // 緑から赤に切り替わる状況はあり得ないとみなして処理を組まない。

  // NOTE:再検索時、APIリクエスト後にアイコン状態が変更等あり得るため、検印の活性チェックを再度行う
  // 指図記録検印の活性切り替え
  switchDisabledPrdRecordSealButton();
};

/**
 * 記録詳細ダイアログの初期設定
 */
const prdConfirmInfoListInit = async () => {
  // NOTE:未選択なら処理させない。エラーメッセージはTabulatorTable側で出してくれる。
  if (props.selectedRow === null) return;

  // NOTE:ボタン状態はAPIレスポンスの結果によって再度上書きされます
  // 初期表示専用の、全ボタン状態リセット
  resetAllButtonStatus();

  // 標準コンボボックス取得APIのリクエスト
  await requestApiGetComboBoxDataStandard();

  // 記録詳細初期表示APIのリクエスト
  try {
    await requestApiGetConfRecInfoInit();
  } catch (error) {
    // Promise.rejectされていたらダイアログを起動させない
    return;
  }

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)
    await prdConfirmInfoListInit();
  },
);
</script>
<style lang="scss" scoped>
$namespace: 'prd-confirm-info-list-dialog';
// NOTE: 製造記録確認、承認、参照で似通った実装をしているため、修正時は要確認
$boxTextFontSize: 18px; // ボックス見出しのフォントサイズ
$boxTextHeight: $boxTextFontSize + 2px; // ボックス見出しの高さ(フォントサイズより少し大きく確保)
$boxLineWidth: 1px; // ボックスの枠線の太さ
// (枠線内)ボックスの幅と高さ
$boxWidth: 180px;
$boxHeight: 200px;
// 枠線付きボックスの幅と高さ
$boxBorderWidth: $boxWidth + $boxLineWidth * 2;
$boxBorderHeight: $boxHeight + $boxLineWidth * 2;
// ボックスエリア単体の幅と高さ(ボックス+見出し)
$boxAreaWidth: $boxBorderWidth;
$boxAreaHeight: $boxBorderHeight + $boxTextHeight; // 見出しが上に付くため足しておく

.#{$namespace} {
  // 画面中央ボックス群
  &_box-card-margin {
    height: $boxAreaHeight; // 各箱エリアの高さと同一
    margin-top: 28px;
    display: flex;
    justify-content: space-between;
  }
  // 箱エリア一つ辺りの設定
  &_button-box {
    width: $boxAreaWidth;
    height: $boxAreaHeight;
    float: left;
    text-align: center !important;
  }
  // 箱枠線
  &_button-border-box {
    width: $boxWidth; // 枠線抜きの幅
    height: $boxHeight; // 枠線抜きの高さ
    border: 1px solid #000000;
  }
  // 箱内ボタンテキスト
  &_button-area-text {
    font-size: $boxTextFontSize;
    display: block;
    height: $boxTextHeight;
  }
  // 箱内ボタンエリアに対する設定
  &_button-area {
    // NOTE:ボタン位置を10pxから始めるためのネガティブマージン
    margin-top: -20px;
  }
  // 箱内ボタンマージン
  &_button-margin {
    margin-top: 30px;
  }
  // 画面下部ボタンエリア
  &_button-area-bottom {
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
  }
  // 画面下部ボタンリスト
  &_button-area-bottom-list {
    // NOTE: 作成時点ではボタン1つのみだが、仕様変更を想定してflex設定
    display: flex;
  }
}
</style>
