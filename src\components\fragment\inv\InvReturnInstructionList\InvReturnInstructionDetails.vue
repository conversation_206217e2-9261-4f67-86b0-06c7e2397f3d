<template>
  <!-- 返納指示詳細 -->
  <DialogWindow
    :title="$t('Inv.Chr.txtInvReturnInstructionDetails')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    :buttons="[
      {
        text: $t('Cm.Chr.btnCancel'),
        type: 'secondary',
        size: 'normal',
        clickHandler: () => {
          closeDialog('fragmentDialogVisible');
        },
      },
    ]"
  >
    <BaseHeading
      level="2"
      :text="$t('Inv.Chr.txtInvReturnInstructionDetailsList')"
      fontSize="24px"
    />
    <!-- 共通のテーブル -->
    <TabulatorTable :propsData="tablePropsDataRef" />
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { ReturnInstGrpList } from '@/types/HookUseApi/InvTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { DialogProps } from '@/types/MessageBoxTypes';
import { useGetInventoryReturnInstructionList } from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import tablePropsData from './invReturnInstructionDetails';

type Props = {
  isClicked: boolean;
  selectedRowData: ReturnInstGrpList | null;
  privilegesBtnRequestData: CommonRequestType;
};
const props = defineProps<Props>();

type DialogRefKey = 'singleButton' | 'fragmentDialogVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  fragmentDialogVisible: false,
};
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

// ダイアログ内テーブル設定
const tablePropsDataRef = ref<TabulatorTableIF>({
  ...tablePropsData,
});

/**
 * 初期設定
 */
const invReturnInstructionDetailsInit = async () => {
  if (!props.selectedRowData) return;
  showLoading();
  // 返納指示（明細）情報取得
  const { responseRef, errorRef } = await useGetInventoryReturnInstructionList({
    ...props.privilegesBtnRequestData,
    rtnInstGrpNo: props.selectedRowData.rtnInstGrpNo,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    tablePropsDataRef.value.tableData =
      responseRef.value.data.rData.rtnInstList;
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, invReturnInstructionDetailsInit);
</script>
