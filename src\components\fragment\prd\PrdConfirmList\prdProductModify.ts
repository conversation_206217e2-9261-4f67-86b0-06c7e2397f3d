import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// 標準コンボボックスとカスタムフォームを紐づけるID
export const COMBINE_ID = {
  MOD_EXPL: 'prdProductModExpl',
} as const;

// 出来高修正ダイアログのアイテム定義
export const getPrdProductModifyFormItems: () => CustomFormType['formItems'] =
  () => ({
    // 品目コード
    prdMatNo: {
      label: { text: t('Prd.Chr.txtMaterialCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 出来高品名
    dspNmJp: {
      label: { text: t('Prd.Chr.txtVolumeName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 生産バッチ番号
    batchNo: {
      label: { text: t('Prd.Chr.txtProductBatchNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 製造番号
    lotNo: {
      label: { text: t('Prd.Chr.txtLotNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 出来高ラベル
    lblSid: {
      label: { text: t('Prd.Chr.txtProductionLabel') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 単位
    unitNmJp: {
      label: { text: t('Prd.Chr.txtUnit') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    // 出来高
    rsltQty: {
      label: { text: t('Prd.Chr.txtProduction') },
      formModelValue: '',
      formRole: 'textBox',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.nonNegativeRealNumericOnly(),
      ],
    },
    // 修正コメント入力
    modExpl: {
      formModelValue: '',
      label: { text: t('Prd.Chr.txtModifyCommentInput') },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('textBox'), rules.length(64)],
      formRole: 'textComboBox',
      selectOptions: [],
      cmbId: COMBINE_ID.MOD_EXPL,
    },
  });

// 出来高修正ダイアログのモデル定義
export const prdProductModifyFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getPrdProductModifyFormItems());
