<template>
  <!-- 再実行SOPフロー選択ダイアログ -->
  <!-- 見出し 再実行SOPフロー選択 -->
  <DialogWindow
    :title="$t('Odr.Chr.txtReRunSOPFlowSelect')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    :class="'odr-select-rerun-sop-flow'"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- テキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="orderSopFlowListInfoShowRef.infoShowItems"
      :isLabelVertical="orderSopFlowListInfoShowRef.isLabelVertical"
    />

    <!--再実行SOPフロー選択テーブル -->
    <TabulatorTable
      :propsData="tablePropsDataOrderSelectRerunSopFlowRef"
      @selectRows="updateSelectedRows"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 再バッチ指示登録の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxOrderSelectRerunSopFlowVisible"
    :dialogProps="messageBoxOrderSelectRerunSopFlowPropsRef"
    :cancelCallback="
      () => closeDialog('messageBoxOrderSelectRerunSopFlowVisible')
    "
    :submitCallback="requestApiOrderSelectRerunSopFlow"
  />
  <!-- 再バッチ指示登録完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxOrderSelectRerunSopFlowFinishedVisible"
    :dialogProps="messageBoxOrderSelectRerunSopFlowFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxOrderSelectRerunSopFlowFinished"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';

// import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogProps } from '@/types/MessageBoxTypes';
import createMessageBoxForm from '@/utils/commentMessageBox';
import {
  useGetOrderSopFlowList,
  useGetComboBoxDataStandard,
  useAddOrderRebatchInstruction,
} from '@/hooks/useApi';
import {
  GetOrderSopFlowListRequestData,
  GetOrderSopFlowListData,
  GetOrderProcessListInRebatchInstructionData,
  AddOrderRebatchInstructionRequestData,
} from '@/types/HookUseApi/OdrTypes';
import {
  CommonRequestType,
  ComboBoxDataStandardReturnData,
} from '@/types/HookUseApi/CommonTypes';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import { InfoShowType } from '@/types/InfoShowTypes';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import getOrderSopFlowListInfoShowItems from './odrSelectRerunSopFlow';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxOrderSelectRerunSopFlowVisible'
  | 'messageBoxOrderSelectRerunSopFlowFinishedVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxOrderSelectRerunSopFlowVisible: false,
  messageBoxOrderSelectRerunSopFlowFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// テキスト項目表示
const orderSopFlowListInfoShowRef = ref<InfoShowType>({
  infoShowItems: getOrderSopFlowListInfoShowItems(),
  isLabelVertical: true,
});

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 再バッチ指示登録の確認メッセージボックス
// コメントあり警告用フォーム設定
const messageBoxForm = createMessageBoxForm('message', 'cmtWarning'); // 第二引数が標準コンボボックス取得のキー名
let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;

const messageBoxOrderSelectRerunSopFlowPropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titleAddOrderRebatchInstruction'),
  content: t('Odr.Msg.contentAddOrderRebatchInstruction'),
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

// 再バッチ指示登録の完了メッセージボックス
const messageBoxOrderSelectRerunSopFlowFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

// 親ダイアログから渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  odrNo: string; // 親ダイアログのodrNo
  selectedRowData: GetOrderProcessListInRebatchInstructionData | null; // 親ページの選択行情報
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 再実行SOPフロー選択取得用テーブル設定
const tablePropsDataOrderSelectRerunSopFlowRef = ref<TabulatorTableIF>({
  pageName: 'RxSopFlowList',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'sopFlowNo', // 主キー。ユニークになるものを設定。
  showCheckbox: {
    show: true,
    condition: '', // 条件無し
    conditionValue: 0,
    allAllowed: true,
  },
  column: [
    // バッチ展開
    {
      title: 'Odr.Chr.txtBatchExpansion',
      field: 'sopBatchType',
      width: COLUMN_WIDTHS.ODR.SOP_BATCH_TYPE,
    },
    // SOPフロー名称
    {
      title: 'Odr.Chr.txtSOPFlowName',
      field: 'sopFlowNmJp',
      width: COLUMN_WIDTHS.SOP_FLOW_NM,
    },
    // SOPフローNo（隠しカラム）
    { title: '', field: 'sopFlowNo', hidden: true },
  ],
  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
});

// 選択行情報(複数)の格納
let selectedRows: GetOrderSopFlowListData[] = [];

// 選択行情報の更新
const updateSelectedRows = (v: GetOrderSopFlowListData[]) => {
  // 選択複数行情報を保存
  selectedRows = v;
  if (v.length > 0) {
    updateDialogChangeFlagRef(true);
  } else {
    updateDialogChangeFlagRef(false);
  }
};

// 入力付き確認メッセージボックスの入力を初期化
const clearInputMessageBoxForm = () => {
  if ('isPrompt' in messageBoxOrderSelectRerunSopFlowPropsRef.value) {
    messageBoxOrderSelectRerunSopFlowPropsRef.value.formItems.message.formModelValue =
      '';
  }
};

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // 行未選択時のエラーメッセージ表示
  if (selectedRows.length === 0) {
    // メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = t('Cm.Chr.txtUnselectedData');
    messageBoxApiErrorPropsRef.value.content = t('Cm.Msg.unselectedData');
    openDialog('messageBoxApiErrorVisible');
    return false;
  }

  if (
    props.odrNo === '' ||
    props.selectedRowData === null ||
    props.selectedRowData.prcSeq === null ||
    props.selectedRowData.batchNo === null
  ) {
    return false;
  }

  // 製造指図コメント更新の確認
  clearInputMessageBoxForm();
  openDialog('messageBoxOrderSelectRerunSopFlowVisible');
  return false;
};

// 再バッチ指示登録の確認メッセージ'OK'押下時処理
// 再バッチ指示登録のAPIリクエスト処理
const requestApiOrderSelectRerunSopFlow = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxOrderSelectRerunSopFlowVisible');

  // パスワード認証ダイアログ(W1Z2510)を表示する
  // 認証が成功した場合、下記の処理を行う
  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    return;
  }

  if (!('isPrompt' in messageBoxOrderSelectRerunSopFlowPropsRef.value)) return;

  showLoading();

  if (
    props.odrNo === '' ||
    props.selectedRowData === null ||
    props.selectedRowData.prcSeq === null ||
    props.selectedRowData.batchNo === null
  ) {
    return;
  }

  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する。
  const requestData: AddOrderRebatchInstructionRequestData = {
    odrNo: props.odrNo,
    prcSeq: props.selectedRowData.prcSeq,
    batchNo: props.selectedRowData.batchNo,
    modExpl:
      messageBoxOrderSelectRerunSopFlowPropsRef.value.formItems.message.formModelValue.toString(),
    sopFlowList: [],
  };

  selectedRows.forEach((row) => {
    requestData.sopFlowList.push({
      sopFlowNo: row.sopFlowNo,
    });
  });

  const { responseRef, errorRef } = await useAddOrderRebatchInstruction({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxOrderSelectRerunSopFlowPropsRef.value.title,
    msgboxMsgTxt: messageBoxOrderSelectRerunSopFlowPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    msgboxInputCmt:
      messageBoxOrderSelectRerunSopFlowPropsRef.value.formItems.message.formModelValue.toString(),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // 4．再バッチ指示登録
  // ・データベースを更新した後、以下のメッセージを表示する。
  // 再バッチ指示登録完了
  messageBoxOrderSelectRerunSopFlowFinishedPropsRef.value.title =
    responseRef.value.data.rTitle;
  messageBoxOrderSelectRerunSopFlowFinishedPropsRef.value.content =
    responseRef.value.data.rMsg;

  closeLoading();

  openDialog('messageBoxOrderSelectRerunSopFlowFinishedVisible');
};

// 再バッチ指示登録完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxOrderSelectRerunSopFlowFinished = () => {
  // 再検索用に親に実行を通知
  emit('submit');

  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxOrderSelectRerunSopFlowFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

/**
 * 再実行SOPフロー選択ダイアログの初期設定
 */
const odrSelectRerunSopFlowInit = async () => {
  if (
    props.odrNo === '' ||
    props.selectedRowData === null ||
    props.selectedRowData.prcSeq === null ||
    props.selectedRowData.batchNo === null
  ) {
    return;
  }

  // ダイアログ終了チェック初期化
  updateDialogChangeFlagRef(false);

  // 選択行情報初期化
  selectedRows = [];

  showLoading();

  // cmtWarningで標準コンボボックス取得
  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtWarning',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'ODR_RE_BATCH' },
      },
    ],
  });

  if (
    'isPrompt' in messageBoxOrderSelectRerunSopFlowPropsRef.value &&
    comboBoxDataStandardReturnData
  ) {
    const resetData = createMessageBoxForm('message', 'cmtWarning');
    messageBoxOrderSelectRerunSopFlowPropsRef.value.formItems =
      resetData.formItems;

    // NOTE:共通コンボボックス情報を元にコメント付き警告メッセージを設定
    setCustomFormComboBoxOptionList(
      messageBoxOrderSelectRerunSopFlowPropsRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }

  // 再実行SOPフロー選択一覧取得のAPIを行う。
  const requestData: GetOrderSopFlowListRequestData = {
    odrNo: props.odrNo,
    prcSeq: props.selectedRowData.prcSeq,
    batchNo: props.selectedRowData.batchNo,
  };
  const { responseRef, errorRef } = await useGetOrderSopFlowList({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // 処方SOPフロー一覧取得 テキスト項目 レイアウト用初期値設定
  Object.entries(responseRef.value.data.rData).forEach(([key, value]) => {
    if (key in orderSopFlowListInfoShowRef.value.infoShowItems) {
      orderSopFlowListInfoShowRef.value.infoShowItems[key].infoShowModelValue =
        value?.toString() ?? '';
    }
  });

  // テーブル設定
  tablePropsDataOrderSelectRerunSopFlowRef.value.tableData =
    responseRef.value.data.rData.rxSopFlowList;

  closeLoading();

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await odrSelectRerunSopFlowInit();
  },
);
</script>
