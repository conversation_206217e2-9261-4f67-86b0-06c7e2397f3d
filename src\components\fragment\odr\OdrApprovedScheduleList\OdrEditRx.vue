<template>
  <!-- 処方変更ダイアログ -->
  <DialogWindow
    :title="$t('Odr.Chr.txtPrescriptionChange')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="odrEditRxFormRef.formModel"
      :formItems="odrEditRxFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          odrEditRxFormRef.customForm = v;
        }
      "
      @changeFormModel="
        (changeFlag, { changeLogModList }) => {
          updateDialogChangeFlagRef(changeFlag);
          updateCurrentChangedFormModelMap(changeLogModList);
        }
      "
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 小日程計画処方変更の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPrescriptionEditVisible"
    :dialogProps="messageBoxPrescriptionEditPropsRef"
    :cancelCallback="() => closeDialog('messageBoxPrescriptionEditVisible')"
    :submitCallback="requestApiPrescriptionEdit"
  />
  <!-- 小日程計画処方変更完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPrescriptionEditFinishedVisible"
    :dialogProps="messageBoxPrescriptionEditFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxPrescriptionEditFinished"
  />
  <!-- 処方選択ダイアログ -->
  <OdrSelectRx
    :isClicked="isClickedShowOdrSelectRxDialogRef"
    :materialCode="materialCode"
    :materialName="materialName"
    :unitName="unitName"
    :orderDates="orderDates"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    @submit="reflectPrescriptionInfo"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import onValidateHandler from '@/utils/validateHandler'; // handleValidationBySingleRowSelect,
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import { PrescriptionListTableRowData } from '@/types/PrescriptionSelectDialogTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import OdrSelectRx from '@/components/include/odr/OdrSelectRx.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  useGetPrescriptionModify,
  useModifySchedulePrescription,
} from '@/hooks/useApi';
import {
  ModifySchedulePrescriptionRequestData,
  GetApprovalScheduleListData,
  GetPrescriptionModifyRequestData,
  GetPrescriptionModifyResData,
} from '@/types/HookUseApi/OdrTypes';
import {
  CommonRequestType,
  LogModListType,
} from '@/types/HookUseApi/CommonTypes';
import { getOdrEditRxFormItems, odrEditRxFormModel } from './odrEditRx';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxPrescriptionEditVisible'
  | 'messageBoxPrescriptionEditFinishedVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxPrescriptionEditVisible: false,
  messageBoxPrescriptionEditFinishedVisible: false,
};

// 子に渡す情報パラメータ
let materialCode: string = '';
let materialName: string = '';
let unitName: string = '';
let orderDates: string = '';
// '処方選択' クリック
const isClickedShowOdrSelectRxDialogRef = ref<boolean>(false);

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 小日程計画処方変更の確認メッセージボックス
const messageBoxPrescriptionEditPropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titlePrescriptionEdit'),
  content: t('Odr.Msg.contentPrescriptionEdit'),
  type: 'question',
});

// 小日程計画処方変更の完了メッセージボックス
const messageBoxPrescriptionEditFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

const odrEditRxFormRef = ref<CustomFormType>({
  formItems: getOdrEditRxFormItems(),
  formModel: odrEditRxFormModel,
});

// 処方変更初期表示のレスポンスデータ
let initResponseData: GetPrescriptionModifyResData = {
  skdNo: '',
  planPrcNo: '',
  matNo: '',
  dspNmJp: '',
  dspLotNo: '',
  rxNo: '',
  rxNmJp: '',
  mbrNo: '',
  validYmd: '',
  stdPrdQty: '',
  unitNmJp: '',
  planQty: '',
  odrDts: '',
  updDts: '',
};

let logModList: LogModListType = [];
// 変更履歴更新時処理
const updateCurrentChangedFormModelMap = (changeLogModList: LogModListType) => {
  logModList = changeLogModList;
};

type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  selectedRowData: GetApprovalScheduleListData | null; // 親ページの選択行情報
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 処方選択ダイアログの 実行 押下時処理
// 処方情報の反映
const reflectPrescriptionInfo = (v: PrescriptionListTableRowData) => {
  // 処方コード
  odrEditRxFormRef.value.formItems.rxNo.formModelValue = v.rxNo;
  // 処方名
  odrEditRxFormRef.value.formItems.rxNmJp.formModelValue = v.rxNmJp;
  // MBR番号
  odrEditRxFormRef.value.formItems.mbrNo.formModelValue = v.mbrNo;
  // 有効期限
  odrEditRxFormRef.value.formItems.validYmd.formModelValue = `${v.validStYmd} - ${v.validEdYmd}`;
  // NOTE:テーブル上はカンマがついているが、入力としては不要なためカンマ除去
  const stdPrdQty = v.stdPrdQty.replace(/,/g, '');
  // 標準生産量
  odrEditRxFormRef.value.formItems.stdPrdQty.formModelValue = stdPrdQty;

  // 処方変更コメント
  odrEditRxFormRef.value.formItems.rxModExpl.formModelValue = '';
  if (odrEditRxFormRef.value.formItems.rxModExpl.formRole === 'textBox') {
    // 処方選択を実行後に活性状態にする
    odrEditRxFormRef.value.formItems.rxModExpl.props!.disabled = false;
  }
};

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // 1.下記チェックを行い、チェックOKなら処理継続する。
  // １ 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    odrEditRxFormRef.value.customForm !== undefined &&
    (await odrEditRxFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return false;
  }

  // 2.以下のメッセージを表示
  // 処方変更の確認
  openDialog('messageBoxPrescriptionEditVisible');
  return false;
};

// 小日程計画処方変更の確認メッセージ'OK'押下時処理
// 小日程計画処方変更のAPIリクエスト処理
const requestApiPrescriptionEdit = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxPrescriptionEditVisible');

  showLoading();
  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する
  const requestData: ModifySchedulePrescriptionRequestData = {
    skdNo: initResponseData.skdNo,
    rxNo: odrEditRxFormRef.value.formItems.rxNo.formModelValue.toString(),
    mbrNo: odrEditRxFormRef.value.formItems.mbrNo.formModelValue.toString(),
    rxModExpl:
      odrEditRxFormRef.value.formItems.rxModExpl.formModelValue.toString(),
    updDts: initResponseData.updDts,
    logModList,
  };
  const { responseRef, errorRef } = await useModifySchedulePrescription({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxPrescriptionEditPropsRef.value.title,
    msgboxMsgTxt: messageBoxPrescriptionEditPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return false;
  }

  if (responseRef.value) {
    // 4．小日程計画を更新する。
    // ・データベースを更新した後、以下のメッセージを表示する。
    // 小日程計画処方変更完了
    messageBoxPrescriptionEditFinishedPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxPrescriptionEditFinishedPropsRef.value.content =
      responseRef.value.data.rMsg;
  }

  closeLoading();
  openDialog('messageBoxPrescriptionEditFinishedVisible');
  return false;
};

// 小日程計画処方変更完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxPrescriptionEditFinished = () => {
  // 再検索用に親に実行を通知
  emit('submit', props.privilegesBtnRequestData);

  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxPrescriptionEditFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

// 処方選択ボタンクリック時イベントの設定
const buttonOdrEditRxOnClickHandler = () => {
  // 処方選択ダイアログのパラメータセット
  materialCode =
    odrEditRxFormRef.value.formItems.matNo.formModelValue.toString();
  materialName =
    odrEditRxFormRef.value.formItems.dspNmJp.formModelValue.toString();
  unitName =
    odrEditRxFormRef.value.formItems.unitNmJp.formModelValue.toString();
  orderDates =
    odrEditRxFormRef.value.formItems.odrDts.formModelValue.toString();

  // 処方選択ダイアログを表示開始
  // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
  isClickedShowOdrSelectRxDialogRef.value =
    !isClickedShowOdrSelectRxDialogRef.value;
};

// 処方選択ButtonFormの初期設定
const odrEditRxButtonFormInit = () => {
  if (odrEditRxFormRef.value.formItems.buttonOdrEditRx.formRole !== 'button') {
    return;
  }

  // ボタンクリックイベント設定
  odrEditRxFormRef.value.formItems.buttonOdrEditRx.onClickHandler =
    buttonOdrEditRxOnClickHandler;
};

/**
 * 処方変更ダイアログの初期設定
 */
const odrEditRxInit = async () => {
  if (props.selectedRowData === null) return;

  updateDialogChangeFlagRef(false);
  logModList = [];

  showLoading();

  // 処方変更初期表示のAPIを行う。
  const requestData: GetPrescriptionModifyRequestData = {
    skdNo: props.selectedRowData.skdNo,
  };
  const { responseRef, errorRef } = await useGetPrescriptionModify({
    ...props.privilegesBtnRequestData,
    ...requestData,
    // NOTE: 直前メッセージは無い。msgbox関連は設定不要。
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  closeLoading();
  if (responseRef.value) {
    // ダイアログ表示初期値として、レスポンス情報を格納
    initResponseData = responseRef.value.data.rData;
  }

  // FormItems初期化
  odrEditRxFormRef.value.formItems = getOdrEditRxFormItems();

  // 処方選択ButtonFormの初期設定
  odrEditRxButtonFormInit();

  if (odrEditRxFormRef.value.formItems.planQty.formRole !== 'textBox') {
    return;
  }

  // カスタムフォーム初期値設定
  Object.entries(initResponseData).forEach(([key, value]) => {
    if (
      odrEditRxFormRef.value.formModel &&
      key in odrEditRxFormRef.value.formModel
    ) {
      odrEditRxFormRef.value.formItems[key].formModelValue =
        value?.toString() ?? '';
    }
  });

  // NOTE:処方変更初期表示APIで来る標準生産量、計画生産量はカンマ付きで来ないため除去対応しない。

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

// ダイアログ起動条件の監視
watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await odrEditRxInit();
  },
);
</script>
