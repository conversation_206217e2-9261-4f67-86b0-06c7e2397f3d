import END_POINT from '@/constants/endPoint';
import {
  GetWeighingSOPListRequestData,
  GetWeighingSOPListResData,
} from '@/types/HookUseApi/WgtTypes';
import {
  ExtendCommonRequestType,
  ExtendCommonRequestWithMainApiFlagType,
} from '@/types/HookUseApi/CommonTypes';
import { useApi } from './util';

// 秤量前後SOP一覧検索
const useGetWeighingSOPList = (
  data: ExtendCommonRequestType<GetWeighingSOPListRequestData>,
) =>
  useApi<
    ExtendCommonRequestWithMainApiFlagType<GetWeighingSOPListRequestData>,
    GetWeighingSOPListResData
  >(END_POINT.GET_WGT_SOP_LIST, 'post', { ...data, mainApiFlg: 0 });

export default useGetWeighingSOPList;
