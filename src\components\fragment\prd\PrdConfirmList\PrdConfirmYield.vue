<template>
  <!-- 収率修正ダイアログ -->
  <!-- 見出し 収率 -->
  <DialogWindow
    :title="$t('Prd.Chr.txtYieldValue')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="prdConfirmYieldFormRef.formModel"
      :formItems="prdConfirmYieldFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          prdConfirmYieldFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 収率修正確定完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPrdConfirmYieldFinishedVisible"
    :dialogProps="messageBoxPrdConfirmYieldFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxModifyPrdConfirmYieldFinished"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import onValidateHandler from '@/utils/validateHandler';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  useGetConfirmYield,
  useModifyConfirmYield,
  useGetComboBoxDataStandard,
} from '@/hooks/useApi';
import {
  GetConfirmYieldRequestData,
  GetConfirmYieldResponseData,
  ModifyConfirmYieldRequestData,
} from '@/types/HookUseApi/PrdTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import {
  getPrdConfirmYieldFormItems,
  prdConfirmYieldFormModel,
  COMBINE_ID,
} from './prdConfirmYield';

// 収率修正初期表示APIのレスポンス
let initResponseData: GetConfirmYieldResponseData = {
  yieldVal: '',
  updDts: '',
};

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxPrdConfirmYieldFinishedVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxPrdConfirmYieldFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 収率修正確定の完了メッセージボックス
const messageBoxPrdConfirmYieldFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

const prdConfirmYieldFormRef = ref<CustomFormType>({
  formItems: getPrdConfirmYieldFormItems(),
  formModel: prdConfirmYieldFormModel,
});

// 親ダイアログから渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  odrNo?: string; // 親ダイアログのodrNo
  prcSeq?: number; // 親ダイアログのprcSeq
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 収率修正確定のAPIリクエスト処理
const requestApiModifyConfirmYield = async () => {
  if (props.odrNo === undefined || props.prcSeq === undefined) {
    return;
  }

  showLoading();

  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する。
  const requestData: ModifyConfirmYieldRequestData = {
    odrNo: props.odrNo,
    prcSeq: props.prcSeq,
    updDts: initResponseData.updDts,
    yieldVal:
      prdConfirmYieldFormRef.value.formItems.yieldVal.formModelValue.toString(),
    modExpl:
      prdConfirmYieldFormRef.value.formItems.modExpl.formModelValue.toString(),
  };

  const { responseRef, errorRef } = await useModifyConfirmYield({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // 4．収率修正確定
  // ・データベースを更新した後、以下のメッセージを表示する。
  // 収率修正確定完了
  messageBoxPrdConfirmYieldFinishedPropsRef.value.title =
    responseRef.value.data.rTitle;
  messageBoxPrdConfirmYieldFinishedPropsRef.value.content =
    responseRef.value.data.rMsg;

  closeLoading();

  openDialog('messageBoxPrdConfirmYieldFinishedVisible');
};

// 収率修正確定完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxModifyPrdConfirmYieldFinished = () => {
  // 再検索用に親に実行を通知
  emit('submit');

  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxPrdConfirmYieldFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // 1.下記チェックを行い、チェックOKなら処理継続する。
  // １ 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    prdConfirmYieldFormRef.value.customForm !== undefined &&
    (await prdConfirmYieldFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return false;
  }

  // 収率修正確定のAPIリクエスト処理
  requestApiModifyConfirmYield();

  return false;
};

/**
 * 収率修正ダイアログの初期設定
 */
const prdConfirmYieldInit = async () => {
  if (props.odrNo === undefined || props.prcSeq === undefined) {
    return;
  }

  // ダイアログ終了チェック初期化
  updateDialogChangeFlagRef(false);

  // FormItems初期化
  prdConfirmYieldFormRef.value.formItems = getPrdConfirmYieldFormItems();

  showLoading();

  // 収率修正初期表示のAPIを行う。
  const requestData: GetConfirmYieldRequestData = {
    odrNo: props.odrNo,
    prcSeq: props.prcSeq,
  };
  const { responseRef, errorRef } = await useGetConfirmYield({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // 収率修正初期表示のAPIのレスポンスを保持
  initResponseData = responseRef.value.data.rData;

  // 収率修正情報レイアウト用初期値設定
  setFormModelValueFromApiResponse(
    prdConfirmYieldFormRef,
    responseRef.value.data.rData,
  );

  // 標準コンボボックス取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: COMBINE_ID.MOD_EXPL,
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_ODR_YIELD_MOD' }, // 製造記録の収率修正
      },
    ],
  });

  if (comboBoxResData) {
    setCustomFormComboBoxOptionList(
      prdConfirmYieldFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  closeLoading();

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await prdConfirmYieldInit();
  },
);
</script>
