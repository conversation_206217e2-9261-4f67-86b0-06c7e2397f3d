import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';

const { t } = i18n.global;

// 製造指図情報の縦並び項目定義
const getProductReferenceInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 製造指図番号
    odrNo: {
      label: { text: t('Prd.Chr.txtOrderNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 品目コード
    matNo: {
      label: { text: t('Prd.Chr.txtMaterialCode') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 処方
    rxNmJp: {
      label: { text: t('Prd.Chr.txtPrescriptionName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 品名
    dspNmJp: {
      label: { text: t('Prd.Chr.txtMaterialName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 16,
    },
    // 製造番号
    lotNo: {
      label: { text: t('Prd.Chr.txtLotNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 生産予定量位置調整用
    dummy: {
      label: { text: '' },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 生産予定量
    prdPlanQty: {
      label: { text: t('Prd.Chr.txtPlanQuantity') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 製造開始日予定
    odrStYmd: {
      label: { text: t('Prd.Chr.txtOrderStartDatePlan') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 収率
    yieldVal: {
      label: { text: t('Prd.Chr.txtYieldValue') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 出来高
    rsltQty: {
      label: { text: t('Prd.Chr.txtProduction') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 製造開始日実績
    rsltDts: {
      label: { text: t('Prd.Chr.txtOrderStartDateResults') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 製造記録確認日
    recConfirmDts: {
      label: { text: t('Prd.Chr.txtOrderRecordConfirmDate') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 製造記録確認者
    recConfirmUsr: {
      label: { text: t('Prd.Chr.txtOrderRecordConfirmUser') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 16,
    },
    // 製造記録承認日
    recApprovDts: {
      label: { text: t('Prd.Chr.txtOrderRecordApprovalDate') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 製造記録承認者
    recApprovUsr: {
      label: { text: t('Prd.Chr.txtOrderRecordApprovalUser') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 製造記録承認回数
    recDocVer: {
      label: { text: t('Prd.Chr.txtOrderRecordApproveCount') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
  });

export default getProductReferenceInfoShowItems;
