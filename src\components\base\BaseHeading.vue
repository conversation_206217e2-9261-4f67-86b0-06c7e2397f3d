<template>
  <component :is="Heading" :class="props.class">{{ props.text }}</component>
</template>
<script setup lang="ts">
import { computed } from 'vue';

type Props = {
  text: string;
  level: '1' | '2' | '3' | '4';
  fontSize?: '32px' | '28px' | '24px' | '20px';
  fontWeight?: 'normal' | 'bold';
  class?: string;
};
const props = defineProps<Props>();
const Heading = computed(() => `h${props.level}`);
</script>
<style lang="scss" scoped>
@mixin base {
  color: $gray156;
  font-weight: v-bind('props.fontWeight ?? "bold"');
  line-height: 1;
  margin: 0;
}

h1 {
  @include base;
  font-size: v-bind('props.fontSize ?? "32px"');
}

h2 {
  @include base;
  font-size: v-bind('props.fontSize ?? "28px"');
}

h3 {
  @include base;
  font-size: v-bind('props.fontSize ?? "24px"');
}

h4 {
  @include base;
  font-size: v-bind('props.fontSize ?? "20px"');
}
</style>
