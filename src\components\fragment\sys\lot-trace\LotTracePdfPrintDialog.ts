import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';

const { t } = i18n.global;

export const getLotTracePdfPrintFormItems: () => CustomFormType['formItems'] =
  () => ({
    erpUnitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    unitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    prcNm: {
      label: { text: t('Sys.Chr.txtProcessName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    odrNo: {
      label: { text: t('Sys.Chr.txtOdrNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomWgtRsltDate: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsWgtResultDate') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomMakerLotNo: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsMakerLotNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomMakerNm: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsMakerName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomLotNo: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsLotNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomItemCls: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsItemClassification') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomItemCode: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsItemCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomItemNm: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsItemName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomRsltDate: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsResultDate') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomInvedQualitySts: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsInvestedQualityStatus') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomInvedTestCmtExt: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsInvestedTestComment') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomInvedEfectiveDate: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsInvestedEffectiveDate') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomInvedExpiryDate: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsInvestedExpiryDate') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomCrtQualitySts: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsCurrentQualityStatus') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomCrtTestCmtExt: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsCurrentTestComment') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomCrtEfectiveDate: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsCurrentEffectiveDate') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    bomCrtExpiryDate: {
      label: { text: t('Sys.Chr.txtBillOfMaterialsCurrentExpiryDate') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    vlmLotNo: {
      label: { text: t('Sys.Chr.txtVolumeLotNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    vlmItemCode: {
      label: { text: t('Sys.Chr.txtVolumeItemCode') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    vlmItemNm: {
      label: { text: t('Sys.Chr.txtVolumeItemName') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    vlmRsltDate: {
      label: { text: t('Sys.Chr.txtVolumeResultDate') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    vlmCrtQualitySts: {
      label: { text: t('Sys.Chr.txtVolumeCurrentQualityStatus') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    vlmCrtTestCmtExt: {
      label: { text: t('Sys.Chr.txtVolumeCurrentTestComment') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    lotTraceBinList: {
      formModelValue: [],
      formRole: 'fileUpload',
      props: {
        fileList: [],
        hideTriggerBtn: true,
      },
      label: { text: t('Aog.Chr.txtAttachments') },
    },
  });

export const LotTracePdfPrintFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getLotTracePdfPrintFormItems());
