import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

const { t } = i18n.global;

export const getInspectionInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 品目コード
    bomMatNo: {
      label: { text: t('Sjg.Chr.txtMatNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 品名
    bomMatNm: {
      label: { text: t('Sjg.Chr.txtMatNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 18,
    },
    // 略号
    bomShtNm: {
      label: { text: t('Sjg.Chr.txtShtNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 製造番号
    bomLotNo: {
      label: { text: t('Sjg.Chr.txtLotNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 指図番号
    odrNo: {
      label: { text: t('Sjg.Chr.txtBomOdrNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
  });

// 特別作業指示ダイアログのアイテム定義
export const getSjgComponentInformationAttachmentFormItems: () => CustomFormType['formItems'] =
  () => ({
    bomList: {
      formModelValue: [],
      formRole: 'fileUpload',
      props: {
        fileList: [],
        fileType: 'application/pdf',
        maxLength: 5,
        hideTriggerBtn: true,
      },
    },
  });

// 特別作業指示ダイアログのモデル定義
export const sjgComponentInformationAttachmentFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getSjgComponentInformationAttachmentFormItems());

export const tablePropsData: TabulatorTableIF = {
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  height: '280px',
  dataID: 'lblSid',
  rowHeight: 35,
  hideCheckboxTitleFormatter: true,
  btnColumn: {
    frozen: true, // 凍結列
    columnWidth: 100, // ボタン列幅
    condition: 'disabledBtnFlg',
    conditionValue: 1,
    btnProps: {
      text: t('Cm.Chr.btnDetail'),
      size: 'small',
      type: 'secondary',
    },
  },
  column: [
    {
      title: 'Sjg.Chr.txtLblSid',
      field: 'lblSid',
      width: COLUMN_WIDTHS.LBL_NO,
    },
    {
      title: 'Sjg.Chr.txtLogLotStsNm',
      field: 'logLotStsNm',
      width: COLUMN_WIDTHS.SJG.LOT_STS,
    },
    {
      title: 'Sjg.Chr.txtlotLotStsNm',
      field: 'lotLotStsNm',
      width: COLUMN_WIDTHS.SJG.LOT_STS,
    },
    {
      title: 'Sjg.Chr.txtRsltDts',
      field: 'rsltYmd',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    {
      title: 'Sjg.Chr.txtLogShelfLifeDts',
      field: 'logShelfLifeYmd',
      width: COLUMN_WIDTHS.SJG.LOG_DTS,
    },
    {
      title: 'Sjg.Chr.txtLotShelfLifeDts',
      field: 'lotShelfLifeYmd',
      width: COLUMN_WIDTHS.DATE_YYYYMMDD,
    },
    {
      title: 'Sjg.Chr.txtLogExpiryDts',
      field: 'logExpiryYmd',
      width: COLUMN_WIDTHS.SJG.LOG_DTS,
    },
    {
      title: 'Sjg.Chr.txtLotExpiryNowDts',
      field: 'lotExpiryYmd',
      width: COLUMN_WIDTHS.DATE_YYYYMMDD,
    },
    {
      title: 'Sjg.Chr.txtLogConsDeadlineCheckStart',
      field: 'logConsDeadlineStYmd',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
    {
      title: 'Sjg.Chr.txtLogConsDeadlineCheckEnd',
      field: 'logConsDeadlineEdYmd',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
    {
      title: 'Sjg.Chr.txtLblConsDeadlineCheckStart',
      field: 'lblConsDeadlineStYmd',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
    {
      title: 'Sjg.Chr.txtLblConsDeadlineCheckEnd',
      field: 'lblConsDeadlineEdYmd',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
    {
      title: 'Sjg.Chr.txtLogWgtDts',
      field: 'logWgtYmd',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
    {
      title: 'Sjg.Chr.txtLblWgtDts',
      field: 'lblWgtYmd',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
  ],
  tableData: [],
  noUseConditionSearch: true, // 絞り込み条件無し
  showConditionSearch: false, // ConditionSearch不要
  cellColor: {
    color: 'red',
  },
};
