import i18n from '@/constants/lang';
import { rules } from '@/utils/validator';
import { CustomFormType } from '@/types/CustomFormTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { RuleOption } from '@/types/ValidatorTypes';

const { t } = i18n.global;

// 再秤量指示作成画面の権限に紐づくボタン定義
export const BUTTON_ID = {
  CHECK: 'btnCheck', // 記録確認
  DETAIL: 'btnWeightRecordDetail', // 秤量記録詳細
  EDIT: 'btnWeightRecordEdit', // 秤量記録修正
  SCAN: 'btnScan', // 検索用
} as const;

// 秤量指示書番号の入力チェックルール 検索後vue側で再設定する必要がある為constで定義
export const wgtInstGrpNoRules: RuleOption[] = [
  rules.required('textBox'),
  rules.length(13),
  rules.upperCaseSingleByteAlphanumeric(),
];

export const getProcessDataInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 秤量指示書状態
    wgtInstGrpStsDsp: {
      label: { text: t('Wgt.Chr.txtWeightInstructionStatus') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
  });

export const getSearchFormItems: () => CustomFormType['formItems'] = () => ({
  wgtInstGrpNoDsp: {
    formModelValue: '',
    formRole: 'suffix',
  },
  wgtInstGrpNo: {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    label: { text: t('Wgt.Chr.txtWeightInstructionQRScanMessage') },
    formModelValue: '',
    formRole: 'textBox',
    suffix: { formModelProp: 'wgtInstGrpNoDsp' },
    props: { clearable: true, size: 'small' },
    rules: wgtInstGrpNoRules, // 秤量指示書番号の入力チェックルール
  },
});

export const getSearchFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getSearchFormItems());
