<template>
  <!-- 製造記録修正_SOP実施記録ダイアログ -->
  <!-- 見出し 製造記録修正 SOP実施記録 -->
  <DialogWindow
    :title="$t('Prd.Chr.txtProductionRecordEdit')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- テキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="infoShowRef.infoShowItems"
      :isLabelVertical="infoShowRef.isLabelVertical"
    />

    <CustomForm
      class="Util_mt-16"
      :formModel="dialogFormRef.formModel"
      :formItems="dialogFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          dialogFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 製造記録修正_SOP実施記録修正記録値判定のワーニング表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxDeviationCheckVisible"
    :dialogProps="messageBoxDeviationCheckPropsRef"
    :cancelCallback="() => closeDialog('messageBoxDeviationCheckVisible')"
    :submitCallback="
      () => {
        closeDialog('messageBoxDeviationCheckVisible');
        // チェックOKなら処理継続する。
        // 製造記録修正_SOP実施記録修正確定API呼び出し
        requestApiModifyConfirmSopRecord();
      }
    "
  />
  <!-- 実行時の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxConfirmResolveVisible"
    :dialogProps="messageBoxConfirmResolvePropsRef"
    :cancelCallback="() => closeDialog('messageBoxConfirmResolveVisible')"
    :submitCallback="requestApiCheckConfirmSopRecord"
  />
  <!-- 製造記録修正_SOP実施記録修正確定 完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxModifyConfirmSopRecordFinishedVisible"
    :dialogProps="messageBoxModifyConfirmSopRecordFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxModifyConfirmSopRecordFinished"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import CONST from '@/constants/utils';
import { ref, watch } from 'vue';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import onValidateHandler from '@/utils/validateHandler';
import createMessageBoxForm from '@/utils/commentMessageBox';
import InfoShow from '@/components/parts/InfoShow.vue';
import { InfoShowType } from '@/types/InfoShowTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  useGetConfirmSopRecordInit,
  useGetComboBoxDataStandard,
  useCheckConfirmSopRecord,
  useModifyConfirmSopRecord,
} from '@/hooks/useApi';
import {
  GetConfirmSopRecordInitRequestData,
  GetConfirmSopRecordInitResponseData,
  GetConfirmSOPFlowInfoListData,
  CheckConfirmSopRecordRequestData,
  ModifyConfirmSopRecordRequestData,
  COMBO_BOX_WHERE,
} from '@/types/HookUseApi/PrdTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  COMBINE_ID,
  getInfoShowItems,
  getDialogFormItems,
  dialogFormModel,
} from './prdConfirmSOPModify';

/**
 * 多言語
 */
const { t } = useI18n();

// 製造記録修正_SOP実施記録初期表示APIのレスポンス
let initResponseData: GetConfirmSopRecordInitResponseData = {
  cmtMain1: '', // 作業指示内容1
  cmtMain2: '', // 作業指示内容2
  cmtMain3: '', // 作業指示内容3
  instVal: '', // 指示値
  recVal1: '', // 記録値1
  recVal2: '', // 記録値2
  recVal3: '', // 記録値3
  recVal4: '', // 記録値4
  recVal5: '', // 記録値5
  recVal1Flg: '', // 記録値1非活性フラグ
  recVal2Flg: '', // 記録値2非活性フラグ
  recVal3Flg: '', // 記録値3非活性フラグ
  recVal4Flg: '', // 記録値4非活性フラグ
  recVal5Flg: '', // 記録値5非活性フラグ
  updDts: '', // 更新日時
};

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxDeviationCheckVisible'
  | 'messageBoxConfirmResolveVisible'
  | 'messageBoxModifyConfirmSopRecordFinishedVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxDeviationCheckVisible: false,
  messageBoxConfirmResolveVisible: false,
  messageBoxModifyConfirmSopRecordFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 実行時の確認メッセージボックス
const messageBoxConfirmResolvePropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleProductionRecordEdit'),
  content: t('Prd.Msg.contentProductionRecordEdit'),
  type: 'question',
});

// コメントあり警告用フォーム設定
const messageBoxForm = createMessageBoxForm('message', 'cmtWarning'); // 第二引数が標準コンボボックス取得のキー名
// 異状発生時の確認メッセージボックス
const messageBoxDeviationCheckPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

// 製造記録修正_SOP実施記録修正確定 完了の情報表示
const messageBoxModifyConfirmSopRecordFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

// 共通APIリクエストに渡すための、直前MessageBox表示テキストの保存情報
type MessageBoxInfo = {
  titleText: string;
  messageText: string;
  buttonText: string;
};

let messageBoxInfo: MessageBoxInfo = {
  titleText: '',
  messageText: '',
  buttonText: '',
};

// NOTE:前チェックAPIでワーニングの有無によって直前メッセージが変わるため、それを保存するための機構
// 直前MessageBox表示テキストの保存
const cacheMessageBoxInfo = (v: DialogProps) => {
  messageBoxInfo = {
    titleText: v.title,
    messageText: v.content,
    buttonText: t('Cm.Chr.btnOk'),
  };
};

const infoShowRef = ref<InfoShowType>({
  infoShowItems: getInfoShowItems(),
  isLabelVertical: true,
});

const dialogFormRef = ref<CustomFormType>({
  formItems: getDialogFormItems(),
  formModel: dialogFormModel,
});

// 親から渡す情報群
type Props = {
  odrNo?: string; // 受注番号
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  selectedRowData: GetConfirmSOPFlowInfoListData | null; // 選択行のデータ
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 入力付き確認メッセージボックスの入力を初期化
const clearInputMessageBoxForm = () => {
  if ('isPrompt' in messageBoxDeviationCheckPropsRef.value) {
    messageBoxDeviationCheckPropsRef.value.formItems.message.formModelValue =
      '';
  }
};

// 型ガード関数を定義
// NOTE:テンプレートリテラルで書くとstring型と判定されて型推論できないためisを使用した型ガード関数を用意
function isKeyOfGetConfirmSopRecordInitResponseData(
  key: string,
): key is keyof GetConfirmSopRecordInitResponseData {
  return key in initResponseData;
}

// 製造記録修正_SOP実施記録修正確定のAPIリクエスト処理
const requestApiModifyConfirmSopRecord = async () => {
  closeDialog('messageBoxDeviationCheckVisible');

  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    // 署名ダイアログがキャンセルされた場合の処理
    clearInputMessageBoxForm(); // 入力フォームを初期化
    return;
  }

  if (
    props.selectedRowData === null ||
    props.selectedRowData.sopFlowLnum === null ||
    props.selectedRowData.sopNodeLnum === null
  )
    return;

  showLoading();
  // 製造記録修正_SOP実施記録修正確定APIを実行する。
  const requestData: ModifyConfirmSopRecordRequestData = {
    odrNo: props.selectedRowData.odrNo,
    prcNo: props.selectedRowData.prcNo,
    prcSeq: props.selectedRowData.prcSeq ?? 0,
    batchNo: props.selectedRowData.batchNo ?? 0,
    sopFlowNo: props.selectedRowData.sopFlowNo,
    sopFlowLnum: props.selectedRowData.sopFlowLnum,
    sopNodeNo: props.selectedRowData.sopNodeNo,
    sopNodeLnum: props.selectedRowData.sopNodeLnum,
    // 修正後のテキストボックスの内容を入力
    recModExpl:
      dialogFormRef.value.formItems.recModExpl.formModelValue.toString(),
    recVal1: dialogFormRef.value.formItems.recVal1.formModelValue.toString(),
    recVal2: dialogFormRef.value.formItems.recVal2.formModelValue.toString(),
    recVal3: dialogFormRef.value.formItems.recVal3.formModelValue.toString(),
    recVal4: dialogFormRef.value.formItems.recVal4.formModelValue.toString(),
    recVal5: dialogFormRef.value.formItems.recVal5.formModelValue.toString(),
    beforeRecVal1: initResponseData.recVal1,
    beforeRecVal2: initResponseData.recVal2,
    beforeRecVal3: initResponseData.recVal3,
    beforeRecVal4: initResponseData.recVal4,
    beforeRecVal5: initResponseData.recVal5,
    updDts: initResponseData.updDts,
    recDevExpl:
      'isPrompt' in messageBoxDeviationCheckPropsRef.value
        ? messageBoxDeviationCheckPropsRef.value.formItems.message.formModelValue.toString()
        : '',
  };

  // 製造記録修正_SOP実施記録修正確定API実行
  const { responseRef, errorRef } = await useModifyConfirmSopRecord({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxInfo.titleText,
    msgboxMsgTxt: messageBoxInfo.messageText,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    msgboxInputCmt:
      'isPrompt' in messageBoxDeviationCheckPropsRef.value
        ? messageBoxDeviationCheckPropsRef.value.formItems.message.formModelValue.toString()
        : '',
    ...requestData,
  });

  if (errorRef.value) {
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // ・データベースを更新した後、以下のメッセージを表示する。
    // 工程作業記録_製造記録修正確定完了
    messageBoxModifyConfirmSopRecordFinishedPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxModifyConfirmSopRecordFinishedPropsRef.value.content =
      responseRef.value.data.rMsg;
    // 製造記録修正_SOP実施記録修正確定の完了メッセージ表示
    openDialog('messageBoxModifyConfirmSopRecordFinishedVisible');
    closeLoading();
  }
};

// 製造記録修正_SOP実施記録修正値判定のAPIリクエスト処理
const requestApiCheckConfirmSopRecord = async () => {
  closeDialog('messageBoxConfirmResolveVisible');

  cacheMessageBoxInfo(messageBoxConfirmResolvePropsRef.value);

  if (
    props.odrNo === undefined ||
    props.selectedRowData === null ||
    props.selectedRowData.sopFlowLnum === null ||
    props.selectedRowData.sopNodeLnum === null
  ) {
    return;
  }

  showLoading();
  // 製造記録修正_SOP実施記録修正値判定 API呼び出し
  const requestData: CheckConfirmSopRecordRequestData = {
    odrNo: props.odrNo,
    sopFlowNo: props.selectedRowData.sopFlowNo,
    sopFlowLnum: props.selectedRowData.sopFlowLnum,
    sopNodeLnum: props.selectedRowData.sopNodeLnum,
    recVal1: dialogFormRef.value.formItems.recVal1.formModelValue.toString(), // NOTE: 修正後記録値1のみを判定対象とする
  };
  const { responseRef, errorRef } = await useCheckConfirmSopRecord({
    ...props.privilegesBtnRequestData,
    ...requestData,
    msgboxTitleTxt: messageBoxConfirmResolvePropsRef.value.title,
    msgboxMsgTxt: messageBoxConfirmResolvePropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
  });

  if (errorRef.value) {
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      clearInputMessageBoxForm(); // 入力フォームを初期化
      // 製造記録修正_SOP実施記録修正記録値判定 ワーニング用メッセージボックス起動
      messageBoxDeviationCheckPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxDeviationCheckPropsRef.value.content =
        errorRef.value.response.rMsg;
      openDialog('messageBoxDeviationCheckVisible');

      // ワーニングメッセージを保存
      cacheMessageBoxInfo(messageBoxDeviationCheckPropsRef.value);

      closeLoading();
      return; // ワーニングの場合、ワーニング用messageBox側で処理継続させる。
    }
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return; // レスポンスがない場合継続処理させない
  }

  // 確定APIを呼び出す
  requestApiModifyConfirmSopRecord();
  closeLoading();
};

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // 入力項目に対して共通チェック
  const validate =
    dialogFormRef.value.customForm !== undefined &&
    (await dialogFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return false;
  }

  // 実行時の確認メッセージ表示
  openDialog('messageBoxConfirmResolveVisible');
  return false;
};

// 製造記録修正_SOP実施記録修正確定 完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxModifyConfirmSopRecordFinished = () => {
  // 再検索用に親に実行を通知
  emit('submit');

  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxModifyConfirmSopRecordFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

/**
 * 製造記録修正_SOP実施記録ダイアログの初期設定
 */
const prdConfirmSOPModifyInit = async () => {
  if (
    props.selectedRowData === null ||
    props.selectedRowData.sopFlowLnum === null ||
    props.selectedRowData.sopNodeLnum === null
  ) {
    return;
  }

  updateDialogChangeFlagRef(false);

  showLoading();

  // 製造記録修正_SOP実施記録初期表示 API呼び出し
  const requestData: GetConfirmSopRecordInitRequestData = {
    sopFlowNo: props.selectedRowData.sopFlowNo,
    sopFlowLnum: props.selectedRowData.sopFlowLnum,
    sopNodeLnum: props.selectedRowData.sopNodeLnum,
  };
  const { responseRef, errorRef } = await useGetConfirmSopRecordInit({
    ...props.privilegesBtnRequestData,
    ...requestData,
    // NOTE: 直前メッセージは無い。msgbox関連は設定不要。
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }
  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // レスポンス情報をキャッシュ
  initResponseData = responseRef.value.data.rData;

  // レイアウト用初期値設定
  Object.entries(initResponseData).forEach(([key, value]) => {
    if (key in infoShowRef.value.infoShowItems) {
      infoShowRef.value.infoShowItems[key].infoShowModelValue =
        value?.toString() ?? '';
    }
  });

  // FormItems初期化
  dialogFormRef.value.formItems = getDialogFormItems();

  // 製造記録修正情報レイアウト用初期値設定
  setFormModelValueFromApiResponse(dialogFormRef, responseRef.value.data.rData);

  // recValflg1~5 の値に基づいて disabled プロパティを更新
  for (let i = 1; i <= 5; i++) {
    const flgKey = `recVal${i}Flg`;
    const recKey = `recVal${i}`;
    if (
      isKeyOfGetConfirmSopRecordInitResponseData(flgKey) &&
      recKey in dialogFormRef.value.formItems
    ) {
      const flgValue = initResponseData[flgKey];
      if (dialogFormRef.value.formItems[recKey].formRole === 'textBox') {
        if (flgValue === '1') {
          dialogFormRef.value.formItems[recKey].tags = []; // タグ削除
          dialogFormRef.value.formItems[recKey].props = { disabled: true }; // 非活性化
          dialogFormRef.value.formItems[recKey].rules = []; // ルール削除
        }
      }
    }
  }

  // 標準コンボボックス取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        // 製造記録のSOP修正
        cmbId: COMBINE_ID.REC_MOD_EXPL,
        condKey: 'm_sys_cmt',
        where: { cmt_cat: COMBO_BOX_WHERE.MODIFY.PRD_ODR_SOP_MOD },
      },
      {
        cmbId: 'cmtWarning',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: COMBO_BOX_WHERE.DEVIANT.COMMON },
      },
    ],
  });

  if (comboBoxResData) {
    setCustomFormComboBoxOptionList(
      dialogFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );

    if ('isPrompt' in messageBoxDeviationCheckPropsRef.value) {
      // NOTE: 暫定対応 formItemsを都度生成したフォームで上書きして初期化する
      const resetData = createMessageBoxForm('message', 'cmtWarning');
      messageBoxDeviationCheckPropsRef.value.formItems = resetData.formItems;

      // NOTE:共通コンボボックス情報を元にコメント付き警告メッセージを設定
      setCustomFormComboBoxOptionList(
        messageBoxDeviationCheckPropsRef.value.formItems,
        comboBoxResData.rData.rList,
      );
    }
  }

  closeLoading();
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await prdConfirmSOPModifyInit();
  },
);
</script>
