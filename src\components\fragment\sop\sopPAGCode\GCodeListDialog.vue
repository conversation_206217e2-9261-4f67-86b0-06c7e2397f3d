<template>
  <el-dialog
    :title="state.title"
    v-model="state.isGCodeDialogVisible"
    width="70%"
    :show-close="false"
    :close-on-click-modal="false"
    @open="handleDialogOpen"
  >
    <template #header>
      <BaseHeading level="2" fontSize="24px" :text="state.title" />
    </template>
    <el-form>
      <div>
        <div>
          <!-- 共通のテーブル -->
          <TabulatorTable
            :propsData="tablePropsDataRef"
            @selectRow="updateSelectedRow"
          />
          <!-- メモ削除ダイアログ -->
          <DialogWindow
            :title="$t('SOP.Chr.txtGCodeMemoDelete')"
            :dialogVisible="dialogVisibleRef.deleteDialogVisible"
            @closeDialog="closeDialog('deleteDialogVisible')"
            :onReject="() => console.log('cancel')"
            :onResolve="async () => deleteDialogResolveClickHandler()"
          >
            <CustomForm
              :formModel="gCodeDeleteFormRef.formModel"
              :formItems="gCodeDeleteFormRef.formItems"
              @visible="
                (v: CustomFormType['customForm']) => {
                  gCodeDeleteFormRef.customForm = v;
                }
              "
            />
          </DialogWindow>
          <!-- メモ編集ダイアログ -->
          <DialogWindow
            :title="$t('SOP.Chr.txtGCodeMemoEdit')"
            :dialogVisible="dialogVisibleRef.editDialogVisible"
            @closeDialog="closeDialog('editDialogVisible')"
            :onReject="() => console.log('cancel')"
            :onResolve="async () => editDialogResolveClickHandler()"
          >
            <CustomForm
              :formModel="gCodeEditFormRef.formModel"
              :formItems="gCodeEditFormRef.formItems"
              @visible="
                (v: CustomFormType['customForm']) => {
                  gCodeEditFormRef.customForm = v;
                }
              "
            />
            <div class="code-list">
              <el-table :data="codesList">
                <el-table-column
                  prop="shareRange"
                  label="共有範囲"
                  width="150"
                />
                <el-table-column prop="codes" label="コード" width="150" />
              </el-table>
            </div>
          </DialogWindow>
          <!-- メモ追加ダイアログ -->
          <DialogWindow
            :title="$t('SOP.Chr.txtGCodeMemoAdd')"
            :dialogVisible="dialogVisibleRef.addDialogVisible"
            @closeDialog="closeDialog('addDialogVisible')"
            :onReject="() => console.log('cancel')"
            :onResolve="async () => addDialogResolveClickHandler()"
          >
            <CustomForm
              :formModel="gCodeAddFormRef.formModel"
              :formItems="gCodeAddFormRef.formItems"
              @visible="
                (v: CustomFormType['customForm']) => {
                  gCodeAddFormRef.customForm = v;
                }
              "
            />
            <div class="code-list">
              <el-table :data="codesList">
                <el-table-column
                  prop="shareRange"
                  label="共有範囲"
                  width="150"
                />
                <el-table-column prop="codes" label="コード" width="150" />
              </el-table>
            </div>
          </DialogWindow>
        </div>
      </div>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <div class="dialog-footer-left">
          <ButtonEx
            type="secondary"
            size="normal"
            :text="t('Cm.Chr.btnCancel')"
            @click="handleGCodeCancel"
          />
          <!-- @click="SOPAddCancel()" -->
        </div>
        <div class="dialog-footer-right">
          <ButtonEx
            type="dangerSecond"
            size="normal"
            :text="t('SOP.Chr.btnGCodeMemoDelete')"
            @click="handleGCodeDelete"
          />
          <ButtonEx
            type="secondary"
            size="normal"
            :text="t('SOP.Chr.btnGCodeMemoEdit')"
            @click="handleGCodeEdit"
          />
          <ButtonEx
            type="secondary"
            size="normal"
            :text="t('SOP.Chr.btnGCodeMemoAdd')"
            @click="handleGCodeAdd"
          />
          <ButtonEx
            type="primary"
            size="normal"
            :text="t('SOP.Chr.btnGCodeMemoApply')"
            @click="handleGCodeAppply"
          />
          <!-- @click="SOPAddSave" -->
        </div>
      </div>
    </template>
    <MessageBox
      v-show="dialogVisibleRef.singleButtonRef"
      :dialog-props="messageBoxSingleButtonRef"
      :cancelCallback="() => closeDialog('singleButtonRef')"
      :submitCallback="() => closeDialog('singleButtonRef')"
    />
  </el-dialog>
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import ButtonEx from '@/components/parts/ButtonEx.vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import { reactive, watch, defineAsyncComponent, ref, onMounted } from 'vue';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import {
  DeleteGCode,
  GCodeListData,
  GetGCode,
  InsertGCode,
  UpdateGCode,
} from '@/types/HookUseApi/SopTypes';
import CONST from '@/constants/utils';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import useGetSopGCodeMemoData from '@/hooks/useApi/sopGetGCodeMemoData';
import useInsertSopGCodeMemoData from '@/hooks/useApi/sopInsertGCodeMemoData';
import useUpdateSopGCodeMemoData from '@/hooks/useApi/sopUpdateGCodeMemoData';
import useDeleteSopGCodeMemoData from '@/hooks/useApi/sopDeleteGCodeMemoData';
import CustomForm from '@/components/parts/CustomForm.vue';
import onValidateHandler from '@/utils/validateHandler';
import { CustomFormType } from '@/types/CustomFormTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import {
  gCodeDeleteFormItems,
  gCodeDeleteFormModel,
  gCodeEditFormItems,
  gCodeEditFormModel,
  gCodeAddFormItems,
  gCodeAddFormModel,
} from './gCodeDialog';

const gCodeDeleteFormRef = ref<CustomFormType>({
  formItems: gCodeDeleteFormItems,
  formModel: gCodeDeleteFormModel,
});
const gCodeEditFormRef = ref<CustomFormType>({
  formItems: gCodeEditFormItems,
  formModel: gCodeEditFormModel,
});
const gCodeAddFormRef = ref<CustomFormType>({
  formItems: gCodeAddFormItems,
  formModel: gCodeAddFormModel,
});
const { t } = useI18n();
type DialogRefKey =
  | 'deleteDialogVisible'
  | 'editDialogVisible'
  | 'addDialogVisible'
  | 'singleButtonRef';
const initialState: InitialDialogState<DialogRefKey> = {
  deleteDialogVisible: false,
  editDialogVisible: false,
  addDialogVisible: false,
  singleButtonRef: false,
};
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});
const TabulatorTable = defineAsyncComponent({
  loader: () =>
    import('@/components/model/common/TabulatorTable/TabulatorTable.vue'),
});
const codesList = [
  { shareRange: t('SOP.Chr.txtRangeSystem'), codes: 'F' },
  { shareRange: t('SOP.Chr.txtRangeBulkMatNo'), codes: 'C' },
  { shareRange: t('SOP.Chr.txtRangeMatNo'), codes: 'H' },
  { shareRange: t('SOP.Chr.txtRangeLotNo'), codes: 'L' },
  { shareRange: t('SOP.Chr.txtRangeBatchUnit'), codes: 'B' },
  { shareRange: t('SOP.Chr.txtRangePrcSeq'), codes: 'P' },
  { shareRange: t('SOP.Chr.txtRangeTerminal'), codes: 'T' },
  { shareRange: t('SOP.Chr.txtRangeInstruction'), codes: 'Z' },
];

interface Props {
  isGCodeDialogVisible: boolean;
  title: string;
  commonRequest: CommonRequestType;
}
const props = withDefaults(defineProps<Props>(), {
  isGCodeDialogVisible: false,
  title: '',
});
interface State {
  isGCodeDialogVisible: boolean;
  title: string;
}
const state = reactive<State>({
  isGCodeDialogVisible: props.isGCodeDialogVisible,
  title: props.title,
});

const tablePropsDataRef = ref<TabulatorTableIF>({
  pageName: 'GCodeList',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  title: state.title,
  dataID: 'gCodeId',
  autoWidth: 'gCodeExpl',
  showRadio: true,
  tableBtns: [],
  column: [
    { title: t('SOP.Chr.txtGCodeMemoTitle'), field: 'gCodeId' },
    { title: t('SOP.Chr.txtGCodeMemoComment'), field: 'gCodeExpl' },
    { title: t('SOP.Chr.txtGCodeMemoUpdDts'), field: 'updDts', hidden: true },
  ],
  tableData: [],
  showConditionSearch: false, // ConditionSearchの表示/非表示
  noUseConditionSearch: true,
  selectRowData: '',
});

const emit = defineEmits(['GCodeDialogVisible', 'valueApply']);
const selectedRowRef = ref<GCodeListData | null>(null);
/**
 * // ユーザー別グリッド列の並び順
 * <AUTHOR> Psdcd Created
 */
const updateSelectedRow = (v: GCodeListData) => {
  selectedRowRef.value = v;
};

/**
 * G-コードメモの一覧を取得
 */
const getGCodeMemoList = async () => {
  const apiRequestData: GetGCode = { gcode_id: '' };
  const { responseRef, errorRef } = await useGetSopGCodeMemoData({
    ...props.commonRequest,
    btnId: 'btnGCode',
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  if (responseRef.value) {
    tablePropsDataRef.value.tableData = responseRef.value.data.rData.rList;
  }
};
/**
 * G-コードメモの削除
 */
const deleteGCodeMemo = async () => {
  const apiRequestData: DeleteGCode = {
    gCodeId: gCodeDeleteFormRef.value.formModel.gCodeId,
    updDts: <string>selectedRowRef.value?.updDts,
  };
  const { responseRef, errorRef } = await useDeleteSopGCodeMemoData({
    ...props.commonRequest,
    btnId: 'btnGCodeMemoDelete',
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  if (responseRef.value?.data.rCode === CONST.API_STATUS_CODE.SUCCESS) {
    messageBoxSingleButtonRef.value.title = responseRef.value.data.rTitle;
    messageBoxSingleButtonRef.value.content = responseRef.value.data.rMsg;
    messageBoxSingleButtonRef.value.type = 'info';
    openDialog('singleButtonRef');
    getGCodeMemoList();
  }
};
/**
 * G-コードメモの編集
 */
const updateGCodeMemo = async (prevGCodeId: string) => {
  const apiRequestData: UpdateGCode = {
    gCodeId: gCodeEditFormRef.value.formModel.gCodeId,
    gCodeExpl: gCodeEditFormRef.value.formModel.gCodeExpl,
    prevGCodeId,
    updDts: <string>selectedRowRef.value?.updDts,
  };
  const { responseRef, errorRef } = await useUpdateSopGCodeMemoData({
    ...props.commonRequest,
    btnId: 'btnGCodeMemoEdit',
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  if (responseRef.value?.data.rCode === CONST.API_STATUS_CODE.SUCCESS) {
    messageBoxSingleButtonRef.value.title = responseRef.value.data.rTitle;
    messageBoxSingleButtonRef.value.content = responseRef.value.data.rMsg;
    messageBoxSingleButtonRef.value.type = 'info';
    openDialog('singleButtonRef');
    getGCodeMemoList();
  }
};
/**
 * G-コードメモの追加
 */
const insertGCodeMemo = async () => {
  const apiRequestData: InsertGCode = {
    gCodeId: gCodeAddFormRef.value.formModel.gCodeId,
    gCodeExpl: gCodeAddFormRef.value.formModel.gCodeExpl,
  };
  const { responseRef, errorRef } = await useInsertSopGCodeMemoData({
    ...props.commonRequest,
    btnId: 'btnGCodeMemoAdd',
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  if (responseRef.value?.data.rCode === CONST.API_STATUS_CODE.SUCCESS) {
    messageBoxSingleButtonRef.value.title = responseRef.value.data.rTitle;
    messageBoxSingleButtonRef.value.content = responseRef.value.data.rMsg;
    messageBoxSingleButtonRef.value.type = 'info';
    openDialog('singleButtonRef');
    getGCodeMemoList();
  }
};

/**
 * [反映] ボタン
 */
const handleGCodeAppply = () => {
  if (selectedRowRef.value === null) {
    messageBoxSingleButtonRef.value.title = `${t('Cm.Chr.txtUnselectedData')}`;
    messageBoxSingleButtonRef.value.content = `${t('Cm.Msg.unselectedData')}`;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  emit('valueApply', selectedRowRef.value?.gCodeId);
  state.isGCodeDialogVisible = false;
  emit('GCodeDialogVisible', false);
};
/**
 * [メモ削除] ボタン
 */
const handleGCodeDelete = () => {
  if (selectedRowRef.value === null) {
    messageBoxSingleButtonRef.value.title = `${t('Cm.Chr.txtUnselectedData')}`;
    messageBoxSingleButtonRef.value.content = `${t('Cm.Msg.unselectedData')}`;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  // 初期値セット
  gCodeDeleteFormRef.value.formItems.gCodeId.formModelValue =
    selectedRowRef.value.gCodeId;
  gCodeDeleteFormRef.value.formItems.gCodeExpl.formModelValue =
    selectedRowRef.value.gCodeExpl;
  openDialog('deleteDialogVisible');
};
const deleteDialogResolveClickHandler = async () => {
  await deleteGCodeMemo();
  gCodeDeleteFormRef.value.formItems.gCodeId.formModelValue = '';
  gCodeDeleteFormRef.value.formItems.gCodeExpl.formModelValue = '';
  return true;
};

/**
 * [メモ編集] ボタン
 */
const handleGCodeEdit = () => {
  if (selectedRowRef.value === null) {
    messageBoxSingleButtonRef.value.title = `${t('Cm.Chr.txtUnselectedData')}`;
    messageBoxSingleButtonRef.value.content = `${t('Cm.Msg.unselectedData')}`;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  // 初期値セット
  gCodeEditFormRef.value.formItems.gCodeId.formModelValue =
    selectedRowRef.value.gCodeId;
  gCodeEditFormRef.value.formItems.gCodeExpl.formModelValue =
    selectedRowRef.value.gCodeExpl;
  openDialog('editDialogVisible');
};
const editDialogResolveClickHandler = async () => {
  // 必須入力チェック
  const validate =
    gCodeEditFormRef.value.customForm !== undefined &&
    (await gCodeEditFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (!validate) {
    return false;
  }
  let targetGCodeId = '';
  if (selectedRowRef.value === null) {
    targetGCodeId = '';
  } else {
    targetGCodeId = selectedRowRef.value.gCodeId;
  }
  await updateGCodeMemo(targetGCodeId);
  return true;
};
/**
 * [メモ追加] ボタン
 */
const handleGCodeAdd = () => {
  gCodeAddFormRef.value.formItems.gCodeId.formModelValue = '';
  gCodeAddFormRef.value.formItems.gCodeExpl.formModelValue = '';
  openDialog('addDialogVisible');
};
const addDialogResolveClickHandler = async () => {
  // 必須入力チェック
  const validate =
    gCodeAddFormRef.value.customForm !== undefined &&
    (await gCodeAddFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (!validate) {
    return false;
  }

  await insertGCodeMemo();
  return true;
};
/**
 * [キャンセル] ボタン
 */
const handleGCodeCancel = () => {
  state.isGCodeDialogVisible = false;
  emit('GCodeDialogVisible', false);
};
/**
 * ダイアログ表示時
 */
const handleDialogOpen = () => {
  console.log(tablePropsDataRef.value);
  // tablePropsDataRef.value.selectRowData = '';
};

watch(
  () => props.isGCodeDialogVisible,
  (newOC: Props['isGCodeDialogVisible']) => {
    if (newOC) {
      state.isGCodeDialogVisible = true;
    }
  },
  { deep: true },
);

onMounted(() => {
  getGCodeMemoList();
});
</script>
<style lang="scss" scoped>
$namespace: 'dialog-window';
$commonPadding: '20px';
.#{$namespace} {
  &_header {
    font-size: 28px;
  }
  &_main {
    border-top: 1px solid var(--el-border-color);
    margin-top: 8px;
    padding: #{$commonPadding};
    &-reset {
      margin: -30px -20px;
    }
  }

  &_footer {
    border-top: 1px solid var(--el-border-color);
    padding-top: #{$commonPadding};
    padding-inline: #{$commonPadding};
    &-reset {
      margin-top: -10px;
      margin-inline: -20px;
    }
  }

  &_btns {
    display: flex;
    justify-content: space-between;
  }
}

.dialog-footer {
  margin-top: 5px;
  margin-bottom: 40px;
}
.dialog-footer-left {
  float: left;
}
.dialog-footer-right {
  float: right;
}

.code-list {
  margin-top: 30px;
}
::v-deep .el-table th {
  color: $white750;
  background-color: $blue100;
}
</style>
