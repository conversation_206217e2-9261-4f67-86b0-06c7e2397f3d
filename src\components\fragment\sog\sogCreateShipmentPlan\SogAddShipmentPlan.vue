<template>
  <!-- 出荷予定追加ダイアログ -->
  <DialogWindow
    :title="$t('Sog.Chr.txtSogAddShipmentPlan')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkSogEditShipmentPlanForm()"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="sogAddShipmentPlanRef.formModel"
      :formItems="sogAddShipmentPlanRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          sogAddShipmentPlanRef.customForm = v;
        }
      "
      @selectedItem="updateFormItems"
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 出荷予定追加の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.sogAddShipmentPlanConfirm"
    :dialogProps="messageBoxSogAddShipmentPlanConfirm"
    :cancelCallback="() => closeDialog('sogAddShipmentPlanConfirm')"
    :submitCallback="apiHandler"
  />
  <!-- 出荷予定追加完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxSogAddShipmentPlanCompletedVisible"
    :dialogProps="messageBoxSogAddShipmentPlanCompletedRef"
    :submitCallback="closeAllDialog"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import { useGetComboBoxDataStandard, useAddSogPlan } from '@/hooks/useApi';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  setCustomFormComboBoxOptionList,
  getComboBoxOptionList,
} from '@/utils/comboBoxOptionList';
import { getDateByType, toNumberOrNull } from '@/utils/index';
import { closeLoading, showLoading } from '@/utils/dialog';
import {
  ExtendCommonRequestType,
  CommonRequestType,
  ComboBoxDataStandardReturnData,
} from '@/types/HookUseApi/CommonTypes';
import { AddSogPlanReq, GetSogPlanListData } from '@/types/HookUseApi/SogTypes';
import {
  sogAddShipmentPlanFormModel,
  getSogAddShipmentPlanFormItems,
} from './sogAddShipmentPlan';

const { t } = useI18n();
const props = defineProps<Props>();
type Props = {
  selectedRows: GetSogPlanListData[];
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
};

const emit = defineEmits(['submit']);

let comboBoxResData: ComboBoxDataStandardReturnData | undefined;
let responseSogPlanNo = '';

type DialogRefKey =
  | 'messageBoxApiErrorVisible'
  | 'sogAddShipmentPlanConfirm'
  | 'fragmentDialogVisible'
  | 'messageBoxSogAddShipmentPlanCompletedVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  messageBoxApiErrorVisible: false,
  sogAddShipmentPlanConfirm: false,
  fragmentDialogVisible: false,
  messageBoxSogAddShipmentPlanCompletedVisible: false,
};

const constantData = {
  matNo: 'matNo',
  bpId: 'bpId',
  totalInvQty: 'totalInvQty',
  invQty: 'invQty',
  zoneNo: 'zoneNo',
  locNo: 'locNo',
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

const messageBoxSogAddShipmentPlanConfirm: DialogProps = {
  title: t('Sog.Chr.txtSogAddShipmentPlan'),
  content: t('Sog.Msg.txtAddShippingConfirm'),
  type: 'question',
};

const messageBoxSogAddShipmentPlanCompletedRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const sogAddShipmentPlanRef = ref<CustomFormType>({
  formItems: getSogAddShipmentPlanFormItems(),
  formModel: sogAddShipmentPlanFormModel,
});

/**
 * 出荷予定追加チェック
 */
const checkSogEditShipmentPlanForm = async () => {
  const validate =
    sogAddShipmentPlanRef.value.customForm !== undefined &&
    (await sogAddShipmentPlanRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (validate) {
    openDialog('sogAddShipmentPlanConfirm');
  }
  return false;
};

const apiHandler = async () => {
  closeDialog('sogAddShipmentPlanConfirm');
  showLoading();
  const apiRequestData: ExtendCommonRequestType<AddSogPlanReq> = {
    ...props.privilegesBtnRequestData,
    sogPlanYmd:
      sogAddShipmentPlanRef.value.formItems.sogPlanYmd.formModelValue.toString(),
    bpTrfId:
      sogAddShipmentPlanRef.value.formItems.bpTrfId.formModelValue.toString(),
    matNo:
      sogAddShipmentPlanRef.value.formItems.matNo.formModelValue.toString(),
    lotNo:
      sogAddShipmentPlanRef.value.formItems.lotNo.formModelValue.toString(),
    sogQty:
      sogAddShipmentPlanRef.value.formItems.sogQty.formModelValue.toString(),
    pltCnt: toNumberOrNull(
      sogAddShipmentPlanRef.value.formItems.pltCnt.formModelValue,
    ),
    baleCnt: toNumberOrNull(
      sogAddShipmentPlanRef.value.formItems.baleCnt.formModelValue,
    ),
    srcZoneGrpNo:
      sogAddShipmentPlanRef.value.formItems.srcZoneGrpNo.formModelValue.toString(),
    msgboxTitleTxt: messageBoxSogAddShipmentPlanConfirm.title,
    msgboxMsgTxt: messageBoxSogAddShipmentPlanConfirm.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
  };
  const { responseRef, errorRef } = await useAddSogPlan(apiRequestData);
  if (errorRef.value) {
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    messageBoxSogAddShipmentPlanCompletedRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxSogAddShipmentPlanCompletedRef.value.content =
      responseRef.value.data.rMsg;
    responseSogPlanNo = responseRef.value.data.rData.sogPlanNo;
    openDialog('messageBoxSogAddShipmentPlanCompletedVisible');
  }
  closeLoading();
  return true;
};

const closeAllDialog = () => {
  closeDialog('messageBoxSogAddShipmentPlanCompletedVisible');
  closeDialog('sogAddShipmentPlanConfirm');
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData, responseSogPlanNo);
};

const updateFormItems = (fieldId: string) => {
  if (fieldId === constantData.matNo) {
    if (
      sogAddShipmentPlanRef.value.formItems.matNo.formRole === 'selectComboBox'
    ) {
      if (
        sogAddShipmentPlanRef.value.formItems.matNo.selectOptions.length > 0
      ) {
        const selectOptionData =
          sogAddShipmentPlanRef.value.formItems.matNo.selectOptions;
        const matNo =
          sogAddShipmentPlanRef.value.formItems.matNo.formModelValue.toString();

        if (sogAddShipmentPlanRef.value.formItems.matNo.formModelValue !== '') {
          // 品目が選択済みの場合：選択した品目に該当する単位名を単位に表示する
          sogAddShipmentPlanRef.value.formItems.unitNm.formModelValue =
            getComboBoxOptionList(
              selectOptionData,
              matNo,
              'mes_unit_nm',
            )!.optionValList[0].toString();
        } else {
          // 品目が未選択の場合：単位を空白にする
          sogAddShipmentPlanRef.value.formItems.unitNm.formModelValue = '';
        }
      }
    }
  }
};

/**
 * 初期設定
 */
const SogAddShipmentPlanInit = async () => {
  showLoading();
  updateDialogChangeFlagRef(false);
  sogAddShipmentPlanRef.value.formItems = getSogAddShipmentPlanFormItems();
  comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'bpTrfId',
        condKey: 'm_bp_trf',
      },
      {
        cmbId: 'matNo',
        condKey: 'm_mat',
        optionCol: {
          mes_unit_nm: 'unitNmJp',
        },
      },
      {
        cmbId: 'srcZoneGrpNo',
        condKey: 'm_ic_zone_grp',
        where: {
          dsp_flg_sog1: '1',
        },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setCustomFormComboBoxOptionList(
      sogAddShipmentPlanRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }
  sogAddShipmentPlanRef.value.formItems.sogPlanYmd.formModelValue =
    getDateByType(new Date().toString(), 'YYYY/MM/DD');
  closeLoading();
  openDialog('fragmentDialogVisible');
};

watch(() => props.isClicked, SogAddShipmentPlanInit);
</script>
