<template>
  <el-checkbox-group
    :modelValue="props.modelValue"
    @change="handleChangeModelValue"
    :class="{ 'horizontal-class': !props.isHorizontal }"
  >
    <el-checkbox
      :class="checkboxClass"
      v-for="(item, index) in props.optionsData.value"
      :key="item"
      :label="item"
      :value="item"
      border
      :disabled="props.disabled"
      >{{ $t(optionsData.label[index]) }}</el-checkbox
    >
  </el-checkbox-group>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { SizeType } from '@/types/util';
import SCSS from '@/constants/scssVariables';

type Option = {
  label: string[];
  value: string[];
};

type Props = {
  size?: SizeType;
  width?: string;
  disabled?: boolean;
  isHorizontal?: boolean;
  optionsData: Option;
  modelValue: string[];
};

const props = withDefaults(defineProps<Props>(), {
  size: 'small',
  width: SCSS.widthSmall,
  disabled: false,
  isHorizontal: false,
});

const checkboxClass = computed(() => `checkbox-ex_${props.size}`);

const emit = defineEmits(['update:modelValue']);
const handleChangeModelValue = (value: string[]) => {
  emit('update:modelValue', value);
};
</script>

<style lang="scss" scoped>
$namespace: 'checkbox-ex';
$widthVal: v-bind('props.width');

@mixin setSizeMixin($width, $height) {
  width: $width !important;
  height: $height !important;
}

.#{$namespace} {
  &_custom {
    @include setSizeMixin($widthVal, $height);
  }
  &_large {
    @include setSizeMixin($widthLarge, $height);
  }
  &_middle {
    @include setSizeMixin($widthMiddle, $height);
  }
  &_small {
    @include setSizeMixin($widthSmall, $height);
  }
}
.el-checkbox {
  --el-checkbox-font-size: 16px;
}

.el-checkbox.is-bordered {
  border-color: $gray447;
}
.el-checkbox.is-bordered.is-checked {
  background-color: $blue727;
  border-color: $blue100;
}

:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: $blue100;
}

:deep(.el-checkbox__input .el-checkbox__inner) {
  border-color: $gray447;
}
:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: $blue100;
  border-color: $blue100;
}
.horizontal-class {
  display: inline-grid;
  row-gap: 15px;
}
</style>
