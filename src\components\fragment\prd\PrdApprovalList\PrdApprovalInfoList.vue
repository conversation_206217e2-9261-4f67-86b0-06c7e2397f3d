<template>
  <div class="prd-approval-info-list-dialog">
    <!-- 製造記録承認_記録詳細ダイアログ -->
    <DialogWindow
      :title="$t('Prd.Chr.txtApprovalList')"
      :dialogVisible="dialogVisibleRef.prdApprovalInfoListDialog"
      :buttons="[
        {
          type: 'secondary',
          size: 'normal',
          text: $t('Cm.Chr.btnCancel'),
        },
      ]"
      :width="OVERRIDE_DIALOG_WIDTH"
      @closeDialog="() => closeDialog('prdApprovalInfoListDialog')"
    >
      <!-- 見出し 製造指図情報 -->
      <BaseHeading
        level="2"
        :text="$t('Prd.Chr.txtOrderInformation')"
        fontSize="24px"
      />
      <!-- 製造指図情報の見出し+テキスト項目表示 -->
      <InfoShow
        class="Util_mt-16"
        :infoShowItems="productApprovalInfoShowRef.infoShowItems"
        :isLabelVertical="productApprovalInfoShowRef.isLabelVertical"
      />
      <div class="prd-approval-info-list-dialog_box-card-margin">
        <!-- 秤量記録エリア -->
        <div class="prd-approval-info-list-dialog_button-box">
          <span class="prd-approval-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtWeightRecord')
          }}</span>
          <div class="prd-approval-info-list-dialog_button-border-box">
            <div class="prd-approval-info-list-dialog_button-area">
              <div>
                <!-- 確認ボタン -->
                <ButtonEx
                  class="prd-approval-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnCheck')"
                  @click="clickWeightingRecBtn()"
                />
              </div>
              <div>
                <!-- 修正履歴ボタン-->
                <ButtonEx
                  class="prd-approval-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnFixHistory')"
                  :iconName="wgtRecModifyListButtonIconRef"
                  @click="clickWgtRecModifyListButton()"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- SOP記録エリア -->
        <div class="prd-approval-info-list-dialog_button-box">
          <span class="prd-approval-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtSopRec')
          }}</span>
          <div class="prd-approval-info-list-dialog_button-border-box">
            <div class="prd-approval-info-list-dialog_button-area">
              <div>
                <!-- 確認ボタン -->
                <ButtonEx
                  class="prd-approval-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnCheck')"
                  @click="clickSopRecordBtn()"
                />
              </div>
              <div>
                <!-- 修正履歴ボタン -->
                <ButtonEx
                  class="prd-approval-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnFixHistory')"
                  :iconName="sopRecModHistoryBtnIconRef"
                  @click="clickSopRecModHistoryBtn()"
                />
              </div>
              <div>
                <!-- 異状履歴ボタン -->
                <ButtonEx
                  class="prd-approval-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnDeviationHistory')"
                  :iconName="sopRecDevHistoryBtnIconRef"
                  @click="clickSopRecDevHistoryBtn()"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 投入実績エリア -->
        <div class="prd-approval-info-list-dialog_button-box">
          <span class="prd-approval-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtActualInput')
          }}</span>
          <div class="prd-approval-info-list-dialog_button-border-box">
            <div class="prd-approval-info-list-dialog_button-area">
              <div>
                <!-- 確認ボタン -->
                <ButtonEx
                  class="prd-approval-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnCheck')"
                  @click="clickBomListBtn()"
                />
              </div>
              <div>
                <!-- 修正履歴ボタン -->
                <ButtonEx
                  class="prd-approval-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnFixHistory')"
                  :iconName="inputRsltModHistoryBtnIconRef"
                  @click="clickInputRsltModHistoryBtn()"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 出来高エリア -->
        <div class="prd-approval-info-list-dialog_button-box">
          <span class="prd-approval-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtProduction')
          }}</span>
          <div class="prd-approval-info-list-dialog_button-border-box">
            <div class="prd-approval-info-list-dialog_button-area">
              <div>
                <!-- 確認ボタン -->
                <ButtonEx
                  class="prd-approval-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnCheck')"
                  @click="clickProductListBtn()"
                />
              </div>
              <div>
                <!-- 修正履歴ボタン -->
                <ButtonEx
                  class="prd-approval-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnFixHistory')"
                  :iconName="productModHistoryBtnIconRef"
                  @click="clickProductModHistoryBtn()"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 作業工数エリア -->
        <div class="prd-approval-info-list-dialog_button-box">
          <span class="prd-approval-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtWorkCost')
          }}</span>
          <div class="prd-approval-info-list-dialog_button-border-box">
            <div class="prd-approval-info-list-dialog_button-area">
              <div>
                <!-- 確認ボタン -->
                <ButtonEx
                  class="prd-approval-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnCheck')"
                  @click="clickPrdApprovalWorkButton()"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 枠外下部ボタンエリア -->
      <div class="prd-approval-info-list-dialog_button-area-bottom">
        <!-- 左側ボタン -->
        <div class="prd-approval-info-list-dialog_button-area-bottom-list">
          <!-- コメント確認 -->
          <ButtonEx
            type="secondary"
            size="normal"
            :text="t('Prd.Chr.btnCommentConfirm')"
            @click="clickCommentHistoryBtn()"
          />
        </div>
        <!-- 右側ボタン -->
        <div class="prd-approval-info-list-dialog_button-area-bottom-list">
          <!-- 指図記録 否認 -->
          <ButtonEx
            type="dangerSecond"
            size="normal"
            :text="t('Prd.Chr.btnOrderRecordDisapprove')"
            @click="clickOrderRecordDisapproveBtn()"
          />
          <!-- 指図記録 承認 -->
          <ButtonEx
            type="primary"
            size="normal"
            :text="t('Prd.Chr.btnOrderRecordApprove')"
            :disabled="disabledProductRecordApprovalButtonRef"
            @click="clickOrderRecordApproveBtn()"
          />
        </div>
      </div>
    </DialogWindow>
    <!-- 秤量記録ダイアログ -->
    <PrdApprovalWeighing
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowWeightingRecDialogRef"
      :odrNo="getOdrNo()"
      :infoData="initResponseData"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :dspNarrowType="PRD_APPROVAL_WEIGHING_NARROW_TYPE.APPROVAL"
    />
    <!-- 秤量記録修正履歴ダイアログ -->
    <PrdWgtRecModifyList
      :odrNo="getOdrNo()"
      :isClicked="isClickedShowWgtRecModifyListDialogRef"
      :dspNarrowType="PRD_WGT_REC_MODIFY_LIST_NARROW_TYPE.APPROVAL"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- SOP記録ダイアログ -->
    <PrdApprovalSOPFlowList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowSopRecordDialogRef"
      :selectedRow="selectedRow"
      :infoData="initResponseData"
      :routerName="props.routerName"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :dspNarrowType="PRD_APPROVAL_SOP_FLOW_LIST_NARROW_TYPE.APPROVAL"
    />
    <!-- SOP修正履歴ダイアログ -->
    <PrdModifyLogList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowSopRecordModifyHistoryDialogRef"
      :odrNo="getOdrNo()"
      :dspNarrowType="MODIFY_NARROW_TYPE.APPROVAL"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- 投入実績修正履歴ダイアログ -->
    <PrdBomModifyList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowBomModifyHistoryDialogRef"
      :odrNo="getOdrNo()"
      :dspNarrowType="BOM_LOG_NARROW_TYPE.APPROVAL"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- 出来高修正履歴ダイアログ -->
    <PrdProductModifyList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowPrdModifyHistoryDialogRef"
      :odrNo="getOdrNo()"
      :dspNarrowType="PRD_LOG_NARROW_TYPE.APPROVAL"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- コメント確認ダイアログ -->
    <PrdCommentLogList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowCommentHistoryDialogRef"
      :odrNo="getOdrNo()"
      :dspNarrowType="COMMENT_NARROW_TYPE.APPROVAL"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- 製造記録承認 投入実績ダイアログ -->
    <PrdBomList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowBomListDialogRef"
      :orderDetailInfo="productApprovalInfoShowItems"
      :odrNo="getOdrNo()"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :routerName="props.routerName"
      :dspNarrowType="PRDBOMLIST_NARROW_TYPE.APPROVAL"
    />
    <!-- 製造記録承認 出来高記録ダイアログ -->
    <PrdProductList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowProductListDialogRef"
      :orderDetailInfo="productApprovalInfoShowItems"
      :odrNo="getOdrNo()"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :routerName="props.routerName"
      :dspNarrowType="PRDPRODUCTLIST_NARROW_TYPE.APPROVAL"
    />
    <!-- 異状確認履歴ダイアログ -->
    <PrdApprovalDeviantList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowSopRecordDeviantHistoryDialogRef"
      :selectedRow="selectedRow"
      :infoData="initResponseData"
      :routerName="props.routerName"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :dspNarrowType="PRD_APPROVAL_DEVIANT_LIST_NARROW_TYPE.APPROVAL"
      @clickCancel="prdApprovalInfoListRedisplayInit"
    />
    <!-- 製造記録承認_作業工数ダイアログ -->
    <PrdApprovalWork
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedPrdApprovalWorkDialogRef"
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :accgYmd="initResponseData.accgYmd"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :dspNarrowType="PRD_APPROVAL_WORK_NARROW_TYPE.APPROVAL"
    />
    <!-- APIのエラー表示 -->
    <MessageBox
      v-if="dialogVisibleRef.messageBoxApiErrorVisible"
      :dialogProps="messageBoxApiErrorPropsRef"
      :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
    />
    <!-- 記録確認取消の確認表示 -->
    <MessageBox
      v-if="dialogVisibleRef.messageBoxRecordConfirmCancelVisible"
      :dialogProps="messageBoxRecordConfirmCancelPropsRef"
      :cancelCallback="
        () => closeDialog('messageBoxRecordConfirmCancelVisible')
      "
      :submitCallback="requestApiOrderRecordDisapprove"
    />
    <!-- 記録確認取消の完了表示 -->
    <MessageBox
      v-if="dialogVisibleRef.messageBoxRecordConfirmCancelCompleteVisible"
      :dialogProps="messageBoxRecordConfirmCancelCompletePropsRef"
      :submitCallback="closeDialogFromMessageBoxRecordConfirmCancelComplete"
    />
    <!-- 記録承認の確認表示 -->
    <MessageBox
      v-if="dialogVisibleRef.messageBoxRecordApprovalVisible"
      :dialogProps="messageBoxRecordApprovalPropsRef"
      :cancelCallback="() => closeDialog('messageBoxRecordApprovalVisible')"
      :submitCallback="requestApiOrderRecordApprove"
    />
    <!-- 記録承認の完了表示 -->
    <MessageBox
      v-if="dialogVisibleRef.messageBoxRecordApprovalCompleteVisible"
      :dialogProps="messageBoxRecordApprovalCompletePropsRef"
      :submitCallback="closeDialogFromMessageBoxRecordApprovalComplete"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  MODIFY_NARROW_TYPE,
  BOM_LOG_NARROW_TYPE,
  PRD_LOG_NARROW_TYPE,
  COMMENT_NARROW_TYPE,
  PRDBOMLIST_NARROW_TYPE,
  PRDPRODUCTLIST_NARROW_TYPE,
  PRD_APPROVAL_DEVIANT_LIST_NARROW_TYPE,
  PRD_APPROVAL_WEIGHING_NARROW_TYPE,
  PRD_APPROVAL_SOP_FLOW_LIST_NARROW_TYPE,
  PRD_APPROVAL_WORK_NARROW_TYPE,
  PRD_WGT_REC_MODIFY_LIST_NARROW_TYPE,
  GetApprovalOrderInfoListResponseData,
  GetApprovalRecordInfoInitRequestData,
  GetApprovalRecordInfoInitResponseData,
  ModifyApprovalInfoRequestData,
  ModifyApprovalRecordApprovalRequestData,
} from '@/types/HookUseApi/PrdTypes';
import {
  useGetApprovalRecordInfoInit,
  useModifyApprovalInfo,
  useModifyApprovalRecordApproval,
  useGetComboBoxDataStandard,
} from '@/hooks/useApi';
import BaseHeading from '@/components/base/BaseHeading.vue';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import createMessageBoxForm from '@/utils/commentMessageBox';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import ButtonEx from '@/components/parts/ButtonEx.vue';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import PrdApprovalWeighing from '@/components/include/prd/PrdApprovalWeighing.vue';
import PrdWgtRecModifyList from '@/components/include/prd/PrdWgtRecModifyList.vue';
import PrdApprovalSOPFlowList from '@/components/include/prd/PrdApprovalSOPFlowList.vue';
import PrdApprovalDeviantList from '@/components/include/prd/PrdApprovalDeviantList.vue';
import PrdModifyLogList from '@/components/include/prd/PrdModifyLogList.vue';
import PrdBomModifyList from '@/components/include/prd/PrdBomModifyList.vue';
import PrdProductModifyList from '@/components/include/prd/PrdProductModifyList.vue';
import PrdCommentLogList from '@/components/include/prd/PrdCommentLogList.vue';
import PrdBomList from '@/components/include/prd/PrdBomList.vue';
import PrdProductList from '@/components/include/prd/PrdProductList.vue';
import PrdApprovalWork from '@/components/include/prd/PrdApprovalWork.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { InfoShowType, InfoShowItem } from '@/types/InfoShowTypes';
import { IconTypes } from '@/types/IconTypes';
import getProductApprovalInfoShowItems from './prdApprovalInfoList';

/**
 * 多言語
 */
const { t } = useI18n();

// NOTE:ボタン配置が特殊なため、デフォルトのダイアログ幅を使用せず、上書きします。
const OVERRIDE_DIALOG_WIDTH = '1000px';

const messageBoxForm = createMessageBoxForm('message', 'cmtWarning');

// 製造記録承認_記録詳細初期表示のレスポンスデータ
let initResponseData: GetApprovalRecordInfoInitResponseData = {
  matNo: '',
  dspNmJp: '',
  rxNmJp: '',
  lotNo: '',
  prcSeq: null,
  prdPlanQty: '',
  rsltQty: '',
  odrStYmd: '',
  rsltDts: '',
  accgYmd: '',
  yieldVal: '',
  recConfirmDts: '',
  recConfirmUsr: '',
  recDocVer: '',
  recApprovFlg: '',
  updDts: '',
  devExistFlg: '0',
};

// ボタンアイコン指定値
const iconGreen = 'icon_green';
const iconRed = 'icon_red';

const productApprovalInfoShowRef = ref<InfoShowType>({
  infoShowItems: getProductApprovalInfoShowItems(),
  isLabelVertical: true,
});

// 製造記録確認 ダイアログ用 製造指図情報表示用データ
let productApprovalInfoShowItems: Record<string, InfoShowItem>;

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 記録確認取消の確認メッセージボックス
const messageBoxRecordConfirmCancelPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleSopFlowRecordConfirmCancel'),
  content: t('Prd.Msg.contentSopFlowRecordConfirmCancel'),
  isPrompt: true,
  // NOTE: 取消は必須入力
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

// 記録確認取消の完了メッセージボックス
const messageBoxRecordConfirmCancelCompletePropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

// 記録承認の確認メッセージボックス
const messageBoxRecordApprovalPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleOrderRecordApproval'),
  content: t('Prd.Msg.contentOrderRecordApproval'),
  isPrompt: true,
  isSkipValidation: true, // 任意入力とする
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

// 記録承認の完了メッセージボックス
const messageBoxRecordApprovalCompletePropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

// 入力付き確認メッセージボックスの入力を初期化
const clearInputMessageBoxForm = () => {
  if ('isPrompt' in messageBoxRecordConfirmCancelPropsRef.value) {
    messageBoxRecordConfirmCancelPropsRef.value.formItems.message.formModelValue =
      '';
  }
  if ('isPrompt' in messageBoxRecordApprovalPropsRef.value) {
    messageBoxRecordApprovalPropsRef.value.formItems.message.formModelValue =
      '';
  }
};

// ダイアログの表示切替用定義
const initialState: InitialDialogState<DialogRefKey> = {
  prdApprovalInfoListDialog: false,
  messageBoxApiErrorVisible: false,
  messageBoxRecordConfirmCancelVisible: false,
  messageBoxRecordConfirmCancelCompleteVisible: false,
  messageBoxRecordApprovalVisible: false,
  messageBoxRecordApprovalCompleteVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

type DialogRefKey =
  | 'prdApprovalInfoListDialog'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxRecordConfirmCancelVisible'
  | 'messageBoxRecordConfirmCancelCompleteVisible'
  | 'messageBoxRecordApprovalVisible'
  | 'messageBoxRecordApprovalCompleteVisible';

type Props = {
  selectedRow: GetApprovalOrderInfoListResponseData | null; // 遷移元選択行
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  routerName: string; // TabulatorTable権限
  privilegesBtnRequestData: CommonRequestType;
};
const props = defineProps<Props>();

const emit = defineEmits(['submit']);

// 秤量記録修正履歴ボタン押下チェック
const checkClickedWgtRecModifyListButtonRef = ref<boolean>(false);
// SOP修正履歴ボタン押下チェック
const checkClickedSopRecordModifyHistoryButtonRef = ref<boolean>(false);
// 投入実績修正履歴ボタン押下チェック
const checkClickedInputResultModifyHistoryButtonRef = ref<boolean>(false);
// 出来高修正履歴ボタン押下チェック
const checkClickedProductModifyHistoryButtonRef = ref<boolean>(false);
// 指図記録 承認ボタン活性・非活性切り替え
const disabledProductRecordApprovalButtonRef = ref<boolean>(true);

// 秤量記録修正履歴ボタンアイコン
const wgtRecModifyListButtonIconRef = ref<IconTypes>(iconRed);
// SOP修正履歴ボタンアイコン
const sopRecModHistoryBtnIconRef = ref<IconTypes>(iconRed);
// 投入実績修正履歴ボタンアイコン
const inputRsltModHistoryBtnIconRef = ref<IconTypes>(iconRed);
// 出来高修正履歴ボタンアイコン
const productModHistoryBtnIconRef = ref<IconTypes>(iconRed);
// SOP記録異状履歴ボタンアイコン
const sopRecDevHistoryBtnIconRef = ref<IconTypes>(iconRed);

// 秤量記録確認ボタン' クリック
const isClickedShowWeightingRecDialogRef = ref<boolean>(false);
// 'SOP記録確認ボタン' クリック
const isClickedShowSopRecordDialogRef = ref<boolean>(false);
// 秤量記録修正履歴ボタン クリック
const isClickedShowWgtRecModifyListDialogRef = ref<boolean>(false);
// 'SOP記録修正履歴ボタン' クリック
const isClickedShowSopRecordModifyHistoryDialogRef = ref<boolean>(false);
// '投入実績修正履歴ボタン' クリック
const isClickedShowBomModifyHistoryDialogRef = ref<boolean>(false);
// '出来高修正履歴ボタン' クリック
const isClickedShowPrdModifyHistoryDialogRef = ref<boolean>(false);
// '作業工数確認ボタン' クリック
const isClickedPrdApprovalWorkDialogRef = ref<boolean>(false);
// 'コメント確認ボタン' クリック
const isClickedShowCommentHistoryDialogRef = ref<boolean>(false);
// '投入実績確認ボタン' クリック
const isClickedShowBomListDialogRef = ref<boolean>(false);
// '出来高記録確認ボタン' クリック
const isClickedShowProductListDialogRef = ref<boolean>(false);
// 'SOP異状履歴ボタン' クリック
const isClickedShowSopRecordDeviantHistoryDialogRef = ref<boolean>(false);

const getOdrNo = () => {
  if (props.selectedRow === null) {
    return undefined;
  }

  return props.selectedRow.odrNo;
};

const getPrcSeq = () => {
  // NOTE:規約上付与されているnullを外す対応。基本はBEで絶対送っている想定。
  if (initResponseData.prcSeq === null) {
    // NOTE:念のため0を入れる。0の場合はBE側でチェックされてエラーが出る。
    return 0;
  }

  return initResponseData.prcSeq;
};

/**
 * 秤量記録確認ボタン押下時
 */
const clickWeightingRecBtn = () => {
  isClickedShowWeightingRecDialogRef.value =
    !isClickedShowWeightingRecDialogRef.value;
};

/**
 * SOP記録確認ボタン押下時
 */
const clickSopRecordBtn = () => {
  isClickedShowSopRecordDialogRef.value =
    !isClickedShowSopRecordDialogRef.value;
};

/**
 * 秤量記録修正履歴ボタン押下時
 */
const clickWgtRecModifyListButton = () => {
  isClickedShowWgtRecModifyListDialogRef.value =
    !isClickedShowWgtRecModifyListDialogRef.value;
  checkClickedWgtRecModifyListButtonRef.value = true;
  wgtRecModifyListButtonIconRef.value = iconGreen;
};

/**
 * SOP修正履歴ボタン押下時
 */
const clickSopRecModHistoryBtn = () => {
  isClickedShowSopRecordModifyHistoryDialogRef.value =
    !isClickedShowSopRecordModifyHistoryDialogRef.value;
  checkClickedSopRecordModifyHistoryButtonRef.value = true;
  sopRecModHistoryBtnIconRef.value = iconGreen;
};

/**
 * 投入実績修正履歴ボタン押下時
 */
const clickInputRsltModHistoryBtn = () => {
  isClickedShowBomModifyHistoryDialogRef.value =
    !isClickedShowBomModifyHistoryDialogRef.value;
  checkClickedInputResultModifyHistoryButtonRef.value = true;
  inputRsltModHistoryBtnIconRef.value = iconGreen;
};

/**
 * 出来高修正履歴ボタン押下時
 */
const clickProductModHistoryBtn = () => {
  isClickedShowPrdModifyHistoryDialogRef.value =
    !isClickedShowPrdModifyHistoryDialogRef.value;
  checkClickedProductModifyHistoryButtonRef.value = true;
  productModHistoryBtnIconRef.value = iconGreen;
};

/**
 * SOP異状履歴ボタン押下時
 */
const clickSopRecDevHistoryBtn = () => {
  isClickedShowSopRecordDeviantHistoryDialogRef.value =
    !isClickedShowSopRecordDeviantHistoryDialogRef.value;
};

/**
 * 作業工数確認ボタン押下時
 */
const clickPrdApprovalWorkButton = () => {
  isClickedPrdApprovalWorkDialogRef.value =
    !isClickedPrdApprovalWorkDialogRef.value;
};

/**
 * コメント確認ボタン押下時
 */
const clickCommentHistoryBtn = () => {
  isClickedShowCommentHistoryDialogRef.value =
    !isClickedShowCommentHistoryDialogRef.value;
};

/**
 * 投入実績確認ボタン押下時
 */
const clickBomListBtn = () => {
  // 製造記録承認 投入実績ダイアログ用 製造指図情報表示用データをセット
  productApprovalInfoShowItems = productApprovalInfoShowRef.value.infoShowItems;

  isClickedShowBomListDialogRef.value = !isClickedShowBomListDialogRef.value;
};

/**
 * 出来高記録確認ボタン押下時
 */
const clickProductListBtn = () => {
  // 製造記録承認 出来高記録ダイアログ用 製造指図情報表示用データをセット
  productApprovalInfoShowItems = productApprovalInfoShowRef.value.infoShowItems;

  isClickedShowProductListDialogRef.value =
    !isClickedShowProductListDialogRef.value;
};

/**
 * 指図記録 承認ボタン活性・非活性切り替え
 */
const switchDisabledPrdRecSealBtn = () => {
  if (
    checkClickedWgtRecModifyListButtonRef.value &&
    checkClickedSopRecordModifyHistoryButtonRef.value &&
    checkClickedInputResultModifyHistoryButtonRef.value &&
    checkClickedProductModifyHistoryButtonRef.value &&
    initResponseData.devExistFlg === '0' &&
    initResponseData.recApprovFlg === '0'
  ) {
    disabledProductRecordApprovalButtonRef.value = false;
  } else {
    // NOTE:基本的に非活性に戻ることは無いが、異状履歴から戻ってきた時の再表示処理で戻る可能性がある
    disabledProductRecordApprovalButtonRef.value = true;
  }
};

/**
 * 指図記録 否認ボタン押下時
 */
const clickOrderRecordDisapproveBtn = () => {
  if (props.selectedRow === null) {
    return false;
  }
  if (props.selectedRow.odrNo === '') {
    return false;
  }
  // 記録承認取消の確認メッセージ表示
  clearInputMessageBoxForm(); // 開く前に入力フォームを初期化
  openDialog('messageBoxRecordConfirmCancelVisible');
  return false;
};

/**
 * 指図記録 承認ボタン押下時
 */
const clickOrderRecordApproveBtn = () => {
  if (props.selectedRow === null) {
    return false;
  }
  if (props.selectedRow.odrNo === '') {
    return false;
  }
  // 記録承認の確認メッセージ表示
  clearInputMessageBoxForm(); // 開く前に入力フォームを初期化
  openDialog('messageBoxRecordApprovalVisible');
  return false;
};

/**
 * 指図記録 否認押下時処理
 */
const requestApiOrderRecordDisapprove = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxRecordConfirmCancelVisible');

  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    // 署名ダイアログがキャンセルされた場合の処理
    return;
  }

  if (!('isPrompt' in messageBoxRecordConfirmCancelPropsRef.value)) {
    return;
  }

  showLoading();

  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する。
  const requestData: ModifyApprovalInfoRequestData = {
    odrNo:
      productApprovalInfoShowRef.value.infoShowItems.odrNo.infoShowModelValue,
    modExpl:
      messageBoxRecordConfirmCancelPropsRef.value.formItems.message.formModelValue.toString(),
    updDts: initResponseData.updDts,
  };
  const { responseRef, errorRef } = await useModifyApprovalInfo({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxRecordConfirmCancelPropsRef.value.title,
    msgboxMsgTxt: messageBoxRecordConfirmCancelPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    msgboxInputCmt:
      messageBoxRecordConfirmCancelPropsRef.value.formItems.message.formModelValue.toString(),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // ・データベースを更新した後、以下のメッセージを表示する。
  // 記録確認取消完了
  messageBoxRecordConfirmCancelCompletePropsRef.value.title =
    responseRef.value.data.rTitle;
  messageBoxRecordConfirmCancelCompletePropsRef.value.content =
    responseRef.value.data.rMsg;

  closeLoading();

  openDialog('messageBoxRecordConfirmCancelCompleteVisible');
  emit('submit');
};

/**
 * 指図記録 承認ボタン押下時処理
 */
const requestApiOrderRecordApprove = async () => {
  console.log('requestApiOrderRecordDisapprove');

  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxRecordApprovalVisible');

  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    // 署名ダイアログがキャンセルされた場合の処理
    return;
  }

  if (!('isPrompt' in messageBoxRecordApprovalPropsRef.value)) {
    return;
  }

  showLoading();

  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する。
  const requestData: ModifyApprovalRecordApprovalRequestData = {
    odrNo:
      productApprovalInfoShowRef.value.infoShowItems.odrNo.infoShowModelValue,
    modExpl:
      messageBoxRecordApprovalPropsRef.value.formItems.message.formModelValue.toString(),
    updDts: initResponseData.updDts,
  };
  const { responseRef, errorRef } = await useModifyApprovalRecordApproval({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxRecordApprovalPropsRef.value.title,
    msgboxMsgTxt: messageBoxRecordApprovalPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    msgboxInputCmt:
      messageBoxRecordApprovalPropsRef.value.formItems.message.formModelValue.toString(),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // ・データベースを更新した後、以下のメッセージを表示する。
  // 記録承認完了
  messageBoxRecordApprovalCompletePropsRef.value.title =
    responseRef.value.data.rTitle;
  messageBoxRecordApprovalCompletePropsRef.value.content =
    responseRef.value.data.rMsg;

  closeLoading();

  openDialog('messageBoxRecordApprovalCompleteVisible');
  emit('submit');
};

/**
 * 全てのボタンステータスの初期化処理（初期表示用）
 */
const resetAllButtonStatus = () => {
  disabledProductRecordApprovalButtonRef.value = true;
  checkClickedWgtRecModifyListButtonRef.value = false;
  checkClickedSopRecordModifyHistoryButtonRef.value = false;
  checkClickedInputResultModifyHistoryButtonRef.value = false;
  checkClickedProductModifyHistoryButtonRef.value = false;
  wgtRecModifyListButtonIconRef.value = iconRed;
  sopRecModHistoryBtnIconRef.value = iconRed;
  inputRsltModHistoryBtnIconRef.value = iconRed;
  productModHistoryBtnIconRef.value = iconRed;
  sopRecDevHistoryBtnIconRef.value = iconRed;

  // NOTE:初期表示、再表示どちらも行うべき初期化処理が出た場合は別途関数作成をお願いします。
  // 必要ならPrdConfirmInfoList.vueのresetButtonStatus()を参考にしてください。
};

// NOTE:内部でローディング対応しています。終わったらcloseLoading()されるため注意。
// 記録詳細初期表示APIのリクエストと反映
const requestApiGetApprovalRecordInfoInit = async () => {
  // 型ガード用の判定
  if (props.selectedRow === null) return Promise.reject();

  // ローディング表示
  showLoading();

  // 製造記録承認_記録詳細初期表示APIを呼び出す
  const apiRequestData: GetApprovalRecordInfoInitRequestData = {
    odrNo: props.selectedRow.odrNo,
  };
  const { responseRef, errorRef } = await useGetApprovalRecordInfoInit({
    ...props.privilegesBtnRequestData,
    ...apiRequestData,
  });
  if (errorRef.value) {
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    closeLoading();
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return Promise.reject(); // エラーの場合継続処理させない
  }
  if (responseRef.value) {
    const resData = responseRef.value.data;
    initResponseData = resData.rData;
    // 製造指図情報レイアウト用初期値設定
    Object.entries(initResponseData).forEach(([key, value]) => {
      if (key in productApprovalInfoShowRef.value.infoShowItems) {
        productApprovalInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });
    productApprovalInfoShowRef.value.infoShowItems.odrNo.infoShowModelValue =
      props.selectedRow.odrNo;

    // 異状履歴フラグの有無でアイコン色設定
    if (initResponseData.devExistFlg === '1') {
      sopRecDevHistoryBtnIconRef.value = iconRed;
    } else {
      sopRecDevHistoryBtnIconRef.value = iconGreen;
    }

    switchDisabledPrdRecSealBtn();
  }

  closeLoading();
  return Promise.resolve();
};

/**
 * 製造記録承認_記録詳細ダイアログの初期設定
 */
const prdApprovalInfoListRedisplayInit = async () => {
  // NOTE:再表示時に初期化するボタンがあればここで対応を行う。
  // 必要ならPrdConfirmInfoList.vueのresetButtonStatus()を参考にしてください。

  // NOTE:実装時点で後続処理がないため、Promise.rejectが来た時の処理を考慮していない。あれば考慮してください。
  // 記録詳細初期表示APIのリクエスト
  await requestApiGetApprovalRecordInfoInit();
};

/**
 * 製造記録承認_記録詳細ダイアログの初期設定
 */
const prdApprovalInfoListInit = async () => {
  // NOTE:未選択なら処理させない。エラーメッセージはTabulatorTable側で出してくれる。
  if (props.selectedRow === null) return;

  // NOTE:ボタン状態はAPIレスポンスの結果によって再度上書きされます
  // 初期表示専用の、全ボタン状態リセット
  resetAllButtonStatus();

  // ローディング表示
  showLoading();

  // 標準コンボボックス取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        // 記録確認取消コメント
        cmbId: 'cmtWarning',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_PRC_SOP_DENY' },
      },
      {
        // 製造記録承認コメント
        cmbId: 'cmtApprove',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_ODR_REC_APP' },
      },
    ],
  });

  if (
    'isPrompt' in messageBoxRecordConfirmCancelPropsRef.value &&
    comboBoxResData
  ) {
    // NOTE: 暫定対応 formItemsを都度生成したフォームで上書きして初期化する
    const resetData = createMessageBoxForm('message', 'cmtWarning');
    messageBoxRecordConfirmCancelPropsRef.value.formItems = resetData.formItems;

    // NOTE:共通コンボボックス情報を元にコメント付き警告メッセージを設定
    setCustomFormComboBoxOptionList(
      messageBoxRecordConfirmCancelPropsRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  if ('isPrompt' in messageBoxRecordApprovalPropsRef.value && comboBoxResData) {
    // NOTE: 暫定対応 formItemsを都度生成したフォームで上書きして初期化する
    const resetData = createMessageBoxForm('message', 'cmtApprove');
    messageBoxRecordApprovalPropsRef.value.formItems = resetData.formItems;

    // NOTE:共通コンボボックス情報を元にコメント付き警告メッセージを設定
    setCustomFormComboBoxOptionList(
      messageBoxRecordApprovalPropsRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  // 標準コンボボックス取得に対するcloseLoading
  closeLoading();

  // 記録詳細初期表示APIのリクエスト
  try {
    await requestApiGetApprovalRecordInfoInit();
  } catch (error) {
    // Promise.rejectされていたらダイアログを起動させない
    return;
  }

  // 問題なければダイアログ起動する
  openDialog('prdApprovalInfoListDialog');
};

// 記録確認取消に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxRecordConfirmCancelComplete = () => {
  // 再検索用に親に実行を通知
  emit('submit', props.privilegesBtnRequestData);

  // 自身を閉じる
  closeDialog('messageBoxRecordConfirmCancelCompleteVisible');
  closeDialog('prdApprovalInfoListDialog');
};

// 記録承認に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxRecordApprovalComplete = () => {
  // 再検索用に親に実行を通知
  emit('submit', props.privilegesBtnRequestData);

  // 自身を閉じる
  closeDialog('messageBoxRecordApprovalCompleteVisible');
  closeDialog('prdApprovalInfoListDialog');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    prdApprovalInfoListInit();
  },
);
watch(
  // checkClickedWgtRecModifyListButtonRefの真偽値が切り替わりを監視
  () => checkClickedWgtRecModifyListButtonRef.value,
  async () => {
    switchDisabledPrdRecSealBtn();
  },
);
watch(
  // checkClickedSopRecordModifyHistoryButtonRefの真偽値が切り替わりを監視
  () => checkClickedSopRecordModifyHistoryButtonRef.value,
  async () => {
    switchDisabledPrdRecSealBtn();
  },
);
watch(
  // checkClickedInputResultModifyHistoryButtonRefの真偽値が切り替わりを監視
  () => checkClickedInputResultModifyHistoryButtonRef.value,
  async () => {
    switchDisabledPrdRecSealBtn();
  },
);
watch(
  // checkClickedProductModifyHistoryButtonRefの真偽値が切り替わりを監視
  () => checkClickedProductModifyHistoryButtonRef.value,
  async () => {
    switchDisabledPrdRecSealBtn();
  },
);
</script>
<style lang="scss" scoped>
$namespace: 'prd-approval-info-list-dialog';
// NOTE: 製造記録確認、承認、参照で似通った実装をしているため、修正時は要確認
$boxTextFontSize: 18px; // ボックス見出しのフォントサイズ
$boxTextHeight: $boxTextFontSize + 2px; // ボックス見出しの高さ(フォントサイズより少し大きく確保)
$boxLineWidth: 1px; // ボックスの枠線の太さ
// (枠線内)ボックスの幅と高さ
$boxWidth: 180px;
$boxHeight: 200px;
// 枠線付きボックスの幅と高さ
$boxBorderWidth: $boxWidth + $boxLineWidth * 2;
$boxBorderHeight: $boxHeight + $boxLineWidth * 2;
// ボックスエリア単体の幅と高さ(ボックス+見出し)
$boxAreaWidth: $boxBorderWidth;
$boxAreaHeight: $boxBorderHeight + $boxTextHeight; // 見出しが上に付くため足しておく

.#{$namespace} {
  // 画面中央ボックス群
  &_box-card-margin {
    height: $boxAreaHeight; // 各箱エリアの高さと同一
    margin-top: 30px;
    display: flex;
    justify-content: space-between;
  }
  // 箱エリア一つ辺りの設定
  &_button-box {
    width: $boxAreaWidth;
    height: $boxAreaHeight;
    float: left;
    text-align: center !important;
  }
  // 箱枠線
  &_button-border-box {
    width: $boxWidth; // 枠線抜きの幅
    height: $boxHeight; // 枠線抜きの高さ
    border: $boxLineWidth solid #000000;
  }
  // 箱内ボタンテキスト
  &_button-area-text {
    font-size: $boxTextFontSize;
    display: block;
    height: $boxTextHeight;
  }
  // 箱内ボタンエリアに対する設定
  &_button-area {
    // NOTE:ボタン位置を10pxから始めるためのネガティブマージン
    margin-top: -20px;
  }
  // 箱内ボタンマージン
  &_button-margin {
    margin-top: 30px;
  }
  // 画面下部ボタンエリア
  &_button-area-bottom {
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
  }
  // 画面下部ボタンリスト
  &_button-area-bottom-list {
    display: flex;
  }
}
</style>
