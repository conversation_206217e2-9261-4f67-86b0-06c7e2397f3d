<template>
  <div class="sop-setting-main-detail">
    <el-form
      ref="dataSourceRef"
      :rules="dataRules"
      :model="state.partNumericTextInputVal"
      class="data-source-form"
    >
      <div class="sop-node-property-setting-main">
        <div class="sop-node-property-setting-main-contents">
          <!-- [記録値入力方法] -->
          <div>
            <h2 class="require-label top-narrow">
              {{
                $t(
                  'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputMethod',
                )
              }}
            </h2>
            <el-form-item prop="inputMethod">
              <!-- [手入力] -->
              <el-row :gutter="0">
                <el-col :span="10">
                  <RadioEx
                    :isHorizontal="false"
                    :class="'inputMethod'"
                    v-model="state.partNumericTextInputVal.inputMethod"
                    size="custom"
                    width="150px"
                    :optionsData="{
                      value: ['1'],
                      label: [
                        $t(
                          'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputMethodManualInput',
                        ),
                      ],
                    }"
                  />
                </el-col>
                <el-col :span="10">
                  <div class="top-narrow20">
                    <el-form-item prop="inputMethodDecimalPlaces">
                      <small>
                        {{
                          $t(
                            'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputMethodDecimalPlaces',
                          )
                        }}
                      </small>
                      <el-select
                        v-model="
                          state.partNumericTextInputVal.inputMethodDecimalPlaces
                        "
                        :placeholder="$t('Cm.Msg.pleaseSelect')"
                        @change="datasourceChange"
                        @clear="
                          () =>
                            (state.partNumericTextInputVal.inputMethodDecimalPlaces = 0)
                        "
                        clearable
                        filterable
                        :disabled="
                          state.partNumericTextInputVal.inputMethod !== '1' ||
                          state.partNumericTextInputVal.inputSelection !== '1'
                        "
                      >
                        <el-option
                          v-for="item in state.inputMethodDecimalPlacess"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                </el-col>
              </el-row>

              <!-- [固定値] -->
              <RadioEx
                :isHorizontal="false"
                :class="'inputMethod'"
                size="custom"
                width="150px"
                class="top-wide"
                v-model="state.partNumericTextInputVal.inputMethod"
                :optionsData="{
                  value: ['2'],
                  label: [
                    $t(
                      'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputMethodInstructionFixedValue',
                    ),
                  ],
                }"
              />
              <div class="indent-item">
                <el-form-item prop="inputMethodFixedValue">
                  <el-input
                    v-model="
                      state.partNumericTextInputVal.inputMethodFixedValue
                    "
                    @change="datasourceChange"
                    :placeholder="$t('Cm.Msg.pleaseInput')"
                    :disabled="
                      state.partNumericTextInputVal.inputMethod !== '2'
                    "
                  />
                </el-form-item>
              </div>
              <!-- [ノードID] -->
              <RadioEx
                :isHorizontal="false"
                :class="'inputMethod'"
                size="custom"
                width="150px"
                class="top-wide"
                v-model="state.partNumericTextInputVal.inputMethod"
                :optionsData="{
                  value: ['3'],
                  label: [
                    $t(
                      'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputMethodNodeID',
                    ),
                  ],
                }"
              />
              <div class="indent-item">
                <el-form-item prop="inputMethodNodeId">
                  <el-select
                    v-model="state.partNumericTextInputVal.inputMethodNodeId"
                    @change="datasourceChange"
                    @clear="
                      () =>
                        (state.partNumericTextInputVal.inputMethodNodeId = '')
                    "
                    clearable
                    :placeholder="$t('Cm.Msg.pleaseSelect')"
                    filterable
                    :disabled="
                      state.partNumericTextInputVal.inputMethod !== '3'
                    "
                  >
                    <el-option
                      v-for="item in state.nodeNames"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <!-- [G-コード] -->
              <RadioEx
                :isHorizontal="false"
                :class="'inputMethod'"
                size="custom"
                width="150px"
                class="top-wide"
                v-model="state.partNumericTextInputVal.inputMethod"
                :optionsData="{
                  value: ['4'],
                  label: [
                    $t(
                      'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputMethodGCode',
                    ),
                  ],
                }"
              />
              <div class="indent-item">
                <el-form-item prop="inputMethodGCode">
                  <el-input
                    v-model="state.partNumericTextInputVal.inputMethodGCode"
                    @change="datasourceChange"
                    :placeholder="$t('Cm.Msg.pleaseInput')"
                    :disabled="
                      state.partNumericTextInputVal.inputMethod !== '4'
                    "
                  />
                </el-form-item>
                <el-button
                  class="btn-gCode top-wide"
                  :disabled="state.partNumericTextInputVal.inputMethod !== '4'"
                  @click="handleGCodeDialog('inputMethodGCode')"
                >
                  {{
                    $t(
                      'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtSelectGCode',
                    )
                  }}
                </el-button>
              </div>
              <!-- [演算入力] -->
              <el-row>
                <el-col :span="10">
                  <RadioEx
                    :isHorizontal="false"
                    size="custom"
                    width="150px"
                    :class="'inputMethod'"
                    class="top-wide"
                    v-model="state.partNumericTextInputVal.inputMethod"
                    :optionsData="{
                      value: ['5'],
                      label: [
                        $t(
                          'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputMethodArithmeticInput',
                        ),
                      ],
                    }"
                  />
                </el-col>
                <el-col :span="10">
                  <div>
                    <el-form-item
                      prop="inputMethodFormulaButton"
                      class="formula-button"
                    >
                      <ButtonEx
                        type="secondary"
                        size="small"
                        style="z-index: 100"
                        :text="
                          $t(
                            'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputMethodFormula',
                          )
                        "
                        :disabled="
                          state.partNumericTextInputVal.inputMethod !== '5'
                        "
                        @click="clickExpressionHandler()"
                      />
                    </el-form-item>
                  </div>
                </el-col>
              </el-row>
              <el-form-item prop="inputMethodFormula" class="expression-disp">
                <p>
                  {{ `${dispExpressionLabelRef}` }}
                </p>
              </el-form-item>
              <!-- [出来高合計] -->
              <RadioEx
                :isHorizontal="false"
                :class="'inputMethod'"
                class="row-hold top-wide"
                v-model="state.partNumericTextInputVal.inputMethod"
                :disabled="
                  state.partNumericTextInputVal.inputSelection !== '1' ||
                  !state.isRxSop
                "
                :optionsData="{
                  value: ['6'],
                  label: [
                    $t(
                      'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputMethodTotalVolume',
                    ),
                  ],
                }"
              />
              <!-- [投入実績合計] -->
              <RadioEx
                :class="'inputMethod'"
                class="top-wide"
                :isHorizontal="false"
                v-model="state.partNumericTextInputVal.inputMethod"
                :disabled="
                  state.partNumericTextInputVal.inputSelection !== '1' ||
                  !state.isRxSop
                "
                :optionsData="{
                  value: ['7'],
                  label: [
                    $t(
                      'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputMethodTotalActualInput',
                    ),
                  ],
                }"
              />
              <div class="indent-item">
                <el-form-item prop="inputMethodMatNo">
                  <small>
                    {{
                      $t(
                        'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputMethodMatNo',
                      )
                    }}
                  </small>
                  <el-select
                    v-model="state.partNumericTextInputVal.inputMethodMatNo"
                    @change="datasourceChange"
                    @clear="
                      () =>
                        (state.partNumericTextInputVal.inputMethodMatNo = '')
                    "
                    :placeholder="$t('Cm.Msg.pleaseSelect')"
                    clearable
                    filterable
                    :disabled="
                      state.partNumericTextInputVal.inputMethod !== '7'
                    "
                  >
                    <el-option
                      v-for="item in state.componentItems"
                      :key="item.bomMatNo"
                      :label="item.bomMatNm"
                      :value="item.bomMatNo"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <!-- [指図情報から取得] -->
              <RadioEx
                :isHorizontal="false"
                class="top-wide"
                :class="'inputMethod'"
                v-model="state.partNumericTextInputVal.inputMethod"
                :disabled="!state.isRxSop"
                :optionsData="{
                  value: ['8'],
                  label: [
                    $t(
                      'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputMethodInstructionInfo',
                    ),
                  ],
                }"
              />
              <div class="indent-item">
                <el-form-item prop="inputMethodOrderItem">
                  <small>
                    {{
                      $t(
                        'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputMethodOrderItem',
                      )
                    }}
                  </small>
                  <el-select
                    v-model="state.partNumericTextInputVal.inputMethodOrderItem"
                    @change="datasourceChange"
                    @clear="
                      () =>
                        (state.partNumericTextInputVal.inputMethodOrderItem =
                          '')
                    "
                    :placeholder="$t('Cm.Msg.pleaseSelect')"
                    clearable
                    filterable
                    :disabled="
                      state.partNumericTextInputVal.inputMethod !== '8'
                    "
                  >
                    <el-option
                      v-for="item in state.inputMethodOrderItem"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </el-form-item>
          </div>
        </div>
        <div class="sop-node-property-setting-right-navi">
          <!-- [出力先設定] -->
          <h2 class="require-label top-narrow">
            {{
              $t(
                'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtOutputSetting',
              )
            }}
          </h2>
          <el-form-item prop="outputSetting">
            <!-- [なし] -->
            <RadioEx
              class="row-hold"
              :class="'outputSetting'"
              :isHorizontal="false"
              v-model="state.partNumericTextInputVal.outputSetting"
              :optionsData="{
                value: ['0'],
                label: [
                  $t(
                    'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtOutputSettingWithout',
                  ),
                ],
              }"
            />
            <!-- [出来高] -->
            <RadioEx
              :isHorizontal="false"
              :class="'outputSetting'"
              class="top-wide"
              :disabled="
                state.partNumericTextInputVal.inputSelection !== '1' ||
                !state.isRxSop
              "
              v-model="state.partNumericTextInputVal.outputSetting"
              :optionsData="{
                value: ['1'],
                label: [
                  $t(
                    'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtOutputVolumeSetting',
                  ),
                ],
              }"
            />
            <div class="indent-item">
              <!-- [出来高容器指定] -->
              <div class="item-div-title">
                {{
                  $t(
                    'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtVolumeInstructionLabel',
                  )
                }}
              </div>
              <el-form-item prop="outputVolumeContainerSetting">
                <div class="item-inner-child">
                  <RadioEx
                    :isHorizontal="true"
                    :class="'outputVolumeSetting'"
                    :disabled="
                      state.partNumericTextInputVal.outputSetting !== '1'
                    "
                    size="custom"
                    width="120px"
                    v-model="
                      state.partNumericTextInputVal.outputVolumeContainerSetting
                    "
                    :optionsData="{
                      value: ['0', '1'],
                      label: [
                        $t(
                          'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtVolumeWithoutNo',
                        ),
                        $t(
                          'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtVolumeWithoutExist',
                        ),
                      ],
                    }"
                  />
                  <div>
                    <el-form-item prop="volumeContainerNodeId">
                      <small>
                        {{
                          $t(
                            'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtVolumeContainerNodeId',
                          )
                        }}
                      </small>
                      <el-select
                        v-model="
                          state.partNumericTextInputVal.volumeContainerNodeId
                        "
                        @change="datasourceChange"
                        @clear="
                          () =>
                            (state.partNumericTextInputVal.volumeContainerNodeId =
                              '')
                        "
                        :placeholder="$t('Cm.Msg.pleaseSelect')"
                        clearable
                        filterable
                        :disabled="
                          state.partNumericTextInputVal
                            .outputVolumeContainerSetting !== '1' ||
                          state.partNumericTextInputVal.outputSetting !== '1'
                        "
                      >
                        <el-option
                          v-for="item in state.nodeNames"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                </div>
              </el-form-item>
              <!-- [出来高パレット] -->
              <div class="item-div-title">
                {{
                  $t(
                    'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtVolumePaletteLabel',
                  )
                }}
              </div>
              <el-form-item prop="outputVolumePaletteSetting">
                <div class="item-inner-child">
                  <RadioEx
                    :isHorizontal="true"
                    :class="'outputVolumeSetting'"
                    :disabled="
                      state.partNumericTextInputVal.outputSetting !== '1'
                    "
                    size="custom"
                    width="120px"
                    v-model="
                      state.partNumericTextInputVal.outputVolumePaletteSetting
                    "
                    :optionsData="{
                      value: ['0', '1'],
                      label: [
                        $t(
                          'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtVolumeWithoutNo',
                        ),
                        $t(
                          'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtVolumeWithoutExist',
                        ),
                      ],
                    }"
                  />
                  <div>
                    <el-form-item prop="volumePaletteNodeId">
                      <small>
                        {{
                          $t(
                            'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtVolumePaletteNodeId',
                          )
                        }}
                      </small>
                      <el-select
                        v-model="
                          state.partNumericTextInputVal.volumePaletteNodeId
                        "
                        @change="datasourceChange"
                        @clear="
                          () =>
                            (state.partNumericTextInputVal.volumePaletteNodeId =
                              '')
                        "
                        :placeholder="$t('Cm.Msg.pleaseSelect')"
                        clearable
                        filterable
                        :disabled="
                          state.partNumericTextInputVal
                            .outputVolumePaletteSetting !== '1' ||
                          state.partNumericTextInputVal.outputSetting !== '1'
                        "
                      >
                        <el-option
                          v-for="item in state.nodeNames"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                </div>
              </el-form-item>
              <div>
                <small>
                  {{
                    $t(
                      'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtVolumeZoneCd',
                    )
                  }}
                </small>
                <el-form-item prop="volumeZoneCd">
                  <el-select
                    class="top-narrow10"
                    v-model="state.partNumericTextInputVal.volumeZoneCd"
                    @change="datasourceChange"
                    @clear="
                      () => (state.partNumericTextInputVal.volumeZoneCd = '')
                    "
                    :placeholder="$t('Cm.Msg.pleaseSelect')"
                    clearable
                    filterable
                    :disabled="
                      state.partNumericTextInputVal.outputSetting !== '1'
                    "
                  >
                    <el-option
                      v-for="item in state.zoneCodes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </div>
            <!-- [G-コード] -->
            <RadioEx
              :isHorizontal="false"
              :class="'outputSetting'"
              class="top-wide"
              v-model="state.partNumericTextInputVal.outputSetting"
              :optionsData="{
                value: ['2'],
                label: [
                  $t(
                    'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtOutputSettingGCode',
                  ),
                ],
              }"
            />
            <div class="indent-item">
              <el-form-item prop="outputSettingGCode">
                <el-input
                  v-model="state.partNumericTextInputVal.outputSettingGCode"
                  @change="datasourceChange"
                  :placeholder="$t('Cm.Msg.pleaseInput')"
                  :disabled="
                    state.partNumericTextInputVal.outputSetting !== '2'
                  "
                />
              </el-form-item>
            </div>
            <el-button
              class="btn-gCode btn-right"
              :disabled="state.partNumericTextInputVal.outputSetting !== '2'"
              @click="handleGCodeDialog('outputSettingGCode')"
            >
              {{
                $t(
                  'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtSelectGCode',
                )
              }}
            </el-button>
            <!-- [理論収率] -->
            <RadioEx
              class="row-hold"
              :class="'outputSetting'"
              :isHorizontal="false"
              v-model="state.partNumericTextInputVal.outputSetting"
              :disabled="
                state.partNumericTextInputVal.inputSelection !== '1' ||
                !state.isRxSop
              "
              :optionsData="{
                value: ['3'],
                label: [
                  $t(
                    'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtOutputSettingYield',
                  ),
                ],
              }"
            />
            <!-- [作業工数] -->
            <RadioEx
              :isHorizontal="false"
              :class="'outputSetting'"
              class="top-wide"
              v-model="state.partNumericTextInputVal.outputSetting"
              :disabled="
                state.partNumericTextInputVal.inputSelection !== '1' ||
                !state.isRxSop
              "
              :optionsData="{
                value: ['4'],
                label: [
                  $t(
                    'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtOutputSettingManHour',
                  ),
                ],
              }"
            />
            <div class="indent-item">
              <el-form-item prop="outputSettingWorkItem">
                <small class="item-inner-title">
                  {{
                    $t(
                      'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtOutputSettingWorkItem',
                    )
                  }}
                </small>
                <el-select
                  v-model="state.partNumericTextInputVal.outputSettingWorkItem"
                  @change="datasourceChange"
                  @clear="
                    () =>
                      (state.partNumericTextInputVal.outputSettingWorkItem = '')
                  "
                  :placeholder="$t('Cm.Msg.pleaseSelect')"
                  clearable
                  filterable
                  :disabled="
                    state.partNumericTextInputVal.outputSetting !== '4'
                  "
                >
                  <el-option
                    v-for="item in state.outputSettingWorkItem"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </el-form-item>
        </div>
      </div>
      <div class="sop-node-property-setting-left-navi">
        <!-- [入力選択] -->
        <h2 class="require-label top-narrow">
          {{
            $t(
              'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputSelection',
            )
          }}
        </h2>
        <el-form-item prop="inputSelection">
          <RadioEx
            :isHorizontal="true"
            :class="'inputSelection'"
            v-model="state.partNumericTextInputVal.inputSelection"
            :optionsData="{
              value: ['1', '2'],
              label: [
                $t(
                  'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputSelectionNumeric',
                ),
                $t(
                  'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInputSelectionCharactor',
                ),
              ],
            }"
          />
        </el-form-item>
        <!-- [指示値設定] -->
        <h2 class="require-label">
          {{
            $t(
              'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInstructionValueSetting',
            )
          }}
        </h2>
        <el-form-item prop="instructionValueSetting">
          <!-- [指示値なし] -->
          <RadioEx
            :isHorizontal="false"
            :class="'instructionValueSetting'"
            v-model="state.partNumericTextInputVal.instructionValueSetting"
            @change="datasourceChange"
            :optionsData="{
              value: ['4'],
              label: [
                $t(
                  'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInstructionNone',
                ),
              ],
            }"
          />
          <!-- [固定値] -->
          <div class="InstructionNone-top-wide">
            <RadioEx
              :isHorizontal="false"
              :class="'instructionValueSetting'"
              v-model="state.partNumericTextInputVal.instructionValueSetting"
              @change="datasourceChange"
              :optionsData="{
                value: ['1'],
                label: [
                  $t(
                    'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInstructionFixedValue',
                  ),
                ],
              }"
            />
            <div class="indent-item">
              <el-form-item prop="instructionFixedValue">
                <el-input
                  v-model="state.partNumericTextInputVal.instructionFixedValue"
                  @change="datasourceChange"
                  :placeholder="$t('Cm.Msg.pleaseInput')"
                  :disabled="
                    state.partNumericTextInputVal.instructionValueSetting !==
                    '1'
                  "
                />
              </el-form-item>
            </div>
          </div>
          <!-- [ノードID] -->
          <RadioEx
            :isHorizontal="false"
            class="top-wide"
            :class="'instructionValueSetting'"
            v-model="state.partNumericTextInputVal.instructionValueSetting"
            @change="datasourceChange"
            :optionsData="{
              value: ['2'],
              label: [
                $t(
                  'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtNodeIDValue',
                ),
              ],
            }"
          />
          <div class="indent-item">
            <el-form-item prop="instructionNodeId">
              <el-select
                v-model="state.partNumericTextInputVal.instructionNodeId"
                @change="datasourceChange"
                @clear="
                  () => (state.partNumericTextInputVal.instructionNodeId = '')
                "
                :placeholder="$t('Cm.Msg.pleaseSelect')"
                clearable
                filterable
                :disabled="
                  state.partNumericTextInputVal.instructionValueSetting !== '2'
                "
              >
                <el-option
                  v-for="item in state.nodeNames"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </div>
          <!-- [G-コード] -->
          <RadioEx
            :isHorizontal="false"
            :class="'instructionValueSetting'"
            v-model="state.partNumericTextInputVal.instructionValueSetting"
            @change="datasourceChange"
            class="top-wide"
            :optionsData="{
              value: ['3'],
              label: [
                $t(
                  'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInstructionGCode',
                ),
              ],
            }"
          />
          <div class="indent-item">
            <el-form-item prop="instructionGCode">
              <el-input
                v-model="state.partNumericTextInputVal.instructionGCode"
                @change="datasourceChange"
                :placeholder="$t('Cm.Msg.pleaseInput')"
                :disabled="
                  state.partNumericTextInputVal.instructionValueSetting !== '3'
                "
              />
            </el-form-item>
            <el-button
              class="btn-gCode"
              :disabled="
                state.partNumericTextInputVal.instructionValueSetting !== '3'
              "
              @click="handleGCodeDialog('instructionGCode')"
            >
              {{
                $t(
                  'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtSelectGCode',
                )
              }}
            </el-button>
          </div>
        </el-form-item>
        <el-form
          ref="unitCdFormRef"
          :rules="unitCdRules"
          :model="state"
          class="data-source-form"
        >
          <!-- [指示値単位] -->
          <h2 id="tag_instUnitTxt">
            {{
              $t(
                'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInstructionValueUnit',
              )
            }}
          </h2>
          <div class="item-width">
            <el-form-item prop="instUnitTxt">
              <small>
                {{
                  $t(
                    'SOP.Chr.SOPDetailSetting.PartNumericTextInput.txtInstructionValueUnit',
                  )
                }}
              </small>
              <el-input
                v-model="state.instUnitTxt"
                @change="datasourceChange"
                :placeholder="$t('Cm.Msg.pleaseInput')"
                :disabled="state.partNumericTextInputVal.inputSelection !== '1'"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
      <GCodeDialog
        :title="t('SOP.Chr.txtGCodeMemoTitle')"
        :isGCodeDialogVisible="state.isGCodeDialogVisible"
        :commonRequest="props.commonRequest"
        @GCodeDialogVisible="GCodeDialogVisible"
        @valueApply="valueApply"
      />
    </el-form>
  </div>
  <!-- 式入力ダイアログ -->
  <ExpressionInputDialog
    :dialogVisible="dialogVisibleRef.expressionInputRef"
    :inputMethodFormula="state.partNumericTextInputVal.inputMethodFormula"
    :destinationNodeNames="state.nodeNames"
    :commonRequest="props.commonRequest"
    @dispExpression="dispExpression"
    ref="expressionInputDialogRef"
    @closeDialog="
      () => {
        closeDialog('expressionInputRef');
      }
    "
  />
  <!-- getRxSopFlowByNo(state.sopDataRx.sopFlowNo); -->
  <MessageBox
    v-show="dialogVisibleRef.singleButtonRef"
    :dialog-props="messageBoxSingleButtonRef"
    :cancelCallback="() => closeDialog('singleButtonRef')"
    :submitCallback="() => closeDialog('singleButtonRef')"
  />
</template>
<script setup lang="ts">
import { reactive, watch, onMounted, ref, computed, nextTick } from 'vue';
import {
  SelectOption,
  SelectOptionNumber,
  PartNumericTextInputProps,
  RxBomMatOption,
  RxSopSettingOption,
} from '@/types/SopDialogInterface';
import RadioEx from '@/components/base/RadioEx.vue';
import { rules } from '@/utils/validator';
import { FormInstance } from 'element-plus';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { useGetComboBoxDataStandard } from '@/hooks/useApi';
import {
  ComboBoxDataOptionData,
  CommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import { GetRxBomMat } from '@/types/HookUseApi/SopTypes';
import { useI18n } from 'vue-i18n';
import ExpressionInputDialog from '@/components/fragment/sop/sopPAExpressionInput/ExpressionInputDialog.vue';
import ButtonEx from '@/components/parts/ButtonEx.vue';
import GCodeDialog from '@/components/fragment/sop/sopPAGCode/GCodeListDialog.vue';
import useSearchRxBomMatNo from '@/hooks/useApi/searchSopRxBomMatNo';
import { closeLoading, showLoading } from '@/utils/dialog';
import {
  gCodeFormatSetting,
  requiredTag,
} from '@/components/model/SopPA/SopChartSetting';

const { t } = useI18n();

const expressionInputDialogRef = ref<InstanceType<
  typeof ExpressionInputDialog
> | null>(null);
type DialogRefKey = 'singleButtonRef' | 'expressionInputRef';
const initialState: InitialDialogState<DialogRefKey> = {
  singleButtonRef: false,
  expressionInputRef: false,
};
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  type: 'error',
});
interface Props {
  partNumericTextInput: PartNumericTextInputProps;
  destinationNodeNames: SelectOption[];
  commonRequest: CommonRequestType;
  instUnitTxt: string;
  sopDataRx: RxSopSettingOption;
  isRxSop: boolean;
}
interface State {
  partNumericTextInputVal: PartNumericTextInputProps;
  nodeNames: SelectOption[];
  inputMethodDecimalPlacess: SelectOptionNumber[];
  inputMethodOrderItem: SelectOption[];
  outputSettingWorkItem: SelectOption[];
  unitList: ComboBoxDataOptionData[];
  componentItems: RxBomMatOption[];
  zoneCodes: ComboBoxDataOptionData[];
  isGCodeDialogVisible: boolean;
  instUnitTxt: string;
  sopDataRxVal: RxSopSettingOption;
  isRxSop: boolean;
}
interface ValidationParams {
  prop: string;
  condition?: boolean;
}

/**
 * 数値文字入力
 * @vue-prop {Object} partNumericTextInput
 */
const props = withDefaults(defineProps<Props>(), {
  partNumericTextInput: () => ({
    inputSelection: '',
    instructionValueSetting: '',
    instructionFixedValue: '',
    instructionNodeId: '',
    instructionGCode: '',
    inputMethod: '',
    inputMethodDecimalPlaces: 0,
    inputMethodFixedValue: '',
    inputMethodNodeId: '',
    inputMethodGCode: '',
    inputMethodFormula: '',
    inputMethodMatNo: '',
    inputMethodOrderItem: '',
    outputSetting: '',
    outputVolumeContainerSetting: '',
    outputVolumePaletteSetting: '',
    volumePaletteNodeId: '',
    volumeContainerNodeId: '',
    volumeZoneCd: '',
    outputSettingGCode: '',
    outputSettingWorkItem: '',
  }),
  destinationNodeNames: () => [],
  instUnitTxt: '',
  sopDataRx: () => ({
    sopFlowNo: '',
    sopFlowNmJp: '',
    matNo: '',
    matNm: '',
    rxNo: '',
    rxNm: '',
    prcSeq: 0,
    prcNm: '',
    sopBatchType: '',
    helpBinPath: '',
    forcePrivGrpCd: '',
    skipPrivGrpCd: '',
    mstRelPermFlg: '',
    dspSeq: null,
    crtDts: '',
    updDts: '',
  }),
  isRxSop: false,
});

const state = reactive<State>({
  partNumericTextInputVal: props.partNumericTextInput,
  nodeNames: props.destinationNodeNames,
  instUnitTxt: props.instUnitTxt,
  inputMethodDecimalPlacess: [
    {
      value: 0,
      label: '0',
    },
    {
      value: 1,
      label: '1',
    },
    {
      value: 2,
      label: '2',
    },
    {
      value: 3,
      label: '3',
    },
    {
      value: 4,
      label: '4',
    },
    {
      value: 5,
      label: '5',
    },
    {
      value: 6,
      label: '6',
    },
    {
      value: 7,
      label: '7',
    },
    {
      value: 8,
      label: '8',
    },
    {
      value: 9,
      label: '9',
    },
    {
      value: 10,
      label: '10',
    },
    {
      value: 11,
      label: '11',
    },
  ],
  inputMethodOrderItem: [
    {
      value: '1',
      label: 'バッチトータルの生産予定量',
    },
    {
      value: '2',
      label: '製造番号',
    },
    {
      value: '3',
      label: '使用期限',
    },
    {
      value: '4',
      label: 'バッチ毎の生産予定量',
    },
    {
      value: '5',
      label: '自バッチ番号',
    },
    {
      value: '6',
      label: 'バッチ数',
    },
    {
      value: '7',
      label: '中間製品名',
    },
    {
      value: '8',
      label: '中間製品略号',
    },
    {
      value: '9',
      label: '出来高品名',
    },
    {
      value: '10',
      label: '出来高品コード',
    },
    {
      value: '11',
      label: '製造指図数量',
    },
  ],
  outputSettingWorkItem: [
    {
      value: 'H01',
      label: '人・清掃',
    },
    {
      value: 'H02',
      label: '人・稼働',
    },
    {
      value: 'H03',
      label: '人・切替準備',
    },
    {
      value: 'M01',
      label: '機械・稼働',
    },
    {
      value: 'L01',
      label: 'ロット数',
    },
  ],
  unitList: [],
  componentItems: [],
  zoneCodes: [],
  isGCodeDialogVisible: false,
  sopDataRxVal: props.sopDataRx,
  isRxSop: props.isRxSop,
});

const dispExpressionLabelRef = ref(
  `入力した式：${state.partNumericTextInputVal.inputMethodFormula}`,
);
const clickExpressionHandler = () => {
  openDialog('expressionInputRef');
};
const dispExpression = (expressionValue: string) => {
  dispExpressionLabelRef.value = `入力した式：`;
  if (expressionInputDialogRef.value !== null && expressionValue !== '') {
    state.partNumericTextInputVal.inputMethodFormula = expressionValue;
    dispExpressionLabelRef.value = `${dispExpressionLabelRef.value}${state.partNumericTextInputVal.inputMethodFormula}`;
  }
};
const currentFieldNameRef = ref<keyof PartNumericTextInputProps | null>(null);

const handleGCodeDialog = (targetElement: keyof PartNumericTextInputProps) => {
  currentFieldNameRef.value = targetElement;
  state.isGCodeDialogVisible = true;
};
const GCodeDialogVisible = (isShow: boolean) => {
  state.isGCodeDialogVisible = isShow;
};
const valueApply = (data: string) => {
  if (data !== null && currentFieldNameRef.value !== null) {
    // state.partNumericTextInputVal[currentFieldNameRef.value as keyof PartNumericTextInputProps] = data.toString();
    switch (currentFieldNameRef.value) {
      case 'instructionGCode':
        state.partNumericTextInputVal.instructionGCode = data;
        break;
      case 'inputMethodGCode':
        state.partNumericTextInputVal.inputMethodGCode = data;
        break;
      case 'outputSettingGCode':
        state.partNumericTextInputVal.outputSettingGCode = data;
        break;
      default:
        break;
    }
  }
};

const setErrorStyle = (classNm: string) => {
  nextTick(() => {
    const eles = document.querySelectorAll<HTMLElement>(
      `.${classNm}>.el-radio`,
    );
    eles.forEach((ele) => {
      const elestyle = ele.style;
      elestyle.border = '1.5px solid #f56c6c';
    });
  });
};
const resetStyle = (classNm: string) => {
  nextTick(() => {
    const eles = document.querySelectorAll<HTMLElement>(
      `.${classNm}>.el-radio`,
    );
    eles.forEach((ele) => {
      const elestyle = ele.style;
      elestyle.border = '';
    });
  });
};
const requiredRadio =
  ({ prop, condition }: ValidationParams) =>
  async (_: unknown, value: string) => {
    if (condition === false) {
      resetStyle(prop);
      return Promise.resolve();
    }
    if (value === null || value === '') {
      setErrorStyle(prop);
      return Promise.reject(new Error(''));
    }
    resetStyle(prop);
    return Promise.resolve();
  };
// GCodeフォーマットチェック
const gCodeFormat =
  (propName: string) =>
  (rule: { field: string }, value: string, callback: (val?: Error) => void) => {
    if (
      state.partNumericTextInputVal.instructionValueSetting !== '3' &&
      propName === 'instructionGCode'
    ) {
      callback();
    }
    if (
      state.partNumericTextInputVal.inputMethod !== '4' &&
      propName === 'inputMethodGCode'
    ) {
      callback();
    }
    if (
      state.partNumericTextInputVal.outputSetting !== '2' &&
      propName === 'outputSettingGCode'
    ) {
      callback();
    }
    if (rule.field !== '') {
      const regex = gCodeFormatSetting();
      const match = value.match(regex);
      if (match === undefined || match === null) {
        callback(new Error(`${t('Cm.Chr.txtGCodeErrorFormat')}`));
      }
      callback();
    }
  };

const unitCdRules = computed(() => ({
  // 指示値単位
  instUnitTxt: [
    {
      required: state.partNumericTextInputVal.inputSelection === '1',
      message: t('Cm.Chr.txtRequiredInput'),
      trigger: 'blur',
    },
  ],
}));
const dataRules = computed(() => ({
  // 入力選択
  inputSelection: [
    {
      validator: requiredRadio({ prop: 'inputSelection' }),
    },
  ],
  // 指示値設定
  instructionValueSetting: [
    {
      validator: requiredRadio({ prop: 'instructionValueSetting' }),
    },
  ],
  instructionFixedValue: [
    {
      required: state.partNumericTextInputVal.instructionValueSetting === '1',
      message: t('Cm.Chr.txtRequiredInput'),
      trigger: 'blur',
    },
    state.partNumericTextInputVal.inputSelection === '1' &&
    state.partNumericTextInputVal.instructionValueSetting === '1'
      ? rules.placesOfNumeric({ int: 11, decimal: 12 })
      : rules.length(32),
  ],
  instructionNodeId: [
    {
      required: state.partNumericTextInputVal.instructionValueSetting === '2',
      message: t('Cm.Chr.txtRequiredSelect'),
      trigger: 'blur',
    },
  ],
  instructionGCode: [
    {
      required: state.partNumericTextInputVal.instructionValueSetting === '3',
      message: t('Cm.Chr.txtRequiredInput'),
      trigger: 'blur',
    },
    { validator: gCodeFormat('instructionGCode'), trigger: 'blur' },
  ],
  // 記録値入力方法
  inputMethod: [
    {
      validator: requiredRadio({ prop: 'inputMethod' }),
    },
  ],
  inputMethodDecimalPlaces: [
    {
      required: state.partNumericTextInputVal.inputMethod === '1',
      message: t('Cm.Chr.txtRequiredSelect'),
      trigger: 'blur',
    },
  ],
  inputMethodFixedValue: [
    {
      required: state.partNumericTextInputVal.inputMethod === '2',
      message: t('Cm.Chr.txtRequiredInput'),
      trigger: 'blur',
    },
    state.partNumericTextInputVal.inputSelection === '1'
      ? rules.placesOfNumeric({ int: 11, decimal: 12 })
      : rules.length(32),
  ],
  inputMethodNodeId: [
    {
      required: state.partNumericTextInputVal.inputMethod === '3',
      message: t('Cm.Chr.txtRequiredSelect'),
      trigger: 'blur',
    },
  ],
  inputMethodGCode: [
    {
      required: state.partNumericTextInputVal.inputMethod === '4',
      message: t('Cm.Chr.txtRequiredInput'),
      trigger: 'blur',
    },
    { validator: gCodeFormat('inputMethodGCode'), trigger: 'blur' },
  ],
  inputMethodMatNo: [
    {
      required: state.partNumericTextInputVal.inputMethod === '7',
      message: t('Cm.Chr.txtRequiredSelect'),
      trigger: 'blur',
    },
  ],
  inputMethodOrderItem: [
    {
      required: state.partNumericTextInputVal.inputMethod === '8',
      message: t('Cm.Chr.txtRequiredSelect'),
      trigger: 'blur',
    },
  ],
  // 出力先設定
  outputSetting: [
    {
      validator: requiredRadio({ prop: 'outputSetting' }),
    },
  ],
  outputVolumeContainerSetting: [
    {
      validator: requiredRadio({
        prop: 'outputVolumeContainerSetting',
        condition: state.partNumericTextInputVal.outputSetting === '1',
      }),
    },
  ],
  outputVolumePaletteSetting: [
    {
      validator: requiredRadio({
        prop: 'outputVolumePaletteSetting',
        condition: state.partNumericTextInputVal.outputSetting === '1',
      }),
    },
  ],
  volumeContainerNodeId: [
    {
      required:
        state.partNumericTextInputVal.outputVolumeContainerSetting === '1',
      message: t('Cm.Chr.txtRequiredSelect'),
      trigger: 'blur',
    },
  ],
  volumePaletteNodeId: [
    {
      required:
        state.partNumericTextInputVal.outputVolumePaletteSetting === '1',
      message: t('Cm.Chr.txtRequiredSelect'),
      trigger: 'blur',
    },
  ],
  volumeZoneCd: [
    {
      required: state.partNumericTextInputVal.outputSetting === '1',
      message: t('Cm.Chr.txtRequiredSelect'),
      trigger: 'blur',
    },
  ],
  outputSettingGCode: [
    {
      required: state.partNumericTextInputVal.outputSetting === '2',
      message: t('Cm.Chr.txtRequiredInput'),
      trigger: 'blur',
    },
    { validator: gCodeFormat('outputSettingGCode'), trigger: 'blur' },
  ],
  outputSettingWorkItem: [
    {
      required: state.partNumericTextInputVal.outputSetting === '4',
      message: t('Cm.Chr.txtRequiredSelect'),
      trigger: 'blur',
    },
  ],
  inputMethodFormulaButton: [
    {
      required:
        state.partNumericTextInputVal.inputMethod === '5' &&
        state.partNumericTextInputVal.inputMethodFormula === '',
      message: t('Cm.Chr.txtRequiredInput'),
      trigger: 'blur',
    },
  ],
}));
const dataSourceRef = ref<FormInstance>();
const unitCdFormRef = ref<FormInstance>();

const getRxBomMat = async () => {
  const apiRequestData: GetRxBomMat = {
    matNo: state.sopDataRxVal.matNo,
    rxNo: state.sopDataRxVal.rxNo,
    prcSeq: state.sopDataRxVal.prcSeq,
  };
  const { responseRef, errorRef } = await useSearchRxBomMatNo({
    ...props.commonRequest,
    ...apiRequestData,
  });
  if (errorRef.value) {
    return;
  }
  if (responseRef.value) {
    const bomMatNoList = responseRef.value.data.rData.rList;
    if (bomMatNoList.length > 0) {
      bomMatNoList.forEach((bomMat) => {
        state.componentItems.push(bomMat);
      });
    }
  }
};

watch(
  () => props.partNumericTextInput,
  (newVal: Props['partNumericTextInput']) => {
    if (newVal) {
      state.partNumericTextInputVal = newVal;
      state.nodeNames = props.destinationNodeNames;
    }
  },
  { deep: true },
);
watch(
  () => props.destinationNodeNames,
  (newVal) => {
    state.nodeNames = newVal;
  },
);
watch(
  () => state.partNumericTextInputVal.inputSelection,
  (newVal) => {
    if (newVal) {
      if (
        newVal === '2' &&
        (state.partNumericTextInputVal.outputSetting === '1' ||
          state.partNumericTextInputVal.outputSetting === '3' ||
          state.partNumericTextInputVal.outputSetting === '4')
      ) {
        state.partNumericTextInputVal.outputSetting = '';
      }
      if (
        newVal === '2' &&
        (state.partNumericTextInputVal.inputMethod === '6' ||
          state.partNumericTextInputVal.inputMethod === '7' ||
          state.partNumericTextInputVal.inputMethod === '8')
      ) {
        state.partNumericTextInputVal.inputMethod = '';
      }
    }
  },
);
watch(
  () => state.partNumericTextInputVal.inputSelection,
  (newVal) => {
    if (newVal) {
      // 必須タグの表示切替処理
      document.querySelectorAll("[id^='tag_']").forEach((el) => {
        if (el.querySelector('.custom-form_tag')) {
          // 必須タグ削除
          el.removeChild(el.querySelector('.custom-form_tag')!);
        }
        if (newVal === '1') {
          if (!el.querySelector('span')) {
            // 必須タグ追加
            el.appendChild(requiredTag(t));
          }
        }
      });
    }
  },
);
watch(
  () => props.instUnitTxt,
  (newVal) => {
    if (newVal) {
      state.instUnitTxt = newVal;
    }
  },
);
watch(
  () => props.sopDataRx,
  (newVal: Props['sopDataRx']) => {
    if (newVal) {
      state.sopDataRxVal = newVal;
    }
  },
  { deep: true },
);
watch(
  () => props.isRxSop,
  (newVal: Props['isRxSop']) => {
    if (newVal) {
      state.isRxSop = newVal;
    }
  },
);

const emit = defineEmits(['individualParaChange', 'unitCdChange']);
const datasourceChange = () => {
  emit('individualParaChange', state.partNumericTextInputVal);
  emit('unitCdChange', state.instUnitTxt);
};
/**
 * コンボボックスデータ取得
 */
const getComboBoxData = async () => {
  showLoading();
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.commonRequest,
    condList: [
      {
        cmbId: 'numericZone',
        condKey: 'm_ic_zone_sop',
        where: { dsp_flg_sop2: '1' },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    // --[ゾーンコード]
    const zoneList = comboBoxResData.rData.rList.filter(
      (item: ComboBoxDataOptionData) => item.condKey === 'm_ic_zone_sop',
    );
    zoneList.forEach((option) => {
      state.zoneCodes.push(option);
    });
  }
  closeLoading();
};
onMounted(() => {
  getComboBoxData();
  if (state.isRxSop) {
    getRxBomMat();
  }
  // 必須タグ生成
  nextTick(() => {
    const elements = Array.from(document.querySelectorAll('.require-label'));
    elements.forEach((item) => {
      if (item.querySelector('span')) return;
      const tag = document.createElement('span');
      tag.textContent = t('Cm.Chr.txtTagOfRequired');
      tag.className = `Util_ml-8 custom-form_tag el-tag el-tag--danger el-tag--dark is-round`;
      tag.style.marginLeft = `8px`;
      item.appendChild(tag);
    });
    // 必須タグの表示切替処理
    document.querySelectorAll("[id^='tag_']").forEach((el) => {
      if (el.querySelector('.custom-form_tag')) {
        // 必須タグ削除
        el.removeChild(el.querySelector('.custom-form_tag')!);
      }
      if (state.partNumericTextInputVal.inputSelection === '1') {
        if (!el.querySelector('span')) {
          // 必須タグ追加
          el.appendChild(requiredTag(t));
        }
      }
    });
  });
});
defineExpose({
  validateConditionForm: () => dataSourceRef.value?.validate(),
  validateUnitCdForm: () => unitCdFormRef.value?.validate(),
});
</script>
<style lang="scss" scoped>
.sop-setting-main-detail {
  font-size: 12px;
  font-weight: normal;
}
.sop-setting-main-detail h2 {
  font-size: 12px;
  font-weight: normal;
  color: $gray156;
  display: flex;
  align-items: center;
}
.sop-setting-main-detail h2::after {
  margin-left: 10px;
  content: '';
  flex-grow: 1;
  /* 余白を分け与える */
  height: 1px;
  background: #e6e6e6;
}
.sop-node-property-setting-main {
  width: 70%;
  float: right;
}
.sop-node-property-setting-main-contents,
.sop-node-property-setting-left-navi,
.sop-node-property-setting-right-navi {
  height: 640px;
  padding: 5px 20px 5px;
}
.sop-node-property-setting-main-contents {
  color: #000;
  float: left;
  width: 45%;
}

.indent-item {
  margin-left: 50px;
  .el-form-item {
    width: 270px;
    margin-bottom: 10px;
    margin-top: 3px;
  }
  small {
    line-height: 1;
    color: #848588;
    margin-top: 0px;
    margin-bottom: 0px;
  }
}

.item-inner {
  margin-left: 50px;
  color: #303133;
  word-wrap: break-word;
  margin-top: -15px;
  .el-form-item {
    width: 95%;
  }
}
.item-inner-child {
  width: 100%;
  margin-bottom: -10px;
  small {
    color: #848588;
    margin-bottom: 0px;
    margin-top: 0px;
  }
  .el-form-item {
    margin-left: 30px;
  }
}
.item-inner-title {
  width: 290px;
  font-size: 11px;
  color: #303133;
  word-wrap: break-word;
}
.item-div-title {
  width: 290px;
  font-size: 13px;
  color: #303133;
  word-wrap: break-word;
  margin-bottom: -10px;
}
.top-wide {
  margin-top: 8px;
}
.InstructionNone-top-wide {
  margin-top: 32px;
}
.top-narrow {
  margin-top: -5px;
}
.top-narrow20 {
  margin-top: -22px;
  margin-left: 30px;
}
.top-narrow10 {
  margin-top: -10px;
}
.top-narrow15 {
  margin-top: -15px;
}
.left-wide {
  margin-left: 30px;
  width: 280px;
}
.row-hold {
  width: 100%;
  margin-bottom: 10px;
  height: 18px;
}
.item-width {
  width: 320px;
}

// Gコードボタン
.btn-gCode {
  font-size: 12px;
  margin-top: -10px;
  margin-bottom: 10px;
}
.btn-center {
  margin-left: 100px;
  margin-top: -14px;
  margin-bottom: 10px;
}
.btn-right {
  margin-left: 50px;
}
.btn-gCode.is-disabled,
.btn-center.is-disabled,
.btn-right.is-disabled {
  background-color: #f5f7fa;
}

.culc-input-btn {
  margin-top: 22px;
  margin-left: -50px;
  margin-bottom: -5px;
}

.expression-disp {
  height: 20px;
  margin-bottom: -5px;
  float: left;
  width: 100%;
  p {
    line-height: 1;
    color: #848588;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    margin-top: 0px;
  }
}

.formula-button {
  margin-top: 10px;
  margin-left: 30px;
  .span {
    font-weight: normal;
  }
}

.el-form-item__label {
  font-size: 13px;
}
.el-select {
  width: 100%;
}
.el-tabs__header {
  margin: 0px;
}
small {
  color: #848588;
  margin-bottom: -10px;
}
</style>
