import i18n from '@/constants/lang';
import CONST from '@/constants/utils';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// 出荷予定追加ダイアログのアイテム定義
export const getSogAddShipmentPlanFormItems: () => CustomFormType['formItems'] =
  () => ({
    unitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    sogPlanNo: {
      formModelValue: '',
      label: { text: t('Sog.Chr.txtSogPlanNo') }, // 出荷予定番号
      formRole: 'textBox',
      props: { disabled: true },
    },
    sogPlanYmd: {
      formModelValue: '',
      label: { text: t('Sog.Chr.txtSogPlanYmd') }, // 出荷予定日
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('date'), rules.futureDate()],
      formRole: 'date',
      props: { modelValue: '', type: 'date' },
    },
    bpTrfId: {
      formModelValue: '',
      label: { text: t('Sog.Chr.txtBpTrfNm') }, // 出荷先
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'bpTrfId',
    },
    matNo: {
      formModelValue: '',
      label: { text: t('Sog.Chr.txtMatNoNm') }, // 品目コード/品名
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('selectComboBox')],
      props: { disabled: false, optionsData: [] },
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'matNo',
    },
    lotNo: {
      formModelValue: '',
      label: { text: t('Sog.Chr.txtLotNo') }, // 製造番号
      rules: [
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]), // 共通仕様.禁則文字①除外チェック
      ],
      formRole: 'textBox',
    },
    sogQty: {
      formModelValue: '',
      label: { text: t('Sog.Chr.txtSogQty') }, // 出荷予定量
      suffix: { formModelProp: 'unitNm' },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [
        rules.required('textBox'),
        rules.positiveRealNumericOnly(), // 正の実数
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
      ],
      formRole: 'textBox',
    },
    pltCnt: {
      formModelValue: '',
      label: { text: t('Sog.Chr.txtPltCnt') }, // パレット数
      rules: [rules.naturalNumericOnly(), rules.length(9)],
      formRole: 'textBox',
    },
    baleCnt: {
      formModelValue: '',
      label: { text: t('Sog.Chr.txtBaleCnt') }, // 段ボール数
      rules: [rules.naturalNumericOnly(), rules.length(9)],
      formRole: 'textBox',
    },
    srcZoneGrpNo: {
      formModelValue: '',
      label: { text: t('Sog.Chr.txtSrcZoneGrpNo') }, // 出庫元ゾーングループコード
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'srcZoneGrpNo',
    },
  });

// 出荷予定追加ダイアログのモデル定義
export const sogAddShipmentPlanFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getSogAddShipmentPlanFormItems());
