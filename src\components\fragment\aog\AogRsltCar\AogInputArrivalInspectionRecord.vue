<template>
  <!-- 入荷時検品記録ダイアログ -->
  <DialogWindow
    :title="$t('Aog.Chr.txtAogCarStsInput')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onResolve="async () => checkEditForm()"
    :onReject="commonRejectHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="AogRsltCarDialogFormRef.formModel"
      :formItems="AogRsltCarDialogFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          AogRsltCarDialogFormRef.customForm = v;
        }
      "
      @changeFormModel="
        (changeFlag, { changeLogModList }) => {
          updateDialogChangeFlagRef(changeFlag);
          updateCurrentChangedFormModelMap1(changeLogModList);
        }
      "
    />
    <BaseHeading
      level="2"
      :text="$t('Aog.Chr.txtInspectionRecordItems')"
      fontSize="24px"
      class="Util_my-16"
    />
    <CustomForm
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="AogRsltCarRadioButtonDialogFormRef.formModel"
      :formItems="AogRsltCarRadioButtonDialogFormRef.formItems"
      @change="updateFormItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          AogRsltCarRadioButtonDialogFormRef.customForm = v;
        }
      "
      @changeFormModel="
        (v, { changeLogModList }) => {
          updateDialogChangeFlagRef(v, 'AogRsltCarRadioButtonDialogFormRef');
          updateCurrentChangedFormModelMap2(changeLogModList);
        }
      "
    />
  </DialogWindow>

  <!-- エラーメッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.editAogRsltCarErrorInfo"
    :dialogProps="messageBoxEditAogRsltCarErrorInfoRef"
    :submitCallback="() => closeDialog('editAogRsltCarErrorInfo')"
  />
  <!-- 検品記録入力の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.editAogRsltCarConfirm"
    :dialogProps="messageBoxEditAogRsltCarConfirmProps"
    :cancelCallback="() => closeDialog('editAogRsltCarConfirm')"
    :submitCallback="apiHandler"
  />
  <!-- 検品記録入力の完了メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.editAogRsltCarInfo"
    :dialogProps="messageBoxEditAogRsltCarPropsRef"
    :submitCallback="closeAllDialog"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { rules, check } from '@/utils/validator';
import BaseHeading from '@/components/base/BaseHeading.vue';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import CONST from '@/constants/utils';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import {
  AogRsltCarData,
  AogRsltCarListData,
  CarResultData,
} from '@/types/HookUseApi/AogTypes';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  getComboBoxReturnData,
  setCustomFormComboBoxOptionList,
} from '@/utils/comboBoxOptionList';
import { useModifyAogRsltCar, useSearchAogRsltCar } from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import {
  CommonRequestType,
  LogModListType,
} from '@/types/HookUseApi/CommonTypes';
import CONST_FLAGS from '@/constants/flags';
import {
  createFormModelByFormItems,
  setFormModelValueFromApiResponse,
} from '@/utils/customForm';
import useSearchAogRsltCarInit from '@/hooks/useApi/getAogRsltCarInit';
import {
  getAogInputArrivalInspectionRecord,
  AogRsltCarFormModel,
} from './AogInputArrivalInspectionRecord';
import {
  getAogInputArrivalInspectionRecordRadioButton,
  AogRsltCarRadioButtonFormModel,
  carResultData,
} from './AogInputArrivalInspectionRecordRadioButton';

const START_RESULT_NUM = 1;
const END_RESULT_NUM = 20;

const AogRsltCarDialogFormRef = ref<CustomFormType>({
  formItems: getAogInputArrivalInspectionRecord(),
  formModel: AogRsltCarFormModel,
});

const AogRsltCarRadioButtonDialogFormRef = ref<CustomFormType>({
  formItems: getAogInputArrivalInspectionRecordRadioButton(),
  formModel: AogRsltCarRadioButtonFormModel,
});

let logModList1: LogModListType = [];
let logModList2: LogModListType = [];
const updateCurrentChangedFormModelMap1 = (
  changeLogModList: LogModListType,
) => {
  logModList1 = changeLogModList;
};

const updateCurrentChangedFormModelMap2 = (
  changeLogModList: LogModListType,
) => {
  logModList2 = changeLogModList;
};

type Props = {
  selectedRowData: AogRsltCarListData | null;
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const customFormRenderingTriggerRef = ref(false);

const { t } = useI18n();
const emit = defineEmits(['submit']);

let AogRsltCarEditData: AogRsltCarData = {
  aogInstNo: '',
  aogInstGrpNo: '',
  matNo: '',
  matNm: '',
  lotNo: '',
  pickPltCnt: null,
  qltReqFlg: '',
  expiryDspCtrl: '',
  expiryDspTxt: '',
  expiryYmd: '',
  expiryStCd: '',
  shelfLifeDspCtrl: '',
  shelfLifeDspTxt: '',
  shelfLifeYmd: '',
  shelfLifeStCd: '',
  mBpId: '',
  bpNm: '',
  makerLotNo: '',
  edNo: '',
  carExpl: '',
  aogRsltCarUpdDts: '',
  ...carResultData,
};

type DialogRefKey =
  | 'editAogRsltCarErrorInfo'
  | 'editAogRsltCarConfirm'
  | 'editAogRsltCarInfo'
  | 'fragmentDialogVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  editAogRsltCarErrorInfo: false,
  editAogRsltCarConfirm: false,
  editAogRsltCarInfo: false,
  fragmentDialogVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxEditAogRsltCarErrorInfoRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const messageBoxEditAogRsltCarConfirmProps: DialogProps = {
  title: t('Aog.Msg.titleAogRsltCarInputConfirm'),
  content: t('Aog.Msg.contentAogRsltCarRegistrationConfirm'),
  type: 'question',
};

const messageBoxEditAogRsltCarPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const makerLotNoModFlgRef = ref<string>('0');
let expiryCheck = false;
let shelfCheck = false;

/**
 * 入荷時検品記録入力チェック
 */
const checkEditForm = async () => {
  const validateAogRsltCar =
    AogRsltCarDialogFormRef.value.customForm !== undefined &&
    (await AogRsltCarDialogFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  const validateInspectionRecordItem =
    AogRsltCarRadioButtonDialogFormRef.value.customForm !== undefined &&
    (await AogRsltCarRadioButtonDialogFormRef.value.customForm.validate(
      (isValid) => {
        onValidateHandler(isValid);
      },
    ));
  if (validateAogRsltCar && validateInspectionRecordItem) {
    if (expiryCheck === true && shelfCheck === true) {
      if (
        check.fromToDate(
          AogRsltCarDialogFormRef.value.formModel.shelfLifeYmd.toString(),
          AogRsltCarDialogFormRef.value.formModel.expiryYmd.toString(),
        )
      ) {
        messageBoxEditAogRsltCarErrorInfoRef.value.title = t(
          'Tst.Msg.txtChecksumExpiry',
        );
        messageBoxEditAogRsltCarErrorInfoRef.value.content = t(
          'Tst.Msg.txtExpiryMustBeInTheFuture',
        );
        messageBoxEditAogRsltCarErrorInfoRef.value.type = 'error';
        openDialog('editAogRsltCarErrorInfo');
        return false;
      }
    }
    openDialog('editAogRsltCarConfirm');
  }
  return false;
};

/**
 * 入荷時検品記録
 */
const apiHandler = async () => {
  closeDialog('editAogRsltCarConfirm');
  showLoading();
  if (
    AogRsltCarDialogFormRef.value.formItems.makerLotNo.formModelValue.toString() !==
    AogRsltCarEditData.makerLotNo
  ) {
    makerLotNoModFlgRef.value = '1';
  }

  const carRslt: CarResultData = carResultData;
  for (let i = START_RESULT_NUM; i <= END_RESULT_NUM; i++) {
    const idx = String(i).padStart(2, '0');
    const itemKey = `carRsltCd${idx}`;
    const item = AogRsltCarRadioButtonDialogFormRef.value.formItems[itemKey];
    const modelValue =
      AogRsltCarRadioButtonDialogFormRef.value.formModel[itemKey]?.toString() ??
      '';

    // NOTE: CustomFormに渡していないkeyを利用する必用があるため、as を使用しています
    carRslt[`carText${idx}` as keyof CarResultData] = item?.label?.text ?? '';
    carRslt[`carRsltCd${idx}` as keyof CarResultData] = modelValue;

    let selectedLabel = '';
    if (item && item.formRole === 'radio' && !Array.isArray(item)) {
      const optionsData = item.props?.optionsData;
      if (
        optionsData &&
        Array.isArray(optionsData.value) &&
        Array.isArray(optionsData.label)
      ) {
        const valueIdx = optionsData.value.indexOf(modelValue);
        if (valueIdx !== -1) {
          selectedLabel = optionsData.label[valueIdx];
        }
      }
    }
    carRslt[`carRsltNm${idx}` as keyof CarResultData] = selectedLabel;
  }

  const logModList = [...logModList1, ...logModList2];
  const editIndividualInventoryFormModel = {
    ...props.privilegesBtnRequestData,
    aogInstNo: AogRsltCarDialogFormRef.value.formModel.aogInstNo.toString(),
    makerLotNo: AogRsltCarDialogFormRef.value.formModel.makerLotNo.toString(),
    qltReqFlg: AogRsltCarEditData.qltReqFlg,
    expiryStCd: AogRsltCarEditData.expiryStCd,
    expiryYmd: AogRsltCarDialogFormRef.value.formModel.expiryYmd.toString(),
    shelfLifeStCd: AogRsltCarEditData.shelfLifeStCd,
    shelfLifeYmd:
      AogRsltCarDialogFormRef.value.formModel.shelfLifeYmd.toString(),
    carExpl:
      AogRsltCarRadioButtonDialogFormRef.value.formModel.carExpl.toString(),
    aogRsltCarUpdDts: AogRsltCarEditData.aogRsltCarUpdDts,
    makerLotNoModFlg: makerLotNoModFlgRef.value.toString(),
    ...carRslt,
    logModList,
  };
  // 入荷時検品記録を入力する
  const { responseRef, errorRef } = await useModifyAogRsltCar({
    ...editIndividualInventoryFormModel,
    msgboxTitleTxt: messageBoxEditAogRsltCarConfirmProps.title,
    msgboxMsgTxt: messageBoxEditAogRsltCarConfirmProps.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
  });
  if (errorRef.value) {
    messageBoxEditAogRsltCarErrorInfoRef.value.title =
      errorRef.value.response.rTitle;
    messageBoxEditAogRsltCarErrorInfoRef.value.content =
      errorRef.value.response.rMsg;
    messageBoxEditAogRsltCarErrorInfoRef.value.type = 'error';
    openDialog('editAogRsltCarErrorInfo');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    messageBoxEditAogRsltCarPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxEditAogRsltCarPropsRef.value.content =
      responseRef.value.data.rMsg;
    openDialog('editAogRsltCarInfo');
  }
  closeLoading();
  return true;
};

/**
 * ラジオボタンの判定によるコメント必須条件の更新
 */
const updateFormItems = () => {
  const hasNotP = Object.keys(
    AogRsltCarRadioButtonDialogFormRef.value.formItems,
  )
    .filter((key) => key.startsWith('carRsltCd'))
    .some(
      (key) =>
        AogRsltCarRadioButtonDialogFormRef.value.formItems[key]
          .formModelValue !== 'P',
    );
  if (hasNotP) {
    AogRsltCarRadioButtonDialogFormRef.value.formItems.carExpl.tags = [
      { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
    ];
    AogRsltCarRadioButtonDialogFormRef.value.formItems.carExpl.rules = [
      rules.required('textComboBox'),
      rules.length(64, t('Cm.Chr.txtLength', [64])),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ];
  } else {
    delete AogRsltCarRadioButtonDialogFormRef.value.formItems.carExpl.tags;
    delete AogRsltCarRadioButtonDialogFormRef.value.formItems.carExpl.rules;
    AogRsltCarRadioButtonDialogFormRef.value.formItems.carExpl.rules = [
      rules.length(64, t('Cm.Chr.txtLength', [64])),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ];
  }
  customFormRenderingTriggerRef.value = !customFormRenderingTriggerRef.value;
};

/**
 * ダイアログを閉じる
 */
const closeAllDialog = () => {
  closeDialog('editAogRsltCarInfo');
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData);
};

/**
 * 初期設定
 */
const aogRsltCarDaialogInit = async () => {
  if (!props.selectedRowData) return;
  showLoading();
  makerLotNoModFlgRef.value = '0';
  AogRsltCarDialogFormRef.value.formItems =
    getAogInputArrivalInspectionRecord();

  expiryCheck = false;
  shelfCheck = false;
  logModList1 = [];
  logModList2 = [];

  // 初期情報の取得
  const {
    responseRef: useSearchAogRsltCarInitResRef,
    errorRef: useSearchAogRsltCarInitErrorRef,
  } = await useSearchAogRsltCarInit({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtAogAdd',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'AOG_CAR_ADD' },
      },
    ],
  });

  if (useSearchAogRsltCarInitErrorRef.value) {
    messageBoxEditAogRsltCarErrorInfoRef.value.title =
      useSearchAogRsltCarInitErrorRef.value.response.rTitle;
    messageBoxEditAogRsltCarErrorInfoRef.value.content =
      useSearchAogRsltCarInitErrorRef.value.response.rMsg;
    messageBoxEditAogRsltCarErrorInfoRef.value.type = 'error';
    openDialog('editAogRsltCarErrorInfo');
    closeLoading();
    return;
  }
  if (useSearchAogRsltCarInitResRef.value) {
    const {
      rDts,
      rCode,
      rTitle,
      rMsg,
      rData: resRData,
    } = useSearchAogRsltCarInitResRef.value.data;
    const { aogCarRsltList, rList } = resRData;
    const comboBoxResData = getComboBoxReturnData({
      rDts,
      rCode,
      rTitle,
      rMsg,
      rData: { rList },
    });

    AogRsltCarRadioButtonDialogFormRef.value.formItems =
      getAogInputArrivalInspectionRecordRadioButton(aogCarRsltList);
    AogRsltCarRadioButtonDialogFormRef.value.formModel =
      createFormModelByFormItems(
        getAogInputArrivalInspectionRecordRadioButton(aogCarRsltList),
      );

    setCustomFormComboBoxOptionList(
      // 標準コンボボックスセット
      AogRsltCarRadioButtonDialogFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  // 入荷時検品記録ダイアログの情報を取得
  const { responseRef, errorRef } = await useSearchAogRsltCar({
    ...props.privilegesBtnRequestData,
    aogInstNo: props.selectedRowData.aogInstNo,
  });
  if (errorRef.value) {
    messageBoxEditAogRsltCarErrorInfoRef.value.title =
      errorRef.value.response.rTitle;
    messageBoxEditAogRsltCarErrorInfoRef.value.content =
      errorRef.value.response.rMsg;
    messageBoxEditAogRsltCarErrorInfoRef.value.type = 'error';
    openDialog('editAogRsltCarErrorInfo');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    AogRsltCarEditData = responseRef.value.data.rData;
    setFormModelValueFromApiResponse(
      AogRsltCarDialogFormRef,
      AogRsltCarEditData,
    );
    setFormModelValueFromApiResponse(
      AogRsltCarRadioButtonDialogFormRef,
      AogRsltCarEditData,
    );
  }

  if (AogRsltCarEditData.expiryDspCtrl === '0') {
    // 非表示
    delete AogRsltCarDialogFormRef.value.formItems.expiryYmd;
  } else if (AogRsltCarEditData.expiryDspCtrl === '1') {
    // ラベル(テキストボックス)
    if (
      AogRsltCarDialogFormRef.value.formItems.expiryYmd.formRole === 'textBox'
    ) {
      AogRsltCarDialogFormRef.value.formItems.expiryYmd.props!.disabled = true;
      AogRsltCarDialogFormRef.value.formItems.expiryYmd.formModelValue =
        AogRsltCarEditData.expiryYmd;
    }
  } else {
    // カレンダー
    AogRsltCarDialogFormRef.value.formItems.expiryYmd.formRole = 'date';
    if (AogRsltCarDialogFormRef.value.formItems.expiryYmd.formRole === 'date') {
      AogRsltCarDialogFormRef.value.formItems.expiryYmd.tags = [
        { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
      ];
      AogRsltCarDialogFormRef.value.formItems.expiryYmd.rules = [
        rules.required('date'),
      ];
      AogRsltCarDialogFormRef.value.formItems.expiryYmd.props!.type = 'date';
      AogRsltCarDialogFormRef.value.formItems.expiryYmd.props!.modelValue = '';
      AogRsltCarDialogFormRef.value.formItems.expiryYmd.props!.size = 'small';
      AogRsltCarDialogFormRef.value.formItems.expiryYmd.props!.disabled = false;
      if (
        AogRsltCarEditData.expiryStCd ===
          CONST_FLAGS.MAT_EXPIRY_ST_CD_STATUS.EXPIRE_INPUT &&
        AogRsltCarEditData.qltReqFlg === CONST_FLAGS.QLT_REQ_FLG.ON
      ) {
        expiryCheck = true;
        AogRsltCarDialogFormRef.value.formItems.expiryYmd.rules.push(
          rules.futureDate(),
        );
      }
      AogRsltCarDialogFormRef.value.formItems.expiryYmd.formModelValue =
        AogRsltCarEditData.expiryYmd;
    }
  }

  if (AogRsltCarEditData.shelfLifeDspCtrl === '0') {
    // 非表示
    delete AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd;
  } else if (AogRsltCarEditData.shelfLifeDspCtrl === '1') {
    // ラベル(テキストボックス)
    if (
      AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.formRole ===
      'textBox'
    ) {
      AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.props!.disabled =
        true;
      AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.formModelValue =
        AogRsltCarEditData.shelfLifeYmd;
    }
  } else {
    // カレンダー
    AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.formRole = 'date';
    if (
      AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.formRole === 'date'
    ) {
      AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.tags = [
        { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
      ];
      AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.rules = [
        rules.required('date'),
      ];
      AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.props!.type = 'date';
      AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.props!.modelValue =
        '';
      AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.props!.size =
        'small';
      AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.props!.disabled =
        false;
      if (
        AogRsltCarEditData.shelfLifeStCd ===
          CONST_FLAGS.MAT_SHELF_LIFE_ST_CD_STATUS.EXPIRE_INPUT &&
        AogRsltCarEditData.qltReqFlg === CONST_FLAGS.QLT_REQ_FLG.ON
      ) {
        shelfCheck = true;
        AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.rules.push(
          rules.futureDate(),
        );
      }
      AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.formModelValue =
        AogRsltCarEditData.shelfLifeYmd;
    }
  }

  // 使用期限、有効期限ラベル設定
  if (AogRsltCarDialogFormRef.value.formItems.expiryYmd) {
    AogRsltCarDialogFormRef.value.formItems.expiryYmd.label = {
      text: AogRsltCarEditData.expiryDspTxt,
    };
  }

  if (AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd) {
    AogRsltCarDialogFormRef.value.formItems.shelfLifeYmd.label = {
      text: AogRsltCarEditData.shelfLifeDspTxt,
    };
  }

  // 初期コメント必須条件
  updateFormItems();

  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(
  () => props.isClicked,
  async () => {
    await aogRsltCarDaialogInit();
  },
);
</script>
<style lang="scss" scoped>
$namespace: 'order-addition-dialog';

.#{$namespace} {
  background-color: $white750;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
</style>
