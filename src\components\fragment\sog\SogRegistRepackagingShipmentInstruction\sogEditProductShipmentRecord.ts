import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

export const getSogEditProductShipmentRecordFormItems: () => CustomFormType['formItems'] =
  () => ({
    sogInstNo: {
      label: { text: t('Sog.Chr.txtSogInstNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 24,
    },
    matNo: {
      label: { text: t('Sog.Chr.txtMatNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 24,
    },
    matNm: {
      label: { text: t('Sog.Chr.txtMatNm') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
      span: 24,
    },
    lotNo: {
      label: { text: t('Sog.Chr.txtLotNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 24,
    },
    sogPlanYmd: {
      label: { text: t('Sog.Chr.txtSogPlanYmd') },
      formModelValue: '',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      formRole: 'date',
      props: { modelValue: '', type: 'date', size: 'small' },
      rules: [rules.required('date'), rules.futureDate()],
    },
    bpTrfId: {
      label: { text: t('Sog.Chr.txtBpTrfNm') },
      formModelValue: '',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: 'bpTrfId',
    },
    shipModExpl: {
      label: { text: t('Sog.Chr.txtComment') },
      formModelValue: '',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [
        rules.required('textComboBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
      ],
      formRole: 'textComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: 'cmtSogInstShipMod',
    },
  });

export const sogEditProductShipmentRecordFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getSogEditProductShipmentRecordFormItems());
