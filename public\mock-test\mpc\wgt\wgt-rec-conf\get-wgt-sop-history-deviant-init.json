{"rDts": "2024/11/21 14:58:18", "rCode": 200, "rTitle": "秤量前後SOP異状履歴初期表示", "rMsg": "秤量前後SOP異状履歴初期表示メッセージ", "rData": {"devHistoryList": [{"devCorrLv": 1, "sopFlowNmJp": "造粒-本作業 1", "backgroundColor": "yellow", "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "部屋の温度を入力してください", "cmtMain3": "部屋の温度を入力してください", "instVal": "24", "unitNmJp": "℃", "recVal": "26", "thValType": "絶対値", "thValLlmt": "23", "thValUlmt": "25", "edDts": "2023/10/03 10:11:53", "modUsr": "松下 次郎", "modExpl": "温度が高いため再検査", "cmtDts": "2023/10/03 10:11:53"}, {"devCorrLv": 2, "sopFlowNmJp": "造粒-本作業 1", "backgroundColor": "orange", "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "部屋の温度を入力してください", "cmtMain3": "部屋の温度を入力してください", "instVal": "24", "unitNmJp": "℃", "recVal": "26", "thValType": "絶対値", "thValLlmt": "23", "thValUlmt": "25", "edDts": "2023/10/02 10:11:53", "modUsr": "松下 次郎", "modExpl": "温度が高いため再検査", "cmtDts": "2023/10/03 10:11:53"}, {"devCorrLv": 3, "backgroundColor": "red", "sopFlowNmJp": "造粒-本作業 1", "cmtMain1": "", "cmtMain2": "部屋の温度を入力してください", "cmtMain3": "", "instVal": "24", "unitNmJp": "℃", "recVal": "26", "thValType": "絶対値", "thValLlmt": "23", "thValUlmt": "25", "edDts": "2023/10/05 10:11:53", "modUsr": "松下 次郎", "modExpl": "温度が高いため再検査", "cmtDts": "2023/10/03 10:11:53"}, {"devCorrLv": 0, "backgroundColor": "", "sopFlowNmJp": "造粒-本作業 1", "cmtMain1": "", "cmtMain2": "部屋の温度を入力してください", "cmtMain3": "", "instVal": "24", "unitNmJp": "℃", "recVal": "26", "thValType": "絶対値", "thValLlmt": "23", "thValUlmt": "25", "edDts": "2023/10/01 10:11:53", "modUsr": "松下 次郎", "modExpl": "温度が高いため再検査", "cmtDts": "2023/10/03 10:11:53"}, {"devCorrLv": 1, "backgroundColor": "yellow", "sopFlowNmJp": "造粒-本作業 1", "cmtMain1": "", "cmtMain2": "部屋の温度を入力してください", "cmtMain3": "", "instVal": "24", "unitNmJp": "℃", "recVal": "26", "thValType": "絶対値", "thValLlmt": "23", "thValUlmt": "25", "edDts": "2023/10/07 10:11:53", "modUsr": "松下 次郎", "modExpl": "温度が高いため再検査", "cmtDts": "2023/10/03 10:11:53"}, {"devCorrLv": 2, "backgroundColor": "orange", "sopFlowNmJp": "造粒-本作業 1", "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "", "cmtMain3": "", "instVal": "24", "unitNmJp": "℃", "recVal": "26", "thValType": "絶対値", "thValLlmt": "23", "thValUlmt": "25", "edDts": "2023/9/03 10:11:53", "modUsr": "松下 次郎", "modExpl": "温度が高いため再検査", "cmtDts": "2023/10/03 10:11:53"}]}}