<template>
  <!-- 入荷予定コピー追加ダイアログ -->
  <DialogWindow
    :title="$t('Aog.Chr.txtAogCopyArrivalPlan')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkCopyForm()"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="aogArrivalPlanCopyFormRef.formModel"
      :formItems="aogArrivalPlanCopyFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          aogArrivalPlanCopyFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <!-- 入荷予定コピー追加の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.copyAogArrivalPlanConfirm"
    :dialogProps="messageBoxCopyAogArrivalPlanConfirmProps"
    :cancelCallback="() => closeDialog('copyAogArrivalPlanConfirm')"
    :submitCallback="apiHandler"
  />
  <!-- 入荷予定コピー追加のチェックメッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.copyAogArrivalPlan"
    :dialogProps="messageBoxCopyAogArrivalPlanPropsRef"
    :submitCallback="closeAllDialog"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  AogPlanListData,
  AogPlanCopyAddData,
  AddAttBinData,
  AddAogPlanCopyReq,
} from '@/types/HookUseApi/AogTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import { getFilesForApiRequest } from '@/utils/fileUpload';
import { getDateByType } from '@/utils/index';
import {
  useGetComboBoxDataStandard,
  useGetAogPlanCopyAdd,
  useAddAogPlanCopy,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  getAogArrivalPlanCopyFormItems,
  aogArrivalPlanCopyFormModel,
} from './aogCopyArrivalPlan';

const aogArrivalPlanCopyFormRef = ref<CustomFormType>({
  formItems: getAogArrivalPlanCopyFormItems(),
  formModel: aogArrivalPlanCopyFormModel,
});

type Props = {
  isClicked: boolean;
  selectedRowData: AogPlanListData | null;
  privilegesBtnRequestData: CommonRequestType;
};

const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

let aogPlanCopyAddData: AogPlanCopyAddData = {
  aogPlanNo: '',
  poDtlNo: '',
  matNo: '',
  matNm: '',
  edNo: '',
  erpAogQty: '',
  erpUnitNm: '',
  unitNm: '',
  mBpId: '',
  bpNm: '',
  poApNo: '',
  poDtlExpl: '',
  poApExpl: '',
};

type DialogRefKey =
  | 'singleButton'
  | 'copyAogArrivalPlanConfirm'
  | 'copyAogArrivalPlan'
  | 'fragmentDialogVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  copyAogArrivalPlanConfirm: false,
  copyAogArrivalPlan: false,
  fragmentDialogVisible: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxCopyAogArrivalPlanConfirmProps: DialogProps = {
  title: t('Aog.Chr.txtAogPlanAddConfirm'),
  content: t('Aog.Msg.copyAogArrivalPlanCorrectionConfirm'),
  type: 'question',
};

const messageBoxCopyAogArrivalPlanPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

let responseAogPlanNo = '';

const FILE_AOG_ATT_BIN = {
  NAME_KEY: 'attBinFileNm',
  FILE_KEY: 'attBinFile',
  UNIQUE_KEY: 'attBinNo',
} as const;

/**
 * 入荷予定コピー追加（実行）
 */
const checkCopyForm = async () => {
  const validate =
    aogArrivalPlanCopyFormRef.value.customForm !== undefined &&
    (await aogArrivalPlanCopyFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (validate) {
    // 入荷予定コピー追加の確認メッセージ
    openDialog('copyAogArrivalPlanConfirm');
  }
  return false;
};

/**
 * 入荷予定コピー追加
 */
const apiHandler = async () => {
  closeDialog('copyAogArrivalPlanConfirm');
  showLoading();
  const addAogAttBinList = await getFilesForApiRequest<AddAttBinData>(
    aogArrivalPlanCopyFormRef.value.formModel.files,
    {
      fileNameKey: FILE_AOG_ATT_BIN.NAME_KEY,
      fileKey: FILE_AOG_ATT_BIN.FILE_KEY,
    },
  );
  const addAogPlanCopyData: AddAogPlanCopyReq = {
    planYmd: aogArrivalPlanCopyFormRef.value.formModel.planYmd.toString(),
    makerLotNo: aogArrivalPlanCopyFormRef.value.formModel.makerLotNo.toString(),
    aogQty: aogArrivalPlanCopyFormRef.value.formModel.aogQty.toString(),
    addAogAttBinList,
    planExpl: aogArrivalPlanCopyFormRef.value.formModel.planExpl.toString(),
    originalAogPlanNo: aogPlanCopyAddData.aogPlanNo,
  };
  // 入荷予定コピー追加
  const { responseRef, errorRef } = await useAddAogPlanCopy({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxCopyAogArrivalPlanConfirmProps.title,
    msgboxMsgTxt: messageBoxCopyAogArrivalPlanConfirmProps.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    ...addAogPlanCopyData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    messageBoxCopyAogArrivalPlanPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxCopyAogArrivalPlanPropsRef.value.content =
      responseRef.value.data.rMsg;
    responseAogPlanNo = responseRef.value.data.rData.aogPlanNo;
    openDialog('copyAogArrivalPlan');
  }
  closeLoading();
  return true;
};

/**
 * ダイアログウィンドウを閉じる
 */
const closeAllDialog = async () => {
  closeDialog('copyAogArrivalPlan');
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData, responseAogPlanNo);
};

// 初期表示データ設定
const aogCopyArrivalPlanInit = async () => {
  if (!props.selectedRowData) return;
  updateDialogChangeFlagRef(false);

  showLoading();
  // 連携テストダイアログの初期化必要なら書く
  aogArrivalPlanCopyFormRef.value.formItems = getAogArrivalPlanCopyFormItems();

  aogArrivalPlanCopyFormRef.value.formItems.planYmd.formModelValue =
    getDateByType(new Date().toString(), 'YYYY/MM/DD');

  // 2.入荷予定コピー追加データ取得
  const { responseRef, errorRef } = await useGetAogPlanCopyAdd({
    ...props.privilegesBtnRequestData,
    aogPlanNo: props.selectedRowData!.aogPlanNo,
  });

  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }

  if (responseRef.value) {
    aogPlanCopyAddData = responseRef.value.data.rData;
    setFormModelValueFromApiResponse(
      aogArrivalPlanCopyFormRef,
      aogPlanCopyAddData,
    );
  }

  // 標準コンボボックスデータ取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtAogPlan',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'AOG_PLAN' },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setCustomFormComboBoxOptionList(
      aogArrivalPlanCopyFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(
  () => props.isClicked,
  async () => {
    await aogCopyArrivalPlanInit();
  },
);
</script>
