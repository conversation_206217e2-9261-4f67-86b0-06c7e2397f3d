import { ConstantRouterOption } from '@/types/RouterTypes';
import { createRouter, createWebHistory } from 'vue-router';
import SCREENID from '@/constants/screenId';
import CONST from '@/constants/utils';

export const constantRouterMap: ConstantRouterOption[] = [
  {
    path: '/',
    meta: { title: 'Cm.Chr.Menu.txtLogin' },
    component: () => import('@/components/page/LoginPage.vue'),
  },
  {
    path: '/notfoundpage',
    component: () => import('@/components/page/NotFoundPage.vue'),
  },
  {
    path: CONST.ERROR_PAGE_PATH,
    component: () => import('@/components/page/ErrorPage.vue'),
  },
];
export const asyncRouter: () => ConstantRouterOption[] = () => [
  {
    path: '/#', // Logout実行すると、Login画面へ遷移する時、本行のPathを取得してしまった、上のpathの情報を取得できない。区別するために、＃を付けた。
    name: 'layout',
    component: () => import('@/components/layout/DynamicLayout.vue'),
    meta: {
      title: 'Cm.Chr.Menu.txtHome',
    },
    children: [
      {
        name: 'homePage',
        path: '/homepage',
        meta: {
          title: 'Cm.Chr.Menu.txtHome',
          icon: 'menuHome',
        },
        component: () => import('@/components/page/HomePage.vue'),
      },
      {
        name: 'aog',
        path: '/aog',
        meta: {
          hidden: false,
          title: 'Cm.Chr.Menu.txtAog',
          icon: 'icon_menu_aog',
        },
        children: [
          {
            name: SCREENID.AOG_PLAN_LIST,
            path: '/aog/aog-arrival-plan-list',
            meta: {
              title: 'Cm.Chr.Menu.txtAogPlanList',
            },
            component: () =>
              import('@/components/page/aog/AogArrivalPlanList.vue'),
          },
          {
            name: SCREENID.AOG_CREATE_ARRIVAL_INSTRUCTION,
            path: '/aog/aog-create-arrival-instruction',
            meta: {
              title: 'Cm.Chr.Menu.txtAogInstructionCreate',
            },
            component: () =>
              import('@/components/page/aog/AogCreateArrivalInstruction.vue'),
          },
          {
            name: SCREENID.AOG_ARRIVAL_INSTRUCTION_LIST,
            path: '/aog/aog-arrival-instruction-list',
            meta: {
              title: 'Cm.Chr.Menu.txtAogArrivalInstructionList',
            },
            component: () =>
              import('@/components/page/aog/AogArrivalInstructionList.vue'),
          },
          {
            // 貼付確認ラベル発行予定一覧
            name: SCREENID.AOG_LABEL_ISSUANCE_PLAN_LIST,
            path: '/aog/aog-label-issuance-plan-list',
            meta: {
              title: 'Cm.Chr.Menu.txtAogLabelIssuancePlanList',
            },
            component: () =>
              import('@/components/page/aog/AogLabelIssuancePlanList.vue'),
          },
          {
            name: SCREENID.AOG_RSLT_CAR,
            path: '/aog/aog-rslt-car-list',
            meta: {
              title: 'Cm.Chr.Menu.txtAogRsltCarList',
            },
            component: () =>
              import(
                '@/components/page/aog/AogArrivalInspectionRecordList.vue'
              ),
          },
          {
            name: SCREENID.AOG_RSLT_CAR_CHECK,
            path: '/aog/aog-rslt-car-check-list',
            meta: {
              title: 'Cm.Chr.Menu.txtAogRsltCarCheck',
            },
            component: () =>
              import(
                '@/components/page/aog/AogConfirmArrivalInspectionRecord.vue'
              ),
          },
          {
            name: SCREENID.AOG_CONFIRM_RECEIPT_RECORD,
            path: '/aog/aog-confirm-receipt-record',
            meta: {
              title: 'Cm.Chr.Menu.txtAogConfirmReceiptRecord',
            },
            component: () =>
              import('@/components/page/aog/AogConfirmReceiptRecord.vue'),
          },
          {
            name: SCREENID.AOG_ARRIVAL_RECORD_LIST,
            path: '/aog/aog-arrival-record-list',
            meta: {
              title: 'Cm.Chr.Menu.txtAogArrivalRecordList',
            },
            component: () => import('@/components/page/sys/SysAnySearch.vue'),
          },
          {
            name: SCREENID.AOG_ARRIVAL_RECORD_DETAILS,
            path: '/aog/aog-arrival-record-Details',
            meta: {
              title: 'Cm.Chr.Menu.txtAogArrivalRecordDetails',
            },
            component: () => import('@/components/page/sys/SysAnySearch.vue'),
          },
        ],
      },
      {
        name: 'trf',
        path: '/trf',
        meta: {
          hidden: false,
          title: 'Cm.Chr.Menu.txtTransfer',
          icon: 'icon_menu_trf',
        },
        children: [
          {
            name: SCREENID.TRF_SHIPPABLE_INSTRUCTION_LIST_FORMULATION,
            path: '/trf/trf-shippable-instruction-list-mode-0',
            meta: {
              title: 'Cm.Chr.Menu.txtTrfShippableInstructionListFormulation',
            },
            component: () =>
              import('@/components/page/trf/TrfShippableInstructionList.vue'),
          },
          {
            name: SCREENID.TRF_SHIPPABLE_INSTRUCTION_LIST_PACKAGING,
            path: '/trf/trf-shippable-instruction-list-mode-1',
            meta: {
              title: 'Cm.Chr.Menu.txtTrfShippableInstructionListPackaging',
            },
            component: () =>
              import('@/components/page/trf/TrfShippableInstructionList.vue'),
          },
          {
            name: SCREENID.TRF_INDIVIDUAL_SHIPMENT_REQUEST,
            path: '/trf/trf-individual-shipment-request-mode-0',
            meta: {
              title: 'Cm.Chr.Menu.txtTrfIndividualShipmentRequest',
            },
            component: () =>
              import('@/components/page/trf/TrfIndividualShipmentRequest.vue'),
          },
          {
            name: SCREENID.TRF_INDIVIDUAL_SHIPMENT_REQUEST_FORMULATION,
            path: '/trf/trf-individual-shipment-request-mode-1',
            meta: {
              title: 'Cm.Chr.Menu.txtTrfIndividualShipmentRequestFormulation',
            },
            component: () =>
              import('@/components/page/trf/TrfIndividualShipmentRequest.vue'),
          },
          {
            name: SCREENID.TRF_INDIVIDUAL_SHIPMENT_REQUEST_PACKAGING,
            path: '/trf/trf-individual-shipment-request-mode-2',
            meta: {
              title: 'Cm.Chr.Menu.txtTrfIndividualShipmentRequestPackaging',
            },
            component: () =>
              import('@/components/page/trf/TrfIndividualShipmentRequest.vue'),
          },
          {
            name: SCREENID.TRF_SHIPMENT_REQUEST_LIST,
            path: '/trf/trf_shipment_request_list-mode-0',
            meta: {
              title: 'Cm.Chr.Menu.txtTrfShipmentRequestList',
            },
            component: () =>
              import('@/components/page/trf/TrfShipmentRequestList.vue'),
          },
          {
            name: SCREENID.TRF_SHIPMENT_REQUEST_LIST_FORMULATION,
            path: '/trf/trf_shipment_request_list-mode-1',
            meta: {
              title: 'Cm.Chr.Menu.txtTrfShipmentRequestListFormulation',
            },
            component: () =>
              import('@/components/page/trf/TrfShipmentRequestList.vue'),
          },
          {
            name: SCREENID.TRF_SHIPMENT_REQUEST_LIST_PACKAGING,
            path: '/trf/trf_shipment_request_list-mode-2',
            meta: {
              title: 'Cm.Chr.Menu.txtTrfShipmentRequestListPackaging',
            },
            component: () =>
              import('@/components/page/trf/TrfShipmentRequestList.vue'),
          },
          {
            name: SCREENID.TRF_SHIPMENT_INSTRUCTION_LIST,
            path: '/trf/trf-shipment-instruction-list-mode-0',
            meta: {
              title: 'Cm.Chr.Menu.txtTrfShipmentInstructionList',
            },
            component: () =>
              import('@/components/page/trf/TrfShipmentInstructionList.vue'),
          },
          {
            name: SCREENID.TRF_COMFIRMED_SHIPMENT_INSTRUCTION_LIST,
            path: '/trf/trf-shipment-instruction-list-mode-1',
            meta: {
              title: 'Cm.Chr.Menu.txtTrfConfirmedShipmentInstructionList',
            },
            component: () =>
              import('@/components/page/trf/TrfShipmentInstructionList.vue'),
          },
          {
            name: SCREENID.TRF_LOAD_SHIPMENT_REPORT,
            path: '/trf/trf-load-shipment-report',
            meta: {
              title: 'Cm.Chr.Menu.txtTrfLoadShipmentReport',
            },
            component: () =>
              import('@/components/page/trf/TrfLoadShipmentReport.vue'),
          },
          {
            name: SCREENID.TRF_CREATE_SHIPMENT_REQUESTBY_INSTRUCTION_REFERENCE_FORMULATION,
            path: '/trf/trf_create_shipment_requestby_instruction_reference-mode-0',
            meta: {
              hidden: true,
              title:
                'Cm.Chr.Menu.txtTrfCreateShipmentRequestbyInstructionReferenceFormulation',
              parent: SCREENID.TRF_SHIPPABLE_INSTRUCTION_LIST_FORMULATION,
            },
            component: () =>
              import(
                '@/components/page/trf/TrfCreateShipmentRequestbyInstructionReference.vue'
              ),
          },
          {
            name: SCREENID.TRF_CREATE_SHIPMENT_REQUESTBY_INSTRUCTION_REFERENCE_PACKAGING,
            path: '/trf/trf_create_shipment_requestby_instruction_reference-mode-1',
            meta: {
              hidden: true,
              title:
                'Cm.Chr.Menu.txtTrfCreateShipmentRequestbyInstructionReferencePackaging',
              parent: SCREENID.TRF_SHIPPABLE_INSTRUCTION_LIST_PACKAGING,
            },
            component: () =>
              import(
                '@/components/page/trf/TrfCreateShipmentRequestbyInstructionReference.vue'
              ),
          },
          {
            name: SCREENID.TRF_PRINT_RECEIPT_CONFIRMATION_LABEL,
            path: '/trf/trf-print-receipt-confirmation-label',
            meta: {
              title: 'Cm.Chr.Menu.txtTrfPrintReceiptConfirmationLabel',
              parent: SCREENID.TRF_LOAD_SHIPMENT_REPORT,
              hidden: true,
            },
            component: () =>
              import(
                '@/components/page/trf/TrfPrintReceiptConfirmationLabel.vue'
              ),
          },
        ],
      },
      {
        // 計画指図
        name: 'orders',
        path: '/order',
        meta: {
          hidden: false,
          title: 'Cm.Chr.Menu.txtOrder',
          icon: 'icon_menu_orders',
        },
        children: [
          {
            // 小日程計画一覧
            name: SCREENID.ODR_SCHEDULE_LIST,
            path: '/order/odr-schedule-list',
            meta: {
              title: 'Cm.Chr.Menu.txtScheduleList',
            },
            component: () =>
              import('@/components/page/odr/OdrScheduleList.vue'),
          },
          {
            // 製造指図登録
            name: SCREENID.ODR_APPROVED_SCHEDULE_LIST,
            path: '/order/odr-approved-schedule-list',
            meta: {
              title: 'Cm.Chr.Menu.txtOrderRegistration',
            },
            component: () =>
              import('@/components/page/odr/OdrApprovedScheduleList.vue'),
          },
          {
            // 製造指図承認
            name: SCREENID.ODR_SELECT_ORDER_LIST_FOR_APPROVAL,
            path: '/order/odr-select-order-list-for-approval',
            meta: {
              title: 'Cm.Chr.Menu.txtOrderApproval',
            },
            component: () =>
              import('@/components/page/odr/OdrSelectOrderListForApproval.vue'),
          },
          {
            // 製造指図一覧
            name: SCREENID.ODR_SELECT_APPROVED_ORDER_LIST,
            path: '/order/odr-select-approved-order-list',
            meta: {
              title: 'Cm.Chr.Menu.txtOrderList',
            },
            component: () =>
              import('@/components/page/odr/OdrSelectApprovedOrderList.vue'),
          },
          {
            // 製造指図状況
            name: SCREENID.ODR_REGISTED_ORDER_LIST,
            path: '/order/odr-registed-order-list',
            meta: {
              title: 'Cm.Chr.Menu.txtOrderStateList',
            },
            component: () =>
              import('@/components/page/odr/OdrRegistedOrderList.vue'),
          },
        ],
      },
      {
        // 秤量
        name: 'weights',
        path: '/weigh',
        meta: {
          hidden: false,
          title: 'Cm.Chr.Menu.txtWeighing',
          icon: 'icon_menu_weights',
        },
        children: [
          {
            // 秤量指示作成
            name: SCREENID.WGT_CREATE_WEIGHING_INSTRUCTION,
            path: '/weigh/wgt-create-weighing_instruction',
            meta: {
              title: 'Cm.Chr.Menu.txtWeighingInstructionCreate',
            },
            component: () =>
              import('@/components/page/wgt/WgtCreateWeighingInstruction.vue'),
          },
          {
            // 秤量指示書一覧
            name: SCREENID.WGT_WEIGHING_INSTRUCTION_LIST,
            path: '/weigh/wgt-weighing-instruction-list',
            meta: {
              title: 'Cm.Chr.Menu.txtWeighingInstructionList',
            },
            component: () =>
              import('@/components/page/wgt/WgtWeighingInstructionList.vue'),
          },
          {
            // 秤量記録確認
            name: SCREENID.WGT_CONFIRM_WEIGHING_RECORD,
            path: '/weigh/wgt-confirm-weighing-record',
            meta: {
              title: 'Cm.Chr.Menu.txtWeighingRecord',
            },
            component: () =>
              import('@/components/page/wgt/WgtConfirmWeighingRecord.vue'),
          },
          {
            // 再秤量指示作成
            name: SCREENID.WGT_CREATE_RE_WEIGHING_INSTRUCTION,
            path: '/weigh/wgt_create_re_weighing_instruction',
            meta: {
              title: 'Cm.Chr.Menu.txtReWeighingInstructionCreate',
            },
            component: () =>
              import(
                '@/components/page/wgt/WgtCreateReWeighingInstruction.vue'
              ),
          },
          {
            // 秤量室変更
            name: SCREENID.WGT_CHANGE_WEIGHING_ROOM_LIST,
            path: '/weigh/wgt-change-weighing-room-list',
            meta: {
              title: 'Cm.Chr.Menu.txtWeighingRoomChange',
            },
            component: () =>
              import('@/components/page/wgt/WgtChangeWeighingRoomList.vue'),
          },
          {
            // 秤量前後SOP一覧
            name: SCREENID.WGT_WEIGHING_SOP_LIST,
            path: '/weigh/wgt-weighing-sop-list',
            meta: {
              title: 'Cm.Chr.Menu.txtWeighingSopList',
            },
            component: () =>
              import('@/components/page/wgt/WgtWeighingSOPList.vue'),
          },
        ],
      },
      {
        // 製造
        name: 'products',
        path: '/product',
        meta: {
          hidden: false,
          title: 'Cm.Chr.Menu.txtPrd',
          icon: 'icon_menu_products',
        },
        children: [
          {
            name: SCREENID.PRD_CONFIRM_LIST,
            path: '/product/prd-confirm-list',
            meta: {
              title: 'Cm.Chr.Menu.txtPrdConfirmList',
            },
            component: () => import('@/components/page/prd/PrdConfirmList.vue'),
          },
          {
            // 製造記録承認
            name: SCREENID.PRD_APPROVAL_LIST,
            path: '/product/prd-approval-list',
            meta: {
              title: 'Cm.Chr.Menu.txtPrdApprovalList',
            },
            component: () =>
              import('@/components/page/prd/PrdApprovalList.vue'),
          },
          {
            // 製造記録参照
            name: SCREENID.PRD_REF_LIST,
            path: '/product/prd-prc-reference',
            meta: {
              title: 'Cm.Chr.Menu.txtPrdRefList',
            },
            component: () =>
              import('@/components/page/prd/PrdReferenceList.vue'),
          },
          {
            // 工程作業記録
            name: SCREENID.PRD_PRC_FLOW_LIST,
            path: '/product/prd-prc-flow-list',
            meta: {
              title: 'Cm.Chr.Menu.txtPrdPrcFlowList',
            },
            component: () => import('@/components/page/prd/PrdPrcFlowList.vue'),
          },
        ],
      },
      {
        name: 'tst',
        path: '/tst',
        meta: {
          hidden: false,
          title: 'Cm.Chr.Menu.txtTst',
          icon: 'icon_menu_tst',
        },
        children: [
          {
            name: SCREENID.TST_TEST_REQUEST_CANDIDATE_LIST,
            path: '/tst/tst-test-request-candidate-list',
            meta: {
              title: 'Cm.Chr.Menu.txtTestRequestCandidateCreate',
            },
            component: () =>
              import('@/components/page/tst/TstTestRequestCandidateList.vue'),
          },
          {
            name: SCREENID.TST_TEST_REQUEST_STATUS_LIST,
            path: '/tst/tst-test-request-status-list',
            meta: {
              title: 'Cm.Chr.Menu.txtTstTestRequestStatusList',
            },
            component: () =>
              import('@/components/page/tst/TstTestRequestStatusList.vue'),
          },
          {
            name: SCREENID.TST_SAMPLING_LIST_FORMULATION,
            path: '/tst/tst-sampling-list',
            meta: {
              title: 'Tst.Chr.txtSamplingInventoryCorrection',
            },
            component: () =>
              import('@/components/page/tst/TstSamplingList.vue'),
          },
          {
            name: SCREENID.TST_QUALITY_CONTROL_INVENTORY_LIST,
            path: '/tst/tst-quality-control-inventory-list',
            meta: {
              title: 'Cm.Chr.Menu.txtTstChangeQualityStatusExpiration',
            },
            component: () =>
              import(
                '@/components/page/tst/TstQualityControlInventoryList.vue'
              ),
          },
        ],
      },

      {
        name: 'sjg',
        path: '/sjg',
        meta: {
          hidden: false,
          title: 'Cm.Chr.Menu.txtShipmentDecision',
          icon: 'icon_menu_sjg',
        },
        children: [
          {
            name: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
            path: '/sjg/sjg-inspection-candidate-list',
            meta: {
              title: 'Cm.Chr.Menu.txtSjgInspectionCandidateList',
            },
            component: () =>
              import('@/components/page/sjg/SjgInspectionCandidateList.vue'),
          },
          {
            name: SCREENID.SJG_SELECT_INSPECTION_ITEM,
            path: '/sjg/sjg-select-inspection-item',
            meta: {
              hidden: true,
              title: 'Cm.Chr.Menu.txtSjgSelectInspectionItem',
              parent: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
            },
            component: () =>
              import('@/components/page/sjg/SjgSelectInspectionItem.vue'),
          },
          {
            name: SCREENID.SJG_SHIPMENT_DECISION_CANDIDATE_LIST,
            path: '/sjg/sjg-shipment-decision-candidate-list',
            meta: {
              title: 'Cm.Chr.Menu.txtShipmentDecision',
            },
            component: () =>
              import(
                '@/components/page/sjg/SjgShipmentDecisionCandidateList.vue'
              ),
          },
          {
            name: SCREENID.SJG_SHIPMENT_DECISION_RESULTS_LIST,
            path: '/sjg/sjg-shipment-decision-results-list',
            meta: {
              title: 'Cm.Chr.Menu.txtSjgShipmentDecisionResultList',
            },
            component: () =>
              import(
                '@/components/page/sjg/SjgShipmentDecisionResultsList.vue'
              ),
          },
        ],
      },
      {
        name: 'sog',
        path: '/sog',
        meta: {
          hidden: false,
          title: 'Cm.Chr.Menu.txtSog',
          icon: 'icon_menu_sog',
        },
        children: [
          {
            // 出荷予定作成
            name: SCREENID.SOG_CREATE_SHIPMENT_PLAN,
            path: '/sog/sog-create-shipment-plan',
            meta: {
              title: 'Cm.Chr.Menu.txtSogPlanList',
            },
            component: () =>
              import('@/components/page/sog/SogCreateShipmentPlan.vue'),
          },
          {
            // 製品出庫指示作成
            name: SCREENID.SOG_CREATE_PRODUCT_SHIPMENT_INSTRUCTION,
            path: '/sog/create-product-shipment-instruction',
            meta: {
              title: 'Cm.Chr.Menu.txtSogCreateProductShipmentInstruction',
            },
            component: () =>
              import(
                '@/components/page/sog/SogCreateProductShipmentInstruction.vue'
              ),
          },
          {
            // 製品出庫指示一覧
            name: SCREENID.SOG_PRODUCT_SHIPMENT_INSTRUCTION_LIST,
            path: '/sog/sog-product-shipment-instruction-list',
            meta: {
              title: 'Cm.Chr.Menu.txtSogProductShipmentInstructionList',
            },
            component: () =>
              import(
                '@/components/page/sog/SogProductShipmentInstructionList.vue'
              ),
          },
          {
            name: SCREENID.SOG_CONFIRM_PRODUCT_SHIPMENT_RECORD,
            path: '/sog/sog-product-shipment-record-list',
            meta: {
              title: 'Sog.Chr.txtConfirmProductShipmentRecord',
            },
            component: () =>
              import(
                '@/components/page/sog/SogConfirmProductShipmentRecord.vue'
              ),
          },
          {
            name: SCREENID.SOG_REGIST_SHIPMENT_INSTRUCTION,
            path: '/sog/sog-regist-shipment-instruction',
            meta: {
              title: 'Cm.Chr.Menu.txtSogRegistShipmentInstruction',
            },
            component: () =>
              import('@/components/page/sog/SogRegistShipmentInstruction.vue'),
          },
          {
            name: SCREENID.SOG_REGIST_REPACKAGING_SHIPMENT_INSTRUCTION,
            path: '/sog/sog-regist-repackaging-shipment-instruction',
            meta: {
              title: 'Cm.Chr.Menu.txtSogRegistRepackagingShipmentInstruction',
            },
            component: () =>
              import(
                '@/components/page/sog/SogRegistRepackagingShipmentInstruction.vue'
              ),
          },
          {
            name: SCREENID.SOG_SHIPMENT_INSTRUCTION_LIST,
            path: '/sog/sog-shipment-instruction-list',
            meta: {
              title: 'Cm.Chr.Menu.txtSogShipmentInstructionList',
            },
            component: () =>
              import('@/components/page/sog/SogShipmentInstructionList.vue'),
          },
          {
            name: SCREENID.SOG_CONFIRM_SHIPMENT_RECORD,
            path: '/sog/sog-confirm-shipment-record',
            meta: {
              title: 'Cm.Chr.Menu.txtSogConfirmShipmentRecord',
            },
            component: () =>
              import('@/components/page/sog/SogConfirmShipmentRecord.vue'),
          },
          {
            name: SCREENID.SOG_CONFIRMED_SHIPMENT_RECORD_LIST,
            path: '/sog/sog-confirmed-shipment-record-list',
            meta: {
              title: 'Cm.Chr.Menu.txtSogConfirmedShipmentRecordList',
            },
            component: () => import('@/components/page/sys/SysAnySearch.vue'),
          },
          {
            name: SCREENID.SOG_SHIPMENT_STATUS_REPORT,
            path: '/sog/sog-shipment-status-report',
            meta: {
              title: 'Cm.Chr.Menu.txtSogStatusReportInstruction',
            },
            component: () =>
              import('@/components/page/sog/SogShipmentStatusReport.vue'),
          },
          {
            name: SCREENID.SOG_SHIPMENT_RECORD_REPORT,
            path: '/sog/sog-shipment-record-report',
            meta: {
              title: 'Sog.Chr.txtShipmentRecordReportPDFCreate',
            },
            component: () =>
              import('@/components/page/sog/SogShipmentRecordReport.vue'),
          },
        ],
      },
      {
        name: 'inventories',
        path: '/inventory',
        meta: {
          hidden: false,
          title: 'Cm.Chr.Menu.txtInventory',
          icon: 'icon_menu_inventories',
        },
        children: [
          {
            name: SCREENID.INV_INVENTORY_LIST,
            path: '/inventory/inventory-list',
            meta: {
              title: 'Cm.Chr.Menu.txtInventoryList',
            },
            component: () =>
              import('@/components/page/inv/InvInventoryList.vue'),
          },
          {
            name: SCREENID.INV_LOT_LIST,
            path: '/inventory/lot-list-mode-0',
            meta: {
              title: 'Cm.Chr.Menu.txtLotList',
            },
            component: () => import('@/components/page/inv/InvLotList.vue'),
          },
          {
            name: SCREENID.INV_LOT_LIST_FORMULATION,
            path: '/inventory/lot-list-mode-2',
            meta: {
              title: 'Cm.Chr.Menu.txtLotListFormulation',
            },
            component: () => import('@/components/page/inv/InvLotList.vue'),
          },
          {
            name: SCREENID.INV_LOT_LIST_PACKAGING,
            path: '/inventory/lot-list-mode-3',
            meta: {
              title: 'Cm.Chr.Menu.txtLotListPackaging',
            },
            component: () => import('@/components/page/inv/InvLotList.vue'),
          },
          {
            name: SCREENID.INV_INDIVIDUAL_PACKAGING_INVENTORY_LIST,
            path: '/inventory/individual-inventory-list-mode-1',
            meta: {
              title: 'Cm.Chr.Menu.txtIndividualInventoryList',
            },
            component: () =>
              import(
                '@/components/page/inv/InvIndividualPackagingInventoryList.vue'
              ),
          },
          {
            name: SCREENID.INV_INDIVIDUAL_PACKAGING_INVENTORY_LIST_FORMULATION,
            path: '/inventory/individual-inventory-list-mode-4',
            meta: {
              title:
                'Cm.Chr.Menu.txtIndividualPackagingInventoryListFormulation',
            },
            component: () =>
              import(
                '@/components/page/inv/InvIndividualPackagingInventoryList.vue'
              ),
          },
          {
            name: SCREENID.INV_INDIVIDUAL_PACKAGING_INVENTORY_LIST_PACKAGING,
            path: '/inventory/individual-inventory-list-mode-5',
            meta: {
              title: 'Cm.Chr.Menu.txtIndividualPackagingInventoryListPackaging',
            },
            component: () =>
              import(
                '@/components/page/inv/InvIndividualPackagingInventoryList.vue'
              ),
          },
          {
            name: SCREENID.INV_SEARCH_INDIVIDUAL_PACKAGING_INVENTORY,
            path: '/inventory/search-individual-packaging-inventory-mode-0',
            meta: {
              title: 'Cm.Chr.Menu.txtInvSearchIndividualPackagingInventory',
            },
            component: () =>
              import(
                '@/components/page/inv/InvSearchIndividualPackagingInventory.vue'
              ),
          },
          {
            name: SCREENID.INV_SEARCH_INDIVIDUAL_PACKAGING_INVENTORY_FORMULATION,
            path: '/inventory/search-individual-packaging-inventory-mode-1',
            meta: {
              title:
                'Cm.Chr.Menu.txtInvSearchIndividualPackagingInventoryFormulation',
            },
            component: () =>
              import(
                '@/components/page/inv/InvSearchIndividualPackagingInventory.vue'
              ),
          },
          {
            name: SCREENID.INV_SEARCH_INDIVIDUAL_PACKAGING_INVENTORY_PACKAGING,
            path: '/inventory/search-individual-packaging-inventory-mode-2',
            meta: {
              title:
                'Cm.Chr.Menu.txtInvSearchIndividualPackagingInventoryPackaging',
            },
            component: () =>
              import(
                '@/components/page/inv/InvSearchIndividualPackagingInventory.vue'
              ),
          },
          {
            name: SCREENID.RETURN_INSTRUCTION_CREATE,
            path: '/inventory/inv-create-return-instruction',
            meta: {
              title: 'Cm.Chr.Menu.txtInventoryReturnInstructionCreate',
            },
            component: () =>
              import('@/components/page/inv/InvCreateReturnInstruction.vue'),
          },
          {
            name: SCREENID.INV_RETURN_INSTRUCTION_LIST,
            path: '/inventory/return-instruction-list',
            meta: {
              title: 'Cm.Chr.Menu.txtInvReturnInstructionList',
            },
            component: () =>
              import('@/components/page/inv/InvReturnInstructionList.vue'),
          },
          {
            name: SCREENID.INV_LOAD_RETURN_INSTRUCTION,
            path: '/inventory/inv-load-return-instruction',
            meta: {
              title: 'Cm.Chr.Menu.txtInvLoadReturnInstruction',
            },
            component: () =>
              import('@/components/page/inv/InvLoadReturnInstruction.vue'),
          },
          {
            name: SCREENID.INV_REGIST_RETURN,
            path: '/inventory/regist-return',
            meta: {
              title: 'Cm.Chr.Menu.txtInventoryRegistReturn',
            },
            component: () =>
              import('@/components/page/inv/InvRegistReturn.vue'),
          },
          {
            name: SCREENID.INV_STORAGE_AND_RETRIEVAL,
            path: '/inventory/inv-storage-and-retrieval',
            meta: {
              title: 'Cm.Chr.Menu.txtStorageAndRetrieval',
            },
            component: () =>
              import('@/components/page/inv/InvStorageAndRetrieval.vue'),
          },
          {
            name: SCREENID.INV_INVENTORY_MISMATCH_LIST,
            path: '/inventory/inventory-mismatch-list',
            meta: {
              title: 'Cm.Chr.Menu.txtInvInventoryMismatchList',
            },
            component: () =>
              import('@/components/page/inv/InvInventoryMismatchList.vue'),
          },
          {
            name: SCREENID.INV_SNAPSHOT_SCHEDULE_INST,
            path: '/inventory/inventory-snapshot-schedule-inst',
            meta: {
              title: 'Cm.Chr.Menu.txtInvSnapshotScheduleInst',
            },
            component: () => import('@/components/page/sys/SysAnySearch.vue'),
          },
          {
            name: SCREENID.INV_INVENTORY_SNAPSHOT_PROCESSING_LIST,
            path: '/inventory/inv-inventory-snapshot-processing-list',
            meta: {
              title: 'Cm.Chr.Menu.txtInvInventorySnapshotProcessingList',
            },
            component: () =>
              import(
                '@/components/page/inv/InvInventorySnapshotProcessingList.vue'
              ),
          },
          {
            name: SCREENID.INV_LOAD_RETURN_TARGET_LABEL,
            path: '/inventory/inv-load-return-target-label',
            meta: {
              title: 'Cm.Chr.Menu.txtInvLoadReturnTargetLabel',
              parent: SCREENID.INV_LOAD_RETURN_INSTRUCTION,
              hidden: true,
            },
            component: () =>
              import('@/components/page/inv/InvLoadReturnTargetLabel.vue'),
          },
        ],
      },
      {
        name: 'system',
        path: '/system',
        meta: {
          hidden: false,
          title: 'Mgr.Chr.txtSystemManagement',
          icon: 'icon_menu_system',
        },
        children: [
          {
            name: SCREENID.SYS_LOT_TRACE,
            path: '/system/lot-trace',
            meta: {
              hidden: false,
              title: 'Cm.Chr.Menu.txtLotTrace',
            },
            component: () =>
              import('@/components/page/system/lottrace/LotTrace.vue'),
          },
          {
            name: SCREENID.SYS_LBL_TRC,
            path: '/system/label-trace',
            meta: {
              title: 'Cm.Chr.Menu.txtLabelTrace',
            },
            component: () => import('@/components/page/system/LabelTrace.vue'),
          },
          {
            name: SCREENID.SYS_RPT_LBL,
            path: '/system/reprint-label',
            meta: {
              title: 'Cm.Chr.Menu.txtReprintLabel',
            },
            component: () =>
              import('@/components/page/system/ReprintLabel.vue'),
          },
          {
            name: SCREENID.SYS_SIGNATURE_LOG_LIST,
            path: '/system/signature-log-list',
            meta: {
              hidden: false,
              title: 'Cm.Chr.Menu.txtSignatureLogList',
            },
            component: () => import('@/components/page/sys/SysAnySearch.vue'),
          },
          {
            name: SCREENID.SYS_OPERATION_LOG_LIST,
            path: '/system/operation-log-list',
            meta: {
              hidden: false,
              title: 'Cm.Chr.Menu.txtOperationLogList',
            },
            component: () => import('@/components/page/sys/SysAnySearch.vue'),
          },
          {
            name: SCREENID.EIF_ERPFILE_INTEGRATION_HISTORY,
            path: '/system/eif-erpfile-integration-history',
            meta: {
              hidden: false,
              title: 'Cm.Chr.Menu.txtEifERPFileIntegrationHistory',
            },
            component: () => import('@/components/page/sys/SysAnySearch.vue'),
          },
          {
            name: SCREENID.EIF_LIMSFILE_INTEGRATION_HISTORY,
            path: '/system/eif-limsfile-integration-history',
            meta: {
              hidden: false,
              title: 'Cm.Chr.Menu.txtEifLIMSFileIntegrationHistory',
            },
            component: () => import('@/components/page/sys/SysAnySearch.vue'),
          },
          {
            name: SCREENID.EIF_WAREHOUSE_SYSTEM_FILE_INTEGRATION_HISTORY,
            path: '/system/eif-warehouse-systemfile-integration-history',
            meta: {
              hidden: false,
              title: 'Cm.Chr.Menu.txtEifWarehouseSystemFileIntegrationHistory',
            },
            component: () => import('@/components/page/sys/SysAnySearch.vue'),
          },
          {
            name: SCREENID.SYS_SOPFLOW_EXCLUSIVE_RELEASE,
            path: '/system/sys-sopflow-exclusive-release',
            meta: {
              hidden: false,
              title: 'Cm.Chr.Menu.txtSysSOPFlowExclusiveRelease',
            },
            component: () => import('@/components/page/sys/SysAnySearch.vue'),
          },
          {
            name: SCREENID.SYS_DEVICE_EXCLUSIVE_RELEASE,
            path: '/system/sys-device-exclusive-release',
            meta: {
              hidden: false,
              title: 'Cm.Chr.Menu.txtSysDeviceExclusiveRelease',
            },
            component: () => import('@/components/page/sys/SysAnySearch.vue'),
          },
          {
            name: SCREENID.SOPMASTER_CREATION_EXCLUSIVE_RELEASE,
            path: '/system/sopmaster-creation-exculusive-release',
            meta: {
              hidden: false,
              title: 'Cm.Chr.Menu.txtSOPMasterCreationExclusiveRelease',
            },
            component: () => import('@/components/page/sys/SysAnySearch.vue'),
          },
          {
            name: 'userManage',
            path: '/system/user-manage',
            meta: {
              title: 'Mgr.Chr.txtUserManagement',
            },
            component: () => import('@/components/page/system/UserManage.vue'),
          },
          {
            name: 'roleManage',
            path: '/system/role-manage',
            meta: {
              title: 'Mgr.Chr.txtGroupManagement',
            },
            component: () => import('@/components/page/system/UserManage.vue'),
          },
          {
            name: 'permissionManage',
            path: '/system/permission-manage',
            meta: {
              title: 'Mgr.Chr.txtPermissionManagement',
            },
            component: () =>
              import('@/components/page/system/PermissionManage.vue'),
          },
          {
            name: 'log',
            path: '/system/log',
            meta: {
              hidden: false,
              title: 'Mgr.Chr.txtLogManagement',
            },
            children: [
              {
                name: 'operationLog',
                path: '/system/log/operation-log-list',
                meta: {
                  hidden: false,
                  title: 'Mgr.Chr.txtOperationHistory',
                },
                component: () =>
                  import('@/components/page/system/log/OperationLogList.vue'),
                children: [
                  {
                    name: 'operationLogDet',
                    path: '/system/log/operation-log-detail',
                    meta: {
                      hidden: true,
                      title: 'Mgr.Chr.txtOperationHistoryDetails',
                    },
                    component: () =>
                      import(
                        '@/components/page/system/log/OperationLogDetail.vue'
                      ),
                  },
                ],
              },
              {
                name: 'systemLog',
                path: '/system/log/system-log-list',
                meta: {
                  hidden: false,
                  title: 'Mgr.Chr.txtSystemLog',
                },
                component: () =>
                  import('@/components/page/system/log/SystemLogList.vue'),
                children: [
                  {
                    name: 'systemLogDet',
                    path: '/system/log/system-log-detail',
                    meta: {
                      hidden: true,
                      title: 'Mgr.Chr.txtSystemLogDetails',
                    },
                    component: () =>
                      import(
                        '@/components/page/system/log/SystemLogDetail.vue'
                      ),
                  },
                ],
              },
              {
                name: 'loginLog',
                path: '/system/log/login-log-list',
                meta: {
                  hidden: false,
                  title: 'Mgr.Chr.txtRegistrationHistory',
                },
                component: () =>
                  import('@/components/page/system/log/LoginLogList.vue'),
                children: [
                  {
                    name: 'loginLogDet',
                    path: '/system/log/login-log-detail',
                    meta: {
                      hidden: true,
                      title: 'Mgr.Chr.txtRegistrationHistoryDetails',
                    },
                    component: () =>
                      import('@/components/page/system/log/LoginLogDetail.vue'),
                  },
                ],
              },
            ],
          },
          {
            name: 'dictionaryManage',
            path: '/system/dictionary-manage',
            meta: {
              title: 'Mgr.Chr.txtDictionaryManagement',
            },
            component: () =>
              import('@/components/page/system/DictionaryManage.vue'),
          },
        ],
      },
      {
        name: 'master',
        path: '/master',
        meta: {
          hidden: false,
          title: 'Cm.Chr.Menu.txtMaster',
          icon: 'icon_menu_master',
        },
        children: [
          {
            name: SCREENID.SOP_RX_LIST,
            path: '/sop-pa/sop-rx-list',
            meta: {
              title: 'Cm.Chr.Menu.txtRxSop',
            },
            component: () =>
              import('@/components/page/SopPA/SopPAChartList.vue'),
          },
          {
            name: SCREENID.SOP_PRC_LIST,
            path: '/sop-pa/sop-prc-list',
            meta: {
              title: 'Cm.Chr.Menu.txtPrcSop',
            },
            component: () =>
              import('@/components/page/SopPA/SopPAPrcChartList.vue'),
          },
          {
            name: SCREENID.SOP_WGT_LIST,
            path: '/sop-pa/sop-wgt-list',
            meta: {
              title: 'Cm.Chr.Menu.txtWgtSop',
            },
            component: () =>
              import('@/components/page/SopPA/SopPAWgtChartList.vue'),
          },
          {
            name: SCREENID.SOP_PA_CHART,
            path: '/sop-pa/sop-pa-chart',
            meta: {
              title: 'Cm.Chr.Menu.txtSopEdit',
              hidden: true,
            },
            component: () => import('@/components/page/SopPAChartPage.vue'),
          },
          {
            name: SCREENID.MST_MASTER_LIST,
            path: '/master/master-list',
            meta: {
              hidden: false,
              title: 'Cm.Chr.Menu.txtMasterList',
            },
            component: () => import('@/components/page/mst/MstMasterList.vue'),
          },
          {
            name: SCREENID.MST_MBR_CREATE,
            path: '/master/mbr-create',
            meta: {
              hidden: false,
              title: 'Cm.Chr.Menu.txtMBRCreate',
            },
            component: () => import('@/components/page/mst/MstMBRCreate.vue'),
          },
          {
            name: SCREENID.MST_SELECT_MBR_COMPARISON_MASTER_CREATE,
            path: '/master/mbr-create/select-mbr-comparison-master',
            meta: {
              hidden: true,
              title: 'Cm.Chr.Menu.txtSelectMBRComparisonMaster',
              parent: SCREENID.MST_MBR_CREATE,
            },
            component: () =>
              import('@/components/page/mst/MstSelectMBRComparisonMaster.vue'),
          },
          {
            name: SCREENID.MST_MBR_APPLICATION,
            path: '/master/mbr-application',
            meta: {
              hidden: false,
              title: 'Cm.Chr.Menu.txtMBRApplication',
            },
            component: () =>
              import('@/components/page/mst/MstMBRApplication.vue'),
          },
          {
            name: SCREENID.MST_SELECT_MBR_COMPARISON_MASTER_APPLICATION,
            path: '/master/mbr-application/select-mbr-comparison-master',
            meta: {
              hidden: true,
              title: 'Cm.Chr.Menu.txtSelectMBRComparisonMaster',
              parent: SCREENID.MST_MBR_APPLICATION,
            },
            component: () =>
              import('@/components/page/mst/MstSelectMBRComparisonMaster.vue'),
          },
          {
            name: SCREENID.MST_APPROVE_MBR,
            path: '/master/approve-mbr',
            meta: {
              hidden: false,
              title: 'Cm.Chr.Menu.txtApproveMBR',
            },
            component: () => import('@/components/page/mst/MstApproveMBR.vue'),
          },
          {
            name: SCREENID.MST_SELECT_MBR_COMPARISON_MASTER_APPROVE,
            path: '/master/approve-mbr/select-mbr-comparison-master',
            meta: {
              hidden: true,
              title: 'Cm.Chr.Menu.txtSelectMBRComparisonMaster',
              parent: SCREENID.MST_APPROVE_MBR,
            },
            component: () =>
              import('@/components/page/mst/MstSelectMBRComparisonMaster.vue'),
          },
        ],
      },
    ],
  },
];
const router = createRouter({
  history: createWebHistory(),
  routes: constantRouterMap,
});
export default router;
