import { LogModListType } from './CommonTypes';

// 小日程計画一覧取得 リクエストデータ
export type GetScheduleListRequestData = {
  odrDtsFrom: string; // 製造開始日(FROM)
  odrDtsTo: string; // 製造開始日(TO)
  skdSts: string; // 計画確定
  planPrcNo: string; // 計画番号
  odrNo: string; // 製造指図番号
  matNo: string; // 品目No
  dspLotNo: string; // 製造番号
  odrSts: string; // 製造指図状態
  odrCat: string; // 指図分類
  bulkMatNo: string; // 中間製品品目No
};

// 小日程計画一覧取得 レスポンス データ部分
export type GetScheduleListData = {
  skdNo: string; // オーダー番号
  planPrcNo: string; // 計画番号
  odrNo: string; // 製造指図番号
  matDspNmJp: string; // 品名
  bulkDspNmJp: string; // 中間製品名
  odrCatNm: string; // 指図分類
  planQty: string; // 計画生産量
  unitNmJp: string; // 単位
  odrDts: string; // 製造開始予定日
  skdStsNm: string; // 小日程計画状態
  odrStsNm: string; // 指図状態
  skdBomLotSts: string; // ロット予約状態
  matNo: string; // 品目コード
  bulkMatNo: string; // 中間製品品目コード
  dspLotNo: string; // 製造番号
  rxNo: string; // 処方コード
  rxNmJp: string; // 処方名
  manFlgNm: string; // 小日程計画作成分類
  hasSkdAddExpl: string; // 計画コメント有無
  rxValidStsNm: string; // 処方期限状態名
  updDts: string; // 更新日時
  backgroundColor: string; // 背景色
};

// 小日程計画一覧取得 レスポンスデータ
export type GetScheduleListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: { scheduleList: GetScheduleListData[] }; // 小日程計画一覧
};

// 小日程計画確定前チェック 計画確定一覧の項目情報
export type CheckBeforeScheduleApprovalScheduleConfirmData = {
  skdNo: string; // オーダー番号
};

// 小日程計画確定前チェック リクエストデータ
export type CheckBeforeScheduleApprovalRequestData = {
  skdConfirmList: CheckBeforeScheduleApprovalScheduleConfirmData[]; // 計画確定一覧
};

// 小日程計画確定前チェック レスポンスデータ
export type CheckBeforeScheduleApprovalRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 小日程計画確定 計画確定一覧の項目情報
export type ModifyScheduleApprovalScheduleConfirmData = {
  skdNo: string; // オーダー番号
  updDts: string; // 更新日時
};

// 小日程計画確定 リクエストデータ
export type ModifyScheduleApprovalRequestData = {
  skdConfirmList: ModifyScheduleApprovalScheduleConfirmData[]; // 計画確定一覧
};

// 小日程計画確定 レスポンスデータ
export type ModifyScheduleApprovalRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 小日程計画確定取消 計画確定取消一覧の項目情報
export type ModifySchedulePlanScheduleConfirmedCancellationData = {
  skdNo: string; // オーダー番号
  updDts: string; // 更新日時
};

// 小日程計画確定取消 リクエストデータ
export type ModifySchedulePlanRequestData = {
  skdConfirmedCancellationList: ModifySchedulePlanScheduleConfirmedCancellationData[]; // 計画確定取消一覧
};

// 小日程計画確定取消 レスポンスデータ
export type ModifySchedulePlanRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 小日程計画変更初期表示 リクエストデータ
export type GetScheduleModifyInitRequestData = {
  skdNo: string; // オーダー番号
};

// 小日程計画変更初期表示 レスポンス データ部分
export type GetScheduleModifyInitResData = {
  skdNo: string; // オーダー番号
  planPrcNo: string; // 計画番号
  matNo: string; // 品目No
  dspNmJp: string; // 品名
  unitNmJp: string; // 品目単位
  rxNo: string; // 処方コード
  rxNmJp: string; // 処方名
  mbrNo: string; // MBR番号
  validYmd: string; // 有効期限
  stdPrdQty: string; // 標準生産量
  planQty: string; // 計画生産量
  odrDts: string; // 製造開始予定日
  skdAddExpl: string; // 計画コメント
  updDts: string; // 更新日時
  isLotReservedFlg: boolean; // ロット予約済フラグ
};

// 小日程計画変更初期表示 レスポンスデータ
export type GetScheduleModifyInitRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetScheduleModifyInitResData;
};

// 小日程計画変更 リクエストデータ
export type ModifyScheduleRequestData = {
  skdNo: string; // オーダー番号
  rxNo: string; // 処方No
  mbrNo: string; // MBR管理番号
  planQty: string; // 計画生産量
  odrDts: string; // 製造開始予定日
  skdAddExpl: string; // 製造計画コメント
  rxModExpl: string; // 処方変更コメント
  updDts: string; // 更新日時
  logModList: LogModListType;
};

// 小日程計画変更 レスポンスデータ
export type ModifyScheduleRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 計画追加 リクエストデータ
export type AddScheduleRequestData = {
  planPrcNo: string; // 計画番号
  matNo: string; // 品目No
  planQty: string; // 計画生産量
  odrDts: string; // 製造開始予定日
  rxNo: string; // 処方No
  mbrNo: string; // MBR番号
  skdAddExpl: string; // 計画コメント
};

// 計画追加 レスポンスデータ
export type AddScheduleRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 小日程計画削除初期表示 リクエストデータ
export type GetScheduleDeleteInitRequestData = {
  skdNo: string; // オーダー番号
};

// 小日程計画削除 レスポンス データ部分
export type GetScheduleDeleteInitResData = {
  skdNo: string; // オーダー番号
  planPrcNo: string; // 計画番号
  matNo: string; // 品目コード
  dspNmJp: string; // 品名
  unitNmJp: string; // 品目単位
  rxNmJp: string; // 処方名
  mbrNo: string; // MBR番号
  validYmd: string; // 有効期限
  stdPrdQty: string; // 標準生産量
  planQty: string; // 計画生産量
  odrDts: string; // 製造開始予定日
  skdAddExpl: string; // 計画コメント
  updDts: string; // 更新日時
};

// 小日程計画削除初期表示 レスポンスデータ
export type GetScheduleDeleteInitRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetScheduleDeleteInitResData;
};

// 小日程計画削除 リクエストデータ
export type DeleteScheduleRequestData = {
  skdNo: string; // オーダー番号
  updDts: string; // 更新日時
};

// 小日程計画削除 レスポンスデータ
export type DeleteScheduleRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 処方一覧 リクエストデータ
export type GetPrescriptionListRequestData = {
  matNo: string; // 品目番号
  odrDts: string; // 製造開始予定日
};

// 処方一覧 データ部分
export type GetPrescriptionListData = {
  rxNo: string; // 処方コード
  rxNmJp: string; // 処方名
  mbrNo: string; // MBR番号
  validStYmd: string; // 有効期限開始日
  validEdYmd: string; // 有効期限終了日
  stdPrdQty: string; // 標準生産量
};

// 処方一覧 リスト部分
type GetPrescriptionList = {
  // 処方一覧 続く階層2のリストを保持
  rxList: GetPrescriptionListData[];
};

// 処方リスト用 レスポンスデータ
export type GetPrescriptionListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetPrescriptionList;
};

// ロット予約解除 リクエストデータ
export type DeleteScheduleBillOfMaterialsLotRequestData = {
  skdNo: string; // オーダー番号
  prcSeq: number; // 製造工程順
  bomMatNo: string; // 品目コード
};

// ロット予約解除 レスポンスデータ
export type DeleteScheduleBillOfMaterialsLotRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 確定小日程計画一覧取得 リクエストデータ
export type GetApprovalScheduleListRequestData = {
  odrDtsFrom: string; // 製造開始日(FROM)
  odrDtsTo: string; // 製造開始日(TO)
  planPrcNo: string; // 計画番号
  odrNo: string; // 製造指図番号
  matNo: string; // 品目No
  dspLotNo: string; // 製造番号
  odrSts: string; // 製造指図状態
  odrCat: string; // 指図分類
  bulkMatNo: string; // 中間製品品目No
};

// 確定小日程計画一覧取得 レスポンス データ部分
export type GetApprovalScheduleListData = {
  skdNo: string; // オーダー番号
  planPrcNo: string; // 計画番号
  odrNo: string; // 製造指図番号
  matDspNmJp: string; // 品名
  bulkDspNmJp: string; // 中間製品名
  odrCatNm: string; // 指図分類
  planQty: string; // 計画生産量
  unitNmJp: string; // 単位
  odrDts: string; // 製造開始予定日
  skdBomLotSts: string; // ロット予約状態
  matNo: string; // 品目コード
  bulkMatNo: string; // 中間製品品目コード
  dspLotNo: string; // 製造番号
  rxNo: string; // 処方コード
  rxNmJp: string; // 処方名
  manFlgNm: string; // 計画作成区分
  hasSkdAddExpl: string; // 計画コメント有無
  hasDenialExpl: string; // 否認コメント有無
  hasAppBinNo: string; // 特別作業指示有無
  rxValidStsNm: string; // 処方期限状態
  editNoValidStsNm: string; // 版番号期限状態
  hasRxModExpl: string; // 処方変更
  hasEditNoModExpl: string; // 版番号変更
  backgroundColor: string; // 背景色
};

// 指図登録初期表示 リクエストデータ
export type GetOrderAddInitRequestData = {
  skdNo: string; // オーダー番号
};

// 指図登録初期表示 レスポンス データ部分
export type GetOrderAddInitResData = {
  skdNo: string; // オーダー番号
  planPrcNo: string; // 計画番号
  matNo: string; // 品目コード
  dspNmJp: string; // 品名
  rxNmJp: string; // 処方名
  mbrNo: string; // MBR番号
  validYmd: string; // 処方有効期限
  stdPrdQty: string; // 標準生産量
  planQty: string; // 計画生産量
  unitNmJp: string; // 単位
  odrDts: string; // 製造開始予定日
  expiryDtsType: string; // 使用期限区分
  expiryDtsFlg: boolean; // 使用期限日活性フラグ
  expiryCalcDtsType: string; // 使用期限起算日区分
  expiryCalcDtsFlg: boolean; // 使用期限起算日活性フラグ
  shelfLifeDtsType: string; // 有効期限区分
  shelfLifeDtsFlg: boolean; // 有効期限日活性フラグ
  shelfLifeCalcDtsType: string; // 有効期限起算日区分
  shelfLifeCalcDtsFlg: boolean; // 有効期限起算日活性フラグ
  handOverTxt: string; // 指図コメント
  binFileNm: string; // 特別作業指示ファイル名
  appBinNo: string; // 特別作業指示管理No
  skdUpdDts: string; // 小日程計画更新日時
  odrUpdDts: string; // 製造指図更新日時
};

// 指図登録初期表示 レスポンスデータ
export type GetOrderAddInitRes = {
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetOrderAddInitResData;
};

// 製造番号情報取得前チェック リクエストデータ
export type CheckBeforeGetLotNoInfoRequestData = {
  skdNo: string; // オーダー番号
  odrStDts: string; // 製造開始予定日
  expiryDts: string; // 使用期限日
  expiryCalcDts: string; // 使用期限起算日
  shelfLifeDts: string; // 有効期限日
  shelfLifeCalcDts: string; // 有効期限起算日
};

// 製造番号情報取得前チェック レスポンスデータ
export type CheckBeforeGetLotNoInfoRes = {
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 製造番号情報取得 リクエストデータ
export type GetLotNoInfoRequestData = {
  skdNo: string; // オーダー番号
  odrStDts: string; // 製造開始予定日
  expiryDts: string; // 使用期限日
  expiryCalcDts: string; // 使用期限起算日
  shelfLifeDts: string; // 有効期限日
  shelfLifeCalcDts: string; // 有効期限起算日
};

// 製造番号情報取得 レスポンス データ部分
export type GetLotNoInfoResData = {
  dspLotNo: string; // 製造番号
  lotNoType: string; // 製造番号発番区分
  lotNoFlg: boolean; // 製造番号活性フラグ
  expiryDts: string; // 使用期限日
  expiryCalcDts: string; // 使用期限起算日
  shelfLifeDts: string; // 有効期限日
  shelfLifeCalcDts: string; // 有効期限起算日
};

// 製造番号情報取得 レスポンスデータ
export type GetLotNoInfoRes = {
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetLotNoInfoResData;
};

// 指図登録前チェック リクエストデータ
export type CheckBeforeAddOrderRequestData = {
  skdNo: string; // オーダー番号
  lotNo: string; // 製造番号
  odrStDts: string; // 製造開始予定日
  odrQty: string; // 製造指図量
  shelfLifeCalcDts: string; // 有効期限起算日
  shelfLifeDts: string; // 有効期限日
  expiryCalcDts: string; // 使用期限起算日
  expiryDts: string; // 使用期限日
  handOverTxt: string; // 指図コメント
  addAttBinFileNm: string; // 追加用特別作業指示ファイル名
  addAttBinFile: string; // 追加用特別作業指示ファイルオブジェクト
  delAttBinNo: string; // 削除用特別作業指示ファイル管理No
  skdUpdDts: string; // 小日程計画更新日時
  odrUpdDts: string; // 製造指図更新日時
};

// 指図登録前チェック レスポンスデータ
export type CheckBeforeAddOrderRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 指図登録 リクエストデータ
export type AddOrderRequestData = {
  skdNo: string; // オーダー番号
  lotNo: string; // 製造番号
  odrStDts: string; // 製造開始予定日
  odrQty: string; // 製造指図量
  shelfLifeCalcDts: string; // 有効期限起算日
  shelfLifeDts: string; // 有効期限日
  expiryCalcDts: string; // 使用期限起算日
  expiryDts: string; // 使用期限日
  handOverTxt: string; // 指図コメント
  addAttBinFileNm: string; // 追加用特別作業指示ファイル名
  addAttBinFile: string; // 追加用特別作業指示ファイルオブジェクト
  delAttBinNo: string; // 削除用特別作業指示ファイル管理No
  skdUpdDts: string; // 小日程計画更新日時
  odrUpdDts: string; // 製造指図更新日時
};

// 指図登録 レスポンスデータ
export type AddOrderRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 確定小日程計画一覧取得 レスポンスデータ
export type GetApprovalScheduleListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: { scheduleList: GetApprovalScheduleListData[] }; // 小日程計画一覧
};

// 処方変更初期表示 リクエストデータ
export type GetPrescriptionModifyRequestData = {
  skdNo: string; // オーダー番号
};

// 処方変更初期表示 レスポンス データ部分
export type GetPrescriptionModifyResData = {
  skdNo: string; // オーダー番号
  planPrcNo: string; // 計画番号
  matNo: string; // 品目コード
  dspNmJp: string; // 品名
  dspLotNo: string; // 製造番号
  rxNo: string; // 処方コード
  rxNmJp: string; // 処方名
  mbrNo: string; // MBR番号
  validYmd: string; // 有効期限
  stdPrdQty: string; // 標準生産量
  unitNmJp: string; // 単位
  planQty: string; // 計画生産量
  odrDts: string; // 製造開始予定日
  updDts: string; // 更新日時
};

// 処方変更初期表示 レスポンスデータ
export type GetPrescriptionModifyRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetPrescriptionModifyResData;
};

// 小日程計画処方変更 リクエストデータ
export type ModifySchedulePrescriptionRequestData = {
  skdNo: string; // オーダー番号
  rxNo: string; // 処方コード
  mbrNo: string; // MBR番号
  rxModExpl: string; // 処方変更コメント
  updDts: string; // 更新日時
  logModList: LogModListType; // 変更履歴
};

// 小日程計画処方変更 レスポンスデータ
export type ModifySchedulePrescriptionRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: [];
};

// 指図予定詳細取得 リクエストデータ
export type GetMasterPrescriptionProcessListRequestData = {
  skdNo: string; // オーダー番号
};

// 指図予定詳細取得 レスポンス 処方工程一覧のデータ単体部分
export type GetMasterPrescriptionProcessListResProcessData = {
  prcSeq: number | null; // 製造工程順
  prcNmJp: string; // 製造工程
  prdDspNmJp: string; // 仕掛品名/工程品名
  stdBatchTimes: number | null; // 標準バッチ回数
  stdPrdQty: string; // 標準生産量
  unitNmJp: string; // 単位
};

// 指図予定詳細取得 レスポンス データ部分
export type GetMasterPrescriptionProcessListResData = {
  skdNo: string; // オーダー番号
  planPrcNo: string; // 計画番号
  matDspNmJp: string; // 品名
  odrDts: string; // 製造予定日
  skdAddExpl: string; // 計画コメント
  denialExpl: string; // 否認コメント
  rxPrcList: GetMasterPrescriptionProcessListResProcessData[]; // 処方工程一覧
};

// 指図予定詳細取得 レスポンスデータ
export type GetMasterPrescriptionProcessListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetMasterPrescriptionProcessListResData;
};

// 構成品予定詳細取得 リクエストデータ
export type GetMasterPrescriptionBillOfMaterialsListRequestData = {
  skdNo: string; // オーダー番号
};

// 構成品予定詳細取得 レスポンス 構成品一覧のデータ単体部分
export type GetMasterPrescriptionBillOfMaterialsListResBillOfMaterialsData = {
  prcSeq: number | null; // 製造工程順
  bomMatSeq: number | null; // 投入順序
  bomMatNo: string; // 構成品目コード
  bomDspNmJp: string; // 構成品名
  edMgtType: string; // 版管理種別
  edNo: string; // 版番号
  hasSkdBom: string; // 版指定
  stdBomQty: string; // 標準仕込量
  batchWgt: string; // バッチ指示重量
  bomUnitNmJp: string; // 単位
  updDts: string; // 更新日時
};

// 構成品予定詳細取得 レスポンス データ部分
export type GetMasterPrescriptionBillOfMaterialsListResData = {
  skdNo: string; // オーダー番号
  dspNmJp: string; // 品名
  rxNmJp: string; // 処方名
  stdPrdQty: string; // 標準生産量
  unitNmJp: string; // 単位
  rxBomList: GetMasterPrescriptionBillOfMaterialsListResBillOfMaterialsData[]; // 構成品一覧
};

// 構成品予定詳細取得 レスポンスデータ
export type GetMasterPrescriptionBillOfMaterialsListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetMasterPrescriptionBillOfMaterialsListResData;
};

// 版変更削除 リクエストデータ
export type DeleteScheduleBillOfMaterialsRequestData = {
  skdNo: string; // オーダー番号
  prcSeq: number; // 製造工程順
  bomMatNo: string; // 品目コード
  updDts: string; // 更新日時
};

// 版変更削除 レスポンスデータ
export type DeleteScheduleBillOfMaterialsRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 版変更初期表示 リクエストデータ
export type GetScheduleBillOfMaterialsInitRequestData = {
  skdNo: string; // オーダー番号
  prcSeq: number; // 製造工程順
  bomMatNo: string; // 構成品目コード
};

// 版変更初期表示 レスポンス データ部分
export type GetScheduleBillOfMaterialsInitResData = {
  currentEdNo: string; // 現版番号
  edYmd: string; // 新版番号切替日
  // 包材マスタ情報
  matEdList: {
    edNo: string; // 指定版番号
  }[];
};

// 版変更初期表示 レスポンスデータ
export type GetScheduleBillOfMaterialsInitRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetScheduleBillOfMaterialsInitResData;
};

// 版変更 リクエストデータ
export type AddScheduleBillOfMaterialsRequestData = {
  skdNo: string; // オーダー番号
  prcSeq: number; // 製造工程順
  bomMatNo: string; // 品目コード
  edNo: string; // 指定版番号
  modExpl: string; // 修正コメント
};

// 版変更 レスポンスデータ
export type AddScheduleBillOfMaterialsRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 投入品一覧取得 リクエストデータ
export type GetBomListRequestData = {
  skdNo: string; // オーダー番号
};

// 投入品一覧取得 レスポンス 処方投入品一覧データ部分
export type GetBomListResDataPrescriptionBomData = {
  matSdef: string; // 投入品区分
  matSdefNm: string; // 投入品区分表示名
  skdBomLotSts: string; // 予約状態
  bomMatNo: string; // 構成品目コード
  bomDspNmJp: string; // 構成品名
  prcSeq: number | null; // 製造工程順
  stdBomQty: string; // 標準仕込量
  designLotQty: string; // 構成品指定ロット予約数量
  unitNmJp: string; // 単位
};

// 投入品一覧取得 レスポンス データ部分
export type GetBomListResData = {
  skdNo: string; // オーダー番号
  planPrcNo: string; // 計画番号
  odrCatNm: string; // 指図分類
  matDspNmJp: string; // 製造品名
  mbrNo: string; // MBR番号
  rxNo: string; // 処方コード
  rxNmJp: string; // 処方名
  planQty: string; // 計画生産量
  rxBomList: GetBomListResDataPrescriptionBomData[];
};

// 投入品一覧取得 レスポンスデータ
export type GetBomListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetBomListResData;
};

// 原材料ロット一覧取得 リクエストデータ
export type GetRawBillOfMaterialsLotListRequestData = {
  skdNo: string; // オーダー番号
  mbrNo: string; // MBR番号
  rxNo: string; // 処方コード
  bomMatNo: string; // 構成品目コード
  prcSeq: number; // 製造工程順
  matSdef: string; // 投入品区分
};

// 原材料ロット一覧取得 レスポンス ロット予約対象一覧 データ部分
export type GetRawBillOfMaterialsLotListLotReservationData = {
  lotSid: string; // システム用ロットID
  lotNo: string; // 管理番号
  lotGenDts: string; // 入荷日
  shelfLifeDts: string; // 有効期限
  expiryDts: string; // 使用期限
  invQty: string; // 在庫量
  remainingInvQty: string; // 予約可能量
  designLotQty: string; // 予約量
  designLotQtyUnitNmJp: string; // 予約量単位
  updDts: string; // 更新日時
};

// 原材料ロット一覧取得 レスポンス データ部分
export type GetRawBillOfMaterialsLotListResData = {
  skdNo: string; // オーダー番号
  mbrNo: string; // MBR番号
  rxNo: string; // 処方コード
  prcSeq: number | null; // 製造工程順
  skdBomLotSts: string; // 予約状態
  bomMatNo: string; // 構成品目コード
  dspNmJp: string; // 構成品目名称
  stdBomQty: string; // 標準仕込量
  designLotQtyTotal: string; // 予約量（合計）
  designLotQtyTotalUnitNmJp: string; // 予約量（合計）単位
  lotReservationList: GetRawBillOfMaterialsLotListLotReservationData[]; // ロット予約対象一覧
};

// 原材料ロット一覧取得 レスポンスデータ
export type GetRawBillOfMaterialsLotListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetRawBillOfMaterialsLotListResData;
};

// 原材料ロット予約前チェック リクエストデータ
export type CheckBeforeAddScheduleBillOfMaterialsLotRequestData = {
  skdNo: string; // オーダー番号
  prcSeq: number; // 製造工程順
  bomMatNo: string; // 構成品目コード
  designLotSid: string; // 構成品指定ロットID
  designLotQty: string; // 構成品指定ロット予約数量
};

// 原材料ロット予約前チェック レスポンスデータ
export type CheckBeforeAddScheduleBillOfMaterialsLotRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 原材料ロット予約 リクエストデータ
export type AddMaterialScheduleBillOfMaterialsLotRequestData = {
  skdNo: string; // オーダー番号
  prcSeq: number; // 製造工程順
  bomMatNo: string; // 構成品目コード
  designLotSid: string; // 構成品指定ロットID
  designLotQty: string; // 構成品指定ロット予約数量
};

// 原材料ロット予約 レスポンスデータ
export type AddMaterialScheduleBillOfMaterialsLotRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// 中間製品ロット一覧取得 リクエストデータ
export type GetBulkBillOfMaterialsLotListRequestData = {
  skdNo: string; // オーダー番号
  mbrNo: string; // MBR番号
  rxNo: string; // 処方コード
  bomMatNo: string; // 構成品目コード
  prcSeq: number; // 製造工程順
  matSdef: string; // 投入品区分
};

// 中間製品ロット一覧取得 レスポンス ロット予約対象一覧 データ部分
export type GetBulkBillOfMaterialsLotListLotReservationData = {
  lotSid: string; // システム用ロットID
  lotNo: string; // 製造番号
  lotGenDts: string; // 製造日時
  shelfLifeDts: string; // 有効期限
  expiryDts: string; // 使用期限
  invQty: string; // 在庫量
  remainingInvQty: string; // 予約可能量
  designLotQty: string; // 予約量
  designLotQtyUnitNmJp: string; // 予約量単位
  updDts: string; // 更新日時
};

// 中間製品ロット一覧取得 レスポンス データ部分
export type GetBulkBillOfMaterialsLotListResData = {
  skdNo: string; // オーダー番号
  mbrNo: string; // MBR番号
  rxNo: string; // 処方コード
  prcSeq: number | null; // 製造工程順
  skdBomLotSts: string; // 予約状態
  bomMatNo: string; // 構成品目コード
  dspNmJp: string; // 構成品目名称
  stdBomQty: string; // 標準仕込量
  designLotQtyTotal: string; // 予約量（合計）
  designLotQtyTotalUnitNmJp: string; // 予約量（合計）単位
  lotReservationList: GetBulkBillOfMaterialsLotListLotReservationData[]; // ロット予約対象一覧
};

// 中間製品ロット一覧取得 レスポンスデータ
export type GetBulkBillOfMaterialsLotListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetBulkBillOfMaterialsLotListResData;
};

// 中間製品ロット予約 リクエストデータ
export type AddBulkScheduleBillOfMaterialsLotRequestData = {
  skdNo: string; // オーダー番号
  prcSeq: number; // 製造工程順
  bomMatNo: string; // 構成品目コード
  designLotSid: string; // 構成品指定ロットID
  designLotQty: string; // 構成品指定ロット予約数量
};

// 中間製品ロット予約 レスポンスデータ
export type AddBulkScheduleBillOfMaterialsLotRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: []; // 共通以外のレスポンス無し
};

// SOPフロー予定取得 リクエストデータ
export type GetMasterPrescriptionSopFlowRequestData = {
  skdNo: string; // 製造指図番号
  prcSeq: number; // 製造工程順
};

// SOPフロー予定取得 レスポンス SOPフロー一覧のデータ単体部分
export type GetMasterPrescriptionSopFlowResDataSopFlowListData = {
  sopFlowNo: string; // SOPフローNo
  sopFlowNmJp: string; // SOPフロー名称
  sopBatchType: string; // バッチ展開種別
};

// SOPフロー予定取得 レスポンス データ部分
export type GetMasterPrescriptionSopFlowResData = {
  skdNo: string; // オーダー番号
  planPrcNo: string; // 計画番号
  dspNmJp: string; // 品名
  odrDts: string; // 製造開始予定日
  prcNmJp: string; // 製造工程
  prdDspNmJp: string; // 仕掛品名/工程品名
  stdBatchTimes: number | null; // 標準バッチ回数
  stdPrdQty: string; // 標準生産量
  unitNmJp: string; // 単位
  sopFlowList: GetMasterPrescriptionSopFlowResDataSopFlowListData[]; // SOPフロー一覧
};

// SOPフロー予定取得 レスポンスデータ
export type GetMasterPrescriptionSopFlowRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetMasterPrescriptionSopFlowResData;
};

// 製造指図登録後一覧取得 リクエストデータ
export type GetRegisteredOrderListRequestData = {
  odrDtsFrom: string; // 製造開始日(FROM)
  odrDtsTo: string; // 製造開始日(TO)
  planPrcNo: string; // 計画番号
  odrNo: string; // 製造指図番号
  matNo: string; // 品目No
  lotNo: string; // 製造番号
  odrSts: string; // 指図状態
  odrCat: string; // 指図分類
  lotoutFlg: string[]; // 指図中止フラグ
  bulkMatNo: string; // 中間製品品目No
};

// 製造指図登録後一覧取得 レスポンス データ部分
export type GetRegisteredOrderListData = {
  planPrcNo: string; // 計画番号
  odrNo: string; // 製造指図番号
  dspNmJp: string; // 品名
  bulkDspNmJp: string; // 中間製品名
  odrCat: string; // 指図分類
  odrSts: string; // 指図状態
  odrQty: string; // 指図予定数
  unitNmJp: string; // 単位
  odrStYmd: string; // 製造開始予定日
  odrEdYmd: string; // 製造終了予定日
  expiryDts: string; // 使用期限
  matNo: string; // 品目コード
  bulkMatNo: string; // 中間製品品目コード
  lotNo: string; // 製造番号
  rxNo: string; // 処方コード
  rxNmJp: string; // 処方名
  lotout: string; // 指図中止
  hasSkdAddExpl: string; // 計画コメント有無
  hasHandOverTxt: string; // 指図コメント有無
  hasAppBinNo: string; // 特別作業指示有無
};

// 製造指図登録後一覧取得 レスポンスデータ
export type GetRegisteredOrderListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: { orderList: GetRegisteredOrderListData[] }; // 製造指図登録後一覧
};

// 指図詳細取得 リクエストデータ
export type GetOrderProcessListRequestData = {
  odrNo: string; // 製造指図番号
};

// 指図詳細取得 レスポンス データ部分
export type GetOrderProcessListData = {
  prcSeq: number | null; // 製造工程順
  prcNmJp: string; // 製造工程
  prdBatchTimes: number | null; // バッチ回数
  prdDspNmJp: string; // 仕掛品名/工程品名
  prdPlanQty: string; // 予定生産量
  unitNmJp: string; // 単位
  prdStYmd: string; // 生産開始予定日
  prdEdYmd: string; // 生産終了予定日
};

export type GetOrderProcessListResData = {
  lotNo: string; // 製造番号
  odrNo: string; // 製造指図番号
  dspNmJp: string; // 品名
  odrYmd: string; // 製造予定日
  skdAddExpl: string; // 計画コメント
  handOverTxt: string; // 指図コメント
  odrReportBinNo: string; // 製造指図書管理No
  odrPrcList: GetOrderProcessListData[]; // 製造工程一覧
};

// 指図詳細取得 レスポンスデータ
export type GetOrderProcessListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetOrderProcessListResData;
};

// 製造指図承認一覧取得 リクエストデータ
export type GetOrderApprovalListRequestData = {
  odrStYmdFrom: string; // 製造開始日(FROM)
  odrStYmdTo: string; // 製造開始日(TO)
  planPrcNo: string; // 計画番号
  odrNo: string; // 製造指図番号
  matNo: string; // 品目No
  lotNo: string; // 製造番号
  odrCat: string; // 指図分類
  bulkMatNo: string; // 中間製品品目No
};

// 製造指図承認一覧取得 レスポンス データ部分
export type GetOrderApprovalListData = {
  planPrcNo: string; // 計画番号
  odrNo: string; // 製造指図番号
  dspNmJp: string; // 品名
  bulkDspNmJp: string; // 中間製品名
  odrCat: string; // 指図分類
  odrQty: string; // 製造指図量
  unitNmJp: string; // 単位
  odrStYmd: string; // 製造開始予定日
  odrEdYmd: string; // 製造終了予定日
  expiryDts: string; // 使用期限
  matNo: string; // 品目コード
  bulkMatNo: string; // 中間製品品目コード
  lotNo: string; // 製造番号
  rxNo: string; // 処方コード
  rxNmJp: string; // 処方名
  hasSkdAddExpl: string; // 計画コメント有無
  hasHandOverTxt: string; // 指図コメント有無
  hasAppBinNo: string; // 特別作業指示有無
  updDts: string; // 更新日時
  backgroundColor: string; // 背景色
};

// 製造指図承認一覧取得 レスポンスデータ
export type GetOrderApprovalListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: { orderList: GetOrderApprovalListData[] };
};

// 製造指図一覧取得 リクエストデータ
export type GetApprovedOrderListRequestData = {
  odrStYmdFrom: string; // 製造開始日(FROM)
  odrStYmdTo: string; // 製造開始日(TO)
  planPrcNo: string; // 計画番号
  odrNo: string; // 製造指図番号
  matNo: string; // 品目No
  lotNo: string; // 製造番号
  odrCat: string; // 指図分類
  lotoutFlg: string[]; // 指図中止フラグ
  bulkMatNo: string; // 中間製品品目No
};

// 製造指図一覧取得 レスポンス データ部分
export type GetApprovedOrderListData = {
  planPrcNo: string; // 計画番号
  odrNo: string; // 製造指図番号
  dspNmJp: string; // 品名
  bulkDspNmJp: string; // 中間製品名
  odrCat: string; // 指図分類
  odrSts: string; // 指図状態
  lotNo: string; // 製造番号
  rxNo: string; // 処方コード
  rxNmJp: string; // 処方名
  odrQty: string; // 製造指図量
  unitNmJp: string; // 単位
  odrStYmd: string; // 製造開始予定日
  odrEdYmd: string; // 製造終了予定日
  lotout: string; // 指図中止
  expiryDts: string; // 使用期限
  matNo: string; // 品目コード
  bulkMatNo: string; // 中間製品品目コード
  hasSkdAddExpl: string; // 計画コメント有無
  hasHandOverTxt: string; // 指図コメント有無
  hasAppBinNo: string; // 特別作業指示有無
};

// 製造指図一覧取得 レスポンスデータ
export type GetApprovedOrderListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: { orderList: GetApprovedOrderListData[] };
};

// 構成品一覧取得 リクエストデータ
export type GetOrderBillOfMaterialsListRequestData = {
  odrNo: string; // 製造指図番号
};

// 構成品一覧取得 レスポンス 構成品一覧部
export type GetOrderBillOfMaterialsListData = {
  prcSeq: number | null; // 製造工程順
  batchNo: number | null; // バッチNo
  bomMatSeq: number | null; // 投入順序
  bomMatNo: string; // 構成品目コード
  bomDspNmJp: string; // 構成品名
  edNo: number | null; // 版番号
  bomPlanQty: string; // 予定仕込量
  bomUnitNmJp: string; // 単位
};

// 構成品一覧取得 レスポンス データ部
export type GetOrderBillOfMaterialsListResData = {
  odrNo: string; // 製造指図番号
  dspNmJp: string; // 品名
  odrQty: string; // 製造指図量
  unitNmJp: string; // 単位
  odrStYmd: string; // 生産開始予定日
  odrEdYmd: string; // 生産終了予定日
  odrBomList: GetOrderBillOfMaterialsListData[];
};

// 構成品一覧取得 レスポンスデータ
export type GetOrderBillOfMaterialsListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetOrderBillOfMaterialsListResData;
};

// SOPフロー一覧取得 リクエストデータ
export type GetOrderSopFlowRequestData = {
  odrNo: string; // 製造指図番号
  prcSeq: number; // 製造工程順
};

// NOTE:GetOrderSopFlowListDataと命名被るため既存と違う名前に変更しています
// SOPフロー一覧取得 レスポンス SOPフロー一覧部
export type GetOrderSopFlowResDataSopFlowListData = {
  sopFlowNo: string; // SOPフローNo
  sopFlowLnum: number | null; // SOPフロー実行ログ番号
  sopFlowNmJp: string; // SOPフロー名称
  batchNo: number | null; // バッチNo
  sopFlowSts: string; // SOPフロー状態
  stDts: string; // SOPフロー開始日時
  edDts: string; // SOPフロー終了日時
};

// SOPフロー一覧取得 レスポンス データ部
export type GetOrderSopFlowResData = {
  odrNo: string; // 製造指図番号
  prcSeq: number | null; // 製造工程順
  prcNmJp: string; // 製造工程
  prdDspNmJp: string; // 仕掛品名/工程品名
  prdPlanQty: string; // 予定生産量
  unitNmJp: string; // 単位
  prdStYmd: string; // 生産開始予定日
  prdEdYmd: string; // 生産終了予定日
  sopFlowList: GetOrderSopFlowResDataSopFlowListData[];
};

// SOPフロー一覧取得 レスポンスデータ
export type GetOrderSopFlowRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetOrderSopFlowResData;
};

// 指図コメント入力初期表示 リクエストデータ
export type GetOrderHandOverTextRequestData = {
  odrNo: string; // 製造指図番号
};

// 指図コメント入力初期表示 レスポンス データ部
export type GetOrderHandOverTextResData = {
  odrNo: string; // 製造指図番号
  planPrcNo: string; // 計画番号
  matNo: string; // 品目コード
  dspNmJp: string; // 品名
  lotNo: string; // 製造番号
  rxNmJp: string; // 処方名
  mbrNo: string; // MBR番号
  validYmd: string; // 処方有効期限
  stdPrdQty: string; // 標準生産量
  odrStYmd: string; // 製造開始予定日
  odrEdYmd: string; // 製造終了予定日
  odrQty: string; // 製造指図量
  unitNmJp: string; // 単位
  expiryDts: string; // 使用期限日
  handOverTxt: string; // 指図コメント
  updDts: string; // 更新日付
};

// 指図コメント入力初期表示 レスポンスデータ
export type GetOrderHandOverTextRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetOrderHandOverTextResData;
};

// 指図コメント更新 リクエストデータ
export type ModifyOrderHandOverTextRequestData = {
  odrNo: string; // 製造指図番号
  handOverTxt: string; // 指図コメント
  odrUpdDts: string; // 製造指図更新日付
  logModList: LogModListType;
};

// 指図コメント更新 レスポンスデータ
export type ModifyOrderHandOverTextRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: [];
};

// 秤量指示明細一覧取得 リクエストデータ
export type GetWeighingInstructionListFromOrderRequestData = {
  odrNo: string; // 製造指図番号
};

// 秤量指示明細一覧取得 レスポンス 秤量指示明細一覧部
export type GetWeighingInstructionListFromOrderData = {
  wgtInstNo: string; // 秤量指示明細番号
  wgtInstSts: string; // 秤量指示明細状態
  prcNmJp: string; // 製造工程
  batchNo: number | null; // バッチNo
  wgtSopsetKey: string; // 秤量セットキー
  wgtRoomNmJp: string; // 秤量室
  bomMatNo: string; // 秤量品目コード
  wgtDspNmJp: string; // 秤量品名
  bomPlanQty: string; // 指図指示量
  unitNmJp: string; // 単位
  bomMatSeq: number | null; // 秤量品投入番号
  wgtTiterKeyDsp: string; // 補正計算
};

// 秤量指示明細一覧取得 レスポンス データ部
export type GetWeighingInstructionListFromOrderResData = {
  wgtInstList: GetWeighingInstructionListFromOrderData[];
};

// 秤量指示明細一覧取得 レスポンスデータ
export type GetWeighingInstructionListFromOrderRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetWeighingInstructionListFromOrderResData;
};

// 特別作業指示初期表示 リクエストデータ
export type GetOrderAppendixRequestData = {
  odrNo: string; // 製造指図番号
};

// 特別作業指示初期表示 レスポンス データ部
export type GetOrderAppendixResData = {
  odrNo: string; // 製造指図番号
  attBinFileNm: string; // ファイル名
  attBinNo: string; // 特別作業指示管理No
  updDts: string; // 更新日付
};

// 特別作業指示初期表示 レスポンスデータ
export type GetOrderAppendixRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetOrderAppendixResData;
};

// 特別作業指示更新 リクエストデータ
export type ModifyOrderAppendixRequestData = {
  odrNo: string; // 製造指図番号
  addAttBinFileNm: string; // 追加用ファイル名
  addAttBinFile: string; // 追加用ファイルオブジェクト
  delAttBinNo: string; // 削除用ファイル管理No
  updDts: string; // 更新日付
  modExpl: string; // 修正コメント
};

// 特別作業指示更新 レスポンスデータ
export type ModifyOrderAppendixRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: [];
};

// 製造指図承認 リクエスト データ部
export type ModifyOrderApprovalRequestData = {
  odrApprovalList: ModifyOrderApprovalListRequestData[];
};

// 製造指図承認 リクエスト データ部
export type ModifyOrderApprovalListRequestData = {
  odrNo: string; // 製造指図番号
  updDts: string; // 製造指図更新日付
};

// 製造指図承認 レスポンスデータ
export type ModifyOrderApprovalRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: [];
};

// 製造指図否認初期表示 リクエストデータ
export type GetOrderDenialInitRequestData = {
  odrNo: string; // 製造指図番号
};

// 製造指図否認初期表示 レスポンス データ部
export type GetOrderDenialInitResData = {
  odrNo: string; // 製造指図番号
  planPrcNo: string; // 計画番号
  matNo: string; // 品目コード
  dspNmJp: string; // 品名
  lotNo: string; // 製造番号
  rxNmJp: string; // 処方名
  mbrNo: string; // MBR番号
  validYmd: string; // 処方有効期限
  stdPrdQty: string; // 標準生産量
  odrStYmd: string; // 製造開始予定日
  odrEdYmd: string; // 製造終了予定日
  odrQty: string; // 製造指図量
  unitNmJp: string; // 単位
  expiryDts: string; // 使用期限日
  handOverTxt: string; // 指図コメント
  odrUpdDts: string; // 製造指図更新日付
  lotInfoUpdDts: string; // ロット情報更新日付
};

// 製造指図否認初期表示 レスポンスデータ
export type GetOrderDenialInitRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetOrderDenialInitResData;
};

// 製造指図否認 リクエスト
export type ModifyOrderDenialRequestData = {
  odrNo: string; // 製造指図番号
  odrUpdDts: string; // 製造指図更新日付
  lotInfoUpdDts: string; // ロット情報更新日付
  modExpl: string; // 修正コメント
};

// 製造指図否認 レスポンスデータ
export type ModifyOrderDenialRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: [];
};

// 製造工程一覧取得 リクエストデータ
export type GetOrderProcessListInRebatchInstructionRequestData = {
  odrNo: string; // 製造指図番号
};

// 製造工程一覧取得 レスポンス 製造工程一覧部
export type GetOrderProcessListInRebatchInstructionData = {
  prcSeq: number | null; // 製造工程順
  prcNmJp: string; // 製造工程
  batchNo: number | null; // バッチNo
  dspNmJp: string; // 仕掛品名/工程品名
  prdPlanQty: string; // 予定生産量
  unitNmJp: string; // 単位
  recConfilm: string; // 製造記録確認
  odrAdjustType: string; // 製造指図量調整種別
  sopFlowSts: string; // SOPフロー状態
};

// 製造工程一覧取得 レスポンスデータ データ部
export type GetOrderProcessListInRebatchInstructionResData = {
  lotNo: string; // 製造番号
  odrNo: string; // 製造指図番号
  dspNmJp: string; // 品名
  odrYmd: string; // 製造予定日
  skdAddExpl: string; // 計画コメント
  handOverTxt: string; // 指図コメント
  odrPrcList: GetOrderProcessListInRebatchInstructionData[]; // 製造工程一覧
};

// 製造工程一覧取得 レスポンスデータ
export type GetOrderProcessListInRebatchInstructionRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetOrderProcessListInRebatchInstructionResData;
};

// 処方SOPフロー一覧取得 リクエストデータ
export type GetOrderSopFlowListRequestData = {
  odrNo: string; // 製造指図番号
  prcSeq: number; // 製造工程順
  batchNo: number; // バッチNo
};

// 処方SOPフロー一覧取得 レスポンス SOPフロー一覧部
export type GetOrderSopFlowListData = {
  sopFlowNo: string; // SOPフローNo
  sopFlowNmJp: string; // SOPフロー名称
  sopBatchType: string; // バッチ展開種別
};

// 処方SOPフロー一覧取得 レスポンスデータ データ部
export type GetOrderSopFlowListResData = {
  lotNo: string; // 製造番号
  prcNmJp: string; // 製造工程
  batchNo: number | null; // バッチNo
  rxSopFlowList: GetOrderSopFlowListData[]; // SOPフロー一覧
};

// 処方SOPフロー一覧取得 レスポンスデータ
export type GetOrderSopFlowListRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: GetOrderSopFlowListResData;
};

// 再バッチ指示登録 リクエスト SOPフロー一覧部
export type AddOrderRebatchInstructionSopFlowData = {
  sopFlowNo: string; // SOPフローNo
};

// 再バッチ指示登録 リクエスト
export type AddOrderRebatchInstructionRequestData = {
  odrNo: string; // 製造指図番号
  prcSeq: number; // 製造工程順
  batchNo: number; // バッチNo
  modExpl: string; // 修正コメント
  sopFlowList: AddOrderRebatchInstructionSopFlowData[]; // SOPフロー一覧
};

// 再バッチ指示登録 レスポンスデータ
export type AddOrderRebatchInstructionRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: [];
};

// NarrowType用データ
export const ODR_DENIAL_ORDER_NARROW_TYPE = {
  DENIAL: 'Denial', // 指図否認
  ERASE: 'Erase', // 指図取消
  DISCONTINUE: 'Discontinue', // 指図中止
} as const;

// 計画取込 リクエスト 計画取込一覧部
export type AddScheduleImportSkdList = {
  erpifMesId: string; // MESID
  skdNo: string; // 品目オーダNO（計画指図No）
  mesMatNo: string; // MES品目コード
  erpMfgZoneNo: string; // 製造場所コード
  erpStrZoneNo: string; // 格納場所コード
  erpUnitCd: string; // 単位コード
  skdQty: string; // 指図予定数
  skdYmd: string; // 着手予定日
  avlYmd: string; // 使用可能予定日
  arrgtNo1: string; // 手配No1(納入月)
  arrgtNo2: string; // 手配No2（連番(製剤単位)）
  arrgtNo3: string; // 手配No3（製剤ロットNo）
  arrgtNo4: string; // 手配No4（連番（製剤を取り分ける優先順））
  erpExpl: string; // 備考
};

// 計画取込 リクエストデータ
export type AddScheduleImportRequestData = {
  importSkdList: AddScheduleImportSkdList[]; // 計画取込一覧
};

// 計画取込 レスポンスデータ
export type AddScheduleImportRes = {
  // 共通レスポンスデータ
  rDts: string;
  rCode: number;
  rTitle: string;
  rMsg: string;
  rData: [];
};
