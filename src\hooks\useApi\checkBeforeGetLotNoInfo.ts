import END_POINT from '@/constants/endPoint';
import {
  CheckBeforeGetLotNoInfoRes,
  CheckBeforeGetLotNoInfoRequestData,
} from '@/types/HookUseApi/OdrTypes';
import {
  ExtendCommonRequestType,
  ExtendCommonRequestWithMainApiFlagType,
} from '@/types/HookUseApi/CommonTypes';
import { useApi } from './util';

// 製造番号情報取得前チェック
const useCheckBeforeGetLotNoInfo = (
  data: ExtendCommonRequestType<CheckBeforeGetLotNoInfoRequestData>,
) =>
  useApi<
    ExtendCommonRequestWithMainApiFlagType<CheckBeforeGetLotNoInfoRequestData>,
    CheckBeforeGetLotNoInfoRes
  >(END_POINT.CHECK_BEFORE_GET_LOT_NO_INFO, 'post', { ...data, mainApiFlg: 0 });

export default useCheckBeforeGetLotNoInfo;
