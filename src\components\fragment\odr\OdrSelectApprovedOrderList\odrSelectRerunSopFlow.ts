import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';

const { t } = i18n.global;

const getOrderSopFlowListInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 製造番号
    lotNo: {
      label: { text: t('Odr.Chr.txtLotNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
    // 製造工程
    prcNmJp: {
      label: { text: t('Odr.Chr.txtOrderProcess') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
    // バッチ番号
    batchNo: {
      label: { text: t('Odr.Chr.txtBatchNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
  });

export default getOrderSopFlowListInfoShowItems;
