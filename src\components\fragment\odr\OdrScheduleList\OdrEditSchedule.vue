<template>
  <!-- 計画変更ダイアログ -->
  <DialogWindow
    :title="$t('Odr.Chr.txtPlanChange')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="odrEditScheduleFormRef.formModel"
      :formItems="odrEditScheduleFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          odrEditScheduleFormRef.customForm = v;
        }
      "
      @changeFormModel="
        (changeFlag, { changeLogModList }) => {
          updateDialogChangeFlagRef(changeFlag);
          updateCurrentChangedFormModelMap(changeLogModList);
        }
      "
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 製造開始予定日未入力時のエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxOrderDateNoInputVisible"
    :dialogProps="messageBoxOrderDateNoInputPropsRef"
    :submitCallback="() => closeDialog('messageBoxOrderDateNoInputVisible')"
  />
  <!-- 小日程計画更新の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPlanUpdateVisible"
    :dialogProps="messageBoxPlanUpdatePropsRef"
    :cancelCallback="() => closeDialog('messageBoxPlanUpdateVisible')"
    :submitCallback="requestApiPlanUpdate"
  />
  <!-- 小日程計画更新完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPlanUpdateFinishedVisible"
    :dialogProps="messageBoxPlanUpdateFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxPlanUpdateFinished"
  />
  <!-- 処方選択ダイアログ -->
  <OdrSelectRx
    :isClicked="isClickedShowOdrSelectRxDialogRef"
    :materialCode="materialCode"
    :materialName="materialName"
    :unitName="unitName"
    :orderDates="orderDates"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    @submit="reflectPrescriptionInfo"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import onValidateHandler, {
  handleValidationBySingleRowSelect,
} from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import { PrescriptionListTableRowData } from '@/types/PrescriptionSelectDialogTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import OdrSelectRx from '@/components/include/odr/OdrSelectRx.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import { rules } from '@/utils/validator';
import CONST from '@/constants/utils';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { useGetScheduleModifyInit, useModifySchedule } from '@/hooks/useApi';
import {
  ModifyScheduleRequestData,
  GetScheduleListData,
  GetScheduleModifyInitRequestData,
  GetScheduleModifyInitResData,
} from '@/types/HookUseApi/OdrTypes';
import {
  CommonRequestType,
  LogModListType,
} from '@/types/HookUseApi/CommonTypes';
import {
  getOdrEditScheduleFormItems,
  odrEditScheduleFormModel,
} from './odrEditSchedule';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxOrderDateNoInputVisible'
  | 'messageBoxPlanUpdateVisible'
  | 'messageBoxPlanUpdateFinishedVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxOrderDateNoInputVisible: false,
  messageBoxPlanUpdateVisible: false,
  messageBoxPlanUpdateFinishedVisible: false,
};

// 子に渡す情報パラメータ
let materialCode: string = '';
let materialName: string = '';
let unitName: string = '';
let orderDates: string = '';

// カスタムフォームの描画更新トリガー
const customFormRenderingTriggerRef = ref(false);
// '処方選択' クリック
const isClickedShowOdrSelectRxDialogRef = ref<boolean>(false);

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 製造開始予定日未入力時のメッセージボックス
const messageBoxOrderDateNoInputPropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titleOrderDateNoInput'),
  content: t('Odr.Msg.contentOrderDateNoInput'),
  isSingleBtn: true,
  type: 'error',
});

// 小日程計画更新の確認メッセージボックス
const messageBoxPlanUpdatePropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titlePlanUpdate'),
  content: t('Odr.Msg.contentPlanUpdate'),
  type: 'question',
});

// 小日程計画更新の完了メッセージボックス
const messageBoxPlanUpdateFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

const odrEditScheduleFormRef = ref<CustomFormType>({
  formItems: getOdrEditScheduleFormItems(),
  formModel: odrEditScheduleFormModel,
});

// 小日程計画削除初期表示のレスポンスデータ
let initResponseData: GetScheduleModifyInitResData = {
  skdNo: '',
  planPrcNo: '',
  matNo: '',
  dspNmJp: '',
  unitNmJp: '',
  rxNo: '',
  rxNmJp: '',
  mbrNo: '',
  validYmd: '',
  stdPrdQty: '',
  planQty: '',
  odrDts: '',
  skdAddExpl: '',
  updDts: '',
  isLotReservedFlg: false,
};

let logModList: LogModListType = [];
// 変更履歴更新時処理
const updateCurrentChangedFormModelMap = (changeLogModList: LogModListType) => {
  logModList = changeLogModList;
};

type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  selectedRows: GetScheduleListData[]; // 親ページの選択行情報
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 標準生産量、計画生産量のカンマ削除処理
const deleteCommaFormItemsQuantity = () => {
  if (odrEditScheduleFormRef.value.formItems.stdPrdQty.formRole !== 'textBox')
    return;
  if (odrEditScheduleFormRef.value.formItems.planQty.formRole !== 'textBox')
    return;

  // 標準生産量
  odrEditScheduleFormRef.value.formItems.stdPrdQty.formModelValue =
    odrEditScheduleFormRef.value.formItems.stdPrdQty.formModelValue.replace(
      /,/g,
      '',
    );
  // 計画生産量
  odrEditScheduleFormRef.value.formItems.planQty.formModelValue =
    odrEditScheduleFormRef.value.formItems.planQty.formModelValue.replace(
      /,/g,
      '',
    );
};

// 処方選択ダイアログの 実行 押下時処理
// 処方情報の反映
const reflectPrescriptionInfo = (v: PrescriptionListTableRowData) => {
  // 処方コード
  odrEditScheduleFormRef.value.formItems.rxNo.formModelValue = v.rxNo;
  // 処方名
  odrEditScheduleFormRef.value.formItems.rxNmJp.formModelValue = v.rxNmJp;
  // MBR番号
  odrEditScheduleFormRef.value.formItems.mbrNo.formModelValue = v.mbrNo;
  // 有効期限
  odrEditScheduleFormRef.value.formItems.validYmd.formModelValue = `${v.validStYmd} - ${v.validEdYmd}`;

  // 標準生産量
  odrEditScheduleFormRef.value.formItems.stdPrdQty.formModelValue = v.stdPrdQty;
  // NOTE:テーブル上はカンマがついているが、入力としては不要なためカンマ除去
  deleteCommaFormItemsQuantity();

  // 処方変更コメント
  odrEditScheduleFormRef.value.formItems.rxModExpl.formModelValue = '';
  if (odrEditScheduleFormRef.value.formItems.rxModExpl.formRole === 'textBox') {
    // 処方選択を実行後に活性状態にする
    odrEditScheduleFormRef.value.formItems.rxModExpl.props!.disabled = false;

    // 活性状態に伴い、必須状態を付与する
    odrEditScheduleFormRef.value.formItems.rxModExpl.tags = [
      { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
    ];
    // NOTE: .ts の初期設定と連動していない。rules追加時は注意。
    odrEditScheduleFormRef.value.formItems.rxModExpl.rules = [
      rules.required('textBox'),
      rules.length(64),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ];

    // 処方選択時に毎回空文字を入れるため、バリデーションが残らないよう状態リセットを行う
    odrEditScheduleFormRef.value.customForm?.clearValidate('rxModExpl');
    // 必須tag付与するための再描画トリガー
    customFormRenderingTriggerRef.value = !customFormRenderingTriggerRef.value;
  }
};

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // 1.下記チェックを行い、チェックOKなら処理継続する。
  // １ 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    odrEditScheduleFormRef.value.customForm !== undefined &&
    (await odrEditScheduleFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return false;
  }

  // 2.以下のメッセージを表示
  // 計画変更の確認
  openDialog('messageBoxPlanUpdateVisible');
  return false;
};

// 計画更新の確認メッセージ'OK'押下時処理
// 計画更新のAPIリクエスト処理
const requestApiPlanUpdate = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxPlanUpdateVisible');

  showLoading();
  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する
  const requestData: ModifyScheduleRequestData = {
    skdNo: initResponseData.skdNo,
    rxNo: odrEditScheduleFormRef.value.formItems.rxNo.formModelValue.toString(),
    mbrNo:
      odrEditScheduleFormRef.value.formItems.mbrNo.formModelValue.toString(),
    planQty:
      odrEditScheduleFormRef.value.formItems.planQty.formModelValue.toString(),
    odrDts:
      odrEditScheduleFormRef.value.formItems.odrDts.formModelValue.toString(),
    skdAddExpl:
      odrEditScheduleFormRef.value.formItems.skdAddExpl.formModelValue.toString(),
    rxModExpl:
      odrEditScheduleFormRef.value.formItems.rxModExpl.formModelValue.toString(),
    updDts: initResponseData.updDts,
    logModList,
  };
  const { responseRef, errorRef } = await useModifySchedule({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxPlanUpdatePropsRef.value.title,
    msgboxMsgTxt: messageBoxPlanUpdatePropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return false;
  }

  if (responseRef.value) {
    // 4．小日程計画を更新する。
    // ・データベースを更新した後、以下のメッセージを表示する。
    // 小日程計画更新完了
    messageBoxPlanUpdateFinishedPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxPlanUpdateFinishedPropsRef.value.content =
      responseRef.value.data.rMsg;
  }

  closeLoading();
  openDialog('messageBoxPlanUpdateFinishedVisible');
  return false;
};

// 小日程計画更新完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxPlanUpdateFinished = () => {
  // 再検索用に親に実行を通知
  emit('submit', props.privilegesBtnRequestData);

  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxPlanUpdateFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

// 処方選択ボタンクリック時イベントの設定
const buttonOdrSelectRxOnClickHandler = () => {
  // 製造開始予定日入力状態か判定
  if (odrEditScheduleFormRef.value.formItems.odrDts.formModelValue === '') {
    // 製造開始予定日未入力時エラーメッセージ表示
    openDialog('messageBoxOrderDateNoInputVisible');
    return;
  }
  // 処方選択ダイアログのパラメータセット
  materialCode =
    odrEditScheduleFormRef.value.formItems.matNo.formModelValue.toString();
  materialName =
    odrEditScheduleFormRef.value.formItems.dspNmJp.formModelValue.toString();
  unitName =
    odrEditScheduleFormRef.value.formItems.unitNmJp.formModelValue.toString();
  orderDates =
    odrEditScheduleFormRef.value.formItems.odrDts.formModelValue.toString();

  // 処方選択ダイアログを表示開始
  // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
  isClickedShowOdrSelectRxDialogRef.value =
    !isClickedShowOdrSelectRxDialogRef.value;
};

// 処方選択ButtonFormの初期設定
const odrSelectRxButtonFormInit = () => {
  if (
    odrEditScheduleFormRef.value.formItems.buttonOdrSelectRx.formRole !==
    'button'
  ) {
    return;
  }

  // NOTE:小日程計画がロット予約済みの場合、非活性
  odrEditScheduleFormRef.value.formItems.buttonOdrSelectRx.props!.disabled =
    initResponseData.isLotReservedFlg;

  // ボタンクリックイベント設定
  odrEditScheduleFormRef.value.formItems.buttonOdrSelectRx.onClickHandler =
    buttonOdrSelectRxOnClickHandler;
};

/**
 * 計画変更ダイアログの初期設定
 */
const odrEditScheduleInit = async () => {
  // NOTE:未選択なら処理させない。エラーメッセージはTabulatorTable側で出してくれる。
  if (props.selectedRows.length === 0) return;

  // 単一選択されていなかった場合はエラー表示とする
  try {
    await handleValidationBySingleRowSelect(props.selectedRows);
  } catch (error) {
    return;
  }

  // NOTE:単一チェック済みなので確実に単一行。先頭取得する
  const selectedRow = props.selectedRows.at(0)!;

  updateDialogChangeFlagRef(false);
  logModList = [];

  showLoading();

  // 小日程計画変更初期表示のAPIを行う。
  const requestData: GetScheduleModifyInitRequestData = {
    skdNo: selectedRow.skdNo,
  };
  const { responseRef, errorRef } = await useGetScheduleModifyInit({
    ...props.privilegesBtnRequestData,
    ...requestData,
    // NOTE: 直前メッセージは無い。msgbox関連は設定不要。
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  closeLoading();
  if (responseRef.value) {
    // ダイアログ表示初期値として、レスポンス情報を格納
    initResponseData = responseRef.value.data.rData;
  }

  // FormItems初期化
  odrEditScheduleFormRef.value.formItems = getOdrEditScheduleFormItems();

  // 処方選択ButtonFormの初期設定
  odrSelectRxButtonFormInit();

  if (odrEditScheduleFormRef.value.formItems.planQty.formRole !== 'textBox') {
    return;
  }

  // NOTE:計画生産量 ※小日程計画がロット予約済みの場合、入力不可
  odrEditScheduleFormRef.value.formItems.planQty.props!.disabled =
    initResponseData.isLotReservedFlg;

  // カスタムフォーム初期値設定
  Object.entries(initResponseData).forEach(([key, value]) => {
    if (
      odrEditScheduleFormRef.value.formModel &&
      key in odrEditScheduleFormRef.value.formModel
    ) {
      odrEditScheduleFormRef.value.formItems[key].formModelValue =
        value?.toString() ?? '';
    }
  });

  // NOTE:テーブル上はカンマがついているが、入力としては不要なためカンマ除去
  deleteCommaFormItemsQuantity();

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

// ダイアログ起動条件の監視
watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await odrEditScheduleInit();
  },
);
</script>
