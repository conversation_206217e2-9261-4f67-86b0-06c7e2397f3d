import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';

const { t } = i18n.global;

const getProcessDataInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // オーダー番号
    skdNo: {
      label: { text: t('Odr.Chr.txtScheduleNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
    // 計画番号
    planPrcNo: {
      label: { text: t('Odr.Chr.txtPlanProcedureNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
    // 品名
    matDspNmJp: {
      label: { text: t('Odr.Chr.txtMaterialName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
    // 製造予定日
    odrDts: {
      label: { text: t('Odr.Chr.txtOrderScheduleDate') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
    // 計画コメント
    skdAddExpl: {
      label: { text: t('Odr.Chr.txtPlanComment') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
    // 否認コメント
    denialExpl: {
      label: { text: t('Odr.Chr.txtDenyComment') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
  });

export default getProcessDataInfoShowItems;
