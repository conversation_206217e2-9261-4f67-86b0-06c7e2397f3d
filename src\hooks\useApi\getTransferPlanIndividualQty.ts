import END_POINT from '@/constants/endPoint';
import {
  GetTransferPlanIndividualQtyReq,
  GetTransferPlanIndividualQtyRes,
} from '@/types/HookUseApi/TrfTypes';
import {
  ExtendCommonRequestType,
  ExtendCommonRequestWithMainApiFlagType,
} from '@/types/HookUseApi/CommonTypes';
import { useApi } from './util';

const useGetTransferPlanIndividualQty = (
  data: ExtendCommonRequestType<GetTransferPlanIndividualQtyReq>,
) =>
  useApi<
    ExtendCommonRequestWithMainApiFlagType<GetTransferPlanIndividualQtyReq>,
    GetTransferPlanIndividualQtyRes
  >(END_POINT.GET_TRF_PLAN_INV_QTY, 'post', { ...data, mainApiFlg: 0 });
export default useGetTransferPlanIndividualQty;
