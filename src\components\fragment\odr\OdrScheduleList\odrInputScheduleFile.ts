import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import { ref } from 'vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

// CSVカラム名
export const CSV_COLUMN_NAMES = [
  'mes_id',
  'itm_ord_no',
  'itm_cd',
  'prod_loc_cd',
  'strg_loc_cd',
  'inpt_unit_cd',
  'inst_qty',
  'schd_prod_st_dt',
  'schd_avl_dt',
  'sno1',
  'sno2',
  'sno3',
  'sno4',
  'rmrks',
] as const;

// テーブル設定
export const tablePropsDataOrderInputScheduleFileRef = ref<TabulatorTableIF>({
  pageName: 'OdrInputScheduleFile',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'skdNo', // 主キー。ユニークになるものを設定。

  column: [
    // MESID
    {
      title: 'Odr.Chr.txtOdrSkdCsvMesId',
      field: 'erpifMesId',
      width: COLUMN_WIDTHS.ODR.MES_ID,
    },
    // 品目オーダNo
    {
      title: 'Odr.Chr.txtOdrSkdCsvMaterialOrderNo',
      field: 'skdNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    // MES品目コード
    {
      title: 'Odr.Chr.txtOdrSkdCsvMesMaterialCode',
      field: 'mesMatNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    // 製造場所コード
    {
      title: 'Odr.Chr.txtOdrSkdCsvOrderLocationCode',
      field: 'erpMfgZoneNo',
      width: COLUMN_WIDTHS.ODR.ERP_MFG_ZONE_NO,
    },
    // 格納場所コード
    {
      title: 'Odr.Chr.txtOdrSkdCsvStorageLocationCode',
      field: 'erpStrZoneNo',
      width: COLUMN_WIDTHS.ODR.ERP_STR_ZONE_NO,
    },
    // 単位コード
    {
      title: 'Odr.Chr.txtOdrSkdCsvUnitCode',
      field: 'erpUnitCd',
      width: COLUMN_WIDTHS.ODR.ERP_UNIT_CD,
    },
    // 指図予定数
    {
      title: 'Odr.Chr.txtOdrSkdCsvOrderSceduleNumber',
      field: 'skdQty',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.ODR_QTY,
    },
    // 着手予定日
    {
      title: 'Odr.Chr.txtOdrSkdCsvStartScheduleDate',
      field: 'skdYmd',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDD,
    },
    // 使用可能予定日
    {
      title: 'Odr.Chr.txtOdrSkdCsvAvailableScheduleDate',
      field: 'avlYmd',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDD,
    },
    // 手配No１（納入月）
    {
      title: 'Odr.Chr.txtOdrSkdCsvArrgtNo1',
      field: 'arrgtNo1',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.ODR.ARRGT_NO1,
    },
    // 手配No２（連番（製剤単位））
    {
      title: 'Odr.Chr.txtOdrSkdCsvArrgtNo2',
      field: 'arrgtNo2',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.ODR.ARRGT_NO2,
    },
    // 手配No３（製剤ロットNo）
    {
      title: 'Odr.Chr.txtOdrSkdCsvArrgtNo3',
      field: 'arrgtNo3',
      width: COLUMN_WIDTHS.LOT_NO,
    },
    // 手配No４（製剤を取り分ける優先順）
    {
      title: 'Odr.Chr.txtOdrSkdCsvArrgtNo4',
      field: 'arrgtNo4',
      width: COLUMN_WIDTHS.ODR.ARRGT_NO4,
    },
    // 備考
    {
      title: 'Odr.Chr.txtOdrSkdCsvRemarks',
      field: 'erpExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
  ],
  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
});
