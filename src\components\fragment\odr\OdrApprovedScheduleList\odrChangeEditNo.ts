import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// 版変更ダイアログのアイテム定義
export const getOdrChangeEditNoFormItems: () => CustomFormType['formItems'] =
  () => ({
    currentEdNo: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtCurrentEditionNo') }, // 現版番号
      formRole: 'textBox',
      props: { disabled: true },
    },
    edYmd: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtEditionChangeDate') }, // 新版番号切替日
      formRole: 'textBox',
      props: { disabled: true },
    },
    matEdList: {
      formModelValue: '',
      label: { text: t('Odr.Chr.txtSelectEditionNo') }, // 指定版番号
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: '', // NOTE:標準コンボボックスの仕組みを利用しない。レスポンスのmatEdListを元に項目生成する。
    },
  });

// 版変更ダイアログのモデル定義
export const odrChangeEditNoFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getOdrChangeEditNoFormItems());
