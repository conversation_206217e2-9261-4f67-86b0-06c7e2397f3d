import { ref } from 'vue';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';

// 検証環境一覧用テーブル設定
export const tablePropsDataVerificationEnvironmentRef = ref<TabulatorTableIF>({
  pageName: 'VerificationEnvironment',
  dataID: 'uniqueKey', // 主キー。ユニークになるものを設定。
  height: '230px',
  column: [], // NOTE: カラムは動的に変わるため、画面表示時に作成する
  showCheckbox: {
    show: true,
    condition: '', // 条件無し
    conditionValue: 0,
    allAllowed: true,
  },
  tableData: [],
  // NOTE:権限が親からprops経由で来るため、Init関数内でonSelectBtnsを生成する
  onSelectBtns: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
  // NOTE: TabulatorTable.vue の formatterCellColor を実行しセルの文字色を変更するためダミーで設定
  cellColor: {
    color: '#333',
    columns: [
      {
        modificationType: 'dummy',
      },
    ],
  },
});

// 本番環境一覧用テーブル設定
export const tablePropsDataProductionEnvironmentRef = ref<TabulatorTableIF>({
  pageName: 'ProductionEnvironment',
  dataID: '', // 主キー。ユニークになるものを設定。
  height: '230px',
  column: [], // NOTE: カラムは動的に変わるため、画面表示時に作成する
  tableData: [],
  // NOTE:権限が親からprops経由で来るため、Init関数内でonSelectBtnsを生成する
  onSelectBtns: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
});
