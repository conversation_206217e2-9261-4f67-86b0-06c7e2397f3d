import END_POINT from '@/constants/endPoint';
import {
  ModifyWeighingRoomChangeRequestEraseRequestData,
  ModifyWeighingRoomChangeRequestEraseRes,
} from '@/types/HookUseApi/WgtTypes';
import {
  ExtendCommonRequestType,
  ExtendCommonRequestWithMainApiFlagType,
} from '@/types/HookUseApi/CommonTypes';
import { useApi } from './util';

// 秤量室変更依頼取消
const useModifyWeighingRoomChangeRequestErase = (
  data: ExtendCommonRequestType<ModifyWeighingRoomChangeRequestEraseRequestData>,
) =>
  useApi<
    ExtendCommonRequestWithMainApiFlagType<ModifyWeighingRoomChangeRequestEraseRequestData>,
    ModifyWeighingRoomChangeRequestEraseRes
  >(END_POINT.MOD_WGT_ROOM_CHG_REQUEST_ERASE, 'post', {
    ...data,
    mainApiFlg: 0,
  });

export default useModifyWeighingRoomChangeRequestErase;
