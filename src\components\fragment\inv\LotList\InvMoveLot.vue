<template>
  <!-- ロット移動ダイアログ -->
  <DialogWindow
    :title="$t('Inv.Chr.btnMoveLot')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkMoveLotForm()"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="inventoryMoveLotFormRef.formModel"
      :formItems="inventoryMoveLotFormRef.formItems"
      @selectedItem="updateFormItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          inventoryMoveLotFormRef.customForm = v;
        }
      "
      @changeFormModel="
        (changeFlag, { changeLogModList }) => {
          updateDialogChangeFlagRef(changeFlag);
          updateCurrentChangedFormModelMap(changeLogModList);
        }
      "
    />
    <div class="inv-move-lot_tabulator-wrapper Util_mt-16">
      <!-- 在庫一覧 -->
      <BaseHeading
        level="2"
        :text="$t('Inv.Chr.txtInventoryList')"
        fontSize="24px"
        class="Util_mt-16"
      />
      <!-- DialogWindow内で書けば、Tabulatorを表示できる -->
      <TabulatorTable :propsData="tablePropsDialogRef" />
    </div>
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <!-- ロット移動の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.inventoryMoveLotConfirm"
    :dialogProps="messageBoxMoveLotConfirmProps"
    :cancelCallback="() => closeDialog('inventoryMoveLotConfirm')"
    :submitCallback="
      () => {
        closeDialog('inventoryMoveLotConfirm');
        apiHandler(messageBoxMoveLotConfirmProps);
      }
    "
  />
  <!-- ロット移動のチェックメッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.inventoryMoveLotInfo"
    :dialogProps="messageBoxMoveLotPropsRef"
    :cancelCallback="closeAllDialog"
    :submitCallback="closeAllDialog"
  />
  <!-- ロット移動前チェックのワーニングメッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.checkLotBeforeMoveWarning"
    :dialogProps="messageBoxCheckLotBeforeMovePropsRef"
    :cancelCallback="() => closeDialog('checkLotBeforeMoveWarning')"
    :submitCallback="
      () => {
        closeDialog('checkLotBeforeMoveWarning');
        apiHandler(
          messageBoxCheckLotBeforeMovePropsRef,
          WARNING_NECESSITY_FLAG.UNNECESSARY,
        );
      }
    "
  />
</template>
<script setup lang="ts">
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import CONST from '@/constants/utils';
import onValidateHandler from '@/utils/validateHandler';
import { CustomFormType } from '@/types/CustomFormTypes';
import { DynamicModels } from '@/types/ConditionSearchTypes';
import {
  InventoryLotMoveData,
  InventoryListData,
  ModifyInventoryLotMoveReq,
  InvInfoListData,
} from '@/types/HookUseApi/InvTypes';
import {
  ComboBoxDataOptionData,
  ComboBoxDataStandardReturnData,
  CommonRequestType,
  ExtendCommonRequestType,
  LogModListType,
} from '@/types/HookUseApi/CommonTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import createMessageBoxForm from '@/utils/commentMessageBox';
import { DialogProps } from '@/types/MessageBoxTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  setCustomFormComboBoxOptionList,
  setCustomFormFilterComboBoxOptionList,
} from '@/utils/comboBoxOptionList';
import {
  useGetComboBoxDataStandard,
  useGetInventoryLotMove,
  useModifyInventoryLotMove,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import {
  getInventoryLotMoveFormItems,
  inventoryLotMoveFormModel,
  tablePropsData,
} from './invMoveLot';

const inventoryMoveLotFormRef = ref<CustomFormType>({
  formItems: getInventoryLotMoveFormItems(),
  formModel: inventoryLotMoveFormModel,
});

let logModList: LogModListType = [];
const updateCurrentChangedFormModelMap = (changeLogModList: LogModListType) => {
  logModList = changeLogModList;
};

type Props = {
  selectedRowData: InventoryListData | null;
  dynamicModels: DynamicModels;
  isClicked: boolean;
  dspNarrowType: string;
  privilegesBtnRequestData: CommonRequestType;
};
const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);
const customFormRenderingTriggerRef = ref(false);
const constantData = {
  zoneNo: 'zoneNo',
  locNo: 'locNo',
};
/**
 * 警告要否フラグ
 * - UNNECESSARY: 警告が不要であることを表します（値: 0）。
 * - NECESSITY: 警告が必要であることを表します（値: 1）：デフォルト値。
 */
const WARNING_NECESSITY_FLAG = {
  UNNECESSARY: 0,
  NECESSITY: 1,
} as const;

let inventoryLotMoveData: InventoryLotMoveData = {
  matNo: '',
  matNm: '',
  lotNo: '',
  lotStsNm: '',
  lotLockStsNm: '',
  lotUpdDts: '',
  dspNarrowType: '',
  invList: [],
};
let comboBoxOptionList: ComboBoxDataOptionData[] = [];
let invInfoListData: InvInfoListData[] = [];
type DialogRefKey =
  | 'singleButton'
  | 'inventoryMoveLotConfirm'
  | 'inventoryMoveLotInfo'
  | 'fragmentDialogVisible'
  | 'checkLotBeforeMoveWarning';

const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  inventoryMoveLotConfirm: false,
  inventoryMoveLotInfo: false,
  fragmentDialogVisible: false,
  checkLotBeforeMoveWarning: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);
const tablePropsDialogRef = ref<TabulatorTableIF>({
  ...tablePropsData,
});

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxMoveLotConfirmProps: DialogProps = {
  title: t('Inv.Chr.txtConfirm'),
  content: t('Inv.Msg.inventoryMoveLotCorrectionConfirm'),
  type: 'question',
};

const messageBoxMoveLotPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxForm = createMessageBoxForm('message', 'cmtWarningZoneEnt');
let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;
const messageBoxCheckLotBeforeMovePropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

/**
 * ロット移動チェック
 */
const checkMoveLotForm = async () => {
  const validate =
    inventoryMoveLotFormRef.value.customForm !== undefined &&
    (await inventoryMoveLotFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (validate) {
    openDialog('inventoryMoveLotConfirm');
  }
  return false;
};

/**
 * 確認メッセージ
 */
const apiHandler = async (
  messageBoxProps: DialogProps,
  warningNecessityFlg: number = WARNING_NECESSITY_FLAG.NECESSITY,
) => {
  closeDialog('checkLotBeforeMoveWarning');
  showLoading();

  let moveLotFormModel: ExtendCommonRequestType<ModifyInventoryLotMoveReq> = {
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedRowData!.lotSid,
    invInfoList: invInfoListData,
    destZoneNo: inventoryMoveLotFormRef.value.formModel.zoneNo.toString(),
    destLocNo: inventoryMoveLotFormRef.value.formModel.locNo.toString(),
    afmRefNo: inventoryMoveLotFormRef.value.formModel.afmRefNo.toString(),
    afmExpl: inventoryMoveLotFormRef.value.formModel.cmtCatInvMov.toString(),
    warnAfmExpl: '',
    lotUpdDts: inventoryLotMoveData.lotUpdDts,
    msgboxTitleTxt: messageBoxProps.title,
    msgboxMsgTxt: messageBoxProps.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    warningNecessityFlg,
    logModList,
  };
  if (
    'isPrompt' in messageBoxProps &&
    warningNecessityFlg === WARNING_NECESSITY_FLAG.UNNECESSARY &&
    comboBoxDataStandardReturnData
  ) {
    moveLotFormModel = {
      ...moveLotFormModel,
      msgboxInputCmt: messageBoxProps.formModel.message.toString(),
      warnAfmExpl: messageBoxProps.formModel.message.toString(),
    };

    moveLotFormModel.warnAfmExpl = messageBoxProps.formModel.message.toString();
  }
  // ３．ロット移動する
  const { responseRef, errorRef } =
    await useModifyInventoryLotMove(moveLotFormModel);
  if (errorRef.value) {
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      messageBoxCheckLotBeforeMovePropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxCheckLotBeforeMovePropsRef.value.content =
        errorRef.value.response.rMsg;
      const resetData = createMessageBoxForm('message', 'cmtWarningZoneEnt');
      if (
        'isPrompt' in messageBoxCheckLotBeforeMovePropsRef.value &&
        comboBoxDataStandardReturnData
      ) {
        messageBoxCheckLotBeforeMovePropsRef.value.formItems =
          resetData.formItems;
        setCustomFormComboBoxOptionList(
          messageBoxCheckLotBeforeMovePropsRef.value.formItems,
          comboBoxDataStandardReturnData.rData.rList,
        );
      }
      openDialog('checkLotBeforeMoveWarning');
    } else {
      messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
      messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
      messageBoxSingleButtonRef.value.type = 'error';
      openDialog('singleButton');
    }
    closeLoading();
    return;
  }
  if (responseRef.value) {
    messageBoxMoveLotPropsRef.value.title = responseRef.value.data.rTitle;
    messageBoxMoveLotPropsRef.value.content = responseRef.value.data.rMsg;
    openDialog('inventoryMoveLotInfo');
  }
  closeLoading();
};

/**
 * 指定アイテムの関連更新を行う
 */
const updateFormItems = (fieldId: string) => {
  // 移動先ロケーションの絞り込み
  if (fieldId !== constantData.zoneNo) return;
  setCustomFormFilterComboBoxOptionList(
    inventoryMoveLotFormRef.value.formItems,
    comboBoxOptionList,
    constantData.locNo,
    constantData.locNo,
    [fieldId],
  );
  if (
    inventoryMoveLotFormRef.value.formItems.locNo.formRole === 'selectComboBox'
  ) {
    let overridePropsDisabled = true;
    if (
      inventoryMoveLotFormRef.value.formItems.locNo.selectOptions.length > 0 &&
      inventoryMoveLotFormRef.value.formItems.zoneNo.formModelValue !== ''
    ) {
      overridePropsDisabled = false;
    }
    inventoryMoveLotFormRef.value.formItems.locNo.props!.disabled =
      overridePropsDisabled;
    inventoryMoveLotFormRef.value.customForm?.clearValidate('locNo');
    customFormRenderingTriggerRef.value = !customFormRenderingTriggerRef.value;
  }
};

const closeAllDialog = () => {
  closeDialog('inventoryMoveLotInfo');
  // W1A2810ロット移動ダイアログを閉じる
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData);
};

/**
 * 初期設定
 */
const invMoveLotInit = async () => {
  if (!props.selectedRowData) return;
  showLoading();
  updateDialogChangeFlagRef(false);
  logModList = [];
  inventoryMoveLotFormRef.value.formItems = getInventoryLotMoveFormItems();
  if (
    inventoryMoveLotFormRef.value.formItems.locNo.formRole === 'selectComboBox'
  ) {
    inventoryMoveLotFormRef.value.formItems.locNo.props!.disabled = false;
  }
  // 2.ロット在庫情報取得(移動用)
  const { responseRef, errorRef } = await useGetInventoryLotMove({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedRowData!.lotSid,
    zoneGrpNo: props.dynamicModels.selectData.zoneGrpNo,
    zoneNo: props.dynamicModels.selectData.zoneNo,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    inventoryLotMoveData = responseRef.value.data.rData;
    tablePropsDialogRef.value.tableData = inventoryLotMoveData.invList;
    if (inventoryLotMoveData.invList.length > 0) {
      invInfoListData = inventoryLotMoveData.invList.map((item) => ({
        lblSid: item.lblSid,
        invUpdDts: item.invUpdDts,
        lblUpdDts: item.lblUpdDts,
      }));
    }

    setFormModelValueFromApiResponse(
      inventoryMoveLotFormRef,
      inventoryLotMoveData,
    );
  }

  // 標準コンボボックスデータ取得
  let dspNarrowTypeVal = '';
  if (props.dspNarrowType === 'M') {
    dspNarrowTypeVal = 'M,N';
  } else if (props.dspNarrowType === 'P') {
    dspNarrowTypeVal = 'P,N';
  }
  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'zoneNo',
        condKey: 'm_ic_zone',
        where:
          props.dspNarrowType === 'N'
            ? { inv_hidden_flg: '0' }
            : { inv_hidden_flg: '0', dsp_narrow_type: dspNarrowTypeVal },
      },
      {
        cmbId: 'locNo',
        condKey: 'm_ic_loc',
        optionCol: { zone_no: 'zoneNo' },
      },
      {
        cmbId: 'cmtInvMov',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'INV_LOT_MOVE' },
      },
      {
        cmbId: 'cmtWarning',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'INV_LOT_MOVE' },
      },
      {
        cmbId: 'cmtWarningZoneEnt',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'INV_WARNING' },
      },
    ],
  });

  if (
    comboBoxDataStandardReturnData &&
    comboBoxDataStandardReturnData.rData.rList.length > 0
  ) {
    comboBoxOptionList = comboBoxDataStandardReturnData.rData.rList;
    setCustomFormComboBoxOptionList(
      inventoryMoveLotFormRef.value.formItems,
      comboBoxDataStandardReturnData!.rData.rList,
    );
    if ('formItems' in messageBoxCheckLotBeforeMovePropsRef.value)
      // コメントメッセージボックス選択肢
      setCustomFormComboBoxOptionList(
        messageBoxCheckLotBeforeMovePropsRef.value.formItems,
        comboBoxDataStandardReturnData.rData.rList,
      );
    updateFormItems('zoneNo');
  }
  inventoryMoveLotFormRef.value.formItems.locNo.formModelValue =
    props.selectedRowData.locNm;
  inventoryMoveLotFormRef.value.formItems.zoneNo.formModelValue =
    props.selectedRowData.zoneNm;
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, invMoveLotInit);
</script>
<style lang="scss" scoped>
$namespace: 'inv-move-lot';

.#{$namespace} {
  &_tabulator-wrapper {
    border-top: 1px solid var(--el-border-color);
  }
}
</style>
