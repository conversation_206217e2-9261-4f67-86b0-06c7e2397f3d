import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';
import CONST from '@/constants/utils';

const { t } = i18n.global;

// 処方選択ダイアログ情報領域のアイテム定義
export const getDialogInformationFormItems: () => CustomFormType['formItems'] =
  () => ({
    unitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    baleUnit: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      formModelValue: '',
      label: { text: t('Aog.Chr.txtBaleUnit') }, // 個装重量
      suffix: { formModelProp: 'unitNm' },
      formatter: 'number',
      formRole: 'textBox',
      props: { size: 'small' },
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.positiveRealNumericOnly(),
      ],
    },
    baleCnt: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      formModelValue: '',
      label: { text: t('Aog.Chr.txtBaleCnt') }, // 個装数
      formatter: 'number',
      formRole: 'textBox',
      props: { size: 'small' },
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 9 }),
        rules.naturalNumericOnly(),
      ],
    },
    baleModExpl: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Aog.Chr.txtComment') }, // コメント
      formModelValue: '',
      rules: [
        rules.required('textComboBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      ],
      formRole: 'textComboBox',
      props: {
        clearable: true,
        size: 'large',
      },
      selectOptions: [],
      cmbId: 'rsnCdAogMod',
    },
  });

// 処方選択ダイアログ情報領域のモデル定義
export const dialogInformationFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getDialogInformationFormItems());
