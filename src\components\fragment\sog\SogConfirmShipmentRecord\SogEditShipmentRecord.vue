<template>
  <!-- 出荷実績修正 -->
  <DialogWindow
    :title="$t('Sog.Chr.txtSogEditShipmentRecord')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkForm()"
    @visible="updateDialogChangeFlagRef"
  >
    <CustomForm
      :formModel="sogEditShipmentRecordFormRef.formModel"
      :formItems="sogEditShipmentRecordFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          sogEditShipmentRecordFormRef.customForm = v;
        }
      "
      @changeFormModel="
        (changeFlag, { changeLogModList }) => {
          updateDialogChangeFlagRef(changeFlag);
          updateCurrentChangedFormModelMap(changeLogModList);
        }
      "
    />
  </DialogWindow>
  <!-- 出荷実績修正の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.sogEditShipmentConfirm"
    :dialogProps="messageBoxSogEditShipmentConfirmProps"
    :cancelCallback="() => closeDialog('sogEditShipmentConfirm')"
    :submitCallback="() => apiHandler(messageBoxSogEditShipmentConfirmProps)"
  />
  <!-- 異常 -->
  <MessageBox
    v-if="dialogVisibleRef.errorConfirm"
    :dialogProps="messageBoxErrorPropsRef"
    :submitCallback="() => closeDialog('errorConfirm')"
  />
  <!-- 情報 -->
  <MessageBox
    v-if="dialogVisibleRef.infoConfirm"
    :dialogProps="messageBoxInfoPropsRef"
    :submitCallback="closeAllDialog"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import onValidateHandler from '@/utils/validateHandler';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  GetSogConfirmShipmentRecordData,
  ModifySogResultFixReq,
} from '@/types/HookUseApi/SogTypes';
import {
  CommonRequestType,
  ExtendCommonRequestType,
  LogModListType,
} from '@/types/HookUseApi/CommonTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import {
  useGetComboBoxDataStandard,
  useGetSogResultFixModify,
  useModifySogResultFix,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  getSogEditShipmentRecordFormItems,
  sogEditShipmentRecordFormModel,
} from './sogEditShipmentRecord';

const sogEditShipmentRecordFormRef = ref<CustomFormType>({
  formItems: getSogEditShipmentRecordFormItems(),
  formModel: sogEditShipmentRecordFormModel,
});

let logModList: LogModListType = [];
const updateCurrentChangedFormModelMap = (changeLogModList: LogModListType) => {
  logModList = changeLogModList;
};

type Props = {
  selectedRowData: GetSogConfirmShipmentRecordData | null;
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
};

const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

type DialogRefKey =
  | 'sogEditShipmentConfirm'
  | 'errorConfirm'
  | 'infoConfirm'
  | 'fragmentDialogVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  sogEditShipmentConfirm: false,
  errorConfirm: false,
  infoConfirm: false,
  fragmentDialogVisible: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxErrorPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const messageBoxInfoPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxSogEditShipmentConfirmProps: DialogProps = {
  title: t('Sog.Chr.txtSogEditShipmentRecordConfirm'),
  content: t('Sog.Msg.sogEditShipmentRecordConfirm'),
  type: 'question',
};

// 出荷実績更新日時
let sogRsltFixUpdDtsValue: string = '';

const checkForm = async () => {
  const validate =
    sogEditShipmentRecordFormRef.value.customForm !== undefined &&
    (await sogEditShipmentRecordFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (validate) {
    openDialog('sogEditShipmentConfirm');
  }
  return false;
};

const apiHandler = async (messageBoxProps: DialogProps) => {
  closeDialog('sogEditShipmentConfirm');
  showLoading();
  const apiRequestData: ExtendCommonRequestType<ModifySogResultFixReq> = {
    ...props.privilegesBtnRequestData,
    sogInstNo:
      sogEditShipmentRecordFormRef.value.formModel.sogInstNo.toString(),
    rsltModQty:
      sogEditShipmentRecordFormRef.value.formModel.rsltModQty.toString(),
    sogSlipModExpl:
      sogEditShipmentRecordFormRef.value.formModel.sogRsltModExpl.toString(),
    sogSlipFixUpdDts: sogRsltFixUpdDtsValue,
    msgboxTitleTxt: messageBoxProps.title,
    msgboxMsgTxt: messageBoxProps.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    logModList,
  };
  const { responseRef, errorRef } = await useModifySogResultFix(apiRequestData);
  if (errorRef.value) {
    closeLoading();
    messageBoxErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('errorConfirm');
    return false;
  }
  if (responseRef.value) {
    messageBoxInfoPropsRef.value.title = responseRef.value.data.rTitle;
    messageBoxInfoPropsRef.value.content = responseRef.value.data.rMsg;
    openDialog('infoConfirm');
  }
  closeLoading();
  return true;
};

const closeAllDialog = () => {
  closeDialog('infoConfirm');
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData);
};

/**
 * 初期設定
 */
const sogEditShipmentRecordInit = async () => {
  if (!props.selectedRowData) return;
  updateDialogChangeFlagRef(false);
  showLoading();
  logModList = [];
  sogEditShipmentRecordFormRef.value.formItems =
    getSogEditShipmentRecordFormItems();

  // 出荷実績修正データ取得
  const { responseRef, errorRef } = await useGetSogResultFixModify({
    ...props.privilegesBtnRequestData,
    sogInstNo: props.selectedRowData.sogInstNo,
  });
  if (errorRef.value) {
    closeLoading();
    messageBoxErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('errorConfirm');
    return;
  }
  if (responseRef.value) {
    sogRsltFixUpdDtsValue = responseRef.value.data.rData.sogSlipFixUpdDts;
    sogEditShipmentRecordFormRef.value.formItems.sogSlipGrpNo.formModelValue =
      props.selectedRowData.sogSlipGrpNo;
    setFormModelValueFromApiResponse(
      sogEditShipmentRecordFormRef,
      responseRef.value.data.rData,
    );
  }
  // 標準コンボボックスデータ取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtSogRsltMod',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'SOG_SLIP_FIX_MOD' },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setCustomFormComboBoxOptionList(
      sogEditShipmentRecordFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, sogEditShipmentRecordInit);
</script>
