import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

const { t } = i18n.global;

// 出庫依頼量変更ダイアログのアイテム定義
export const getShipmentRequestQuantityFormRefFormItems: () => CustomFormType['formItems'] =
  () => ({
    unitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    matNo: {
      label: { text: t('Trf.Chr.txtMatNo') }, // 品目コード
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    matNm: {
      label: { text: t('Trf.Chr.txtMatNm') }, // 品名
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    },
    trfQty: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Trf.Chr.txtTrfQty') }, // 依頼量
      formModelValue: '',
      suffix: { formModelProp: 'unitNm' },
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.positiveRealNumericOnly(),
      ],
      formRole: 'textBox',
    },
    planExpl: {
      label: { text: t('Trf.Chr.txtComment') }, // コメント
      formModelValue: '',
      rules: [rules.length(64, t('Cm.Chr.txtLength', [64]))],
      formRole: 'textComboBox',
      props: {
        clearable: true,
      },
      selectOptions: [],
      cmbId: 'cmtTrfPlan',
    },
  });

// 出庫依頼量変更ダイアログのモデル定義
export const shipmentRequestQuantityFormRefFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getShipmentRequestQuantityFormRefFormItems());

export const tablePropsData: TabulatorTableIF = {
  pagination: false,
  dataID: 'lotNo',
  autoWidth: 'unitNm', // 自動幅の列
  column: [
    {
      title: 'Trf.Chr.txtManagementNo',
      field: 'lotNo',
      width: COLUMN_WIDTHS.LOT_NO,
    },
    { title: 'Trf.Chr.txtEdNo', field: 'edNo', width: COLUMN_WIDTHS.ED_NO },
    {
      title: 'Trf.Chr.txtInventoryQuantity',
      field: 'invQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.INV_QTY,
    },
    {
      title: 'Trf.Chr.txtUnitNm',
      field: 'unitNm',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    {
      title: '',
      field: 'zoneWhFlg',
      hidden: true,
    },
  ],
  multiLevelGrouping: true,
  customParameter: {
    groupField: 'col',
    multiLevelGrouping: {
      groupFields: ['zoneNm', 'zoneInvQty', 'zoneWhNm'],
      titleName: [
        'Trf.Chr.txtZoneNm',
        'Trf.Chr.txtZoneInvQty',
        'Trf.Chr.txtZoneWhNm',
      ], // ゾーン/ゾーン内在庫量
      groupTitleIconType: 'no', // no：非表示、radio：ラジオ、checkbox：チェックボックス
    },
  }, // カスタムパラメータ（groupby）
  showRadio: false,
  tableData: [],
  noUseConditionSearch: true, // 絞り込み条件無し
  showConditionSearch: false, // ConditionSearch不要
};
