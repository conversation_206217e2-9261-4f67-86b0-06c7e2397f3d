import END_POINT from '@/constants/endPoint';
import {
  GetConfirmModifyInfoRequestData,
  GetApprovalModifyInfoInitRequestData,
  GetModifyLogListCommonRes,
  MODIFY_NARROW_TYPE,
} from '@/types/HookUseApi/PrdTypes';
import {
  ExtendCommonRequestType,
  ExtendCommonRequestWithMainApiFlagType,
} from '@/types/HookUseApi/CommonTypes';
import { useApi } from './util';

// NOTE:確認/参照/承認共通の汎用的なAPIフックです。工程作業記録は getModifyLogList.ts を使用します。
// NOTE:このuseApiは、複数のURLを切り替える汎用的なAPIフックです。
// dspNarrowTypeによりURL、リクエストパラメータが切り替わります。
// レスポンスパラメータは完全に同一です。
const useGetModifyLogListCommon = (
  data: ExtendCommonRequestType<
    GetConfirmModifyInfoRequestData | GetApprovalModifyInfoInitRequestData
  >,
  dspNarrowType: string,
) => {
  let endPoint: string = END_POINT.GET_MOD_LOG_LIST;
  // dspNarrowTypeからURLを取得する
  switch (dspNarrowType) {
    case MODIFY_NARROW_TYPE.CONFIRM:
      endPoint = END_POINT.GET_CONF_MOD_INFO;
      break;
    case MODIFY_NARROW_TYPE.APPROVAL:
    case MODIFY_NARROW_TYPE.REFERENCE: // fall through
      endPoint = END_POINT.GET_APP_MOD_INFO_INIT;
      break;
    default:
      console.error('想定外のdspNarrowTypeです。', dspNarrowType);
      break;
  }

  return useApi<
    ExtendCommonRequestWithMainApiFlagType<
      GetConfirmModifyInfoRequestData | GetApprovalModifyInfoInitRequestData
    >,
    GetModifyLogListCommonRes
  >(endPoint, 'post', { ...data, mainApiFlg: 0 });
};

export default useGetModifyLogListCommon;
