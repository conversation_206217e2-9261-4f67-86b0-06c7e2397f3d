import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// 標準コンボボックスとカスタムフォームを紐づけるID
export const COMBINE_ID = {
  MOD_EXPL: 'prdProductModExpl',
} as const;

// 収率修正ダイアログのアイテム定義
export const getPrdConfirmYieldFormItems: () => CustomFormType['formItems'] =
  () => ({
    // 収率 [%]
    yieldVal: {
      label: { text: t('Prd.Chr.txtFixYield') },
      formModelValue: '',
      formRole: 'textBox',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.nonNegativeRealNumericOnly(),
      ],
    },
    // 修正コメント入力
    modExpl: {
      formModelValue: '',
      label: { text: t('Prd.Chr.txtModifyCommentInput') },
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      rules: [rules.required('textBox'), rules.length(64)],
      formRole: 'textComboBox',
      selectOptions: [],
      cmbId: COMBINE_ID.MOD_EXPL,
    },
  });

// 収率修正ダイアログのモデル定義
export const prdConfirmYieldFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getPrdConfirmYieldFormItems());
