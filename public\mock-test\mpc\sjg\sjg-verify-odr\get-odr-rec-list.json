{"rDts": "2024/08/01 12:34:56", "rCode": 200, "rTitle": "APIレスポンスタイトル", "rMsg": "レスポンスメッセージ", "rData": {"odrRecList": [{"backgroundColor": "", "odrNo": "024310", "matNo": "1000001", "matNm": "テルミサルタン", "bomModListExistNm": "有", "bomModList": [{"cmtTimes": 1, "riMatNo": "5000014", "dspNmJp": "ウルソデオキシコール\n酸錠100mg", "unitNmJp": "mg", "bRecVal1": "157500", "aRecVal1": "157480", "bRecVal2": "0", "bRecVal3": "0", "bRecVal4": "0", "bRecVal5": "0", "aRecVal2": "0", "aRecVal3": "0", "aRecVal4": "0", "aRecVal5": "0", "cmtDts": "2023/10/03 10:25:33", "modUsr": "松下　太郎", "modExpl": "記録値に誤りがあった為"}], "prdModListExistNm": "有", "prdModList": [{"cmtTimes": 1, "riMatNo": "5000014", "dspNmJp": "ウルソデオキシコール\n酸錠100mg", "unitNmJp": "kg", "bRecVal1": "50kg", "aRecVal1": "48kg", "cmtDts": "2023/10/03 10:25:33", "modUsr": "松下　太郎", "modExpl": "出来高に誤りがあった為"}], "sopModListExistNm": "有", "sopModList": [{"cmtTimes": 1, "sopFlowNmJp": "造粒-本作業　1", "sopNodeTimes": 1, "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "部屋の温度を入力してください2", "cmtMain3": "部屋の温度を入力してください3", "instUnitTxt": "単位", "bRecVal1": "24", "aRecVal1": "26", "bRecVal2": "24", "aRecVal2": "26", "bRecVal3": "24", "aRecVal3": "26", "bRecVal4": "24", "aRecVal4": "26", "bRecVal5": "24", "aRecVal5": "26", "cmtDts": "2023/10/03 10:25:33", "modUsr": "松下　太郎", "modExpl": "温度計が汚れていたため清掃"}], "devLogListExistNm": "有", "devLogList": [{"devCorrLv": 0, "backgroundColor": null, "rxNmJp": "処方A", "sopFlowNmJp": "造粒-本作業　1", "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "部屋の温度を入力してください", "cmtMain3": "部屋の温度を入力してください", "instVal": "24", "unitNmJp": "℃", "recVal1": "26", "thValType": "絶対値", "thValLlmt": 23, "thValUlmt": 25, "edDts": "2023/10/03 10:25:33", "modUsr": "松下　次郎", "modExpl": " 温度が高いため再検査", "cmtDts": "2023/10/03 10:25:33"}], "recApprovDts": "2023/10/3 12:00:00", "recApprovUsr": "山田太郎", "recApprovExpl": "XXXXXX", "recPdfFile": "A00000000114301", "wgtPdfFile": ""}, {"backgroundColor": "yellow", "odrNo": "024311", "matNo": "1000002", "matNm": "テルミサルタン", "bomModListExistNm": "有", "bomModList": [{"cmtTimes": 1, "riMatNo": "5000014", "dspNmJp": "ウルソデオキシコール\n酸錠100mg", "unitNmJp": "mg", "bRecVal1": "157480", "aRecVal1": "157480", "bRecVal2": "0", "bRecVal3": "0", "bRecVal4": "0", "bRecVal5": "0", "aRecVal2": "0", "aRecVal3": "0", "aRecVal4": "0", "aRecVal5": "0", "cmtDts": "2023/10/03 10:25:33", "modUsr": "松下　太郎", "modExpl": "記録値に誤りがあった為"}], "prdModListExistNm": "有", "prdModList": [{"cmtTimes": 1, "riMatNo": "5000014", "dspNmJp": "ウルソデオキシコール\n酸錠100mg", "unitNmJp": "kg", "bRecVal1": "50kg", "aRecVal1": "48kg", "cmtDts": "2023/10/03 10:25:33", "modUsr": "松下　太郎", "modExpl": "出来高に誤りがあった為"}], "sopModListExistNm": "有", "sopModList": [{"cmtTimes": 1, "sopFlowNmJp": "造粒-本作業　1", "sopNodeTimes": 1, "cmtMain1": "", "cmtMain2": "", "cmtMain3": "部屋の温度を入力してください3", "instUnitTxt": "単位", "bRecVal1": "24", "aRecVal1": "26", "bRecVal2": "24", "aRecVal2": "26", "bRecVal3": "24", "aRecVal3": "26", "bRecVal4": "24", "aRecVal4": "26", "bRecVal5": "24", "aRecVal5": "26", "cmtDts": "2023/10/03 10:25:33", "modUsr": "松下　太郎", "modExpl": "温度計が汚れていたため清掃"}], "devLogListExistNm": "有", "devLogList": [{"devCorrLv": 1, "backgroundColor": "yellow", "rxNmJp": "処方A", "sopFlowNmJp": "造粒-本作業　1", "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "部屋の温度を入力してください", "cmtMain3": "部屋の温度を入力してください", "instVal": "24", "unitNmJp": "℃", "recVal1": "26", "thValType": "相対値", "thValLlmt": 23, "thValUlmt": 25, "edDts": "2023/10/03 10:25:33", "modUsr": "松下　次郎", "modExpl": " 温度が高いため再検査", "cmtDts": "2023/10/03 10:25:33"}], "recApprovDts": "2023/10/3 13:00:00", "recApprovUsr": "山田太郎1", "recApprovExpl": "XXXXXXXXXXXXXXXXXX", "recPdfFile": "A00000000114303", "wgtPdfFile": "A00000000114304"}, {"backgroundColor": "red", "odrNo": "024312", "matNo": "1000004", "matNm": "テルミサルタン", "bomModListExistNm": "有", "bomModList": [{"cmtTimes": 1, "riMatNo": "5000014", "dspNmJp": "ウルソデオキシコール\n酸錠100mg", "unitNmJp": "mg", "bRecVal1": "157480", "aRecVal1": "157480", "bRecVal2": "0", "bRecVal3": "0", "bRecVal4": "0", "bRecVal5": "0", "aRecVal2": "0", "aRecVal3": "0", "aRecVal4": "0", "aRecVal5": "0", "cmtDts": "2023/10/03 10:25:33", "modUsr": "松下　太郎", "modExpl": "記録値に誤りがあった為"}], "prdModListExistNm": "有", "prdModList": [{"cmtTimes": 1, "riMatNo": "5000014", "dspNmJp": "ウルソデオキシコール\n酸錠100mg", "unitNmJp": "kg", "bRecVal1": "50kg", "aRecVal1": "48kg", "cmtDts": "2023/10/03 10:25:33", "modUsr": "松下　太郎", "modExpl": "出来高に誤りがあった為"}], "sopModListExistNm": "有", "sopModList": [{"cmtTimes": 1, "sopFlowNmJp": "造粒-本作業　1", "sopNodeTimes": 1, "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "部屋の温度を入力してください2", "cmtMain3": "部屋の温度を入力してください3", "instUnitTxt": "単位", "bRecVal1": "24", "aRecVal1": "26", "bRecVal2": "24", "aRecVal2": "26", "bRecVal3": "24", "aRecVal3": "26", "bRecVal4": "24", "aRecVal4": "26", "bRecVal5": "24", "aRecVal5": "26", "cmtDts": "2023/10/03 10:25:33", "modUsr": "松下　太郎", "modExpl": "温度計が汚れていたため清掃"}], "devLogListExistNm": "有", "devLogList": [{"devCorrLv": 1, "backgroundColor": "yellow", "rxNmJp": "処方A", "sopFlowNmJp": "造粒-本作業　1", "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "部屋の温度を入力してください", "cmtMain3": "部屋の温度を入力してください", "instVal": "24", "unitNmJp": "℃", "recVal1": "26", "thValType": "相対値(%)", "thValLlmt": 23, "thValUlmt": 25, "edDts": "2023/10/03 10:25:33", "modUsr": "松下　次郎", "modExpl": " 温度が高いため再検査", "cmtDts": "2023/10/03 10:25:33"}, {"devCorrLv": 2, "backgroundColor": "orange", "rxNmJp": "処方A", "sopFlowNmJp": "造粒-本作業　1", "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "部屋の温度を入力してください", "cmtMain3": "部屋の温度を入力してください", "instVal": "24", "unitNmJp": "℃", "recVal1": "26", "thValType": "相対値", "thValLlmt": 23, "thValUlmt": 25, "edDts": "2023/10/03 10:25:33", "modUsr": "松下　次郎", "modExpl": " 温度が高いため再検査", "cmtDts": "2023/10/03 10:25:33"}, {"devCorrLv": 3, "backgroundColor": "red", "rxNmJp": "処方A", "sopFlowNmJp": "造粒-本作業　1", "cmtMain1": "部屋の温度を入力してください", "cmtMain2": "部屋の温度を入力してください", "cmtMain3": "部屋の温度を入力してください", "instVal": "24", "unitNmJp": "℃", "recVal1": "26", "thValType": "-", "thValLlmt": 23, "thValUlmt": 25, "edDts": "2023/10/03 10:25:33", "modUsr": "松下　次郎", "modExpl": " 温度が高いため再検査", "cmtDts": "2023/10/03 10:25:33"}], "recApprovDts": "2023/10/3 13:00:00", "recApprovUsr": "山田太郎1", "recApprovExpl": "XXXXXXXXXXXXXXXXXX", "recPdfFile": "A00000000114303", "wgtPdfFile": "A00000000114304"}]}}