<template>
  <!-- 出荷情報変更 -->
  <DialogWindow
    :title="$t('Sog.Chr.txtSogEditProductShipmentRecord')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkForm()"
    @visible="updateDialogChangeFlagRef"
  >
    <CustomForm
      class="Util_mt-16"
      :formModel="sogEditProductShipmentRecordFormRef.formModel"
      :formItems="sogEditProductShipmentRecordFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          sogEditProductShipmentRecordFormRef.customForm = v;
        }
      "
      @changeFormModel="
        (changeFlag, { changeLogModList }) => {
          updateDialogChangeFlagRef(changeFlag);
          updateCurrentChangedFormModelMap(changeLogModList);
        }
      "
    />
  </DialogWindow>
  <!-- 出荷情報変更の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.sogEditProductShipmentRecordConfirm"
    :dialogProps="messageBoxSogEditProductShipmentRecordConfirmProps"
    :cancelCallback="() => closeDialog('sogEditProductShipmentRecordConfirm')"
    :submitCallback="
      () => apiHandler(messageBoxSogEditProductShipmentRecordConfirmProps)
    "
  />
  <MessageBox
    v-if="dialogVisibleRef.errorConfirm"
    :dialogProps="messageBoxErrorPropsRef"
    :submitCallback="() => closeDialog('errorConfirm')"
  />
  <MessageBox
    v-if="dialogVisibleRef.sogEditProductShipmentRecordInfo"
    :dialogProps="messageBoxSogEditProductShipmentRecordPropsRef"
    :cancelCallback="closeAllDialog"
    :submitCallback="closeAllDialog"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import onValidateHandler, {
  handleValidationBySingleRowSelect,
} from '@/utils/validateHandler';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  GetSogRsltFixListData,
  GetSogInstructionShipmentModifyReq,
  ModSogInstructionShipmentModifyReq,
  GetSogInstructionShipmentModifyResData,
} from '@/types/HookUseApi/SogTypes';
import {
  CommonRequestType,
  ExtendCommonRequestType,
  LogModListType,
} from '@/types/HookUseApi/CommonTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import {
  useGetComboBoxDataStandard,
  useGetSogInstructionShipmentModify,
  useModSogInstructionShipmentModify,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  getSogEditProductShipmentRecordFormItems,
  sogEditProductShipmentRecordFormModel,
} from './sogEditProductShipmentRecord';

const sogEditProductShipmentRecordFormRef = ref<CustomFormType>({
  formItems: getSogEditProductShipmentRecordFormItems(),
  formModel: sogEditProductShipmentRecordFormModel,
});

let logModList: LogModListType = [];
const updateCurrentChangedFormModelMap = (changeLogModList: LogModListType) => {
  logModList = changeLogModList;
};

type Props = {
  selectedRows: GetSogRsltFixListData[];
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
};

const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

let sogEditProductShipmentRecordFormData: GetSogInstructionShipmentModifyResData =
  {
    sogInstNo: '',
    matNo: '',
    matNm: '',
    lotNo: '',
    spgPlanYmd: '',
    bpTrfId: '',
    shipModExpl: '',
    sogInstUpdDts: '',
  };

type DialogRefKey =
  | 'errorConfirm'
  | 'fragmentDialogVisible'
  | 'sogEditProductShipmentRecordConfirm'
  | 'sogEditProductShipmentRecordInfo';

const initialState: InitialDialogState<DialogRefKey> = {
  errorConfirm: false,
  fragmentDialogVisible: false,
  sogEditProductShipmentRecordConfirm: false,
  sogEditProductShipmentRecordInfo: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxErrorPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const messageBoxSogEditProductShipmentRecordPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxSogEditProductShipmentRecordConfirmProps: DialogProps = {
  title: t('Sog.Chr.txtSogEditProductShipmentRecordConfirm'),
  content: t('Sog.Msg.sogEditProductShipmentRecordConfirm'),
  type: 'question',
};

const checkForm = async () => {
  const validate =
    sogEditProductShipmentRecordFormRef.value.customForm !== undefined &&
    (await sogEditProductShipmentRecordFormRef.value.customForm.validate(
      (isValid) => {
        onValidateHandler(isValid);
      },
    ));
  if (validate) {
    openDialog('sogEditProductShipmentRecordConfirm');
  }
  return false;
};

/**
 * 確認メッセージ
 */
const apiHandler = async (messageBoxProps: DialogProps) => {
  closeDialog('sogEditProductShipmentRecordConfirm');
  showLoading();
  const apiRequestData: ExtendCommonRequestType<ModSogInstructionShipmentModifyReq> =
    {
      ...props.privilegesBtnRequestData,
      msgboxTitleTxt: messageBoxProps.title,
      msgboxMsgTxt: messageBoxProps.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      sogInstNo:
        sogEditProductShipmentRecordFormRef.value.formModel.sogInstNo.toString(),
      sogPlanYmd:
        sogEditProductShipmentRecordFormRef.value.formModel.sogPlanYmd.toString(),
      bpTrfId:
        sogEditProductShipmentRecordFormRef.value.formModel.bpTrfId.toString(),
      shipModExpl:
        sogEditProductShipmentRecordFormRef.value.formModel.shipModExpl.toString(),
      sogInstUpdDts: sogEditProductShipmentRecordFormData.sogInstUpdDts,
      logModList,
    };

  const { responseRef, errorRef } =
    await useModSogInstructionShipmentModify(apiRequestData);
  if (errorRef.value) {
    messageBoxErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('errorConfirm');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    messageBoxSogEditProductShipmentRecordPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxSogEditProductShipmentRecordPropsRef.value.content =
      responseRef.value.data.rMsg;
    openDialog('sogEditProductShipmentRecordInfo');
  }
  closeLoading();
  return true;
};

const closeAllDialog = () => {
  closeDialog('sogEditProductShipmentRecordInfo');
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData);
};

/**
 * 初期設定
 */
const sogEditProductShipmentRecordInit = async () => {
  try {
    await handleValidationBySingleRowSelect(props.selectedRows);
  } catch (e) {
    console.log(e);
    return;
  }
  const selectedRow = props.selectedRows.at(0)!;
  updateDialogChangeFlagRef(false);
  logModList = [];
  showLoading();
  sogEditProductShipmentRecordFormRef.value.formItems =
    getSogEditProductShipmentRecordFormItems();

  const apiRequestData: ExtendCommonRequestType<GetSogInstructionShipmentModifyReq> =
    {
      ...props.privilegesBtnRequestData,
      sogInstNo: selectedRow.sogInstNo,
      sogInstUpdDts: selectedRow.sogInstUpdDts,
    };
  // 出荷情報変更データ取得
  const { responseRef, errorRef } = await useGetSogInstructionShipmentModify({
    ...props.privilegesBtnRequestData,
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('errorConfirm');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    sogEditProductShipmentRecordFormData = responseRef.value.data.rData;
    setFormModelValueFromApiResponse(
      sogEditProductShipmentRecordFormRef,
      sogEditProductShipmentRecordFormData,
    );
  }
  // 標準コンボボックスデータ取得
  const comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtSogInstShipMod',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'SOG_INST_SHIP_MOD' },
      },
      {
        cmbId: 'bpTrfId',
        condKey: 'm_bp_trf',
      },
    ],
  });
  if (
    comboBoxDataStandardReturnData &&
    comboBoxDataStandardReturnData.rData.rList.length > 0
  ) {
    setCustomFormComboBoxOptionList(
      sogEditProductShipmentRecordFormRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, sogEditProductShipmentRecordInit);
</script>
