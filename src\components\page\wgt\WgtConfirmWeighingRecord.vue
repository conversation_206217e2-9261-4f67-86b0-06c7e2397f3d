<template>
  <!-- 秤量記録確認ページ -->
  <el-card class="weighing-record_search-panel">
    <!-- 見出し：秤量指示書スキャン -->
    <BaseHeading
      level="2"
      fontSize="24px"
      :text="$t('Wgt.Chr.txtWeightInstructionQRScan')"
    />
    <div class="weighing-record_search-condition">
      <CustomForm
        class="Util_mt-16"
        :triggerRendering="customFormRenderingTriggerRef"
        :formModel="getSearchFormRef.formModel"
        :formItems="getSearchFormRef.formItems"
        width="100%"
        @visible="
          (v: CustomFormType['customForm']) => {
            getSearchFormRef.customForm = v;
          }
        "
        @keyup.enter="keyupEnterHandler"
      />
    </div>
  </el-card>
  <el-card
    v-if="tablePropsDataRef.tableData.length !== 0"
    class="weighing-record_main-panel"
  >
    <el-row class="weighing-record_el-row">
      <!-- 秤量記録一覧の見出し+テキスト項目表示 -->
      <div class="weighing-record_animated-col">
        <InfoShow
          :infoShowItems="processDataInfoShowRef.infoShowItems"
          :isLabelVertical="processDataInfoShowRef.isLabelVertical"
        />
        <BaseHeading
          class="Util_mt-8"
          level="2"
          :text="pageTitle"
          fontSize="24px"
        />
        <!-- 共通のテーブル -->
        <TabulatorTable
          :propsData="tablePropsDataRef"
          :routerName="props.routerInfo.name"
          @selectRow="updateSelectedRow"
        />
        <!-- 秤量記録詳細ダイアログ -->
        <WgtWeighingRecordDetail
          v-if="props.privilegesBtnRequestData[BUTTON_ID.DETAIL]"
          :isClicked="isClickedWgtWeighingRecordDetailDialogRef"
          :privilegesBtnRequestData="
            props.privilegesBtnRequestData[BUTTON_ID.DETAIL]
          "
          :selectedRow="selectedRow"
        />
        <!-- 秤量記録修正ダイアログ -->
        <WgtEditWeighingRecord
          v-if="props.privilegesBtnRequestData[BUTTON_ID.EDIT]"
          :isClicked="isClickedWgtEditWeighingRecordDialogRef"
          :wgtInstNo="getWgtInstNo()"
          :wgtCnt="getWgtCnt()"
          :seqNo="getSeqNo()"
          :privilegesBtnRequestData="
            props.privilegesBtnRequestData[BUTTON_ID.EDIT]
          "
          @submit="getWeighingRecordConfirmInfo"
        />
      </div>
    </el-row>
  </el-card>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 秤量記録確認の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxWeighingRecordConfirmVisible"
    :dialogProps="messageBoxWeighingRecordConfirmPropsRef"
    :cancelCallback="
      () => closeDialog('messageBoxWeighingRecordConfirmVisible')
    "
    :submitCallback="requestApiModifyWeighingRecordConfirm"
  />
  <!-- 秤量記録確認の完了表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxWeighingRecordConfirmFinishedVisible"
    :dialogProps="messageBoxWeighingRecordConfirmFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxWeighingRecordConfirmFinished"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { onMounted, ref, nextTick } from 'vue';
import {
  GetWeighingRecordConfirmResListData,
  GetWeighingRecordConfirmResList,
  ModifyWeighingRecordConfirmRequestData,
} from '@/types/HookUseApi/WgtTypes';
import {
  useGetWeighingRecordConfirm,
  useGetUsrGrid,
  useModifyWeighingRecordConfirm,
} from '@/hooks/useApi';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogProps } from '@/types/MessageBoxTypes';
// 秤量記録詳細
import WgtWeighingRecordDetail from '@/components/fragment/wgt/wgtConfirmWeighingRecord/WgtWeighingRecordDetail.vue';
// 秤量記録修正
import WgtEditWeighingRecord from '@/components/include/wgt/WgtEditWeighingRecord.vue';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import InfoShow from '@/components/parts/InfoShow.vue';
import { InfoShowType } from '@/types/InfoShowTypes';
import { RouterInfoType } from '@/types/RouterTypes';
import { initCommonRequestFromPrivilegesType } from '@/hooks/useApi/util';
import { closeLoading, showLoading } from '@/utils/dialog';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import {
  BUTTON_ID,
  wgtInstGrpNoRules,
  getSearchFormItems,
  getSearchFormModel,
  getProcessDataInfoShowItems,
} from './wgtConfirmWeighingRecord';

const getSearchFormRef = ref<CustomFormType>({
  formItems: getSearchFormItems(),
  formModel: getSearchFormModel,
});

/**
 * 多言語
 */
const { t } = useI18n();
const pageTitle = t('Wgt.Chr.txtWeightRecordConfirmList');

const customFormRenderingTriggerRef = ref(false);
// ダイアログの表示切替用定義
type DialogRefKey =
  | 'messageBoxCheckBeforeScheduleApprovalWarningVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxWeighingRecordConfirmVisible'
  | 'messageBoxWeighingRecordConfirmFinishedVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  messageBoxCheckBeforeScheduleApprovalWarningVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxWeighingRecordConfirmVisible: false,
  messageBoxWeighingRecordConfirmFinishedVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// 秤量記録詳細ダイアログ開始' クリック
const isClickedWgtWeighingRecordDetailDialogRef = ref<boolean>(false);
// 秤量記録修正ダイアログ開始' クリック
const isClickedWgtEditWeighingRecordDialogRef = ref<boolean>(false);

// 選択行情報の格納
let selectedRow: GetWeighingRecordConfirmResListData | null = null;

let weighingRecordConfirmData: GetWeighingRecordConfirmResList = {
  wgtRecordList: [],
  instGrpUpdDts: '',
  logUpdDts: '',
  wgtInstGrpStsDsp: '',
  instGrpProcessedFlg: '',
  recordEditDisableFlg: '',
};

// 秤量指示明細番号の設定
const getWgtInstNo = () => {
  // 行選択されていない場合はundefined
  if (selectedRow === null) {
    return undefined;
  }

  // 選択行情報の秤量指示明細番号を返す
  return selectedRow.wgtInstNo;
};

// 秤量実施回数の設定
const getWgtCnt = () => {
  // 行選択されていない場合はnull
  if (selectedRow === null) {
    return null;
  }

  // 選択行情報の秤量実施回数を返す
  return selectedRow.wgtCnt;
};

// シーケンシャル番号の設定
const getSeqNo = () => {
  // 行選択されていない場合はnull
  if (selectedRow === null) {
    return null;
  }

  // 選択行情報のシーケンシャル番号を返す
  return selectedRow.seqNo;
};

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 秤量記録確認の確認メッセージボックス
const messageBoxWeighingRecordConfirmPropsRef = ref<DialogProps>({
  title: t('Wgt.Msg.titleWeightRecordConfirm'),
  content: t('Wgt.Msg.contentWeightRecordConfirm'),
  type: 'question',
});

// 秤量記録確認の完了メッセージボックス
const messageBoxWeighingRecordConfirmFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

const props = defineProps<RouterInfoType>();
const commonActionRequestData = initCommonRequestFromPrivilegesType({
  menu2Scn: props.routerInfo.name.slice(0, -1),
  menu3Act: props.routerInfo.name.at(-1),
  signType: '',
});

const processDataInfoShowRef = ref<InfoShowType>({
  infoShowItems: getProcessDataInfoShowItems(),
  isLabelVertical: false,
});

// 検索用リクエストデータの格納
let wgtInstGrpNoDsp = '';

// 選択行情報の更新
const updateSelectedRow = (v: GetWeighingRecordConfirmResListData | null) => {
  // 選択行情報を保存
  selectedRow = v;
};

// 秤量指示明細用テーブル設定
const tablePropsDataRef = ref<TabulatorTableIF>({
  pageName: 'Wgtrecconf',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'uniqueKey', // 主キー。ユニークになるものを設定。
  showRadio: true,
  customActionBtns: [
    {
      type: 'primary',
      tabulatorActionId: BUTTON_ID.CHECK,
      text: 'Wgt.Chr.btnCheck', // 記録確認
      disabled: true,
      clickHandler() {
        // NOTE:未選択なら処理させない。エラーメッセージはTabulatorTable側で出してくれる。
        // 秤量記録確認の確認メッセージ表示
        openDialog('messageBoxWeighingRecordConfirmVisible');
      },
    },
  ],
  onSelectBtns: [
    {
      type: 'secondary',
      tabulatorActionId: BUTTON_ID.DETAIL,
      text: 'Wgt.Chr.btnWeightRecordDetail', // 秤量記録詳細
      clickHandler: () => {
        // 秤量記録詳細ダイアログを表示開始
        // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
        isClickedWgtWeighingRecordDetailDialogRef.value =
          !isClickedWgtWeighingRecordDetailDialogRef.value;
      },
    },
    {
      type: 'secondary',
      tabulatorActionId: BUTTON_ID.EDIT,
      text: 'Wgt.Chr.btnWeightRecordEdit', // 秤量記録修正
      clickHandler: () => {
        // NOTE:本来は選択行情報は子でnullチェックするが、このダイアログは例外的に選択行を受け取らないため自前でチェック
        // NOTE:未選択なら処理させない。エラーメッセージはTabulatorTable側で出してくれる。
        if (selectedRow === null) {
          return;
        }

        // 秤量記録修正ダイアログを表示開始
        // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
        isClickedWgtEditWeighingRecordDialogRef.value =
          !isClickedWgtEditWeighingRecordDialogRef.value;
      },
    },
  ],

  column: [
    // 秤量実施回数 隠しカラム
    { title: '', field: 'wgtCnt', hidden: true },
    // シーケンシャル番号 隠しカラム
    { title: '', field: 'seqNo', hidden: true },
    // 秤量品目コード
    {
      title: 'Wgt.Chr.txtWeightMaterialCode',
      field: 'bomMatNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    // 秤量品名
    {
      title: 'Wgt.Chr.txtWeightMaterialName',
      field: 'wgtDspNmJp',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    // 管理番号
    {
      title: 'Wgt.Chr.txtManageNo',
      field: 'lotNo',
      width: COLUMN_WIDTHS.LOT_NO,
    },
    // バッチ番号
    {
      title: 'Wgt.Chr.txtBatchNo',
      field: 'batchNo',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.BAT_NO,
    },
    // 小分け番号
    {
      title: 'Wgt.Chr.txtWeightHandNo',
      field: 'wgtHandNo',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT.WGT_HAND_NO,
    },
    // 指図指示量
    {
      title: 'Wgt.Chr.txtOrderInstructionValue',
      field: 'bomPlanQty',
      sorter: 'number',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.INST_QTY,
    },
    // 秤量指示量
    {
      title: 'Wgt.Chr.txtWeightInstructionValue',
      field: 'scnInstHandVal',
      sorter: 'number',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.INST_QTY,
    },
    // バッチ累積秤取量
    {
      title: 'Wgt.Chr.txtBatchWeightValue',
      field: 'scnBatchTotalVal',
      sorter: 'number',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 秤取量
    {
      title: 'Wgt.Chr.txtWeightValue',
      field: 'scnWgtVal',
      sorter: 'number',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 秤量単位
    {
      title: 'Wgt.Chr.txtWeightUnit',
      field: 'unitNmJp',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    // 風袋重量
    {
      title: 'Wgt.Chr.txtWeightTareValue',
      field: 'conWgt',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.WGT_QTY,
    },
    // 風袋重量単位
    {
      title: 'Wgt.Chr.txtWeightTareValueUnit',
      field: 'conUnitNmJp',
      width: COLUMN_WIDTHS.WGT.CON_UNIT_NM,
    },
    // 秤量日時
    {
      title: 'Wgt.Chr.txtWeightDate',
      field: 'wgtDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
    // 製造品目コード
    {
      title: 'Wgt.Chr.txtMaterialCode',
      field: 'matNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    // 製造品名
    {
      title: 'Wgt.Chr.txtMaterialName',
      field: 'dspNmJp',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    // 秤量指示(明細)番号
    {
      title: 'Wgt.Chr.txtWeightInstructionDetailNo',
      field: 'wgtInstNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    // 計量器
    {
      title: 'Wgt.Chr.txtWeightDevice',
      field: 'deviceNmJp',
      width: COLUMN_WIDTHS.WGT.DEVICE_NM,
    },
    // 記録者
    {
      title: 'Wgt.Chr.txtRecordUser',
      field: 'recUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 再秤量フラグ(表示用)
    {
      title: 'Wgt.Chr.txtReWeightFlag',
      field: 'reWgtFlgDsp',
      width: COLUMN_WIDTHS.WGT.RE_WGT_FLG,
    },
    // 修正コメント
    {
      title: 'Wgt.Chr.txtModifyComment',
      field: 'modExplDsp',
      width: COLUMN_WIDTHS.WGT.DATA_EXIST,
    },
    // レスポンスにユニークなデータが存在しないため、自前で隠しカラムでユニーク情報生成
    { title: '', field: 'uniqueKey', hidden: true },
  ],
  tableData: [],
  usrGridInfo: {
    colSort: [],
    colHide: [],
  },
  title: pageTitle, // csv出力名
  showConditionSearch: true, // カラム編集、CSV出力表示に必要
  noUseConditionSearch: false, // ConditionSearch関連を出す
  hideSearchHeader: true, // 絞り込み不要
});

/**
 * 秤量記録一覧情報取得
 */

const getWeighingRecordConfirmInfo = async (requestData: CommonRequestType) => {
  // NOTE:再検索用初期化処理 エラー時に前のテーブルデータが残ってしまう問題があるため。
  tablePropsDataRef.value.tableData = [];

  showLoading();
  const { responseRef, errorRef } = await useGetWeighingRecordConfirm({
    wgtInstGrpNo: wgtInstGrpNoDsp,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();

    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return false;
  }
  if (responseRef.value) {
    // テーブル表示データ
    tablePropsDataRef.value.tableData =
      responseRef.value.data.rData.wgtRecordList;
    // レスポンスに存在しないユニークキーを独自に設定して隠しカラムに仕込む対応
    tablePropsDataRef.value.tableData.forEach((value) => {
      const tableData = value;
      // 秤量指示明細番号 + 秤量実施回数 + シーケンシャル番号
      tableData.uniqueKey = `${value.wgtInstNo}-${value.wgtCnt}-${value.seqNo}`;
    });
    // テーブルのボタン制御
    if (tablePropsDataRef.value.customActionBtns) {
      // 記録確認ボタン
      const btnCheck = tablePropsDataRef.value.customActionBtns.find(
        (btn) => btn.tabulatorActionId === BUTTON_ID.CHECK,
      );
      if (btnCheck) {
        btnCheck.disabled = !(
          responseRef.value.data.rData.instGrpProcessedFlg === '1'
        );
      }
    }
    if (tablePropsDataRef.value.onSelectBtns) {
      // 秤量記録修正
      const btnEdit = tablePropsDataRef.value.onSelectBtns.find(
        (btn) => btn.tabulatorActionId === BUTTON_ID.EDIT,
      );
      if (btnEdit) {
        btnEdit.disabled =
          responseRef.value.data.rData.recordEditDisableFlg === '1';
      }
    }

    weighingRecordConfirmData = responseRef.value.data.rData;
    Object.keys(weighingRecordConfirmData).forEach((key) => {
      if (
        processDataInfoShowRef.value.infoShowItems &&
        key in processDataInfoShowRef.value.infoShowItems
      ) {
        processDataInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          weighingRecordConfirmData[
            key as keyof GetWeighingRecordConfirmResList
          ]?.toString() ?? '';
      }
    });
  }
  closeLoading();
  return true;
};

// 秤量記録確認のAPIリクエスト処理
const requestApiModifyWeighingRecordConfirm = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxWeighingRecordConfirmVisible');
  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData[BUTTON_ID.CHECK], // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    // 署名ダイアログがキャンセルされた場合の処理
    console.error('署名ダイアログがキャンセルされました', error);
    return;
  }

  showLoading();
  const requestData: ModifyWeighingRecordConfirmRequestData = {
    wgtInstGrpNo:
      getSearchFormRef.value.formItems.wgtInstGrpNoDsp.formModelValue.toString(),
    instGrpUpdDts: weighingRecordConfirmData.instGrpUpdDts,
    logUpdDts: weighingRecordConfirmData.logUpdDts,
  };
  const { responseRef, errorRef } = await useModifyWeighingRecordConfirm({
    ...commonActionRequestData,
    btnId: BUTTON_ID.CHECK,
    msgboxTitleTxt: messageBoxWeighingRecordConfirmPropsRef.value.title,
    msgboxMsgTxt: messageBoxWeighingRecordConfirmPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // ・データベースを更新した後、以下のメッセージを表示する。
    // 秤量記録確認完了
    messageBoxWeighingRecordConfirmFinishedPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxWeighingRecordConfirmFinishedPropsRef.value.content =
      responseRef.value.data.rMsg;

    openDialog('messageBoxWeighingRecordConfirmFinishedVisible');
  }
  closeLoading();
};

// 検索用フォームのフォーカス状態の設定
// isFocus: trueでフォーカス、falseでフォーカスを外す
const setFocusStateSearchForm = (isFocus: boolean) => {
  if (getSearchFormRef.value.customForm === undefined) {
    return;
  }

  if (isFocus) {
    getSearchFormRef.value.customForm.$el.querySelector('input').focus();
  } else {
    getSearchFormRef.value.customForm.$el.querySelector('input').blur();
  }
};

/**
 * 全ての秤量記録一覧を取得
 */
const keyupEnterHandler = async () => {
  // フォームが存在しない場合は処理を終了
  if (getSearchFormRef.value.customForm === undefined) {
    return;
  }
  // フォームのバリデーション
  await getSearchFormRef.value.customForm.validate(async (isValid) => {
    if (!isValid) {
      return;
    }
    // 検索データを格納(再検索処理用)
    wgtInstGrpNoDsp =
      getSearchFormRef.value.formItems.wgtInstGrpNo.formModelValue.toString();
    // 秤量記録一覧情報取得
    const infoRes = await getWeighingRecordConfirmInfo({
      ...commonActionRequestData,
      btnId: BUTTON_ID.SCAN,
    });
    // 成功した場合の処理
    if (infoRes) {
      // 追加処理: 秤量指示書番号とフォームの更新
      getSearchFormRef.value.formItems.wgtInstGrpNoDsp.formModelValue =
        getSearchFormRef.value.formItems.wgtInstGrpNo.formModelValue;
      getSearchFormRef.value.formItems.wgtInstGrpNo.formModelValue = '';
      getSearchFormRef.value.formItems.wgtInstGrpNo.tags = [
        { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
      ];
      // 検索後も秤量指示書番号の入力条件を維持
      getSearchFormRef.value.formItems.wgtInstGrpNo.rules = wgtInstGrpNoRules;
      customFormRenderingTriggerRef.value =
        !customFormRenderingTriggerRef.value;

      // 入力欄のフォーカスを外す
      setFocusStateSearchForm(false);
    }
  });
};

/**
 * ページを初期状態に戻す
 */
const initial = () => {
  // NOTE: 記録確認実行後の初期表示処理
  // テーブルデータを空にし、パネル部分を非表示にする。
  tablePropsDataRef.value.tableData = [];
  // 秤量指示書番号の入力欄、検索後のラベルをクリアする。
  getSearchFormRef.value.formItems.wgtInstGrpNo.formModelValue = '';
  getSearchFormRef.value.formItems.wgtInstGrpNoDsp.formModelValue = '';
  // QRコード読み取りのため、秤量指示書番号の入力欄をアクティブの状態にする。
  setFocusStateSearchForm(true);
};

/**
 * 秤量記録確認画面の初期設定
 */
const wgtConfirmWeighingRecordInit = async () => {
  // ページ初期化
  initial();
  showLoading();

  const usrGridResData = await useGetUsrGrid(props.pageCommonRequest);
  if (usrGridResData) {
    tablePropsDataRef.value.usrGridInfo!.colSort = usrGridResData.colSort;
    tablePropsDataRef.value.usrGridInfo!.colHide = usrGridResData.colHide;
  }

  closeLoading();
};

// 秤量記録確認に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxWeighingRecordConfirmFinished = () => {
  // 自身を閉じる
  closeDialog('messageBoxWeighingRecordConfirmFinishedVisible');
  // ページを再読み込み
  initial();
};

onMounted(async () => {
  // NOTE:nextTickは無くても動作するが他ソースで同様の実装があり、統一のため記載
  await nextTick();
  wgtConfirmWeighingRecordInit();
});
</script>
<style lang="scss" scoped>
$namespace: 'weighing-record';

.#{$namespace} {
  &_search-panel {
    background-color: $white750;
    flex-shrink: 0;
  }

  &_main-panel {
    background-color: $white750;
    display: flex;
    flex-direction: column;
    flex-grow: 1;

    /* element plus */
    :deep(.el-card__body) {
      padding-top: 32px;
      padding-bottom: 0;
      padding-inline: 16px;
      display: flex;
      flex-direction: column;
      flex-grow: 1;
    }
  }
  &_search-condition {
    display: flex;
    flex-wrap: nowrap;
    position: relative;
    box-sizing: border-box;
    :deep(.custom-form_suffix) {
      font-size: 14px;
      margin-left: 16px;
      position: unset;
    }
  }
  &_search-text {
    margin-top: 32px;
    margin-left: 16px;
    height: 32px;
    line-height: 32px;
  }
  &_el-row {
    flex-grow: 1;
  }

  &_suffix {
    font-size: 10px;
    padding-left: 5px;
  }
  &_animated-col {
    display: flex;
    flex-direction: column;
    width: 100%;
    .bottom-action-btn {
      &_wrap {
        display: flex;
        flex-wrap: wrap-reverse;
        justify-content: end;
        gap: 16px 40px;
        margin-top: auto;
        padding-bottom: 16px;
        flex-shrink: 0;
      }
      &_list {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        min-width: 0;
      }
    }
  }
}
</style>
