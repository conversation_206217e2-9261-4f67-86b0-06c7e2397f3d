import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

export const getSogEditShipmentRecordFormItems: () => CustomFormType['formItems'] =
  () => ({
    sogSlipGrpNo: {
      label: { text: t('Sog.Chr.txtSogSlipGrpNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    sogPlanYmd: {
      label: { text: t('Sog.Chr.txtSogPlanYmd') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    bpTrfNm: {
      label: { text: t('Sog.Chr.txtBpTrfNm') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    sogInstNo: {
      label: { text: t('Sog.Chr.txtSogInstNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    matNo: {
      label: { text: t('Sog.Chr.txtMatNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    matNm: {
      label: { text: t('Sog.Chr.txtMatNm') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    lotNo: {
      label: { text: t('Sog.Chr.txtLotNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    instQty: {
      label: { text: t('Sog.Chr.txtInstQty') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
    },
    rsltQty: {
      label: { text: t('Sog.Chr.txtTrfRsltQty') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    rsltModQty: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Sog.Chr.txtRsltModQty') },
      rules: [
        rules.required('textBox'),
        rules.placesOfNumeric({ int: 11, decimal: 12 }),
        rules.positiveRealNumericOnly(),
      ],
      formModelValue: '',
      formRole: 'textBox',
      span: 12,
    },
    sogRsltModExpl: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Sog.Chr.txtComment') },
      formModelValue: '',
      rules: [
        rules.required('textComboBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
      ],
      formRole: 'textComboBox',
      props: { clearable: true },
      selectOptions: [],
      cmbId: 'cmtSogRsltMod',
    },
  });

export const sogEditShipmentRecordFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getSogEditShipmentRecordFormItems());
