import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// MBR申請情報の縦並び項目定義
export const getMBRApplicationInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // MBR番号
    mbrNo: {
      label: { text: t('Mst.Chr.txtMBRNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
    // 品目コード
    matNo: {
      label: { text: t('Mst.Chr.txtMatNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 品名
    matNm: {
      label: { text: t('Mst.Chr.txtMatNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 16,
    },
    // 処方コード
    rxNo: {
      label: { text: t('Mst.Chr.txtPrescriptionCode') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 処方名
    rxNm: {
      label: { text: t('Mst.Chr.txtPrescriptionName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 16,
    },
    // MBR申請者
    reqUsrNm: {
      label: { text: t('Mst.Chr.txtMBRApplicant') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // MBR申請日時
    reqDts: {
      label: { text: t('Mst.Chr.txtMBRApplicationDate') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 16,
    },
    // 指図設定範囲 開始日
    validStYmd: {
      label: { text: t('Mst.Chr.txtOrderSettingRangeStartDate') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 指図設定範囲 終了日
    validEdYmd: {
      label: { text: t('Mst.Chr.txtOrderSettingRangeEndDate') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 16,
    },
    // 申請コメント
    applicationComment: {
      label: { text: t('Mst.Chr.txtApplicationComment') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
  });

// MBR申請確認ダイアログのアイテム定義
export const getMBRApplicationConfirmationFormItems: () => CustomFormType['formItems'] =
  () => ({
    approvalDenialComment: {
      formModelValue: '',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Mst.Chr.txtApprovalDenialComment') }, // 承認/否認コメント
      rules: [
        rules.required('textBox'),
        rules.length(128, t('Cm.Chr.txtLength', [128])),
      ],
      formRole: 'textBox',
      props: {},
      span: 24,
    },
  });

// MBR申請確認ダイアログのモデル定義
export const mbrApplicationConfirmationFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getMBRApplicationConfirmationFormItems());
