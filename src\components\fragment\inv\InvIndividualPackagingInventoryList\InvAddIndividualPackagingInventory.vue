<template>
  <!-- 個装在庫追加ダイアログ -->
  <DialogWindow
    :title="$t('Inv.Chr.txtAddIndividualInventory')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkAddForm()"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="individualInventoryAddFormRef.formModel"
      :formItems="individualInventoryAddFormRef.formItems"
      @selectedItem="updateFormItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          individualInventoryAddFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <!-- 個装在庫追加の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.addIndividualInventoryConfirm"
    :dialogProps="messageBoxAddIndividualInventoryConfirmProps"
    :cancelCallback="() => closeDialog('addIndividualInventoryConfirm')"
    :submitCallback="aogVarFlgCheck"
  />
  <!-- 個装在庫追加のチェックメッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.addIndividualInventoryInfo"
    :dialogProps="messageBoxAddIndividualInventoryPropsRef"
    :submitCallback="closeAllDialog"
  />
  <!-- 個装在庫追加成功メッセージ（警告有） -->
  <MessageBox
    v-if="dialogVisibleRef.addIndividualInventoryWarning"
    :dialogProps="messageBoxAddIndividualInventoryWarningPropsRef"
    :submitCallback="closeAllDialog"
  />
  <!-- 個装在庫を追加のAPIを再度呼び出す -->
  <MessageBox
    v-if="dialogVisibleRef.addInventoryMultiLabelWarning"
    :dialogProps="messageBoxAddInventoryMultiLabelPropsRef"
    :cancelCallback="() => closeDialog('addInventoryMultiLabelWarning')"
    :submitCallback="closeWarningDialog"
  />
  <!-- 入り数割れ数量チェック -->
  <MessageBox
    v-if="dialogVisibleRef.aogVarFlgCheckConfirm"
    :dialogProps="messageBoxAogVarFlgCheckProps"
    :cancelCallback="() => closeDialog('aogVarFlgCheckConfirm')"
    :submitCallback="closeCheckDialog"
  />
  <!-- 管理番号・製造番号検索ダイアログ -->
  <InvSelectLotIndividualPackaging
    :isClicked="isClickedShowSelectLotDialogRef"
    :dspNarrowType="props.dspNarrowType"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    @submit="updateFormItemsValue"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import CONST from '@/constants/utils';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import {
  ComboBoxDataOptionData,
  ComboBoxDataStandardReturnData,
  CommonRequestType,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import {
  AddInventoryMultiLabelReq,
  GetInventoryLotInvList,
} from '@/types/HookUseApi/InvTypes';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  setCustomFormComboBoxOptionList,
  setCustomFormFilterComboBoxOptionList,
} from '@/utils/comboBoxOptionList';
import {
  getDateByType,
  toBigOrNull,
  toFixedDecimalString,
} from '@/utils/index';
import {
  useGetComboBoxDataStandard,
  useAddInventoryMultiLabel,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import Big from 'big.js';
import createMessageBoxForm from '@/utils/commentMessageBox';
import InvSelectLotIndividualPackaging from '@/components/fragment/inv/InvIndividualPackagingInventoryList/InvSelectLotIndividualPackaging.vue';
import {
  getIndividualInventoryAddFormItems,
  individualInventoryAddFormModel,
} from './invAddIndividualPackagingInventory';

const individualInventoryAddFormRef = ref<CustomFormType>({
  formItems: getIndividualInventoryAddFormItems(),
  formModel: individualInventoryAddFormModel,
});

type Props = {
  isClicked: boolean;
  dspNarrowType: string;
  privilegesBtnRequestData: CommonRequestType;
};
const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

/**
 * 警告要否フラグ
 * - UNNECESSARY: 警告が不要であることを表します（値: 0）。
 * - NECESSITY: 警告が必要であることを表します（値: 1）：デフォルト値。
 */
const WARNING_NECESSITY_FLAG = {
  UNNECESSARY: 0,
  NECESSITY: 1,
} as const;

const constantData = {
  zoneNo: 'zoneNo',
  locNo: 'locNo',
  totalInvQty: 'totalInvQty',
  invQty: 'invQty',
};

let selectedRowData: GetInventoryLotInvList = {
  matNo: '',
  matNm: '',
  lotNo: '',
  invQty: '',
  unitNm: '',
  lotSid: '',
  lotUpdDts: '',
  aogVarFlg: '',
  aogBaleUnit: null,
};

const customFormRenderingTriggerRef = ref(false);
// 管理番号/製造番号検索ダイアログ開始' クリック
const isClickedShowSelectLotDialogRef = ref<boolean>(false);
let comboBoxOptionList: ComboBoxDataOptionData[] = [];
type DialogRefKey =
  | 'singleButton'
  | 'addIndividualInventoryConfirm'
  | 'addIndividualInventoryInfo'
  | 'fragmentDialogVisible'
  | 'addInventoryMultiLabelWarning'
  | 'aogVarFlgCheckConfirm'
  | 'addIndividualInventoryWarning';

const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  addIndividualInventoryConfirm: false,
  addIndividualInventoryInfo: false,
  fragmentDialogVisible: false,
  addInventoryMultiLabelWarning: false,
  aogVarFlgCheckConfirm: false,
  addIndividualInventoryWarning: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxAddIndividualInventoryConfirmProps: DialogProps = {
  title: t('Inv.Chr.txtConfirm'),
  content: t('Inv.Msg.addIndividualInventoryCorrectionConfirm'),
  type: 'question',
};

const messageBoxAddIndividualInventoryPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxAddIndividualInventoryWarningPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'warning',
});

const messageBoxForm = createMessageBoxForm('message', 'cmtWarningZoneEnt');
let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;
const messageBoxAddInventoryMultiLabelPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

const messageBoxAogVarFlgCheckProps: DialogProps = {
  title: t('Inv.Chr.txtAogVarFlgNumberConfirm'),
  content: t('Inv.Msg.excessiveQuantity'),
  type: 'warning',
};

/**
 * 個装在庫追加
 */
const checkAddForm = async () => {
  const validate =
    individualInventoryAddFormRef.value.customForm !== undefined &&
    (await individualInventoryAddFormRef.value.customForm.validate(
      (isValid) => {
        onValidateHandler(isValid);
      },
    ));
  if (validate) {
    openDialog('addIndividualInventoryConfirm');
  }
  return false;
};

const apiHandler = async (
  messageBoxProps: DialogProps,
  warningNecessityFlg: number = WARNING_NECESSITY_FLAG.NECESSITY,
) => {
  showLoading();

  let addIndividualInventoryFormModel: ExtendCommonRequestType<AddInventoryMultiLabelReq> =
    {
      ...props.privilegesBtnRequestData,
      matNo: individualInventoryAddFormRef.value.formModel.matNo.toString(),
      lotSid: selectedRowData.lotSid,
      lotNo: individualInventoryAddFormRef.value.formModel.lotNo.toString(),
      totalInvQty:
        individualInventoryAddFormRef.value.formModel.totalInvQty.toString(),
      invQty: individualInventoryAddFormRef.value.formModel.invQty.toString(),
      zoneNo: individualInventoryAddFormRef.value.formModel.zoneNo.toString(),
      locNo: individualInventoryAddFormRef.value.formModel.locNo.toString(),
      afmRefNo:
        individualInventoryAddFormRef.value.formModel.afmRefNo.toString(),
      icRsnCd: individualInventoryAddFormRef.value.formModel.icRsnCd.toString(),
      afmExpl:
        individualInventoryAddFormRef.value.formModel.cmtCatInvMod.toString(),
      warnAfmExpl: '',
      msgboxTitleTxt: messageBoxProps.title,
      msgboxMsgTxt: messageBoxProps.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      warningNecessityFlg,
    };

  if (
    'isPrompt' in messageBoxProps &&
    warningNecessityFlg === WARNING_NECESSITY_FLAG.UNNECESSARY &&
    comboBoxDataStandardReturnData
  ) {
    addIndividualInventoryFormModel = {
      ...addIndividualInventoryFormModel,
      msgboxInputCmt: messageBoxProps.formModel.message.toString(),
      warnAfmExpl: messageBoxProps.formModel.message.toString(),
    };
  }

  // ３．個装在庫を追加する
  const { responseRef, errorRef } = await useAddInventoryMultiLabel(
    addIndividualInventoryFormModel,
  );
  if (errorRef.value) {
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      messageBoxAddInventoryMultiLabelPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxAddInventoryMultiLabelPropsRef.value.content =
        errorRef.value.response.rMsg;
      const resetData = createMessageBoxForm('message', 'cmtWarningZoneEnt');

      if (
        'isPrompt' in messageBoxAddInventoryMultiLabelPropsRef.value &&
        comboBoxDataStandardReturnData
      ) {
        messageBoxAddInventoryMultiLabelPropsRef.value.formItems =
          resetData.formItems;
        setCustomFormComboBoxOptionList(
          messageBoxAddInventoryMultiLabelPropsRef.value.formItems,
          comboBoxDataStandardReturnData.rData.rList,
        );
      }
      openDialog('addInventoryMultiLabelWarning');
    } else if (
      errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1013
    ) {
      messageBoxAddIndividualInventoryWarningPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxAddIndividualInventoryWarningPropsRef.value.content =
        errorRef.value.response.rMsg;
      openDialog('addIndividualInventoryWarning');
      closeLoading();
      return true;
    } else {
      messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
      messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
      messageBoxSingleButtonRef.value.type = 'error';
      openDialog('singleButton');
    }
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    messageBoxAddIndividualInventoryPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxAddIndividualInventoryPropsRef.value.content =
      responseRef.value.data.rMsg;
    openDialog('addIndividualInventoryInfo');
  }
  closeLoading();
  return true;
};

const closeCheckDialog = () => {
  closeDialog('aogVarFlgCheckConfirm');
  apiHandler(messageBoxAogVarFlgCheckProps);
};

const closeWarningDialog = () => {
  closeDialog('addInventoryMultiLabelWarning');
  apiHandler(
    messageBoxAddInventoryMultiLabelPropsRef.value,
    WARNING_NECESSITY_FLAG.UNNECESSARY,
  );
};

const aogVarFlgCheck = () => {
  closeDialog('addIndividualInventoryConfirm');
  // ２ 入り数割れ数量チェック
  if (
    selectedRowData.aogVarFlg === '1' &&
    selectedRowData.aogBaleUnit !== null &&
    Number(individualInventoryAddFormRef.value.formModel.invQty) >
      selectedRowData.aogBaleUnit
  ) {
    openDialog('aogVarFlgCheckConfirm');
  } else {
    apiHandler(messageBoxAddIndividualInventoryConfirmProps);
  }
};

/**
 * 梱数の計算
 * 梱数 = 「全体量 ÷ １梱の数量」
 */
const setInvPackageQtyValue = () => {
  const { totalInvQty, invQty, invPackageQty } =
    individualInventoryAddFormRef.value.formItems;
  const bigTotalInvQty = toBigOrNull(totalInvQty.formModelValue);
  const bigInvQty = toBigOrNull(invQty.formModelValue);
  invPackageQty.formModelValue =
    bigTotalInvQty === null || bigInvQty === null
      ? ''
      : toFixedDecimalString(bigTotalInvQty.div(bigInvQty), {
          decimalDigits: 0,
          roundMode: Big.roundUp,
        });
};

/**
 * 指定アイテムの関連更新を行う
 */
const updateFormItems = (fieldId: string) => {
  // ロケーションの絞り込み
  if (fieldId === constantData.zoneNo) {
    let overridePropsDisabled = true;
    setCustomFormFilterComboBoxOptionList(
      individualInventoryAddFormRef.value.formItems,
      comboBoxOptionList,
      constantData.locNo,
      constantData.locNo,
      [fieldId],
    );
    if (
      individualInventoryAddFormRef.value.formItems.locNo.formRole ===
      'selectComboBox'
    ) {
      if (
        individualInventoryAddFormRef.value.formItems.locNo.selectOptions
          .length > 0 &&
        individualInventoryAddFormRef.value.formItems.zoneNo.formModelValue !==
          ''
      ) {
        overridePropsDisabled = false;
      }
      individualInventoryAddFormRef.value.formItems.locNo.props!.disabled =
        overridePropsDisabled;
      individualInventoryAddFormRef.value.customForm?.clearValidate('locNo');
      customFormRenderingTriggerRef.value =
        !customFormRenderingTriggerRef.value;
    }
  }
  // 全体量/１梱の数量の絞り込み
  if (fieldId === constantData.totalInvQty || fieldId === constantData.invQty) {
    setInvPackageQtyValue();
  }
};

/**
 * 管理番号/製造番号検索画面から取得した値に変更する
 */
const updateFormItemsValue = (v: GetInventoryLotInvList) => {
  selectedRowData = v;
  // 品目コード
  individualInventoryAddFormRef.value.formItems.matNo.formModelValue =
    selectedRowData.matNo;
  // 品名
  individualInventoryAddFormRef.value.formItems.matNm.formModelValue =
    selectedRowData.matNm;
  // 管理番号/製造番号
  individualInventoryAddFormRef.value.formItems.lotNo.formModelValue =
    selectedRowData.lotNo;
  // 全体量単位 / １梱の数量単位
  individualInventoryAddFormRef.value.formItems.unitNm.formModelValue =
    selectedRowData.unitNm;
  // １梱の数量
  individualInventoryAddFormRef.value.formItems.invQty.formModelValue =
    selectedRowData.aogBaleUnit?.toString() ?? '';
};

const closeAllDialog = () => {
  closeDialog('addIndividualInventoryInfo');
  closeDialog('fragmentDialogVisible');
  closeDialog('addIndividualInventoryWarning');
  emit('submit', props.privilegesBtnRequestData);
};

/**
 * 初期設定
 */
const invAddIndividualPackagingInventoryInit = async () => {
  showLoading();
  updateDialogChangeFlagRef(false);

  // 連携テストダイアログの初期化必要なら書く
  individualInventoryAddFormRef.value.formItems =
    getIndividualInventoryAddFormItems();

  individualInventoryAddFormRef.value.formItems.accrualDts.formModelValue =
    getDateByType(new Date().toString(), 'YYYY/MM/DD');
  if (
    individualInventoryAddFormRef.value.formItems.locNo.formRole ===
    'selectComboBox'
  ) {
    individualInventoryAddFormRef.value.formItems.locNo.props!.disabled = false;
  }

  // 管理番号選択のボタンクリックの処理
  if (
    individualInventoryAddFormRef.value.formItems.lotNoSel.formRole === 'button'
  ) {
    individualInventoryAddFormRef.value.formItems.lotNoSel.onClickHandler =
      () => {
        isClickedShowSelectLotDialogRef.value =
          !isClickedShowSelectLotDialogRef.value;
      };
  }

  // 標準コンボボックスデータ取得
  let dspNarrowTypeVal = '';
  if (props.dspNarrowType === 'M') {
    dspNarrowTypeVal = 'M,N';
  } else if (props.dspNarrowType === 'P') {
    dspNarrowTypeVal = 'P,N';
  }
  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'zoneNo',
        condKey: 'm_ic_zone',
        where:
          props.dspNarrowType === 'N'
            ? { inv_gen_flg: '1', inv_hidden_flg: '0' }
            : {
                inv_gen_flg: '1',
                inv_hidden_flg: '0',
                dsp_narrow_type: dspNarrowTypeVal,
              },
      },
      {
        cmbId: 'locNo',
        condKey: 'm_ic_loc',
        optionCol: { zone_no: 'zoneNo' },
      },
      {
        cmbId: 'rsnCd',
        condKey: 'm_ic_rsn',
        where: { inv_add_flg: '1' },
      },
      {
        cmbId: 'cmtInvAdd',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'INV_LBL_ADD' },
      },
      {
        cmbId: 'cmtWarningZoneEnt',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'INV_WARNING' },
      },
    ],
  });
  if (
    comboBoxDataStandardReturnData &&
    comboBoxDataStandardReturnData.rData.rList.length > 0
  ) {
    comboBoxOptionList = comboBoxDataStandardReturnData.rData.rList;
    setCustomFormComboBoxOptionList(
      individualInventoryAddFormRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
    if ('formItems' in messageBoxAddInventoryMultiLabelPropsRef.value) {
      // コメントメッセージボックス選択肢
      setCustomFormComboBoxOptionList(
        messageBoxAddInventoryMultiLabelPropsRef.value.formItems,
        comboBoxDataStandardReturnData.rData.rList,
      );
    }
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, invAddIndividualPackagingInventoryInit);
</script>
