<template>
  <!-- 版変更ダイアログ -->
  <DialogWindow
    :title="$t('Odr.Chr.txtEditionChange')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="odrChangeEditNoFormRef.formModel"
      :formItems="odrChangeEditNoFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          odrChangeEditNoFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 版変更の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxEditionChangeVisible"
    :dialogProps="messageBoxEditionChangePropsRef"
    :cancelCallback="() => closeDialog('messageBoxEditionChangeVisible')"
    :submitCallback="requestApiAddScheduleBillOfMaterials"
  />
  <!-- 版変更完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxEditionChangeFinishedVisible"
    :dialogProps="messageBoxEditionChangeFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxEditionChangeFinished"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import createMessageBoxForm from '@/utils/commentMessageBox';
import {
  CommonRequestType,
  ComboBoxDataStandardReturnData,
} from '@/types/HookUseApi/CommonTypes';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import {
  GetScheduleBillOfMaterialsInitRequestData,
  AddScheduleBillOfMaterialsRequestData,
  GetMasterPrescriptionBillOfMaterialsListResBillOfMaterialsData,
} from '@/types/HookUseApi/OdrTypes';
import {
  useGetComboBoxDataStandard,
  useGetScheduleBillOfMaterialsInit,
  useAddScheduleBillOfMaterials,
} from '@/hooks/useApi';
import {
  getOdrChangeEditNoFormItems,
  odrChangeEditNoFormModel,
} from './odrChangeEditNo';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxEditionChangeVisible'
  | 'messageBoxEditionChangeFinishedVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxEditionChangeVisible: false,
  messageBoxEditionChangeFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// コメントあり警告用フォーム設定
const messageBoxForm = createMessageBoxForm('message', 'cmtWarning'); // 第二引数が標準コンボボックス取得のキー名
let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;
// 版変更の確認メッセージボックス
const messageBoxEditionChangePropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titleEditionChangeRegistration'),
  content: t('Odr.Msg.contentEditionChangeRegistration'),
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

// 版変更の完了メッセージボックス
const messageBoxEditionChangeFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

const odrChangeEditNoFormRef = ref<CustomFormType>({
  formItems: getOdrChangeEditNoFormItems(),
  formModel: odrChangeEditNoFormModel,
});

type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  scheduleNo: string; // オーダー番号
  processSequence: number; // 製造工程順
  selectedRowData: GetMasterPrescriptionBillOfMaterialsListResBillOfMaterialsData | null; // 選択行情報
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 入力付き確認メッセージボックスの入力を初期化
const clearInputMessageBoxForm = () => {
  if ('isPrompt' in messageBoxEditionChangePropsRef.value) {
    messageBoxEditionChangePropsRef.value.formItems.message.formModelValue = '';
  }
};

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  console.log('onResolve odrChangeEditNoDialog');
  // 1.下記チェックを行い、チェックOKなら処理継続する。
  // １ 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    odrChangeEditNoFormRef.value.customForm !== undefined &&
    (await odrChangeEditNoFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return false;
  }

  // 2.以下のメッセージを表示
  // 版変更登録の確認(コメントあり)
  clearInputMessageBoxForm(); // 開く前に入力フォームを初期化
  openDialog('messageBoxEditionChangeVisible');

  return false;
};

// 版変更登録の確認メッセージ'OK'押下時処理
// 版変更のAPIリクエスト処理
const requestApiAddScheduleBillOfMaterials = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxEditionChangeVisible');

  if (props.selectedRowData === null) return;
  if (!('isPrompt' in messageBoxEditionChangePropsRef.value)) return;

  showLoading();

  // 版変更のAPIを行う。
  const requestData: AddScheduleBillOfMaterialsRequestData = {
    skdNo: props.scheduleNo,
    prcSeq: props.processSequence,
    bomMatNo: props.selectedRowData.bomMatNo,
    edNo: odrChangeEditNoFormRef.value.formItems.matEdList.formModelValue.toString(),
    modExpl:
      messageBoxEditionChangePropsRef.value.formItems.message.formModelValue.toString(),
  };

  const { responseRef, errorRef } = await useAddScheduleBillOfMaterials({
    ...props.privilegesBtnRequestData,
    ...requestData,
    msgboxTitleTxt: messageBoxEditionChangePropsRef.value.title,
    msgboxMsgTxt: messageBoxEditionChangePropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    msgboxInputCmt:
      messageBoxEditionChangePropsRef.value.formItems.message.formModelValue.toString(),
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // 4．版変更を登録する。
    // ・データベースを更新した後、以下のメッセージを表示する。
    // 版変更追加完了
    messageBoxEditionChangeFinishedPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxEditionChangeFinishedPropsRef.value.content =
      responseRef.value.data.rMsg;

    openDialog('messageBoxEditionChangeFinishedVisible');
  }

  closeLoading();
};

// 版変更完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxEditionChangeFinished = () => {
  console.log('closeDialogFromMessageBoxEditionChangeFinished');

  // 再検索用に親に実行を通知
  emit('submit', props.privilegesBtnRequestData);

  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxEditionChangeFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

/**
 * 版変更ダイアログの初期設定
 */
const odrChangeEditNoInit = async () => {
  // NOTE:未選択なら処理させない。エラーメッセージはTabulatorTable側で出してくれる。
  if (props.selectedRowData === null) return;

  // ダイアログ終了チェック初期化
  updateDialogChangeFlagRef(false);
  // FormItems初期化
  odrChangeEditNoFormRef.value.formItems = getOdrChangeEditNoFormItems();

  // 標準コンボボックスAPIを挟み込む形でローディング表示
  showLoading();

  // cmtWarningで標準コンボボックス取得
  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtWarning',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'SKD_ED_MOD' },
      },
    ],
  });

  if (
    'isPrompt' in messageBoxEditionChangePropsRef.value &&
    comboBoxDataStandardReturnData
  ) {
    // NOTE: 暫定対応 formItemsを都度生成したフォームで上書きして初期化する
    const resetData = createMessageBoxForm('message', 'cmtWarning');
    messageBoxEditionChangePropsRef.value.formItems = resetData.formItems;

    // NOTE:共通コンボボックス情報を元にコメント付き警告メッセージを設定
    setCustomFormComboBoxOptionList(
      messageBoxEditionChangePropsRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }

  // 版変更初期表示のAPIを行う。
  const requestData: GetScheduleBillOfMaterialsInitRequestData = {
    skdNo: props.scheduleNo,
    prcSeq: props.processSequence,
    bomMatNo: props.selectedRowData.bomMatNo,
  };
  const { responseRef, errorRef } = await useGetScheduleBillOfMaterialsInit({
    ...props.privilegesBtnRequestData,
    ...requestData,
    // NOTE: 直前メッセージは無い。msgbox関連は設定不要。
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // ダイアログ表示初期値設定
    Object.entries(responseRef.value.data.rData).forEach(([key, value]) => {
      if (key in odrChangeEditNoFormRef.value.formItems) {
        // NOTE:選択コンボは別設定のため、上書きしない
        if (
          odrChangeEditNoFormRef.value.formItems[key].formRole !==
          'selectComboBox'
        ) {
          odrChangeEditNoFormRef.value.formItems[key].formModelValue =
            value?.toString() ?? '';
        }
      }
    });

    // 指定版番号の選択コンボ生成
    responseRef.value.data.rData.matEdList.forEach((v) => {
      if (
        odrChangeEditNoFormRef.value.formItems.matEdList.formRole ===
        'selectComboBox'
      ) {
        odrChangeEditNoFormRef.value.formItems.matEdList.selectOptions.push({
          cmbId: '',
          condKey: '',
          optionList: [],
          label: v.edNo, // 版番号は見た目と中身は同一
          value: v.edNo,
        });
      }
    });
  }

  closeLoading();

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

// ダイアログ起動条件の監視
watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await odrChangeEditNoInit();
  },
);
</script>
