<template>
  <DialogWindow
    :title="dialogConfig.title"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onResolve="dialogConfig.onResolve"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    :buttons="dialogConfig.buttons"
  >
    <BaseHeading
      level="2"
      fontSize="24px"
      :text="$t('Aog.Chr.txtAogArrivalInstructionDetails')"
    />
    <div class="custom-form-container">
      <CustomForm
        :formModel="aogArrivalInstructionDetailsFormRef.formModel"
        :formItems="aogArrivalInstructionDetailsFormRef.formItems"
        @visible="
          (v: CustomFormType['customForm']) => {
            aogArrivalInstructionDetailsFormRef.customForm = v;
          }
        "
      />
    </div>
    <div class="Util_mt-32">
      <BaseHeading
        level="2"
        fontSize="24px"
        :text="$t('Aog.Chr.txtAogRecord')"
      />
      <p>{{ $t('Aog.Chr.txtPltList') }}</p>
      <TabulatorTable :propsData="tablePropsDialogRef" />
    </div>
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <!-- 入荷実績量チェック -->
  <MessageBox
    v-if="dialogVisibleRef.checkAogRecordWarning"
    :dialogProps="messageBoxCheckAogRecordPropsRef"
    :cancelCallback="() => closeDialog('checkAogRecordWarning')"
    :submitCallback="aogForceCompleteConfirm"
  />
  <!-- 入荷検品強制完了 -->
  <MessageBox
    v-if="dialogVisibleRef.aogForceCompleteWarning"
    :dialogProps="messageBoxAogForceCompletePropsRef"
    :cancelCallback="() => closeDialog('aogForceCompleteWarning')"
    :submitCallback="aogForceCompleteArrivalInspectionApiHandler"
  />
  <MessageBox
    v-if="dialogVisibleRef.aogForceCompleteInfo"
    :dialogProps="messageBoxAogCompleteRef"
    :submitCallback="closeAllDialog"
  />
  <!-- 入荷検品完了取消-->
  <MessageBox
    v-if="dialogVisibleRef.aogCancelCompleteWarning"
    :dialogProps="messageBoxAogCancelCompletePropsRef"
    :cancelCallback="() => closeDialog('aogCancelCompleteWarning')"
    :submitCallback="aogCancelArrivalInspectionCompleteApiHandler"
  />
  <MessageBox
    v-if="dialogVisibleRef.aogCancelCompleteInfo"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="closeAllDialog"
  />
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  GetAogInstructionData,
  GetAogInstructionForceData,
  AogInstructionData,
  GetAogInstructionRes,
  GetAogIoaForceRes,
} from '@/types/HookUseApi/AogTypes';
import {
  CommonRequestType,
  ComboBoxDataStandardReturnData,
} from '@/types/HookUseApi/CommonTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import {
  useGetComboBoxDataStandard,
  useGetAogInstruction,
  useGetAogForceCompleteArrivalInspection,
  useGetAogCancelArrivalInspectionComplete,
  useModifyAogForceCompleteArrivalInspection,
  useModifyAogCancelArrivalInspectionComplete,
} from '@/hooks/useApi';
import { toBigOrNull } from '@/utils';
import { closeLoading, showLoading } from '@/utils/dialog';
import createMessageBoxForm from '@/utils/commentMessageBox';
import SCREENID from '@/constants/screenId';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  AogRequest,
  DialogConfig,
  DialogConfigApi,
  AxiosCallBackResult,
  aogArrivalInstructionDetailsFormModel,
  getAogArrivalInstructionDetailsFormItems,
  tablePropsData,
} from './aogInstAndIoaForceAndIoaCancel';

const aogArrivalInstructionDetailsFormRef = ref<CustomFormType>({
  formItems: getAogArrivalInstructionDetailsFormItems(),
  formModel: aogArrivalInstructionDetailsFormModel,
});

type Props = {
  selectedRowData: AogInstructionData | null;
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
  screenId: string;
};

const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

let aogArrivalInstructionDetailsFormData: GetAogInstructionData = {
  planYmd: '',
  aogInstNo: '',
  aogInstGrpNo: '',
  poDtlNo: '',
  matNo: '',
  matNm: '',
  mBpId: '',
  bpNm: '',
  makerLotNo: '',
  edNo: '',
  mesAogQty: '',
  mesUnitNm: '',
  planExpl: '',
  aogAttBinList: [],
  aogRsltPltList: [],
};

let aogForceCompleteArrivalInstructionFormData: GetAogInstructionForceData = {
  planYmd: '',
  aogInstNo: '',
  aogInstGrpNo: '',
  poDtlNo: '',
  matNo: '',
  matNm: '',
  mBpId: '',
  bpNm: '',
  makerLotNo: '',
  edNo: '',
  mesAogQty: '',
  mesUnitNm: '',
  planExpl: '',
  aogAttBinList: [],
  aogRsltPltList: [],
  ioaQty: '',
  aogQtyLower: '',
  aogQtyUpper: '',
};

type DialogRefKey =
  | 'singleButton'
  | 'messageBoxSingleButtonRef'
  | 'messageBoxAogCompleteRef'
  | 'fragmentDialogVisible'
  | 'checkAogRecordWarning'
  | 'aogForceCompleteWarning'
  | 'aogCancelCompleteWarning'
  | 'aogForceCompleteInfo'
  | 'aogCancelCompleteInfo';

const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  messageBoxSingleButtonRef: false,
  messageBoxAogCompleteRef: false,
  fragmentDialogVisible: false,
  checkAogRecordWarning: false,
  aogForceCompleteWarning: false,
  aogCancelCompleteWarning: false,
  aogForceCompleteInfo: false,
  aogCancelCompleteInfo: false,
};
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
const tablePropsDialogRef = ref<TabulatorTableIF>({
  ...tablePropsData,
});
const dialogConfig = ref<DialogConfig>({
  title: '',
  condList: [],
  buttons: [],
  onResolve: () => {},
  formData: null,
});
const dialogSingleButton: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    clickHandler: () => {
      closeDialog('fragmentDialogVisible');
    },
  },
];
let comboBoxResData: ComboBoxDataStandardReturnData | undefined;
let aogRsltPltCount: number;

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});
const messageBoxAogCompleteRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});
// 入荷実績量チェック
const messageBoxCheckAogRecordForm = createMessageBoxForm(
  'message',
  'cmtAogRsltPck',
);
const messageBoxCheckAogRecordPropsRef = ref<DialogProps>({
  title: t('Aog.Chr.txtAogRecordAmountCheck'),
  content: '',
  isPrompt: true,
  formModel: messageBoxCheckAogRecordForm.formModel,
  formItems: messageBoxCheckAogRecordForm.formItems,
  type: 'warning',
});
// 入荷検品強制完了
const messageBoxAogForceCompleteForm = createMessageBoxForm(
  'message',
  'cmtAogIoaForce',
);
const messageBoxAogForceCompletePropsRef = ref<DialogProps>({
  title: t('Aog.Chr.txtAogForceArrivalInspection'),
  content: t('Aog.Msg.aogForceCompleteArrivalInspection'),
  isPrompt: true,
  formModel: messageBoxAogForceCompleteForm.formModel,
  formItems: messageBoxAogForceCompleteForm.formItems,
  type: 'warning',
});
// 入荷検品完了取消
const messageBoxAogCancelCompleteForm = createMessageBoxForm(
  'message',
  'cmtAogIoaCancel',
);
const messageBoxAogCancelCompletePropsRef = ref<DialogProps>({
  title: t('Aog.Chr.txtAogCancelArrivalInspectionComplete'),
  content: t('Aog.Msg.aogCancelCompleteArrivalInspection'),
  isPrompt: true,
  formModel: messageBoxAogCancelCompleteForm.formModel,
  formItems: messageBoxAogCancelCompleteForm.formItems,
  type: 'warning',
});

const aogForceCompleteConfirm = async () => {
  closeDialog('checkAogRecordWarning');
  openDialog('aogForceCompleteWarning');
  const resetData = createMessageBoxForm('message', 'cmtAogIoaForce');
  if (
    'isPrompt' in messageBoxAogForceCompletePropsRef.value &&
    comboBoxResData
  ) {
    messageBoxAogForceCompletePropsRef.value.formItems = resetData.formItems;
    setCustomFormComboBoxOptionList(
      messageBoxAogForceCompletePropsRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }
  return false;
};

// 入荷検品強制完了
const checkAogRecordConfirm = async () => {
  const bigIoaQty = toBigOrNull(
    aogForceCompleteArrivalInstructionFormData.ioaQty,
  );
  const bigAogQtyLower = toBigOrNull(
    aogForceCompleteArrivalInstructionFormData.aogQtyLower,
  );
  const bigAogQtyUpper = toBigOrNull(
    aogForceCompleteArrivalInstructionFormData.aogQtyUpper,
  );
  if (!bigIoaQty || !bigAogQtyLower || !bigAogQtyUpper) {
    await aogForceCompleteConfirm();
    return false;
  }
  if (bigIoaQty.lt(bigAogQtyLower) || bigAogQtyUpper.lt(bigIoaQty)) {
    openDialog('checkAogRecordWarning');
    const resetData = createMessageBoxForm('message', 'cmtAogRsltPck');
    if (
      'isPrompt' in messageBoxCheckAogRecordPropsRef.value &&
      comboBoxResData
    ) {
      messageBoxCheckAogRecordPropsRef.value.formItems = resetData.formItems;
      setCustomFormComboBoxOptionList(
        messageBoxCheckAogRecordPropsRef.value.formItems,
        comboBoxResData.rData.rList,
      );
    }
    messageBoxCheckAogRecordPropsRef.value.content = t(
      'Aog.Msg.checkAogRecordWaring',
      [
        aogForceCompleteArrivalInstructionFormData.mesAogQty,
        aogForceCompleteArrivalInstructionFormData.mesUnitNm,
        aogForceCompleteArrivalInstructionFormData.ioaQty,
        aogForceCompleteArrivalInstructionFormData.mesUnitNm,
      ],
    );
  } else {
    await aogForceCompleteConfirm();
    return false;
  }
  return false;
};

const aogCancelCompleteConfirm = async () => {
  openDialog('aogCancelCompleteWarning');
  const resetData = createMessageBoxForm('message', 'cmtAogIoaCancel');
  if (
    'isPrompt' in messageBoxAogCancelCompletePropsRef.value &&
    comboBoxResData
  ) {
    messageBoxAogCancelCompletePropsRef.value.formItems = resetData.formItems;
    setCustomFormComboBoxOptionList(
      messageBoxAogCancelCompletePropsRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }
  return false;
};

// 入荷検品完了取消を実行
const aogCancelArrivalInspectionCompleteApiHandler = async () => {
  closeDialog('aogCancelCompleteWarning');
  showLoading();

  let aogForceCompleteArrivalInspectionFormModel = {
    ...props.privilegesBtnRequestData,
    aogInstNo:
      aogArrivalInstructionDetailsFormRef.value.formModel.aogInstNo.toString(),
    // データ取得時のレスポンスの入荷実績パレットリストの行数を、
    // 強制完了取消のリクエストの検品済パレット数にセットしてください
    pickPltCnt: aogRsltPltCount,
    expl: '',
  };
  if (
    'isPrompt' in messageBoxAogCancelCompletePropsRef.value &&
    comboBoxResData
  ) {
    aogForceCompleteArrivalInspectionFormModel = {
      ...aogForceCompleteArrivalInspectionFormModel,
      expl: messageBoxAogCancelCompletePropsRef.value.formModel.message.toString(),
      msgboxTitleTxt: messageBoxAogCancelCompletePropsRef.value.title,
      msgboxMsgTxt: messageBoxAogCancelCompletePropsRef.value.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      msgboxInputCmt:
        messageBoxAogCancelCompletePropsRef.value.formModel.message.toString(),
    };
  }

  const { responseRef, errorRef } =
    await useModifyAogCancelArrivalInspectionComplete({
      ...aogForceCompleteArrivalInspectionFormModel,
    });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    messageBoxSingleButtonRef.value.title = responseRef.value.data.rTitle;
    messageBoxSingleButtonRef.value.content = responseRef.value.data.rMsg;
    messageBoxSingleButtonRef.value.type = 'info';
    openDialog('aogCancelCompleteInfo');
  }
  closeLoading();
  return true;
};

// 入荷検品強制完了を実行
const aogForceCompleteArrivalInspectionApiHandler = async () => {
  closeDialog('aogForceCompleteWarning');
  showLoading();

  let aogForceCompleteArrivalInspectionFormModel = {
    ...props.privilegesBtnRequestData,
    aogInstNo:
      aogArrivalInstructionDetailsFormRef.value.formModel.aogInstNo.toString(),
    // データ取得時のレスポンスの入荷実績パレットリストの行数を、
    // 入荷検品強制完了のリクエストの検品済パレット数にセットしてください
    pickPltCnt: aogRsltPltCount,
    expl: '',
    pckExpl: '',
  };
  if (
    'isPrompt' in messageBoxCheckAogRecordPropsRef.value &&
    'isPrompt' in messageBoxAogForceCompletePropsRef.value &&
    comboBoxResData
  ) {
    aogForceCompleteArrivalInspectionFormModel = {
      ...aogForceCompleteArrivalInspectionFormModel,
      expl: messageBoxAogForceCompletePropsRef.value.formModel.message.toString(),
      pckExpl:
        messageBoxCheckAogRecordPropsRef.value.formModel.message.toString(),
      msgboxTitleTxt: messageBoxAogForceCompletePropsRef.value.title,
      msgboxMsgTxt: messageBoxAogForceCompletePropsRef.value.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      msgboxInputCmt:
        messageBoxAogForceCompletePropsRef.value.formModel.message.toString(),
    };
  }

  const { responseRef, errorRef } =
    await useModifyAogForceCompleteArrivalInspection({
      ...aogForceCompleteArrivalInspectionFormModel,
    });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    messageBoxAogCompleteRef.value.title = responseRef.value.data.rTitle;
    messageBoxAogCompleteRef.value.content = responseRef.value.data.rMsg;
    openDialog('aogForceCompleteInfo');
  }
  closeLoading();
  return true;
};

const closeAllDialog = () => {
  if (props.screenId === SCREENID.AOG_FORCE_COMPLETE_ARRIVAL_INSPECTION) {
    closeDialog('aogForceCompleteInfo');
    closeDialog('fragmentDialogVisible');
    // W113211 入荷検品強制完了
    emit('submit', props.privilegesBtnRequestData);
  }
  if (props.screenId === SCREENID.AOG_CANCEL_ARRIVAL_INSTRUCTION_COMPLETE) {
    closeDialog('aogCancelCompleteInfo');
    // W113212 入荷検品完了取消
    closeDialog('fragmentDialogVisible');
    emit('submit', props.privilegesBtnRequestData);
  }
};

const apiClickHandler = async (
  api: DialogConfigApi[
    | 'useGetAogForceCompleteArrivalInspection'
    | 'useGetAogInstruction'
    | 'useGetAogCancelArrivalInspectionComplete'],
) => {
  const { responseRef, errorRef } = await api({
    ...props.privilegesBtnRequestData,
    aogInstNo: props.selectedRowData!.aogInstNo,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    tablePropsDialogRef.value.tableData =
      responseRef.value.data.rData.aogRsltPltList;
    aogRsltPltCount = responseRef.value.data.rData.aogRsltPltList.length;
    if (props.screenId === SCREENID.AOG_FORCE_COMPLETE_ARRIVAL_INSPECTION) {
      aogForceCompleteArrivalInstructionFormData = responseRef.value.data
        .rData as GetAogInstructionForceData;
      dialogConfig.value.formData = responseRef.value.data.rData;
    } else {
      aogArrivalInstructionDetailsFormData = responseRef.value.data
        .rData as GetAogInstructionData;
      dialogConfig.value.formData = aogArrivalInstructionDetailsFormData;
    }
    setFormModelValueFromApiResponse(
      aogArrivalInstructionDetailsFormRef,
      dialogConfig.value.formData,
      {
        fileKeys: [
          {
            formModelKey: 'aogAttBinList',
            fileNameKey: 'attBinFileNm',
            fileKeyPropName: 'attBinNo',
          },
        ],
        commonRequestData: props.privilegesBtnRequestData,
      },
    );
    openDialog('fragmentDialogVisible');
  }
};

/**
 * 初期設定
 */
const aogInstAndIoaForceAndIoaCancelInit = async () => {
  if (!props.selectedRowData) return;
  showLoading();
  if (props.screenId === SCREENID.AOG_ARRIVAL_INSTRUCTION_DETAILS) {
    dialogConfig.value.title = t('Aog.Chr.txtAogArrivalInstructionDetails');
    dialogConfig.value.condList = [];
    dialogConfig.value.buttons = dialogSingleButton;
    dialogConfig.value.formData = aogArrivalInstructionDetailsFormData;
    await apiClickHandler(
      useGetAogInstruction as (
        val: AogRequest,
      ) => Promise<AxiosCallBackResult<GetAogInstructionRes>>,
    );
  } else if (
    props.screenId === SCREENID.AOG_FORCE_COMPLETE_ARRIVAL_INSPECTION
  ) {
    dialogConfig.value.title = t('Aog.Chr.txtAogForceArrivalInspection');
    dialogConfig.value.condList = [
      {
        cmbId: 'cmtAogRsltPck',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'AOG_RSLT_PCK' },
      },
      {
        cmbId: 'cmtAogIoaForce',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'AOG_IOA_FORCE' },
      },
    ];
    dialogConfig.value.onResolve = checkAogRecordConfirm;
    dialogConfig.value.formData = aogForceCompleteArrivalInstructionFormData;
    await apiClickHandler(
      useGetAogForceCompleteArrivalInspection as (
        val: AogRequest,
      ) => Promise<AxiosCallBackResult<GetAogIoaForceRes>>,
    );
  } else if (
    props.screenId === SCREENID.AOG_CANCEL_ARRIVAL_INSTRUCTION_COMPLETE
  ) {
    dialogConfig.value.title = t(
      'Aog.Chr.txtAogCancelArrivalInspectionComplete',
    );
    dialogConfig.value.condList = [
      {
        cmbId: 'cmtAogIoaCancel',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'AOG_IOA_CANCEL' },
      },
    ];
    dialogConfig.value.onResolve = aogCancelCompleteConfirm;
    dialogConfig.value.formData = aogArrivalInstructionDetailsFormData;
    await apiClickHandler(
      useGetAogCancelArrivalInspectionComplete as (
        val: AogRequest,
      ) => Promise<AxiosCallBackResult<GetAogInstructionRes>>,
    );
  }
  // 標準コンボボックスデータ取得
  comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: dialogConfig.value.condList,
  });
  // condKeysがない場合、エラー時にダイアログを開かない
  if (
    dialogConfig.value.condList.length === 0 &&
    comboBoxResData === undefined
  ) {
    closeLoading();
    return;
  }
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setCustomFormComboBoxOptionList(
      aogArrivalInstructionDetailsFormRef.value.formItems,
      comboBoxResData!.rData.rList,
    );
    if (props.screenId === SCREENID.AOG_FORCE_COMPLETE_ARRIVAL_INSPECTION) {
      if ('formItems' in messageBoxCheckAogRecordPropsRef.value) {
        // コメントメッセージボックス選択肢
        setCustomFormComboBoxOptionList(
          messageBoxCheckAogRecordPropsRef.value.formItems,
          comboBoxResData.rData.rList,
        );
      }
      if ('formItems' in messageBoxAogForceCompletePropsRef.value) {
        // コメントメッセージボックス選択肢
        setCustomFormComboBoxOptionList(
          messageBoxAogForceCompletePropsRef.value.formItems,
          comboBoxResData.rData.rList,
        );
      }
    }
    if (props.screenId === SCREENID.AOG_CANCEL_ARRIVAL_INSTRUCTION_COMPLETE) {
      if ('formItems' in messageBoxAogCancelCompletePropsRef.value) {
        // コメントメッセージボックス選択肢
        setCustomFormComboBoxOptionList(
          messageBoxAogCancelCompletePropsRef.value.formItems,
          comboBoxResData.rData.rList,
        );
      }
    }
  }
  closeLoading();
};

watch(() => props.isClicked, aogInstAndIoaForceAndIoaCancelInit);
</script>

<style lang="scss" scoped>
.custom-form-container {
  height: 258px;
  overflow-y: auto;
}
</style>
