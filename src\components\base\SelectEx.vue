<template>
  <el-tooltip
    :effect="CONST.TOOLTIP.EFFECT"
    :disabled="tooltipDisabled"
    :content="tooltipContent"
    :popperOptions="popperOption"
    :placement="CONST.TOOLTIP.PLACEMENT"
    popperClass="tooltip-popper"
  >
    <div :class="selectClass">
      <el-select-v2
        v-bind="vBindProps"
        :placeholder="placeholder"
        @change="(v: string) => emit('update:modelValue', v)"
        @blur="(v: FocusEvent) => emit('blur', v)"
        @clear="() => emit('update:modelValue', '')"
        :fitInputWidth="true"
        :options="vBindProps.optionsData"
        popperClass="select-ex-v2-options"
        style="width: 100%"
      />
    </div>
  </el-tooltip>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { SelectExProps } from '@/types/SelectExTypes';
import SCSS from '@/constants/scssVariables';
import CONST from '@/constants/utils';
import popperOption from '@/utils/tooltip';
import excludeProps from '@/utils/excludeProps';

const props = withDefaults(defineProps<SelectExProps>(), {
  size: 'small',
  width: SCSS.widthSmall,
  clearable: true,
  filterable: true,
  validateEvent: true,
  disabled: false,
});

const vBindProps = computed(() => excludeProps(props, ['size', 'width']));
const selectClass = computed(() => `select-ex_${props.size}`);
const tooltipDisabled = computed(() => !vBindProps.value.modelValue);
const tooltipContent = computed(() => {
  const option = vBindProps.value.optionsData.find(
    (item) => item.value === vBindProps.value.modelValue,
  );
  return option ? option.label : '';
});
const placeholder = computed(() => (props.disabled ? '' : props.placeholder));

const emit = defineEmits(['update:modelValue', 'blur']);
</script>
<style lang="scss" scoped>
$namespace: 'select-ex';
$widthVal: v-bind('props.width');

@mixin setSizeMixin($width, $height) {
  width: $width !important;
  height: $height !important;
}

.#{$namespace} {
  &_custom {
    @include setSizeMixin($widthVal, $height);
  }
  &_large {
    @include setSizeMixin($widthLarge, $height);
  }
  &_middle {
    @include setSizeMixin($widthMiddle, $height);
  }
  &_small {
    @include setSizeMixin($widthSmall, $height);
  }
}
:deep(.el-select) {
  width: 100%;
  .el-input__wrapper {
    height: calc($height - ($defaultBorderWidth * 2));
  }
  .el-select__selected-item {
    font-family: sans-serif;
  }
}
</style>
<style lang="scss">
// NOTE: el-select-v2のoptionsクラスはbody直下に表示される為、scopedを外してスタイルを定義しています
.select-ex-v2-options {
  min-width: $widthLarge;
  .el-select-dropdown {
    width: fit-content !important;
  }
  .el-select-dropdown__list {
    min-width: $widthLarge;
  }
  .el-select-dropdown__item {
    line-height: 1.25;
    overflow: visible;
    white-space: pre-wrap;
    display: grid;
    align-items: center;
    font-family: sans-serif;
  }
}
</style>
