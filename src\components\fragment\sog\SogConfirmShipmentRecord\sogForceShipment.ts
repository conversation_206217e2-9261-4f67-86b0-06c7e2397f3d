import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

export type SelectOption = {
  label: string;
  value: string;
};

export const getSogForceShipmentTextFormItems: () => CustomFormType['formItems'] =
  () => ({
    sogSlipGrpNo: {
      label: { text: t('Sog.Chr.txtSogSlipGrpNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 24,
    },
    sogPlanYmd: {
      label: { text: t('Sog.Chr.txtSogPlanYmd') },
      formModelValue: '',
      formRole: 'textBox',
      span: 24,
      props: { disabled: true, size: 'small' },
    },
    bpTrfNm: {
      label: { text: t('Sog.Chr.txtBpTrfNm') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
      span: 24,
    },
  });
export const sogForceShipmentTextFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getSogForceShipmentTextFormItems());

export const getSogForceShipmentExplFormItems: () => CustomFormType['formItems'] =
  () => ({
    sogForceExpl: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Sog.Chr.txtComment') },
      formModelValue: '',
      rules: [
        rules.required('textComboBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
      ],
      formRole: 'textComboBox',
      props: { clearable: true },
      selectOptions: [],
      cmbId: 'cmtSogForce',
    },
  });

export const sogForceShipmentExplFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getSogForceShipmentExplFormItems());

export const getSogForceShipmentTimeFormItems: () => CustomFormType['formItems'] =
  () => ({
    hour: {
      formModelValue: '',
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: '',
    },
    minute: {
      formModelValue: '',
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: '',
    },
  });

export const sogForceShipmentTimeFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getSogForceShipmentTimeFormItems());
