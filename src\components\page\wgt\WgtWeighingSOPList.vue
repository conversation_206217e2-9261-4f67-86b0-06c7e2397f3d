<template>
  <!-- 秤量前後SOP一覧ページ -->
  <el-card class="wgt-weighing-sop-list">
    <el-row
      :class="[
        {
          'wgt-weighing-sop-list_el-row-show-condition':
            tablePropsDataRef.showConditionSearch,
        },
        'wgt-weighing-sop-list_el-row',
      ]"
    >
      <div class="wgt-weighing-sop-list_search">
        <!-- 検索バー -->
        <ConditionSearch
          :conditionData="conditionDataRef"
          @showCondition="showCondition"
          @getSearchData="searchClickHandler"
          @mounted="createSearchRequestData"
        />
      </div>
      <div
        class="wgt-weighing-sop-list_animated-col wgt-weighing-sop-list_table"
      >
        <BaseHeading level="2" :text="pageTitle" fontSize="24px" />
        <!-- 共通のテーブル -->
        <TabulatorTable
          :propsData="tablePropsDataRef"
          :routerName="props.routerInfo.name"
          @selectRow="updateSelectedRow"
          @showCondition="showCondition"
        />
      </div>
    </el-row>
    <!-- 秤量前後SOP作業詳細ダイアログ -->
    <WgtWeighingSOPDetail
      :isClicked="isClickedShowWgtWeighingSOPDetailDialogRef"
      :selectedRowData="selectedRow"
      :privilegesBtnRequestData="
        props.privilegesBtnRequestData[BUTTON_ID.DETAIL]
      "
      :routerName="props.routerInfo.name"
      @submit="requestApiGetWeighingSOPList"
    />
    <!-- APIのエラー表示 -->
    <MessageBox
      v-if="dialogVisibleRef.messageBoxApiErrorVisible"
      :dialogProps="messageBoxApiErrorPropsRef"
      :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
    />
  </el-card>
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { onMounted, ref } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import ConditionSearch from '@/components/model/ConditionSearch/ConditionSearch.vue';
import {
  useGetWeighingSOPList,
  useGetComboBoxDataStandard,
  useGetUsrGrid,
} from '@/hooks/useApi';
import { DynamicModels } from '@/types/ConditionSearchTypes';
import { useSearch } from '@/hooks/useSearch';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  GetWeighingSOPListResListData,
  GetWeighingSOPListRequestData,
} from '@/types/HookUseApi/WgtTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setComboBoxOptionList } from '@/utils/comboBoxOptionList';
import { initCommonRequestFromPrivilegesType } from '@/hooks/useApi/util';
import { RouterInfoType } from '@/types/RouterTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import WgtWeighingSOPDetail from '@/components/fragment/wgt/wgtConfirmWeighingRecord/WgtWeighingSOPDetail.vue';
import {
  BUTTON_ID,
  searchDataModel,
  conditionData,
} from './wgtWeighingSOPList';

/**
 * 多言語
 */
const { t } = useI18n();
const props = defineProps<RouterInfoType>();

// ページの共通リクエストデータ
const commonActionRequestData = initCommonRequestFromPrivilegesType({
  menu2Scn: props.routerInfo.name.slice(0, -1),
  menu3Act: props.routerInfo.name.at(-1),
  signType: '0',
});

const initialState: InitialDialogState<'messageBoxApiErrorVisible'> = {
  messageBoxApiErrorVisible: false,
};
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// NOTE:ルーターの命名ではなく、独自のタグで上書き
const pageTitle = t('Wgt.Chr.txtWeightSopRecord');

// TabulatorTableの行情報定義
type SopRecordTableRowData = GetWeighingSOPListResListData & {
  uniqueKey: string; // テーブル用主キー
};

// 選択行情報の格納
let selectedRow: GetWeighingSOPListResListData | null = null;

// 検索用リクエストデータの格納
let searchRequestData: GetWeighingSOPListRequestData | null = null;

// '作業詳細' クリック
const isClickedShowWgtWeighingSOPDetailDialogRef = ref<boolean>(false);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

const tablePropsDataRef = ref<TabulatorTableIF>({
  pageName: 'WgtWeighingSOPList',
  title: pageTitle, // CSV出力名
  dataID: 'uniqueKey', // 主キー。ユニークになるものを設定。
  showRadio: true,
  tableBtns: [],
  onSelectBtns: [
    {
      type: 'primary',
      text: 'Wgt.Chr.btnFlowDetail', // 作業詳細
      tabulatorActionId: BUTTON_ID.DETAIL,
      clickHandler: () => {
        // SOPフロー情報ダイアログを表示開始
        // クリックをv-bind経由でfragmentに通知する 真偽値を切り替えるたびに通知される
        isClickedShowWgtWeighingSOPDetailDialogRef.value =
          !isClickedShowWgtWeighingSOPDetailDialogRef.value;
      },
    },
  ],
  customActionBtns: [],

  column: [
    // 確認状態
    {
      title: 'Wgt.Chr.txtStatus',
      field: 'recConfirmFlgDsp',
      width: COLUMN_WIDTHS.WGT.REC_CONFIRM_FLG,
    },
    // フロー状態
    {
      title: 'Wgt.Chr.txtFlowStatus',
      field: 'sopFlowStsDsp',
      width: COLUMN_WIDTHS.WGT.SOP_FLOW_STS,
    },
    // 異状レベル
    {
      title: 'Wgt.Chr.txtDeviationLevel',
      field: 'devCorrLv',
      width: COLUMN_WIDTHS.DEV_LV,
    },
    // SOPフロー名
    {
      title: 'Wgt.Chr.txtSopFlowName',
      field: 'sopFlowNmJp',
      width: COLUMN_WIDTHS.SOP_FLOW_NM,
    },
    // SOPフロー開始日時
    {
      title: 'Wgt.Chr.txtSOPFlowStartDate',
      field: 'stDts',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // SOPフロー終了日時
    {
      title: 'Wgt.Chr.txtSOPFlowEndDate',
      field: 'edDts',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // SOPフロー記録検印日時
    {
      title: 'Wgt.Chr.txtSOPFlowRecordStampDate',
      field: 'recConfirmDts',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // 異状コメント
    {
      title: 'Wgt.Chr.txtDeviationComment',
      field: 'devExplDsp',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 作業コメント
    {
      title: 'Wgt.Chr.txtWorkComment',
      field: 'msgExplDsp',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 修正コメント
    {
      title: 'Wgt.Chr.txtModifyComment',
      field: 'modExplDsp',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 異状確認コメント
    {
      title: 'Wgt.Chr.txtDeviationCheckComment',
      field: 'recConfirmDevExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 記録確認コメント
    {
      title: 'Wgt.Chr.txtRecordCheckComment',
      field: 'recConfirmExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 秤量指示書番号
    {
      title: 'Wgt.Chr.txtWeightInstructionsNo',
      field: 'wgtInstGrpNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    // 秤量指示書状態
    {
      title: 'Wgt.Chr.txtWeightInstructionStatus',
      field: 'wgtInstGrpSts',
      width: COLUMN_WIDTHS.WGT.WGT_INST_GRP_STS,
    },
    // 秤量室
    {
      title: 'Wgt.Chr.txtWeightRoom',
      field: 'wgtRoomNmJp',
      width: COLUMN_WIDTHS.WGT.WGT_ROOM_NM,
    },
    // 計量器
    {
      title: 'Wgt.Chr.txtWeightDevice',
      field: 'deviceNmJp',
      width: COLUMN_WIDTHS.WGT.DEVICE_NM,
    },
    // 隠しカラムとして運用
    { title: '', field: 'uniqueKey', hidden: true },
    // 隠しカラムとして運用
    { title: '', field: 'backgroundColor', hidden: true },
  ],
  tableData: [],
  showConditionSearch: true, // ConditionSearchの表示/非表示
  rowColor: {
    useColor: true,
    colorColumn: 'backgroundColor',
  },
  usrGridInfo: {
    colSort: [],
    colHide: [],
  },
});

const {
  conditionDataRef,
  tableSearchDataRef,
  getSearchData,
  updateTableSearchDataProp,
  getApiRequestData,
} = useSearch();
// useSearch直後に初期値渡す。
conditionDataRef.value = conditionData;

// レスポンスデータからユニークキーを生成取得する処理
const getUniqueKey = (responseData: GetWeighingSOPListResListData) =>
  `${responseData.sopFlowNo}-${responseData.sopFlowLnum}-${responseData.wgtInstGrpNo}`;

// レスポンスデータからテーブル用データに変換取得する処理
const getTableData = (responseData: GetWeighingSOPListResListData[]) => {
  const tableData: SopRecordTableRowData[] = [];
  responseData.forEach((res) => {
    // NOTE:レスポンスにはないユニークキー情報を足してテーブル情報とする
    const tableRowData: SopRecordTableRowData = {
      ...res,
      uniqueKey: getUniqueKey(res),
    };
    tableData.push(tableRowData);
  });
  return tableData;
};

// 検索、再検索で呼び出される
// 秤量前後SOP一覧検索のAPIリクエスト処理
const requestApiGetWeighingSOPList = async (requestData: CommonRequestType) => {
  showLoading();

  const { responseRef, errorRef } = await useGetWeighingSOPList({
    ...requestData,
    ...searchRequestData!,
  });
  if (errorRef.value) {
    closeLoading();

    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  tablePropsDataRef.value.searchData = tableSearchDataRef.value;
  tablePropsDataRef.value.tableData = getTableData(
    responseRef.value.data.rData.wgtSopRecList,
  );

  closeLoading();
};

// DynamicModelsから検索API用リクエストデータの生成
const createSearchRequestData = (val: DynamicModels) => {
  const searchData = getSearchData(val, searchDataModel);
  updateTableSearchDataProp(searchData);
  searchRequestData =
    getApiRequestData<GetWeighingSOPListRequestData>(searchData);
};

// 検索 押下時処理
const searchClickHandler = async (val: DynamicModels) => {
  // 検索押下時の状況で再検索したいため、リクエストデータを生成してキャッシュ
  createSearchRequestData(val);

  // 秤量前後SOP一覧検索
  await requestApiGetWeighingSOPList({
    ...commonActionRequestData,
    btnId: BUTTON_ID.SEARCH,
  });
};

/**
 * 秤量前後SOP一覧画面の初期設定
 */
const wgtWeighingSOPListInit = async () => {
  showLoading();

  // 標準コンボボックス取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.pageCommonRequest,
    condList: [
      {
        // 秤量室
        cmbId: 'wgtRoomNo',
        condKey: 'm_wgt_room',
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setComboBoxOptionList(conditionDataRef.value, comboBoxResData.rData.rList);
  }

  // NOTE:以下のコードは requestApiGetWeighingSOPList とリクエストパラメータの指定以外同一
  // 初期表示として秤量前後SOP一覧検索
  const { responseRef, errorRef } = await useGetWeighingSOPList({
    ...props.pageCommonRequest, // 初回設定時はprops経由で共通リクエストパラメータを設定
    ...searchRequestData!,
  });
  if (errorRef.value) {
    closeLoading();

    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }
  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  tablePropsDataRef.value.searchData = tableSearchDataRef.value;
  tablePropsDataRef.value.tableData = getTableData(
    responseRef.value.data.rData.wgtSopRecList,
  );

  const usrGridResData = await useGetUsrGrid(props.pageCommonRequest);
  if (usrGridResData) {
    tablePropsDataRef.value.usrGridInfo!.colSort = usrGridResData.colSort;
    tablePropsDataRef.value.usrGridInfo!.colHide = usrGridResData.colHide;
  }

  closeLoading();
};

const updateSelectedRow = (v: SopRecordTableRowData | null) => {
  selectedRow = v;
};

/**
 * 条件検索サイドバー折りたたみ
 */
const showCondition = () => {
  tablePropsDataRef.value.showConditionSearch =
    !tablePropsDataRef.value.showConditionSearch;
};

onMounted(wgtWeighingSOPListInit);
</script>
<style lang="scss" scoped>
$namespace: 'wgt-weighing-sop-list';

.#{$namespace} {
  background-color: $white750;
  display: flex;
  flex-direction: column;
  flex-grow: 1;

  /** element plus */
  :deep(.el-card__body) {
    padding-top: 32px;
    padding-bottom: 0;
    padding-inline: 16px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  &_el-row {
    flex-grow: 1;
    flex-wrap: nowrap;

    &-show-condition {
      .#{$namespace}_search {
        max-width: 275px;
        opacity: 1;
        transition: max-width 0.28s ease;
        width: 100%;
      }

      .#{$namespace}_table {
        max-width: calc(100% - 290px);
        padding-left: 15px;
      }
    }
  }

  &_search {
    transition: max-width 0.28s ease-in-out;
    max-width: 0;
    opacity: 0;
    flex-shrink: 0;
  }
  &_table {
    max-width: 100%;
  }

  &_animated-col {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }
}
</style>
