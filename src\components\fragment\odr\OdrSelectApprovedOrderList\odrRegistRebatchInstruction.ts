import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';

const { t } = i18n.global;

const getProcessDataInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 製造番号
    lotNo: {
      label: { text: t('Odr.Chr.txtDisplayLotNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
    // 製造指図番号
    odrNo: {
      label: { text: t('Odr.Chr.txtOrderNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
    // 品名
    dspNmJp: {
      label: { text: t('Odr.Chr.txtMaterialName') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
    // 製造予定日
    odrYmd: {
      label: { text: t('Odr.Chr.txtOrderScheduleDate') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
    // 計画コメント
    skdAddExpl: {
      label: { text: t('Odr.Chr.txtPlanComment') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
    // 指図コメント
    handOverTxt: {
      label: { text: t('Odr.Chr.txtHandOverComment') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 12,
    },
  });

export default getProcessDataInfoShowItems;
