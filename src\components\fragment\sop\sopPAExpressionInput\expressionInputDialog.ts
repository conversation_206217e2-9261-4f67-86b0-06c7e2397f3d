import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// const FUNCTION_LIST = [
//   'AVERAGE',
//   'COUNT',
//   'MAX',
//   'MIN',
//   'MOD',
//   'IF',
//   'MID',
//   'LEFT',
//   'RIGHT',
//   'LEN',
//   'VALUE',
//   'val',
//   'DATEDIF',
//   'HOUR',
//   'MINUTE',
//   'DateTimeDiff',
//   'NVAL',
//   'GVAL',
// ];

// type TriggerType = 'blur' | 'change';
// type RuleTriggerType = TriggerType | TriggerType[];
// type RuleItemOption = {
//   message: string;
//   trigger: RuleTriggerType;
//   required?: boolean;
//   type?: string;
//   pattern?: RegExp | string;
//   max?: number | undefined;
//   min?: number | undefined;
//   difference?: number | undefined;
// };
// type SearchPatternOption = (option?: {
//   message?: string;
//   isSearch?: true;
// }) => RuleItemOption;

// type ExpressionRulesOption = {
//   existFunction: SearchPatternOption;
//   checkOperator: SearchPatternOption;
//   checkBrackets: SearchPatternOption;
// };
// const expressionRules: ExpressionRulesOption = {
//   checkOperator: (option = {}) => {
//     const { message } = option;
//     return {
//       pattern: /^(?!.*[\\+\-\\*/~]{2,}).*$/,
//       trigger: 'blur',
//       message: message || '演算子の連続使用はできません',
//     };
//   },
//   existFunction: (option = {}) => {
//     const { message } = option;
//     return {
//       pattern: new RegExp(`(?<=\\b(?:${FUNCTION_LIST.join('|')})\\s*)\\(`),
//       trigger: 'blur',
//       message: message || '指定の関数は使用できません',
//     };
//   },
//   checkBrackets: (option = {}) => {
//     const { message } = option;
//     return {
//       // pattern: /^(?=(?:[^"]*"[^"]*")*[^"]*$)(?:(?:[^()""]|\([^()""]*\))*|\([^()""]*\))*$/,
//       // pattern: /^(?=(?:[^"]*"[^"]*")*[^"]*$)(?:[^()"]|\([^()"]*\))*$/,
//       pattern:
//         /^(?=(?:[^"]*"[^"]*")*[^"]*$)(?:[^()"]|"(?:[^"]*)"|\([^()]*\))*$/,
//       trigger: 'blur',
//       message: message || '式が不正です',
//     };
//   },
// };

// アイテム定義
export const sopExpressionInputDialogFormItems: CustomFormType['formItems'] = {
  expression: {
    formModelValue: '',
    label: { text: t('SOP.Chr.txtExpressionInput') },
    formRole: 'textBox',
    rules: [
      rules.length(256),
      // rules.prohibitedCharacters([`|`, `~`, `'`, `\\`, `;`, `\r`, `\n`]),
      // expressionRules.checkOperator(),
      // expressionRules.existFunction(),
      // expressionRules.checkBrackets(),
    ],
  },
};
// モデル定義
export const sopExpressionInputDialogFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(sopExpressionInputDialogFormItems);
