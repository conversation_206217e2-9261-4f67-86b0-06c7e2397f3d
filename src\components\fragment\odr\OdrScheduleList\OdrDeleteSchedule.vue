<template>
  <!-- 計画削除ダイアログ -->
  <DialogWindow
    :title="$t('Odr.Chr.txtPlanDelete')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="() => console.log('onReject odrDeleteScheduleDialog')"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="odrDeleteScheduleFormRef.formModel"
      :formItems="odrDeleteScheduleFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          odrDeleteScheduleFormRef.customForm = v;
        }
      "
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 小日程計画削除の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPlanDeleteVisible"
    :dialogProps="messageBoxPlanDeletePropsRef"
    :cancelCallback="() => closeDialog('messageBoxPlanDeleteVisible')"
    :submitCallback="requestApiPlanDelete"
  />
  <!-- 小日程計画削除完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPlanDeleteFinishedVisible"
    :dialogProps="messageBoxPlanDeleteFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxPlanDeleteFinished"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import onValidateHandler, {
  handleValidationBySingleRowSelect,
} from '@/utils/validateHandler';
import { closeLoading, showLoading } from '@/utils/dialog';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  DeleteScheduleRequestData,
  GetScheduleDeleteInitResData,
  GetScheduleListData,
  GetScheduleDeleteInitRequestData,
} from '@/types/HookUseApi/OdrTypes';
import { useGetScheduleDeleteInit, useDeleteSchedule } from '@/hooks/useApi';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import {
  getOdrDeleteScheduleFormItems,
  odrDeleteScheduleFormModel,
} from './odrDeleteSchedule';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxPlanDeleteVisible'
  | 'messageBoxPlanDeleteFinishedVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxPlanDeleteVisible: false,
  messageBoxPlanDeleteFinishedVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 小日程計画削除の確認メッセージボックス
const messageBoxPlanDeletePropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titlePlanDelete'),
  content: t('Odr.Msg.contentPlanDelete'),
  type: 'question',
});

// 小日程計画削除の完了メッセージボックス
const messageBoxPlanDeleteFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

const odrDeleteScheduleFormRef = ref<CustomFormType>({
  formItems: getOdrDeleteScheduleFormItems(),
  formModel: odrDeleteScheduleFormModel,
});

// 小日程計画削除初期表示のレスポンスデータ
let initResponseData: GetScheduleDeleteInitResData = {
  skdNo: '',
  planPrcNo: '',
  matNo: '',
  dspNmJp: '',
  unitNmJp: '',
  rxNmJp: '',
  mbrNo: '',
  validYmd: '',
  stdPrdQty: '',
  planQty: '',
  odrDts: '',
  skdAddExpl: '',
  updDts: '',
};

type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  selectedRows: GetScheduleListData[]; // 親ページの選択行情報
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // 1.下記チェックを行い、チェックOKなら処理継続する。
  // １ 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    odrDeleteScheduleFormRef.value.customForm !== undefined &&
    (await odrDeleteScheduleFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return false;
  }

  // 2.以下のメッセージを表示
  // 計画削除の確認
  openDialog('messageBoxPlanDeleteVisible');
  return false;
};

// 計画削除の確認メッセージ'OK'押下時処理
// 計画削除のAPIリクエスト処理
const requestApiPlanDelete = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxPlanDeleteVisible');

  showLoading();
  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する。
  const requestData: DeleteScheduleRequestData = {
    skdNo: initResponseData.skdNo,
    updDts: initResponseData.updDts,
  };
  const { responseRef, errorRef } = await useDeleteSchedule({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxPlanDeletePropsRef.value.title,
    msgboxMsgTxt: messageBoxPlanDeletePropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return false;
  }

  if (responseRef.value) {
    // 4．小日程計画を削除する。
    // ・データベースを削除した後、以下のメッセージを表示する。
    messageBoxPlanDeleteFinishedPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxPlanDeleteFinishedPropsRef.value.content =
      responseRef.value.data.rMsg;
  }

  closeLoading();
  openDialog('messageBoxPlanDeleteFinishedVisible');
  return false;
};

// 小日程計画削除完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxPlanDeleteFinished = () => {
  // 再検索用に親に実行を通知
  emit('submit', props.privilegesBtnRequestData);

  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxPlanDeleteFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

/**
 * 計画削除ダイアログの初期設定
 */
const odrDeleteScheduleInit = async () => {
  // NOTE:未選択なら処理させない。エラーメッセージはTabulatorTable側で出してくれる。
  if (props.selectedRows.length === 0) return;
  // 単一選択されていなかった場合はエラー表示とする
  try {
    await handleValidationBySingleRowSelect(props.selectedRows);
  } catch (error) {
    return;
  }

  // NOTE:単一チェック済みなので確実に単一行。先頭取得する
  const selectedRow = props.selectedRows.at(0)!;

  showLoading();

  // 小日程計画削除初期表示のAPIを行う。
  const requestData: GetScheduleDeleteInitRequestData = {
    skdNo: selectedRow.skdNo,
  };
  const { responseRef, errorRef } = await useGetScheduleDeleteInit({
    ...props.privilegesBtnRequestData,
    ...requestData,
    // NOTE: 直前メッセージは無い。msgbox関連は設定不要。
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  closeLoading();

  if (responseRef.value) {
    // ダイアログ表示初期値として、レスポンス情報を格納
    initResponseData = responseRef.value.data.rData;
  }

  // FormItems初期化
  odrDeleteScheduleFormRef.value.formItems = getOdrDeleteScheduleFormItems();

  // カスタムフォーム初期値設定
  Object.entries(initResponseData).forEach(([key, value]) => {
    if (
      odrDeleteScheduleFormRef.value.formModel &&
      key in odrDeleteScheduleFormRef.value.formModel
    ) {
      odrDeleteScheduleFormRef.value.formItems[key].formModelValue =
        value.toString();
    }
  });

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

// ダイアログ起動条件の監視
watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await odrDeleteScheduleInit();
  },
);
</script>
