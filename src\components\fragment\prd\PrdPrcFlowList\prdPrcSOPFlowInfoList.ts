import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { createFormModelByFormItems } from '@/utils/customForm';

const { t } = i18n.global;

// SOPフロー情報の縦並び項目定義
export const getInfoShowItems: () => InfoShowType['infoShowItems'] = () => ({
  // 製造工程
  prcNmJp: {
    label: { text: t('Prd.Chr.txtOrderProcess') },
    infoShowModelValue: '',
    infoShowRole: 'text',
    span: 12,
  },
  // 承認状態
  recApprovDsp: {
    label: { text: t('Prd.Chr.txtApprovalDsp') },
    infoShowModelValue: '',
    infoShowRole: 'text',
    span: 12,
  },
  // SOPフロー名
  sopFlowNmJp: {
    label: { text: t('Prd.Chr.txtSopFlowName') },
    infoShowModelValue: '',
    infoShowRole: 'text',
    span: 12,
  },
  // 実施記録日
  stDts: {
    label: { text: t('Prd.Chr.txtImplementationRecordDay') },
    infoShowModelValue: '',
    infoShowRole: 'text',
    span: 12,
  },
  // フロー内異状件数
  cmtFlowDev: {
    label: { text: t('Prd.Chr.txtFlowDifferentNumber') },
    infoShowModelValue: '',
    infoShowRole: 'text',
    span: 12,
  },
  // 製造記録承認回数
  recDocVer: {
    label: { text: t('Prd.Chr.txtOrderRecordApproveCount') },
    infoShowModelValue: '',
    infoShowRole: 'text',
    span: 12,
  },
});

// 工程作業記録_SOP作業詳細ダイアログのアイテム定義
export const getDialogFormItems: () => CustomFormType['formItems'] = () => ({
  devModExpl: {
    // NOTE: タグを使用しない。
    tags: [],
    formModelValue: '',
    label: { text: t('Prd.Chr.txtDifferentConfirmComment') },
    // NOTE: rulesは実行時に設定されます
    rules: [],
    formRole: 'textComboBox',
    props: {
      clearable: true,
      placeholder: t('Cm.Chr.txtPlaceholderInputComment'),
    },
    selectOptions: [],
    cmbId: 'devModExpl',
  },
});

// 工程作業記録_SOP作業詳細ダイアログのモデル定義
export const dialogFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getDialogFormItems());
