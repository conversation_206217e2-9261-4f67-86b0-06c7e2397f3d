import CONST from '@/constants/utils';
import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';
import {
  AogCarRsltDtlListData,
  AogRsltCarInitData,
  CarResultData,
} from '@/types/HookUseApi/AogTypes';

const { t } = i18n.global;

export const getAogInputArrivalInspectionRecordRadioButton = (
  aogCarRsltList: AogRsltCarInitData[] = [],
): Record<string, CustomFormType['formItems'][number]> => {
  const createOptionsData = (dtlList?: AogCarRsltDtlListData[]) => ({
    label: dtlList?.map((item) => item.carRsltNm) ?? [],
    value: dtlList?.map((item) => item.carRsltCd) ?? [],
  });

  const result: Record<string, CustomFormType['formItems'][number]> = {};

  aogCarRsltList.forEach((v, i) => {
    const idx = String(i + 1).padStart(2, '0');
    result[`carRsltCd${idx}`] = {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: v.carText },
      formModelValue: '',
      rules: [rules.required('radio')],
      formRole: 'radio',
      props: {
        optionsData: createOptionsData(v.aogCarRsltDtlList),
        modelValue: '',
        disabled: false,
        size: 'small',
        isHorizontal: true,
      },
    };
  });

  result.carExpl = {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    label: { text: t('Inv.Chr.txtComment') },
    formModelValue: '',
    rules: [
      rules.required('textComboBox'),
      rules.length(64, t('Cm.Chr.txtLength', [64])),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ],
    formRole: 'textComboBox',
    props: { clearable: true },
    selectOptions: [],
    cmbId: 'cmtAogAdd',
  };

  return result;
};

export const AogRsltCarRadioButtonFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getAogInputArrivalInspectionRecordRadioButton());

export const carResultData: CarResultData = {
  carText01: '',
  carText02: '',
  carText03: '',
  carText04: '',
  carText05: '',
  carText06: '',
  carText07: '',
  carText08: '',
  carText09: '',
  carText10: '',
  carText11: '',
  carText12: '',
  carText13: '',
  carText14: '',
  carText15: '',
  carText16: '',
  carText17: '',
  carText18: '',
  carText19: '',
  carText20: '',
  carRsltCd01: '',
  carRsltCd02: '',
  carRsltCd03: '',
  carRsltCd04: '',
  carRsltCd05: '',
  carRsltCd06: '',
  carRsltCd07: '',
  carRsltCd08: '',
  carRsltCd09: '',
  carRsltCd10: '',
  carRsltCd11: '',
  carRsltCd12: '',
  carRsltCd13: '',
  carRsltCd14: '',
  carRsltCd15: '',
  carRsltCd16: '',
  carRsltCd17: '',
  carRsltCd18: '',
  carRsltCd19: '',
  carRsltCd20: '',
  carRsltNm01: '',
  carRsltNm02: '',
  carRsltNm03: '',
  carRsltNm04: '',
  carRsltNm05: '',
  carRsltNm06: '',
  carRsltNm07: '',
  carRsltNm08: '',
  carRsltNm09: '',
  carRsltNm10: '',
  carRsltNm11: '',
  carRsltNm12: '',
  carRsltNm13: '',
  carRsltNm14: '',
  carRsltNm15: '',
  carRsltNm16: '',
  carRsltNm17: '',
  carRsltNm18: '',
  carRsltNm19: '',
  carRsltNm20: '',
};
