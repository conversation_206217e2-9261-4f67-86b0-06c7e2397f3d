import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

// NOTE:修正履歴一覧は一部表示、APIの差異があるが、vueとしては共通のものを使う。仕様追加、変更の場合は変更箇所に要注意。
// 修正履歴一覧用テーブル 共通設定
const tablePropsDataSopModListCommon = {
  pageName: 'PrdModifyLogList',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'sopFlowNmJp',

  column: [],
  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
} as const satisfies TabulatorTableIF;

// 確認/参照/承認用テーブル設定
export const tablePropsDataSopModList: TabulatorTableIF = {
  ...tablePropsDataSopModListCommon,

  column: [
    // 修正回数
    {
      title: 'Prd.Chr.txtModifyCount',
      field: 'cmtTimes',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.PRD.CMT_TIMES,
    },
    // SOPフロー名
    {
      title: 'Prd.Chr.txtSopFlowName',
      field: 'sopFlowNmJp',
      width: COLUMN_WIDTHS.SOP_FLOW_NM,
    },
    // 製造工程
    {
      title: 'Prd.Chr.txtOrderProcess',
      field: 'prcNmJp',
      width: COLUMN_WIDTHS.PRC_NM,
    },
    // バッチ番号
    {
      title: 'Prd.Chr.txtBatchNo',
      field: 'riBatchNo',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.BAT_NO,
    },
    // 実行数
    {
      title: 'Prd.Chr.txtSopNodeTimes',
      field: 'sopNodeTimes',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.SOP_NODE_TIMES,
    },
    // 作業指示内容
    {
      title: 'Prd.Chr.txtWorkInstructionDetail',
      field: 'cmtMain1',
      width: COLUMN_WIDTHS.PRD.CMT_MAIN,
    },
    // 単位
    {
      title: 'Prd.Chr.txtUnit',
      field: 'instUnitText',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    // 修正前記録値１
    {
      title: 'Prd.Chr.txtFixBeforeRecordValue1',
      field: 'bRecVal1',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正後記録値１
    {
      title: 'Prd.Chr.txtFixAfterRecordValue1',
      field: 'aRecVal1',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正前記録値２
    {
      title: 'Prd.Chr.txtFixBeforeRecordValue2',
      field: 'bRecVal2',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正後記録値２
    {
      title: 'Prd.Chr.txtFixAfterRecordValue2',
      field: 'aRecVal2',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正前記録値３
    {
      title: 'Prd.Chr.txtFixBeforeRecordValue3',
      field: 'bRecVal3',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正後記録値３
    {
      title: 'Prd.Chr.txtFixAfterRecordValue3',
      field: 'aRecVal3',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正前記録値４
    {
      title: 'Prd.Chr.txtFixBeforeRecordValue4',
      field: 'bRecVal4',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正後記録値４
    {
      title: 'Prd.Chr.txtFixAfterRecordValue4',
      field: 'aRecVal4',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正前記録値５
    {
      title: 'Prd.Chr.txtFixBeforeRecordValue5',
      field: 'bRecVal5',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正後記録値５
    {
      title: 'Prd.Chr.txtFixAfterRecordValue5',
      field: 'aRecVal5',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 記録修正日時
    {
      title: 'Prd.Chr.txtRecordModifyDate',
      field: 'cmtDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // 修正者
    {
      title: 'Prd.Chr.txtModifyUser',
      field: 'modUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正コメント
    {
      title: 'Prd.Chr.txtModifyComment',
      field: 'modExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
  ],
};

// 工程作業記録用
export const tablePropsDataProcess: TabulatorTableIF = {
  ...tablePropsDataSopModListCommon,

  column: [
    // 修正回数
    {
      title: 'Prd.Chr.txtModifyCount',
      field: 'cmtTimes',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.PRD.CMT_TIMES,
    },
    // SOPフロー名
    {
      title: 'Prd.Chr.txtSopFlowName',
      field: 'sopFlowNmJp',
      width: COLUMN_WIDTHS.SOP_FLOW_NM,
    },
    // 実行数
    {
      title: 'Prd.Chr.txtSopNodeTimes',
      field: 'sopNodeTimes',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.SOP_NODE_TIMES,
    },
    // 作業指示内容
    {
      title: 'Prd.Chr.txtWorkInstructionDetail',
      field: 'cmtMain1',
      width: COLUMN_WIDTHS.PRD.CMT_MAIN,
    },
    // 単位
    {
      title: 'Prd.Chr.txtUnit',
      field: 'instUnitText',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    // 修正前記録値１
    {
      title: 'Prd.Chr.txtFixBeforeRecordValue1',
      field: 'bRecVal1',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正後記録値１
    {
      title: 'Prd.Chr.txtFixAfterRecordValue1',
      field: 'aRecVal1',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正前記録値２
    {
      title: 'Prd.Chr.txtFixBeforeRecordValue2',
      field: 'bRecVal2',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正後記録値２
    {
      title: 'Prd.Chr.txtFixAfterRecordValue2',
      field: 'aRecVal2',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正前記録値３
    {
      title: 'Prd.Chr.txtFixBeforeRecordValue3',
      field: 'bRecVal3',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正後記録値３
    {
      title: 'Prd.Chr.txtFixAfterRecordValue3',
      field: 'aRecVal3',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正前記録値４
    {
      title: 'Prd.Chr.txtFixBeforeRecordValue4',
      field: 'bRecVal4',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正後記録値４
    {
      title: 'Prd.Chr.txtFixAfterRecordValue4',
      field: 'aRecVal4',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正前記録値５
    {
      title: 'Prd.Chr.txtFixBeforeRecordValue5',
      field: 'bRecVal5',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 修正後記録値５
    {
      title: 'Prd.Chr.txtFixAfterRecordValue5',
      field: 'aRecVal5',
      width: COLUMN_WIDTHS.PRD.REC_VAL,
    },
    // 記録修正日時
    {
      title: 'Prd.Chr.txtRecordModifyDate',
      field: 'cmtDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // 修正者
    {
      title: 'Prd.Chr.txtModifyUser',
      field: 'modUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    // 修正コメント
    {
      title: 'Prd.Chr.txtModifyComment',
      field: 'modExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
  ],
};
