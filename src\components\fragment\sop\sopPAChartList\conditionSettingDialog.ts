import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// コンボボックスダイアログのアイテム定義
export const selectComboBoxDialogFormItems: CustomFormType['formItems'] = {
  matNo: {
    formModelValue: '',
    label: { text: '品目CD/品目名' },
    formRole: 'selectComboBox',
    selectOptions: [],
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    rules: [rules.required('selectComboBox')],
    cmbId: 'matNo',
  },
  rxNo: {
    formModelValue: '',
    label: { text: '処方' },
    formRole: 'selectComboBox',
    selectOptions: [],
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    rules: [rules.required('selectComboBox')],
    cmbId: 'rxNo',
  },
  prcSeq: {
    formModelValue: '',
    label: { text: '工程順' },
    formRole: 'selectComboBox',
    selectOptions: [],
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    rules: [rules.required('selectComboBox')],
    cmbId: 'prcSeq',
  },
};

// コンボボックスダイアログのモデル定義
export const selectComboBoxDialogFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(selectComboBoxDialogFormItems);
